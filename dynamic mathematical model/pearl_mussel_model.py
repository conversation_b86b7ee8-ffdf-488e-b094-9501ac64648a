"""
珍珠蚌种群健康度模型 - 主程序
重构版本：模块化设计，分离数据生成和可视化功能
"""

import gmsh
import numpy as np
from data_generator import SensorDataGenerator
from visualization import ModelVisualizer
from model_core import PearlMusselModel

def main():
    """主函数：协调各个模块完成完整的建模流程"""

    # 初始化 Gmsh
    gmsh.initialize()
    try:
        gmsh.open('lake_mesher/lake_box_mesh_detailed.msh')
        print("已加载网格文件 'lake_box_mesh_detailed.msh'")
    except:
        print("警告：无法加载网格文件，程序将继续运行但可能出现错误")
        return

    # 获取节点和单元信息
    node_tags, node_coords, _ = gmsh.model.mesh.getNodes()
    node_coords = node_coords.reshape(-1, 3)  # 转换为 (N, 3) 数组
    element_types, element_tags, element_node_tags = gmsh.model.mesh.getElements(dim=3)
    elements = element_node_tags[0].reshape(-1, 4)  # 假设四面体单元

    # 参数设置
    num_nodes = len(node_tags)
    t_span = [0, 30]  # 时间跨度：30天
    u0 = np.full(num_nodes, 0.5)  # 初始健康度指数
    k = 1.0  # 环境承载力

    print(f"网格节点数量: {num_nodes}")

    # 1. 生成传感器数据
    print("正在生成传感器数据...")
    data_generator = SensorDataGenerator(seed=42)
    sensor_data = data_generator.generate_realistic_data(
        node_coords=node_coords,
        num_nodes=num_nodes
    )

    # 保存传感器数据
    data_generator.save_sensor_data(sensor_data, 'sensor_data.npz')
    print("✅ 传感器数据已保存至 'sensor_data.npz'")

    # 2. 运行珍珠蚌模型
    print("正在运行珍珠蚌健康度模型...")
    model = PearlMusselModel()

    # 计算每个节点的生长率和胁迫因子
    r = np.zeros(num_nodes)
    sigma_total = np.zeros(num_nodes)
    for i in range(num_nodes):
        r[i] = model.ideal_growth_rate(sensor_data['temperature'][i], sensor_data['chlorophyll'][i])
        sigma_total[i] = model.total_stress(sensor_data['ph'][i], sensor_data['conductivity'][i])

    # 求解 ODE，计算每个节点的健康度指数
    u_final = np.zeros(num_nodes)
    for i in range(num_nodes):
        t, u = model.solve_ode(u0[i], t_span, r[i], sigma_total[i], k)
        u_final[i] = u[-1]  # 取最终时间步的健康度

    print("✅ 模型计算完成")

    # 3. 可视化结果
    print("正在生成可视化图表...")
    visualizer = ModelVisualizer()

    # 创建综合可视化
    visualizer.create_comprehensive_visualization(
        node_coords=node_coords,
        sensor_data=sensor_data,
        health_index=u_final,
        save_path='results'
    )

    # 保存最终结果
    np.savetxt('health_index.txt', np.column_stack((node_coords, u_final)),
               header='x y z health_index', fmt='%.6f')
    print("✅ 健康度指数已保存至 'health_index.txt'")
    print("✅ 所有可视化图表已保存至 'results' 目录")

    gmsh.finalize()

if __name__ == "__main__":
    main()