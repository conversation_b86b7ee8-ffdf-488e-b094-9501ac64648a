"""
珍珠蚌种群健康度模型核心
包含所有数学模型和计算函数
"""

import numpy as np
from scipy.integrate import solve_ivp

class PearlMusselModel:
    """珍珠蚌种群健康度模型类"""
    
    def __init__(self, 
                 pH_opt=7.5, w_pH=1.0,
                 EC_opt=500, w_EC=200,
                 r_0=0.1, T_opt=25, sigma_T=5, K_m=10):
        """
        初始化模型参数
        
        Args:
            pH_opt (float): 最适pH值
            w_pH (float): pH胁迫宽度参数
            EC_opt (float): 最适电导率 (μS/cm)
            w_EC (float): 电导率胁迫宽度参数
            r_0 (float): 基础生长率
            T_opt (float): 最适温度 (°C)
            sigma_T (float): 温度胁迫标准差
            K_m (float): 叶绿素半饱和常数
        """
        self.pH_opt = pH_opt
        self.w_pH = w_pH
        self.EC_opt = EC_opt
        self.w_EC = w_EC
        self.r_0 = r_0
        self.T_opt = T_opt
        self.sigma_T = sigma_T
        self.K_m = K_m
    
    def stress_function_pH(self, pH):
        """
        pH 胁迫度函数
        
        Args:
            pH (float or np.array): pH值
            
        Returns:
            float or np.array: pH胁迫度 (0-1, 0表示无胁迫)
        """
        return 1 - np.exp(-((pH - self.pH_opt) ** 2) / (self.w_pH ** 2))
    
    def stress_function_EC(self, EC):
        """
        电导率 (EC) 胁迫度函数
        
        Args:
            EC (float or np.array): 电导率值 (μS/cm)
            
        Returns:
            float or np.array: 电导率胁迫度 (0-1, 0表示无胁迫)
        """
        return 1 - np.exp(-((EC - self.EC_opt) ** 2) / (self.w_EC ** 2))
    
    def stress_function_temperature(self, T):
        """
        温度胁迫度函数
        
        Args:
            T (float or np.array): 温度值 (°C)
            
        Returns:
            float or np.array: 温度胁迫度 (0-1, 0表示无胁迫)
        """
        return 1 - np.exp(-((T - self.T_opt) ** 2) / (2 * self.sigma_T ** 2))
    
    def stress_function_chlorophyll(self, ChlA, ChlA_min=5.0, ChlA_max=30.0):
        """
        叶绿素胁迫度函数（营养不足或过量都会产生胁迫）
        
        Args:
            ChlA (float or np.array): 叶绿素a浓度 (μg/L)
            ChlA_min (float): 最低营养需求
            ChlA_max (float): 营养过量阈值
            
        Returns:
            float or np.array: 叶绿素胁迫度 (0-1, 0表示无胁迫)
        """
        # 营养不足胁迫
        low_stress = np.where(ChlA < ChlA_min, 
                             (ChlA_min - ChlA) / ChlA_min, 0)
        
        # 营养过量胁迫
        high_stress = np.where(ChlA > ChlA_max, 
                              (ChlA - ChlA_max) / ChlA_max, 0)
        
        return np.maximum(low_stress, high_stress)
    
    def total_stress(self, pH, EC, T=None, ChlA=None, 
                    w_pH=0.3, w_EC=0.3, w_T=0.2, w_ChlA=0.2):
        """
        综合环境胁迫因子
        
        Args:
            pH (float or np.array): pH值
            EC (float or np.array): 电导率值
            T (float or np.array, optional): 温度值
            ChlA (float or np.array, optional): 叶绿素值
            w_pH, w_EC, w_T, w_ChlA (float): 各因子权重
            
        Returns:
            float or np.array: 综合胁迫度
        """
        # 基础胁迫（pH和电导率）
        sigma_pH = self.stress_function_pH(pH)
        sigma_EC = self.stress_function_EC(EC)
        
        total_stress = w_pH * sigma_pH + w_EC * sigma_EC
        
        # 如果提供了温度数据
        if T is not None:
            sigma_T = self.stress_function_temperature(T)
            total_stress += w_T * sigma_T
        
        # 如果提供了叶绿素数据
        if ChlA is not None:
            sigma_ChlA = self.stress_function_chlorophyll(ChlA)
            total_stress += w_ChlA * sigma_ChlA
        
        return total_stress
    
    def ideal_growth_rate(self, T, ChlA):
        """
        理想生长率，考虑温度和叶绿素a浓度
        
        Args:
            T (float or np.array): 温度 (°C)
            ChlA (float or np.array): 叶绿素a浓度 (μg/L)
            
        Returns:
            float or np.array: 理想生长率
        """
        # 温度对生长的影响（高斯函数）
        temp_component = np.exp(-((T - self.T_opt) ** 2) / (2 * self.sigma_T ** 2))
        
        # 食物（叶绿素）对生长的影响（Michaelis-Menten动力学）
        food_component = ChlA / (self.K_m + ChlA)
        
        return self.r_0 * temp_component * food_component
    
    def ode_model(self, t, u, r, sigma_total, k=1.0):
        """
        核心动力学方程：du/dt = r * u * (1 - u/k) - sigma_total * u
        
        Args:
            t (float): 时间
            u (float): 当前健康度指数
            r (float): 生长率
            sigma_total (float): 总胁迫度
            k (float): 环境承载力
            
        Returns:
            float: 健康度变化率
        """
        return r * u * (1 - u / k) - sigma_total * u
    
    def solve_ode(self, u0, t_span, r, sigma_total, k=1.0, method='RK45'):
        """
        求解 ODE，得到健康度指数 u
        
        Args:
            u0 (float): 初始健康度指数
            t_span (list): 时间跨度 [t_start, t_end]
            r (float): 生长率
            sigma_total (float): 总胁迫度
            k (float): 环境承载力
            method (str): 求解方法
            
        Returns:
            tuple: (时间数组, 健康度数组)
        """
        sol = solve_ivp(self.ode_model, t_span, [u0], 
                       args=(r, sigma_total, k), 
                       method=method, dense_output=True)
        return sol.t, sol.y[0]
    
    def solve_ode_detailed(self, u0, t_span, r, sigma_total, k=1.0, 
                          num_points=100, method='RK45'):
        """
        求解 ODE 并返回详细的时间序列
        
        Args:
            u0 (float): 初始健康度指数
            t_span (list): 时间跨度 [t_start, t_end]
            r (float): 生长率
            sigma_total (float): 总胁迫度
            k (float): 环境承载力
            num_points (int): 输出时间点数量
            method (str): 求解方法
            
        Returns:
            tuple: (时间数组, 健康度数组)
        """
        sol = solve_ivp(self.ode_model, t_span, [u0], 
                       args=(r, sigma_total, k), 
                       method=method, dense_output=True)
        
        # 生成均匀分布的时间点
        t_eval = np.linspace(t_span[0], t_span[1], num_points)
        u_eval = sol.sol(t_eval)[0]
        
        return t_eval, u_eval
    
    def calculate_equilibrium(self, r, sigma_total, k=1.0):
        """
        计算平衡态健康度指数
        
        Args:
            r (float): 生长率
            sigma_total (float): 总胁迫度
            k (float): 环境承载力
            
        Returns:
            float: 平衡态健康度指数
        """
        if r <= sigma_total:
            return 0.0  # 种群无法存活
        else:
            return k * (1 - sigma_total / r)
    
    def sensitivity_analysis(self, base_params, param_variations):
        """
        敏感性分析
        
        Args:
            base_params (dict): 基础参数
            param_variations (dict): 参数变化范围
            
        Returns:
            dict: 敏感性分析结果
        """
        results = {}
        
        for param_name, variation_range in param_variations.items():
            param_values = []
            health_values = []
            
            for variation in variation_range:
                # 修改参数
                modified_params = base_params.copy()
                modified_params[param_name] *= (1 + variation)
                
                # 计算健康度
                if param_name in ['pH', 'EC']:
                    stress = self.total_stress(modified_params['pH'], 
                                             modified_params['EC'])
                    growth = self.ideal_growth_rate(modified_params['T'], 
                                                  modified_params['ChlA'])
                    health = self.calculate_equilibrium(growth, stress)
                    
                    param_values.append(modified_params[param_name])
                    health_values.append(health)
            
            results[param_name] = {
                'param_values': np.array(param_values),
                'health_values': np.array(health_values)
            }
        
        return results
    
    def get_model_info(self):
        """返回模型参数信息"""
        return {
            'pH_optimal': self.pH_opt,
            'pH_tolerance': self.w_pH,
            'EC_optimal': self.EC_opt,
            'EC_tolerance': self.w_EC,
            'temperature_optimal': self.T_opt,
            'temperature_tolerance': self.sigma_T,
            'base_growth_rate': self.r_0,
            'chlorophyll_half_saturation': self.K_m
        }
