"""
传感器数据生成器
生成类似真实传感器数据的模拟数据，包括温度、pH值、电导率和叶绿素值
"""

import numpy as np
from scipy.ndimage import gaussian_filter
from scipy.interpolate import griddata
import matplotlib.pyplot as plt

class SensorDataGenerator:
    """传感器数据生成器类"""
    
    def __init__(self, seed=42):
        """
        初始化数据生成器
        
        Args:
            seed (int): 随机种子，确保结果可重复
        """
        self.seed = seed
        np.random.seed(seed)
        
        # 默认参数范围
        self.default_params = {
            'temperature': {'mean': 25.0, 'std': 3.0, 'min': 18.0, 'max': 32.0},
            'ph': {'mean': 7.5, 'std': 0.8, 'min': 6.0, 'max': 9.0},
            'conductivity': {'mean': 500.0, 'std': 150.0, 'min': 200.0, 'max': 800.0},
            'chlorophyll': {'mean': 15.0, 'std': 8.0, 'min': 2.0, 'max': 40.0}
        }
    
    def generate_spatial_pattern(self, coords, base_value, variation_scale=1.0, 
                                correlation_length=5.0):
        """
        生成具有空间相关性的数据模式
        
        Args:
            coords (np.array): 节点坐标 (N, 3)
            base_value (float): 基础值
            variation_scale (float): 变化幅度
            correlation_length (float): 空间相关长度
            
        Returns:
            np.array: 具有空间模式的数据
        """
        # 提取 x, y 坐标
        x, y = coords[:, 0], coords[:, 1]
        
        # 创建规则网格用于插值
        x_min, x_max = x.min(), x.max()
        y_min, y_max = y.min(), y.max()
        
        # 网格分辨率
        grid_size = 50
        xi = np.linspace(x_min, x_max, grid_size)
        yi = np.linspace(y_min, y_max, grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        # 生成随机噪声场
        noise = np.random.normal(0, variation_scale, (grid_size, grid_size))
        
        # 应用高斯滤波器创建空间相关性
        sigma = correlation_length * grid_size / (x_max - x_min)
        smooth_noise = gaussian_filter(noise, sigma=sigma)
        
        # 插值到实际节点位置
        points = np.column_stack((xi_grid.ravel(), yi_grid.ravel()))
        values = smooth_noise.ravel()
        interpolated = griddata(points, values, (x, y), method='linear', fill_value=0)
        
        return base_value + interpolated
    
    def add_temporal_variation(self, data, time_factor=0.1):
        """
        添加时间变化（模拟日变化或季节变化）
        
        Args:
            data (np.array): 基础数据
            time_factor (float): 时间变化因子
            
        Returns:
            np.array: 添加时间变化后的数据
        """
        # 模拟日变化（正弦波）
        time_variation = time_factor * np.sin(2 * np.pi * np.random.random(len(data)))
        return data + time_variation
    
    def add_measurement_noise(self, data, noise_level=0.02):
        """
        添加测量噪声
        
        Args:
            data (np.array): 原始数据
            noise_level (float): 噪声水平（相对于数据范围）
            
        Returns:
            np.array: 添加噪声后的数据
        """
        data_range = data.max() - data.min()
        noise = np.random.normal(0, noise_level * data_range, len(data))
        return data + noise
    
    def generate_temperature_data(self, coords, num_nodes):
        """生成温度数据"""
        params = self.default_params['temperature']
        
        # 基础空间模式
        base_temp = self.generate_spatial_pattern(
            coords, params['mean'], params['std'], correlation_length=8.0
        )
        
        # 添加深度效应（假设z坐标代表深度）
        if coords.shape[1] > 2:
            depth_effect = -0.5 * (coords[:, 2] - coords[:, 2].min())
            base_temp += depth_effect
        
        # 添加时间变化和噪声
        temp_data = self.add_temporal_variation(base_temp, time_factor=1.5)
        temp_data = self.add_measurement_noise(temp_data, noise_level=0.03)
        
        # 限制在合理范围内
        return np.clip(temp_data, params['min'], params['max'])
    
    def generate_ph_data(self, coords, num_nodes):
        """生成pH值数据"""
        params = self.default_params['ph']
        
        # pH通常在湖泊中有分层现象
        base_ph = self.generate_spatial_pattern(
            coords, params['mean'], params['std'] * 0.6, correlation_length=6.0
        )
        
        # 添加局部变化（模拟植物活动影响）
        local_variation = 0.3 * np.sin(4 * np.pi * coords[:, 0] / coords[:, 0].max()) * \
                         np.cos(4 * np.pi * coords[:, 1] / coords[:, 1].max())
        base_ph += local_variation
        
        # 添加时间变化和噪声
        ph_data = self.add_temporal_variation(base_ph, time_factor=0.2)
        ph_data = self.add_measurement_noise(ph_data, noise_level=0.02)
        
        return np.clip(ph_data, params['min'], params['max'])
    
    def generate_conductivity_data(self, coords, num_nodes):
        """生成电导率数据"""
        params = self.default_params['conductivity']
        
        # 电导率通常与污染源距离相关
        base_ec = self.generate_spatial_pattern(
            coords, params['mean'], params['std'], correlation_length=10.0
        )
        
        # 模拟污染源影响（假设在某个位置有污染源）
        pollution_center = np.array([coords[:, 0].mean(), coords[:, 1].mean()])
        distances = np.sqrt((coords[:, 0] - pollution_center[0])**2 + 
                           (coords[:, 1] - pollution_center[1])**2)
        pollution_effect = 100 * np.exp(-distances / 5.0)
        base_ec += pollution_effect
        
        # 添加时间变化和噪声
        ec_data = self.add_temporal_variation(base_ec, time_factor=20.0)
        ec_data = self.add_measurement_noise(ec_data, noise_level=0.05)
        
        return np.clip(ec_data, params['min'], params['max'])
    
    def generate_chlorophyll_data(self, coords, num_nodes):
        """生成叶绿素值数据"""
        params = self.default_params['chlorophyll']
        
        # 叶绿素分布通常与光照和营养有关
        base_chl = self.generate_spatial_pattern(
            coords, params['mean'], params['std'], correlation_length=4.0
        )
        
        # 模拟光照效应（浅水区叶绿素更高）
        if coords.shape[1] > 2:
            light_effect = 5.0 * np.exp(-(coords[:, 2] - coords[:, 2].min()) / 2.0)
            base_chl += light_effect
        
        # 添加季节性变化
        seasonal_factor = 1.0 + 0.3 * np.sin(2 * np.pi * np.random.random())
        base_chl *= seasonal_factor
        
        # 添加时间变化和噪声
        chl_data = self.add_temporal_variation(base_chl, time_factor=2.0)
        chl_data = self.add_measurement_noise(chl_data, noise_level=0.08)
        
        return np.clip(chl_data, params['min'], params['max'])
    
    def generate_realistic_data(self, node_coords, num_nodes):
        """
        生成所有传感器数据
        
        Args:
            node_coords (np.array): 节点坐标
            num_nodes (int): 节点数量
            
        Returns:
            dict: 包含所有传感器数据的字典
        """
        print("生成温度数据...")
        temperature = self.generate_temperature_data(node_coords, num_nodes)
        
        print("生成pH值数据...")
        ph = self.generate_ph_data(node_coords, num_nodes)
        
        print("生成电导率数据...")
        conductivity = self.generate_conductivity_data(node_coords, num_nodes)
        
        print("生成叶绿素数据...")
        chlorophyll = self.generate_chlorophyll_data(node_coords, num_nodes)
        
        return {
            'temperature': temperature,
            'ph': ph,
            'conductivity': conductivity,
            'chlorophyll': chlorophyll,
            'coordinates': node_coords
        }
    
    def save_sensor_data(self, sensor_data, filename):
        """保存传感器数据到文件"""
        np.savez(filename, **sensor_data)
        print(f"传感器数据已保存至: {filename}")
    
    def load_sensor_data(self, filename):
        """从文件加载传感器数据"""
        data = np.load(filename)
        return {key: data[key] for key in data.files}
    
    def plot_data_statistics(self, sensor_data):
        """绘制数据统计图"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
        titles = ['温度 (°C)', 'pH值', '电导率 (μS/cm)', '叶绿素 (μg/L)']
        
        for i, (param, title) in enumerate(zip(parameters, titles)):
            ax = axes[i//2, i%2]
            data = sensor_data[param]
            
            ax.hist(data, bins=30, alpha=0.7, edgecolor='black')
            ax.set_title(f'{title} 分布')
            ax.set_xlabel(title)
            ax.set_ylabel('频次')
            ax.grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_val = np.mean(data)
            std_val = np.std(data)
            ax.axvline(mean_val, color='red', linestyle='--', 
                      label=f'均值: {mean_val:.2f}')
            ax.axvline(mean_val + std_val, color='orange', linestyle='--', alpha=0.7)
            ax.axvline(mean_val - std_val, color='orange', linestyle='--', alpha=0.7)
            ax.legend()
        
        plt.tight_layout()
        plt.savefig('sensor_data_statistics.png', dpi=300, bbox_inches='tight')
        plt.show()
