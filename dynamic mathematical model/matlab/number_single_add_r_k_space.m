
% %% 计算增长率和环境容量
% % 参数设置
% r0 = 0.5;       % 基础增长率（1/天）
% T_opt = 28;     % 最适温度（℃）
% S_opt = 25;     % 最适盐度（PSU）
% sigma_T = 5;    % 温度耐受范围（℃）
% sigma_S = 5;    % 盐度耐受范围（PSU）
% k0 = 10000;     % 基础环境容量（个体数/网格）
% T_ref = 20;     % 参考温度（℃）
% S_ref = 30;     % 参考盐度（PSU）
% alpha_T = 0.01; % 温度抑制系数（1/℃²）
% alpha_S = 0.005;% 盐度抑制系数（1/PSU²）
% 
% % 使用插值后的温度和盐度数据
% T_grid = theta_interp;
% S_grid = salt_interp;
% 
% % 计算增长率 r(T, S)
% r_interp = r0 * exp(-(T_grid - T_opt).^2 / (2*sigma_T^2)) .* ...
%     exp(-(S_grid - S_opt).^2 / (2*sigma_S^2));
% 
% % 计算环境容量 k(T, S)
% k_interp = k0 * (1 - alpha_T*(T_grid - T_ref).^2) .* ...
%     (1 - alpha_S*(S_grid - S_ref).^2);
% 
% 
% %%
% % 绘制增长率和环境容量
% figure('Position', [100 100 1200 500]);
% 
% % 绘制增长率
% subplot(1,2,1)
% trisurf(t, p(:,1), p(:,2), r_interp, 'FaceColor', 'interp', 'EdgeColor', 'none');
% title('Growth Rate (r)')
% xlim([121, 122.6]); ylim([30.65, 32.3]); 
% view(0,90); axis equal; grid off
% caxis([min(r_interp,[],'omitnan') max(r_interp,[],'omitnan')])
% cbar_r = colorbar('eastoutside');
% cbar_r.Label.String = 'Growth Rate (1/天)';
% 
% % 绘制环境容量
% subplot(1,2,2)
% trisurf(t, p(:,1), p(:,2), k_interp, 'FaceColor', 'interp', 'EdgeColor', 'none');
% title('Environmental Capacity (k)')
% xlim([121, 122.6]); ylim([30.65, 32.3]); 
% view(0,90); axis equal; grid off
% caxis([min(k_interp,[],'omitnan') max(k_interp,[],'omitnan')])
% cbar_k = colorbar('eastoutside');
% cbar_k.Label.String = 'Environmental Capacity (个体数/网格)';
clc
clear

%% 包括了某点点鱼群数量的变化
load('k_10.mat');
load('r_10.mat');
load('p.mat');
load('t.mat');
% 参数设置
u0 = 10000;        % 初始种群数量
C0 = 0;            % 初始微塑料浓度
alpha = 0.6;         % 微塑料影响强度
S = 0.042;         % 微塑料吸收速率
g = 1.2;           % 排泄率

% 导入微塑料浓度数据
initial_concentration = load('/Volumes/T7_Shield/期刊文章/微塑料—Zhang/主代码/3.1等高线并平均/concentration_final.mat', 'concentration');
concentration = initial_concentration.concentration;
% 获取节点坐标和连接信息
coordinates = p; % 节点坐标
elements = t;    % 连接信息

 r_interp= r;
  k_interp= k;

% 找到指定点的索引
point = [121.994, 31.6383];
[~, idx] = min(sum((coordinates - point).^2, 2));

% 初始化种群密度矩阵
u_density = zeros(size(coordinates, 1), size(concentration, 2));

% 对每个节点进行循环
for i = 1:size(coordinates, 1)
    % 获取当前节点的环境微塑料浓度（取最后一个时间步）
    CE_pq = concentration(i, end);
    
    % 获取当前节点的 r 和 k
    current_r = r_interp(i);
    current_k = k_interp(i);
    
    % 定义ODE函数
    odefun = @(t, y) [
        y(1) * (current_r*(1 - y(1)/current_k) - alpha*y(2));  % du/dt
        S * CE_pq - g * y(2)                                   % dC/dt
    ];

    % 时间跨度
    tspan = [0 35];

    % 初始条件
    y0 = [u0; C0];

    % 解ODE
    [t, y_temp] = ode45(odefun, tspan, y0);

    % 存储终态的种群密度
    u_density(i, :) = y_temp(end, 1) * ones(1, size(concentration, 2));

    % 如果是指定点，绘制鱼群数量和体内微塑料含量随时间的变化曲线
    if i == idx
        figure('Position', [100 100 800 600])
        subplot(2, 1, 1)
        plot(t, y_temp(:, 1))
        title('鱼群数量随时间的变化')
        xlabel('时间')
        ylabel('鱼群数量')
        subplot(2, 1, 2)
        plot(t, y_temp(:, 2))
        title('体内微塑料含量随时间的变化')
        xlabel('时间')
        ylabel('体内微塑料含量')
    end
end

% 可视化部分
% 参数设置
sigma = 0.08;
time_steps = size(concentration, 2);

% 选择时间步长
if time_steps >= 15
    selected_steps = [1, 5, 10, 15]; % 手动选择时间步长
else
    selected_steps = round(linspace(1, time_steps, 4)); % 自动生成时间步长
end

% 初始化矩阵时显式指定维度
node_count = size(coordinates, 1);
smoothed_data = zeros(node_count, 4);  % 明确维度为 N×4

% 执行高斯平滑
for i = 1:4
    raw_data = u_density(:, selected_steps(i));
    smoothed_data(:,i) = gauss_smooth(raw_data, coordinates, sigma);

    % 维度验证
    assert(size(smoothed_data,1)==node_count, '维度不匹配')
end

% 图1：稳态分布
figure('Position', [100 100 800 600])
patch('Faces', elements, 'Vertices', coordinates,...
    'FaceVertexCData', smoothed_data(:,4), 'FaceColor','interp');
xlim([121, 122.6]); ylim([30.65, 32.3]); 
view(0,90); axis equal; grid off
colormap(jet(256))
 caxis([0 max(k_interp)*0.8]) % 统一颜色范围
% colorbar('southoutside');
xlabel('Longitude(E)');
ylabel('Latitude(S)');
% title(sprintf('稳态分布 (t=%d)', selected_steps(4)))

% 添加公共colorbar
h = colorbar('Position', [0.92 0.15 0.02 0.7]);
h.Label.String = 'Number of fish (per grid)';
h.FontSize = 12;


%% 高斯平滑处理函数
function smoothed_data = gauss_smooth(data, coordinates, sigma)
    % 确保数据为列向量
    if isrow(data)
        data = data'; 
    end
    
    search_radius = 3*sigma;
    tree = createns(coordinates, 'NSMethod', 'kdtree');
    smoothed_data = zeros(size(data));  % 保持与输入相同维度
    
    parfor i = 1:size(coordinates, 1)
        [idx, dist] = rangesearch(tree, coordinates(i,:), search_radius);
        neighbors = idx{1}(:);  % 强制转换为列向量
        distances = dist{1}(:);
        
        % 排除自身并验证维度
        valid = (neighbors ~= i);
        neighbors = neighbors(valid);
        distances = distances(valid);
        
        if ~isempty(neighbors)
            % 维度一致性检查
            assert(iscolumn(neighbors), 'Neighbors must be column vector')
            assert(iscolumn(distances), 'Distances must be column vector')
            
            weights = exp(-(distances.^2)/(2*sigma^2));
            weights = weights / sum(weights);
            smoothed_data(i) = sum(weights .* data(neighbors));
        else
            smoothed_data(i) = data(i);
        end
    end
end