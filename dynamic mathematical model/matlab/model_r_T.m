% % % 参数设置（最简单的版本，初始版本）
% T_opt = 30;    % 最适温度（℃）
% sigma_T = 5;    % 温度耐受范围（℃）
% r0 = 0.5;       % 基础增长率（1/天）
% 
% % 生成温度范围
% T = linspace(10, 40, 100);  % 温度从10℃到40℃
% 
% % 计算r(T)
% r = r0 * exp(-(T - T_opt).^2 / (2 * sigma_T^2));
% 
% % 绘图
% figure;
% plot(T, r, 'LineWidth', 2);
% xlabel('Temperature (℃)');
% ylabel('Natural Growth Rate (r)');
% title('温度对鱼类自然增长率的影响');
% grid on;
% 
% % 参数设置
% k0 = 10000;       % 基础环境容量（个体数/网格）
% alpha_T = 0.01; % 温度抑制系数（1/℃²）
% T_ref = 20;     % 参考温度（℃）
% 
% % 计算k(T)
% k = k0 * (1 - alpha_T * (T - T_ref).^2);
% 
% % 绘图
% figure;
% plot(T, k, 'LineWidth', 2);
% xlabel('Temperature (℃)');
% ylabel('Environmental Capacity (k)');
% title('温度对鱼类环境容量的影响');
% grid on;






%% 
% 4月16日最终版本


%% 参数设置
r0 = 0.5;       % 基础增长率（1/天）
sigma_T = 5;    % 温度耐受范围（℃）
sigma_S = 5;    % 盐度耐受范围（PSU）
k0 = 10000;     % 基础环境容量（个体数/网格）
alpha_T = 0.005; % 温度抑制系数（1/℃²）
alpha_S = 0.03;% 盐度抑制系数（1/PSU²）


%% 生成温度-盐度向量
T = linspace(10, 40, 100);   % 温度范围：10℃~40℃
S = linspace(20, 40, 100);   % 盐度范围：20~40 PSU
[T_grid, S_grid] = meshgrid(T, S);

%% 自定义颜色设置
custom_colors = [         % RGB颜色矩阵（用户指定）
    234,147,147;          % 浅红色 [2](@ref)
    206,183,179;          % 灰粉色
    148,206,149;          % 浅绿色
    204,186,223;          % 淡紫色
    139,222,231;          % 青色
    200,179,222           % 浅紫色
] / 255;                  % 标准化到0-1范围

%% 最佳参数组合
T_opts = [15, 20, 25, 27];        % 最佳温度
S_opts = [25, 26, 27, 28, 29, 30];% 最佳盐度

%% 创建图形窗口
figure('Units','normalized','Position',[0.1 0.1 0.8 0.7]);

%% 自然增长率-温度子图（固定盐度）
subplot(2,2,1); hold on;
for i = 1:length(T_opts)
    T_opt = T_opts(i);
    r = r0 * exp(-(T - T_opt).^2 / (2 * sigma_T^2)); % 删除与盐度相关的部分
    plot(T, r, 'LineWidth', 2, 'Color', custom_colors(i,:)); % 绘制完整的 T 范围
end
xlabel('Temperature (℃)'); ylabel('r(T, S_{opt})');
title('固定盐度 (S=25 PSU) 时自然增长率随温度变化');
legend(arrayfun(@(x) sprintf('T_{opt}=%d℃', x), T_opts, 'Un', 0), 'Location', 'best');
grid on; box on;

% 自然增长率-盐度子图（固定温度）
subplot(2,2,2); hold on;
for i = 1:length(S_opts)
    S_opt = S_opts(i);
    color_idx = mod(i-1,6)+1; % 循环使用6种颜色
    r = r0 * exp(-(S - S_opt).^2 / (2 * sigma_S^2)); % 修改后的公式
    plot(S, r, 'LineWidth', 2, 'Color', custom_colors(color_idx,:));
end
xlabel('Salinity (PSU)'); ylabel('r(T_{opt}, S)');
title('固定温度 (T=15℃) 时自然增长率随盐度变化');
legend(arrayfun(@(x) sprintf('S_{opt}=%d', x), S_opts, 'Un', 0), 'Location', 'best');
grid on; box on;

% %% 环境容量-温度子图（固定盐度）
subplot(2,2,3); hold on;
for i = 1:length(T_opts)
    T_opt = T_opts(i);
    k = k0 * exp(-alpha_T * (T - T_opt).^2); % 修改后的公式
    plot(T, k, 'LineWidth', 2, 'Color', custom_colors(i,:));
end
xlabel('Temperature (℃)'); ylabel('k(T, S_{opt})');
title('固定盐度 (S=25 PSU) 时环境容量随温度变化');
legend(arrayfun(@(x) sprintf('T_{opt}=%d℃', x), T_opts, 'Un', 0), 'Location', 'best');
grid on; box on;
% 
subplot(2,2,4); hold on;
for i = 1:length(S_opts)
    S_opt = S_opts(i);
    color_idx = mod(i-1,6)+1; % 颜色循环匹配
    k = k0 * exp(-alpha_S * (S - S_opt).^2); % 修改后的公式
    plot(S, k, 'LineWidth', 2, 'Color', custom_colors(color_idx,:));
end
xlabel('Salinity (PSU)'); ylabel('k(T_{opt}, S)');
title('固定温度 (T=15℃) 时环境容量随盐度变化');
legend(arrayfun(@(x) sprintf('S_{opt}=%d', x), S_opts, 'Un', 0), 'Location', 'best');
grid on; box on;

%% 统一图形格式
set(findall(gcf,'Type','axes'), 'FontSize',12, 'FontName','Arial');
colormap(custom_colors); % 设置颜色映射[6](@ref)



%未改好的版本，这个可以再改一下，完整的公式

% 
% %% 参数设置
% r0 = 0.5;       % 基础增长率（1/天）
% sigma_T = 5;    % 温度耐受范围（℃）
% sigma_S = 5;    % 盐度耐受范围（PSU）
% k0 = 10000;     % 基础环境容量（个体数/网格）
% alpha_T = 0.01; % 温度抑制系数（1/℃²）
% alpha_S = 0.005;% 盐度抑制系数（1/PSU²）
% 
% 
% %% 修改环境容量计算（调整抑制系数和高斯函数宽度）
% % alpha_T = 0.005; % 减小温度抑制系数
% % alpha_S = 0.0025; % 减小盐度抑制系数
% sigma_T = 10; % 增加温度耐受范围
% sigma_S = 10; % 增加盐度耐受范围
% 
% %% 生成温度-盐度向量
% T = linspace(10, 40, 100);   % 温度范围：10℃~40℃
% S = linspace(20, 40, 100);   % 盐度范围：20~40 PSU
% [T_grid, S_grid] = meshgrid(T, S);
% 
% %% 自定义颜色设置
% custom_colors = [         % RGB颜色矩阵（用户指定）
%     234,147,147;          % 浅红色 [2](@ref)
%     206,183,179;          % 灰粉色
%     148,206,149;          % 浅绿色
%     204,186,223;          % 淡紫色
%     139,222,231;          % 青色
%     200,179,222           % 浅紫色
% ] / 255;                  % 标准化到0-1范围
% 
% %% 最佳参数组合
% T_opts = [15, 20, 25, 27];        % 最佳温度
% S_opts = [25, 26, 27, 28, 29, 30];% 最佳盐度
% 
% %% 修改环境容量计算（使用高斯形式）
% sigma_kT = 7; % 环境容量的温度耐受范围
% sigma_kS = 5; % 环境容量的盐度耐受范围
% 
% %% 创建图形窗口
% figure('Units','normalized','Position',[0.1 0.1 0.8 0.7]);
% 
% %% 自然增长率-温度子图（固定盐度）
% subplot(2,2,1); hold on;
% for i = 1:length(T_opts)
%     T_opt = T_opts(i);
%     r = r0 * exp(-(T-T_opt).^2/(2*sigma_T^2)) .* exp(-(S-S_opts(1)).^2/(2*sigma_S^2));
%     plot(T, r, 'LineWidth',2, 'Color',custom_colors(i,:)); % 前4种颜色分配T_opts[3](@ref)
% end
% xlabel('Temperature (℃)'); ylabel('r(T, S_{opt})');
% title('固定盐度 (S=25 PSU) 时自然增长率随温度变化');
% legend(arrayfun(@(x)sprintf('T_{opt}=%d℃',x),T_opts,'Un',0), 'Location','best');
% grid on; box on;
% 
% 
% 
% %% 自然增长率-盐度子图（固定温度）
% subplot(2,2,2); hold on;
% for i = 1:length(S_opts)
%     S_opt = S_opts(i);
%     color_idx = mod(i-1,6)+1; % 循环使用6种颜色[3](@ref)
%     r = r0 * exp(-(T_grid-T_opts(1)).^2/(2*sigma_T^2)) .* exp(-(S_grid-S_opt).^2/(2*sigma_S^2));
%     plot(S, r(1,:), 'LineWidth',2, 'Color',custom_colors(color_idx,:));
% end
% xlabel('Salinity (PSU)'); ylabel('r(T_{opt}, S)');
% title('固定温度 (T=15℃) 时自然增长率随盐度变化');
% legend(arrayfun(@(x)sprintf('S_{opt}=%d',x),S_opts,'Un',0), 'Location','best');
% grid on; box on;
% 
% %% 环境容量-温度子图（固定盐度）
% subplot(2,2,3); hold on;
% for i = 1:length(T_opts)
%     T_opt = T_opts(i);
%     k = k0 * exp(-alpha_T*(T_grid-T_opt).^2) .* exp(-alpha_S*(S_grid-S_opts(1)).^2);
%     plot(T, k(:,1), 'LineWidth',2, 'Color',custom_colors(i,:)); % 保持与自然增长率相同颜色[2](@ref)
% end
% xlabel('Temperature (℃)'); ylabel('k(T, S_{opt})');
% title('固定盐度 (S=25 PSU) 时环境容量随温度变化');
% legend(arrayfun(@(x)sprintf('T_{opt}=%d℃',x),T_opts,'Un',0), 'Location','best');
% grid on; box on;
% 
% %% 环境容量-盐度子图（固定温度）
% subplot(2,2,4); hold on;
% for i = 1:length(S_opts)
%     S_opt = S_opts(i);
%     color_idx = mod(i-1,6)+1; % 颜色循环匹配[3](@ref)
%     k = k0 * exp(-alpha_T*(T_grid-T_opts(1)).^2) .* exp(-alpha_S*(S_grid-S_opt).^2);
%     plot(S, k(1,:), 'LineWidth',2, 'Color',custom_colors(color_idx,:));
% end
% xlabel('Salinity (PSU)'); ylabel('k(T_{opt}, S)');
% title('固定温度 (T=15℃) 时环境容量随盐度变化');
% legend(arrayfun(@(x)sprintf('S_{opt}=%d',x),S_opts,'Un',0), 'Location','best');
% grid on; box on;
% 
% %% 统一图形格式
% set(findall(gcf,'Type','axes'), 'FontSize',12, 'FontName','Arial');
% colormap(custom_colors); % 设置颜色映射[6](@ref)





