%包括了某点点鱼群数量的变化
%3月31日：加入随机鱼群数量的初始分布
%%
% 参数设置
% % 生成均匀分布的随机初始种群数量（0-10000）
% C0 = 0;            % 初始微塑料浓度
% r = 0.1;           % 净增长率 (r10 - d1) 原0.1
% alpha = 0.5;         % 微塑料影响强度 (原r11) 原1
% S = 0.042;         % 微塑料吸收速率 (原S1)
% g = 1.2;           % 排泄率 (原g1)
% k_env = 20000;     % 环境容量，需根据实际情况调整
clc
clear

load('k_10.mat');
load('r_10.mat');
load('p.mat');
load('t.mat');
%%
% 浮游植物
% 生成均匀分布的随机初始种群数量（0-10000）
C0 = 0;            % 初始微塑料浓度
r = 0.1;           % 净增长率 (r10 - d1) 原0.1
alpha = 0.5;         % 微塑料影响强度 (原r11) 原1
S = 0.042;         % 微塑料吸收速率 (原S1)
g = 1.2;           % 排泄率 (原g1)
k_env = 20000;     % 环境容量，需根据实际情况调整

% 导入微塑料浓度数据
initial_concentration = load('/Volumes/T7_Shield/期刊文章/微塑料—Zhang/主代码/3.1等高线并平均/concentration_final.mat', 'concentration');
concentration = initial_concentration.concentration;
% 获取节点坐标和连接信息
coordinates = p; % 节点坐标
elements = t;    % 连接信息
%%
% %均匀分布
% u0 = rand(size(coordinates, 1), 1) * 10000;  % 生成均匀分布数组
% 
% sigma_smooth = 0.1;  % 高斯平滑参数
% % 对初始值进行高斯平滑（增强空间相关性）
% u0_smoothed = gauss_smooth(u0, coordinates, sigma_smooth);
% % 绘制初始分布图
% figure('Position', [100 100 800 600])
% patch('Faces', elements, 'Vertices', coordinates,...
%     'FaceVertexCData', u0_smoothed, 'FaceColor', 'interp');
% colormap(jet(256))
% caxis([0 10000])  % 颜色范围与u0初始化范围一致
% colorbar('southoutside', 'Ticks', 0:2000:10000, 'TickLabels', {'0','2k','4k','6k','8k','10k'});
% title('初始种群数量分布 (高斯平滑后)');
% 
% % 绘制直方图与核密度估计
% figure
% subplot(1,2,1)
% histogram(u0, 'Normalization', 'pdf', 'FaceColor', '#4CA6A3')
% title('均匀随机分布直方图')
% xlabel('u0值'), ylabel('概率密度')
% 
% subplot(1,2,2)
% ksdensity(u0)
% title('核密度估计 (KDE)')
% xlabel('u0值'), ylabel('密度')

%% 正态分布
% 参数设置
mu = 5000;          % 均值（控制分布中心）
sigma = 2000;       % 标准差（控制分布宽度）
% 生成正态分布随机数并截断到 [0,10000]
u0 = mu + sigma * randn(size(coordinates, 1), 1);  % 生成标准正态分布
u0 = max(min(u0, 100000), 0);                % 截断到指定范围

% 绘制初始分布图
figure('Position', [100 100 800 600])
patch('Faces', elements, 'Vertices', coordinates,...
    'FaceVertexCData', u0, 'FaceColor', 'interp'); % 直接使用未平滑的 u0
colormap(jet(256))
xlim([121, 122.6]); ylim([30.65, 32.3]); 
view(0,90); axis equal; grid off
caxis([0 10000])  % 颜色范围与u0初始化范围一致
xlabel('Longitude(E)');
ylabel('Latitude(S)');
cb = colorbar; % 获取 colorbar 对象
ylabel(cb, 'Numbers (per grid)'); % 给 colorbar 添加标题




% % 绘制分布图
% figure
% subplot(1,2,1)
% histogram(u0, 'Normalization', 'pdf', 'FaceColor', '#693476')
% title('截断正态分布直方图')
% xlabel('u0值'), ylabel('概率密度')
% 
% subplot(1,2,2)
% ksdensity(u0)
% title('核密度估计 (KDE)')
% xlabel('u0值'), ylabel('密度')

%%





% 找到指定点的索引
point = [121.994, 31.6383];
[~, idx] = min(sum((coordinates - point).^2, 2));

% 初始化种群密度矩阵
u_density = zeros(size(coordinates, 1), size(concentration, 2));

% 对每个节点进行循环
for i = 1:size(coordinates, 1)
    % 获取当前节点的环境微塑料浓度（取最后一个时间步）
    CE_pq = concentration(i, end);

    % 定义ODE函数
    odefun = @(t, y) [
        y(1) * (r*(1 - y(1)/k_env) - alpha*y(2));  % du/dt
        S * CE_pq - g * y(2)                        % dC/dt
    ];

    % 时间跨度
    tspan = [0 15]; % 原15

    % 初始条件
    y0 = [u0(i); C0];  % 使用当前节点的随机初始值

    % 解ODE
    [t, y_temp] = ode45(odefun, tspan, y0);

    % 存储终态的种群密度
    u_density(i, :) = y_temp(end, 1) * ones(1, size(concentration, 2));

    % 如果是指定点，绘制鱼群数量和体内微塑料含量随时间的变化曲线
    if i == idx
        figure('Position', [100 100 800 600])
        subplot(2, 1, 1)
        plot(t, y_temp(:, 1))
        title('鱼群数量随时间的变化')
        xlabel('时间')
        ylabel('鱼群数量')
        subplot(2, 1, 2)
        plot(t, y_temp(:, 2))
        title('体内微塑料含量随时间的变化')
        xlabel('时间')
        ylabel('体内微塑料含量')
    end
end

% 可视化部分
% 参数设置
time_steps = size(concentration, 2);

% 选择时间步长
if time_steps >= 15
    selected_steps = [1, 5, 10, 15]; % 手动选择时间步长
else
    selected_steps = round(linspace(1, time_steps, 4)); % 自动生成时间步长
end

% 高斯平滑函数
function smoothed_data = gauss_smooth(data, coordinates, sigma)
    % 确保数据为列向量
    if isrow(data)
        data = data'; 
    end
    
    search_radius = 3*sigma;
    tree = createns(coordinates, 'NSMethod', 'kdtree');
    smoothed_data = zeros(size(data));  % 保持与输入相同维度
    
    parfor i = 1:size(coordinates, 1)
        [idx, dist] = rangesearch(tree, coordinates(i,:), search_radius);
        neighbors = idx{1}(:);  % 强制转换为列向量
        distances = dist{1}(:);
        
        % 排除自身并验证维度
        valid = (neighbors ~= i);
        neighbors = neighbors(valid);
        distances = distances(valid);
        
        if ~isempty(neighbors)
            % 维度一致性检查
            assert(iscolumn(neighbors), 'Neighbors must be column vector')
            assert(iscolumn(distances), 'Distances must be column vector')
            
            weights = exp(-(distances.^2)/(2*sigma^2));
            weights = weights / sum(weights);
            smoothed_data(i) = sum(weights .* data(neighbors));
        else
            smoothed_data(i) = data(i);
        end
    end
end

% 对最终的鱼群密度进行高斯平滑处理
sigma = 0.08; % 平滑参数
smoothed_u_density = gauss_smooth(u_density(:, end), coordinates, sigma);

% 可视化平滑后的鱼群密度
figure('Position', [100 100 800 600])
patch('Faces', elements, 'Vertices', coordinates,...
    'FaceVertexCData', smoothed_u_density, 'FaceColor', 'interp'); % 使用平滑后的数据
colormap(jet(256))
xlim([121, 122.6]); ylim([30.65, 32.3]); 
view(0,90); axis equal; grid off
caxis([0 10000])  % 颜色范围与u0初始化范围一致
xlabel('Longitude(E)');
ylabel('Latitude(S)');
cb = colorbar; % 获取 colorbar 对象
ylabel(cb, 'Numbers (per grid)'); % 给 colorbar 添加标题

% % 添加公共colorbar
% h = colorbar('Position', [0.92 0.15 0.02 0.7]);
% h.Label.String = '种群密度';
% h.FontSize = 12;





