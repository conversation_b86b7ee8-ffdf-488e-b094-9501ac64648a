# 珍珠蚌种群健康度模型

这是一个重构后的珍珠蚌种群健康度建模系统，采用模块化设计，将数据生成、模型计算和可视化功能分离到不同的文件中，便于管理和扩展。

## 文件结构

```
dynamic mathematical model/
├── pearl_mussel_model.py    # 主程序文件
├── data_generator.py        # 传感器数据生成模块
├── model_core.py           # 模型核心计算模块
├── visualization.py        # 可视化模块
├── test_model.py          # 测试脚本（不依赖网格文件）
└── README.md              # 说明文档
```

## 模块功能

### 1. data_generator.py - 传感器数据生成器
- **功能**: 生成类似真实传感器数据的模拟数据
- **特点**: 
  - 生成具有空间相关性的平滑数据
  - 添加时间变化和测量噪声
  - 模拟真实环境中的数据特征
- **生成的数据**:
  - 温度 (°C): 18-32°C范围，考虑深度效应
  - pH值: 6.0-9.0范围，模拟植物活动影响
  - 电导率 (μS/cm): 200-800范围，模拟污染源影响
  - 叶绿素 (μg/L): 2-40范围，考虑光照和营养效应

### 2. model_core.py - 模型核心
- **功能**: 包含所有数学模型和计算函数
- **主要组件**:
  - 胁迫函数（pH、电导率、温度、叶绿素）
  - 理想生长率计算
  - ODE求解器
  - 敏感性分析工具

### 3. visualization.py - 可视化模块
- **功能**: 提供各种数据可视化功能
- **可视化类型**:
  - 2D散点图（传感器数据分布）
  - 3D可视化（如果有3D坐标）
  - 参数分布直方图
  - 相关性分析热力图
  - 时间序列图

### 4. pearl_mussel_model.py - 主程序
- **功能**: 协调各个模块完成完整的建模流程
- **依赖**: 需要网格文件 `lake_mesher/lake_box_mesh_detailed.msh`

### 5. test_model.py - 测试脚本
- **功能**: 不依赖网格文件的测试版本
- **用途**: 测试各模块功能，生成示例数据

## 使用方法

### 方法1: 使用网格文件（完整版本）
```bash
python pearl_mussel_model.py
```
**前提条件**: 需要有网格文件 `lake_mesher/lake_box_mesh_detailed.msh`

### 方法2: 测试版本（推荐开始使用）
```bash
python test_model.py
```
**优点**: 不需要网格文件，可以直接运行测试所有功能

## 输出文件

运行程序后会生成以下文件：

### 数据文件
- `sensor_data.npz` 或 `test_sensor_data.npz`: 传感器数据
- `health_index.txt` 或 `test_health_index.txt`: 健康度指数结果

### 可视化图表（保存在 `results/` 或 `test_results/` 目录）
- `sensor_data_2d.png`: 传感器数据2D分布图
- `health_index_2d.png`: 健康度指数2D分布图
- `parameter_distributions.png`: 参数分布直方图
- `correlation_matrix.png`: 相关性分析热力图
- `time_series.png`: 时间序列图（测试版本）

## 依赖库

确保安装以下Python库：
```bash
pip install numpy scipy matplotlib seaborn pandas
```

如果使用完整版本，还需要：
```bash
pip install gmsh
```

## 模型参数

### 默认环境参数范围
- **温度**: 18-32°C，最适温度25°C
- **pH值**: 6.0-9.0，最适pH 7.5
- **电导率**: 200-800 μS/cm，最适值500 μS/cm
- **叶绿素**: 2-40 μg/L，半饱和常数10 μg/L

### 模型参数
- **基础生长率**: 0.1 /天
- **环境承载力**: 1.0
- **时间跨度**: 30天

## 自定义使用

### 修改数据生成参数
在 `data_generator.py` 中修改 `default_params` 字典：
```python
self.default_params = {
    'temperature': {'mean': 25.0, 'std': 3.0, 'min': 18.0, 'max': 32.0},
    # ... 其他参数
}
```

### 修改模型参数
在创建 `PearlMusselModel` 实例时传入参数：
```python
model = PearlMusselModel(pH_opt=7.0, T_opt=23.0, r_0=0.15)
```

### 自定义可视化
使用 `ModelVisualizer` 类的各种方法：
```python
visualizer = ModelVisualizer()
visualizer.plot_sensor_data_2d(coords, data, save_path='my_results')
```

## 扩展功能

### 添加新的环境因子
1. 在 `data_generator.py` 中添加新的数据生成函数
2. 在 `model_core.py` 中添加对应的胁迫函数
3. 在 `visualization.py` 中添加可视化支持

### 修改数学模型
在 `model_core.py` 中的 `ode_model` 函数中修改微分方程

## 注意事项

1. **中文字体**: 可视化模块已配置中文字体支持，如果显示有问题，请检查系统字体
2. **内存使用**: 大型网格可能消耗较多内存，建议先用测试版本验证
3. **数据质量**: 生成的数据具有一定的随机性，可通过修改随机种子获得不同结果

## 故障排除

### 常见问题
1. **导入错误**: 确保所有模块文件在同一目录下
2. **网格文件错误**: 使用 `test_model.py` 进行测试
3. **可视化问题**: 检查matplotlib和seaborn是否正确安装

### 联系支持
如有问题，请检查错误信息并参考各模块的文档字符串。
