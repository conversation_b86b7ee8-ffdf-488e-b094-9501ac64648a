"""
可视化模块
提供各种数据可视化功能，包括传感器数据和模型结果的可视化
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import os

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class ModelVisualizer:
    """模型可视化类"""
    
    def __init__(self, figsize=(12, 8), dpi=300):
        """
        初始化可视化器
        
        Args:
            figsize (tuple): 默认图形大小
            dpi (int): 图形分辨率
        """
        self.figsize = figsize
        self.dpi = dpi
        self.color_maps = {
            'temperature': 'coolwarm',
            'ph': 'RdYlBu_r',
            'conductivity': 'viridis',
            'chlorophyll': 'Greens',
            'health': 'RdYlGn'
        }
    
    def create_directory(self, path):
        """创建目录（如果不存在）"""
        if not os.path.exists(path):
            os.makedirs(path)
    
    def plot_sensor_data_2d(self, node_coords, sensor_data, save_path=None):
        """
        绘制传感器数据的2D分布图
        
        Args:
            node_coords (np.array): 节点坐标
            sensor_data (dict): 传感器数据
            save_path (str): 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
        titles = ['温度分布 (°C)', 'pH值分布', '电导率分布 (μS/cm)', '叶绿素分布 (μg/L)']
        
        for i, (param, title) in enumerate(zip(parameters, titles)):
            ax = axes[i//2, i%2]
            
            # 创建散点图
            scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], 
                               c=sensor_data[param], 
                               cmap=self.color_maps[param],
                               s=30, alpha=0.7, edgecolors='black', linewidth=0.5)
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_title(title)
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label(param.capitalize())
            
            # 添加统计信息
            data = sensor_data[param]
            stats_text = f'均值: {np.mean(data):.2f}\n标准差: {np.std(data):.2f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', 
                   facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            plt.savefig(f'{save_path}/sensor_data_2d.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
    
    def plot_health_index_2d(self, node_coords, health_index, save_path=None):
        """
        绘制健康度指数的2D分布图
        
        Args:
            node_coords (np.array): 节点坐标
            health_index (np.array): 健康度指数
            save_path (str): 保存路径
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], 
                           c=health_index, cmap=self.color_maps['health'],
                           s=50, alpha=0.8, edgecolors='black', linewidth=0.5,
                           vmin=0, vmax=1)
        
        ax.set_xlabel('X坐标 (m)')
        ax.set_ylabel('Y坐标 (m)')
        ax.set_title('珍珠蚌种群健康度指数分布')
        ax.set_aspect('equal')
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('健康度指数')
        
        # 添加统计信息
        stats_text = f'均值: {np.mean(health_index):.3f}\n' \
                    f'标准差: {np.std(health_index):.3f}\n' \
                    f'最小值: {np.min(health_index):.3f}\n' \
                    f'最大值: {np.max(health_index):.3f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               verticalalignment='top', bbox=dict(boxstyle='round', 
               facecolor='white', alpha=0.9))
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            plt.savefig(f'{save_path}/health_index_2d.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
    
    def plot_3d_visualization(self, node_coords, data, title, colormap, save_path=None):
        """
        创建3D可视化图
        
        Args:
            node_coords (np.array): 节点坐标
            data (np.array): 要可视化的数据
            title (str): 图标题
            colormap (str): 颜色映射
            save_path (str): 保存路径
        """
        fig = plt.figure(figsize=(12, 9))
        ax = fig.add_subplot(111, projection='3d')
        
        scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
                           c=data, cmap=colormap, s=30, alpha=0.6)
        
        ax.set_xlabel('X坐标 (m)')
        ax.set_ylabel('Y坐标 (m)')
        ax.set_zlabel('Z坐标 (m)')
        ax.set_title(title)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
        cbar.set_label('数值')
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            filename = title.replace(' ', '_').replace('(', '').replace(')', '').lower()
            plt.savefig(f'{save_path}/{filename}_3d.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
    
    def plot_correlation_matrix(self, sensor_data, health_index, save_path=None):
        """
        绘制参数相关性矩阵
        
        Args:
            sensor_data (dict): 传感器数据
            health_index (np.array): 健康度指数
            save_path (str): 保存路径
        """
        # 准备数据
        data_dict = sensor_data.copy()
        data_dict['health_index'] = health_index
        
        # 创建DataFrame用于相关性分析
        import pandas as pd
        df = pd.DataFrame({
            '温度': data_dict['temperature'],
            'pH值': data_dict['ph'],
            '电导率': data_dict['conductivity'],
            '叶绿素': data_dict['chlorophyll'],
            '健康度指数': data_dict['health_index']
        })
        
        # 计算相关性矩阵
        correlation_matrix = df.corr()
        
        # 绘制热力图
        fig, ax = plt.subplots(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, ax=ax, cbar_kws={'label': '相关系数'})
        ax.set_title('环境参数与健康度指数相关性分析')
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            plt.savefig(f'{save_path}/correlation_matrix.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
    
    def plot_parameter_distributions(self, sensor_data, health_index, save_path=None):
        """
        绘制参数分布直方图
        
        Args:
            sensor_data (dict): 传感器数据
            health_index (np.array): 健康度指数
            save_path (str): 保存路径
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
        titles = ['温度 (°C)', 'pH值', '电导率 (μS/cm)', '叶绿素 (μg/L)']
        
        # 绘制传感器数据分布
        for i, (param, title) in enumerate(zip(parameters, titles)):
            ax = axes[i//2, i%2]
            data = sensor_data[param]
            
            ax.hist(data, bins=30, alpha=0.7, edgecolor='black', 
                   color=plt.cm.Set3(i))
            ax.set_title(f'{title} 分布')
            ax.set_xlabel(title)
            ax.set_ylabel('频次')
            ax.grid(True, alpha=0.3)
            
            # 添加统计线
            mean_val = np.mean(data)
            ax.axvline(mean_val, color='red', linestyle='--', 
                      label=f'均值: {mean_val:.2f}')
            ax.legend()
        
        # 绘制健康度指数分布
        ax = axes[1, 2]
        ax.hist(health_index, bins=30, alpha=0.7, edgecolor='black', 
               color='lightgreen')
        ax.set_title('健康度指数分布')
        ax.set_xlabel('健康度指数')
        ax.set_ylabel('频次')
        ax.grid(True, alpha=0.3)
        
        mean_health = np.mean(health_index)
        ax.axvline(mean_health, color='red', linestyle='--', 
                  label=f'均值: {mean_health:.3f}')
        ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            plt.savefig(f'{save_path}/parameter_distributions.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
    
    def create_comprehensive_visualization(self, node_coords, sensor_data, 
                                         health_index, save_path=None):
        """
        创建综合可视化报告
        
        Args:
            node_coords (np.array): 节点坐标
            sensor_data (dict): 传感器数据
            health_index (np.array): 健康度指数
            save_path (str): 保存路径
        """
        print("正在生成传感器数据2D分布图...")
        self.plot_sensor_data_2d(node_coords, sensor_data, save_path)
        
        print("正在生成健康度指数2D分布图...")
        self.plot_health_index_2d(node_coords, health_index, save_path)
        
        print("正在生成参数分布直方图...")
        self.plot_parameter_distributions(sensor_data, health_index, save_path)
        
        print("正在生成相关性分析图...")
        self.plot_correlation_matrix(sensor_data, health_index, save_path)
        
        # 如果有3D坐标，生成3D可视化
        if node_coords.shape[1] > 2:
            print("正在生成3D可视化图...")
            self.plot_3d_visualization(node_coords, health_index, 
                                     '珍珠蚌健康度指数3D分布', 
                                     self.color_maps['health'], save_path)
        
        print("✅ 所有可视化图表生成完成！")
    
    def plot_time_series(self, time_data, health_data, title="健康度时间序列", 
                        save_path=None):
        """
        绘制时间序列图
        
        Args:
            time_data (np.array): 时间数据
            health_data (np.array): 健康度数据
            title (str): 图标题
            save_path (str): 保存路径
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        ax.plot(time_data, health_data, linewidth=2, color='blue')
        ax.set_xlabel('时间 (天)')
        ax.set_ylabel('健康度指数')
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            self.create_directory(save_path)
            plt.savefig(f'{save_path}/time_series.png', 
                       dpi=self.dpi, bbox_inches='tight')
        plt.show()
