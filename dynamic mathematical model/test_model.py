"""
测试脚本 - 不依赖网格文件的简化版本
用于测试模块功能和生成示例数据
"""

import numpy as np
from data_generator import SensorDataGenerator
from visualization import ModelVisualizer
from model_core import PearlMusselModel

def create_test_grid(x_range=(-10, 10), y_range=(-10, 10), z_range=(0, 5), 
                    num_points=100):
    """
    创建测试网格点
    
    Args:
        x_range, y_range, z_range (tuple): 坐标范围
        num_points (int): 点的数量
        
    Returns:
        np.array: 网格坐标 (N, 3)
    """
    np.random.seed(42)
    
    x = np.random.uniform(x_range[0], x_range[1], num_points)
    y = np.random.uniform(y_range[0], y_range[1], num_points)
    z = np.random.uniform(z_range[0], z_range[1], num_points)
    
    return np.column_stack((x, y, z))

def test_data_generation():
    """测试数据生成功能"""
    print("=== 测试数据生成功能 ===")
    
    # 创建测试网格
    node_coords = create_test_grid(num_points=200)
    num_nodes = len(node_coords)
    
    print(f"创建了 {num_nodes} 个测试节点")
    
    # 生成传感器数据
    data_generator = SensorDataGenerator(seed=42)
    sensor_data = data_generator.generate_realistic_data(
        node_coords=node_coords,
        num_nodes=num_nodes
    )
    
    # 显示数据统计
    print("\n传感器数据统计:")
    for param in ['temperature', 'ph', 'conductivity', 'chlorophyll']:
        data = sensor_data[param]
        print(f"{param}: 均值={np.mean(data):.2f}, "
              f"标准差={np.std(data):.2f}, "
              f"范围=[{np.min(data):.2f}, {np.max(data):.2f}]")
    
    # 保存数据
    data_generator.save_sensor_data(sensor_data, 'test_sensor_data.npz')
    print("\n✅ 测试传感器数据已保存至 'test_sensor_data.npz'")
    
    return node_coords, sensor_data

def test_model_calculation(node_coords, sensor_data):
    """测试模型计算功能"""
    print("\n=== 测试模型计算功能 ===")
    
    num_nodes = len(node_coords)
    t_span = [0, 30]  # 30天
    u0 = np.full(num_nodes, 0.5)  # 初始健康度
    k = 1.0  # 环境承载力
    
    # 初始化模型
    model = PearlMusselModel()
    
    # 计算生长率和胁迫因子
    r = np.zeros(num_nodes)
    sigma_total = np.zeros(num_nodes)
    
    for i in range(num_nodes):
        r[i] = model.ideal_growth_rate(
            sensor_data['temperature'][i], 
            sensor_data['chlorophyll'][i]
        )
        sigma_total[i] = model.total_stress(
            sensor_data['ph'][i], 
            sensor_data['conductivity'][i]
        )
    
    print(f"生长率范围: [{np.min(r):.4f}, {np.max(r):.4f}]")
    print(f"胁迫因子范围: [{np.min(sigma_total):.4f}, {np.max(sigma_total):.4f}]")
    
    # 求解ODE
    u_final = np.zeros(num_nodes)
    for i in range(num_nodes):
        t, u = model.solve_ode(u0[i], t_span, r[i], sigma_total[i], k)
        u_final[i] = u[-1]
    
    print(f"最终健康度指数范围: [{np.min(u_final):.4f}, {np.max(u_final):.4f}]")
    print(f"平均健康度指数: {np.mean(u_final):.4f}")
    
    # 保存结果
    np.savetxt('test_health_index.txt', 
               np.column_stack((node_coords, u_final)), 
               header='x y z health_index', fmt='%.6f')
    print("✅ 测试健康度指数已保存至 'test_health_index.txt'")
    
    return u_final

def test_visualization(node_coords, sensor_data, health_index):
    """测试可视化功能"""
    print("\n=== 测试可视化功能 ===")
    
    visualizer = ModelVisualizer()
    
    # 创建综合可视化
    visualizer.create_comprehensive_visualization(
        node_coords=node_coords,
        sensor_data=sensor_data,
        health_index=health_index,
        save_path='test_results'
    )
    
    print("✅ 测试可视化图表已保存至 'test_results' 目录")

def test_time_series():
    """测试时间序列功能"""
    print("\n=== 测试时间序列功能 ===")
    
    model = PearlMusselModel()
    
    # 设置参数
    u0 = 0.5
    t_span = [0, 30]
    r = 0.1
    sigma_total = 0.05
    k = 1.0
    
    # 求解详细时间序列
    t, u = model.solve_ode_detailed(u0, t_span, r, sigma_total, k, num_points=100)
    
    # 可视化时间序列
    visualizer = ModelVisualizer()
    visualizer.plot_time_series(t, u, "珍珠蚌健康度时间演化", 'test_results')
    
    print("✅ 时间序列图已保存")

def test_sensitivity_analysis():
    """测试敏感性分析"""
    print("\n=== 测试敏感性分析 ===")
    
    model = PearlMusselModel()
    
    # 基础参数
    base_params = {
        'pH': 7.5,
        'EC': 500.0,
        'T': 25.0,
        'ChlA': 15.0
    }
    
    # 参数变化范围
    param_variations = {
        'pH': np.linspace(-0.3, 0.3, 20),  # ±30%
        'EC': np.linspace(-0.3, 0.3, 20)   # ±30%
    }
    
    # 进行敏感性分析
    results = model.sensitivity_analysis(base_params, param_variations)
    
    print("敏感性分析完成")
    for param, result in results.items():
        health_range = np.max(result['health_values']) - np.min(result['health_values'])
        print(f"{param} 变化导致健康度变化范围: {health_range:.4f}")

def main():
    """主测试函数"""
    print("开始运行珍珠蚌模型测试...")
    
    try:
        # 1. 测试数据生成
        node_coords, sensor_data = test_data_generation()
        
        # 2. 测试模型计算
        health_index = test_model_calculation(node_coords, sensor_data)
        
        # 3. 测试可视化
        test_visualization(node_coords, sensor_data, health_index)
        
        # 4. 测试时间序列
        test_time_series()
        
        # 5. 测试敏感性分析
        test_sensitivity_analysis()
        
        print("\n🎉 所有测试完成！")
        print("生成的文件:")
        print("- test_sensor_data.npz: 传感器数据")
        print("- test_health_index.txt: 健康度指数")
        print("- test_results/: 可视化图表目录")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
