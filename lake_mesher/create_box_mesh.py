import gmsh
import sys

# --- 1. 参数定义 ---
# 湖泊环境的尺寸 (单位：米)
length = 50.0  # 长 (沿X轴)
width = 30.0   # 宽 (沿Y轴)
height = 10.0  # 高/水深 (沿Z轴)

# 网格尺寸参数 (单位：米)
min_mesh_size = 0.5  # 网格单元的最小尺寸
max_mesh_size = 1  # 网格单元的最大尺寸

# --- 2. 初始化 Gmsh ---
gmsh.initialize(sys.argv)
gmsh.model.add("pearl_lake_box_detailed")

print("开始创建几何模型...")

# --- 3. 创建几何：一个长方体 ---
box_tag = gmsh.model.occ.addBox(0, 0, 0, length, width, height)
print(f"长方体创建成功，尺寸: {length}x{width}x{height}")
gmsh.model.occ.synchronize()

# --- 4. 设置全局网格尺寸选项 ---
print(f"设置网格尺寸范围：最小={min_mesh_size}, 最大={max_mesh_size}")
gmsh.option.setNumber("Mesh.MeshSizeMin", min_mesh_size)
gmsh.option.setNumber("Mesh.MeshSizeMax", max_mesh_size)

# --- 5. 生成三维网格 ---
print("正在生成三维网格...")
gmsh.model.mesh.generate(3)

# --- 6. 保存网格文件 ---
output_filename = 'lake_box_mesh_detailed.msh'
gmsh.write(output_filename)
print(f"✅ 网格生成成功！已保存为 '{output_filename}'")

# --- 7. 可视化 ---
if '-nopopup' not in sys.argv:
    print("正在打开Gmsh图形界面进行预览... 关闭窗口即可退出程序。")
    gmsh.fltk.run()
-
gmsh.finalize()