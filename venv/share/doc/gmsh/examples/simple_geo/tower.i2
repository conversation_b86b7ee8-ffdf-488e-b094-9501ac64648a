
h = 19.6 ; hSol = 1. ;
L = 150. ;

xg  =  0.    ;  yg  = 30.023 + h ;
x1a = -6.782 ;  y1a = 19.355 + h ;
x1b = -9.83  ;  y1b =  8.839 + h ;
x1c = -8.001 ;  y1c =  0.    + h ;

x2a =  6.782 ;  y2a = 19.355 + h ;
x2b =  9.83  ;  y2b =  8.839 + h ;
x2c =  8.001 ;  y2c =  0.    + h ;


/* ------------ */
/*  T o w e r   */
/* ------------ */

ho = -0.288 ;


c0 = 9.144 /2. ;  h0 = 0.10 ;
c1 = 7.422 /2. ;  h1 =  6.139     + ho ;
c2 = 5.639 /2. ;  h2 = 12.497     + ho ;
c3 = 3.657 /2. ;  h3 = 25.527     + ho ;   b3 = 8.001 ;
c4 = 3.457 /2. ;  h4 = h3 + 1.900 ;
c5 = 2.743 /2. ;  h5 = h3 + 8.839 ;        b5 = 9.830 ;
c6 = 2.631 /2. ;  h6 = h5 + 2.320 ;
c7 = 2.118 /2. ;  h7 = h5 + 10.516 ;       b7 = 6.782 ;
c8 = 2.100 /2. ;  h8 = h7 + 1.680 ;
c9 = 1.050 /2. ;  h9 = h7 + 5.029 ;


pt0 = 1. ;  pt1 = 1. ;  pt2 = 1. ;  pt3 = 1. ;  pt4 = 1. ;  pt5 = 1. ;
pt6 = 1. ;  pt7 = 1. ;  pt8 = 1. ;  pt9 = 1. ;

ci = c0 ; hi = h0 ;   pti = pt0 ;   i_p = 0 ;
Include "tower.i4" ;

ci = c1 ; hi = h1 ;   pti = pt1 ;   i_p = 10 ;
Include "tower.i4" ;
Include "tower.i5" ;

ci = c2 ; hi = h2 ;   pti = pt2 ;   i_p = 20 ;
Include "tower.i4" ;
Include "tower.i5" ;

ci = c3 ; hi = h3 ;   pti = pt3 ;   i_p = 30 ;
Include "tower.i4" ;

ci = c4 ; hi = h4 ;   pti = pt4 ;   i_p = 40 ;
Include "tower.i4" ;

ci = c5 ; hi = h5 ;   pti = pt5 ;   i_p = 50 ;
Include "tower.i4" ;

ci = c6 ; hi = h6 ;   pti = pt6 ;   i_p = 60 ;
Include "tower.i4" ;

ci = c7 ; hi = h7 ;   pti = pt7 ;   i_p = 70 ;
Include "tower.i4" ;

ci = c8 ; hi = h8 ;   pti = pt8 ;   i_p = 80 ;
Include "tower.i4" ;

ci = c9 ; hi = h9 ;   pti = pt9 ;   i_p = 90 ;
Point(91) = {  ci , hi , 0. , pti } ;
Point(92) = { -ci , hi , 0. , pti } ;


Point(38) = {  b3 , h3 , 0. , pt3 } ;
Point(39) = { -b3 , h3 , 0. , pt3 } ;

Point(58) = {  b5 , h5 , 0. , pt5 } ;
Point(59) = { -b5 , h5 , 0. , pt5 } ;

Point(78) = {  b7 , h7 , 0. , pt7 } ;
Point(79) = { -b7 , h7 , 0. , pt7 } ;



Line(201) = { 1 , 11 } ;  Line(202) = { 2 , 12 } ;
Line(203) = { 3 , 13 } ;  Line(204) = { 4 , 14 } ;

Line(211) = { 11 , 21 } ;  Line(212) = { 12 , 22 } ;
Line(213) = { 13 , 23 } ;  Line(214) = { 14 , 24 } ;

Line(221) = { 21 , 31 } ;  Line(222) = { 22 , 32 } ;
Line(223) = { 23 , 33 } ;  Line(224) = { 24 , 34 } ;

Line(231) = { 31 , 41 } ;  Line(232) = { 32 , 42 } ;
Line(233) = { 33 , 43 } ;  Line(234) = { 34 , 44 } ;

Line(241) = { 41 , 51 } ;  Line(242) = { 42 , 52 } ;
Line(243) = { 43 , 53 } ;  Line(244) = { 44 , 54 } ;

Line(251) = { 51 , 61 } ;  Line(252) = { 52 , 62 } ;
Line(253) = { 53 , 63 } ;  Line(254) = { 54 , 64 } ;

Line(261) = { 61 , 71 } ;  Line(262) = { 62 , 72 } ;
Line(263) = { 63 , 73 } ;  Line(264) = { 64 , 74 } ;

Line(271) = { 71 , 81 } ;  Line(272) = { 72 , 82 } ;
Line(273) = { 73 , 83 } ;  Line(274) = { 74 , 84 } ;

Line(281) = { 81 , 92 } ;  Line(282) = { 82 , 91 } ;
Line(283) = { 83 , 91 } ;  Line(284) = { 84 , 92 } ;




Line(301) = { 1 , 17 } ;  Line(302) = { 2 , 17 } ;
Line(303) = { 2 , 16 } ;  Line(304) = { 3 , 16 } ;
Line(305) = { 3 , 15 } ;  Line(306) = { 4 , 15 } ;
Line(307) = { 4 , 18 } ;  Line(308) = { 1 , 18 } ;

Line(311) = { 11 , 27 } ;  Line(312) = { 12 , 27 } ;
Line(313) = { 12 , 26 } ;  Line(314) = { 13 , 26 } ;
Line(315) = { 13 , 25 } ;  Line(316) = { 14 , 25 } ;
Line(317) = { 14 , 28 } ;  Line(318) = { 11 , 28 } ;

Line(321) = { 11 , 17 } ;  Line(322) = { 12 , 17 } ;
Line(323) = { 12 , 16 } ;  Line(324) = { 13 , 16 } ;
Line(325) = { 13 , 15 } ;  Line(326) = { 14 , 15 } ;
Line(327) = { 14 , 18 } ;  Line(328) = { 11 , 18 } ;

Line(331) = { 21 , 27 } ;  Line(332) = { 22 , 27 } ;
Line(333) = { 22 , 26 } ;  Line(334) = { 23 , 26 } ;
Line(335) = { 23 , 25 } ;  Line(336) = { 24 , 25 } ;
Line(337) = { 24 , 28 } ;  Line(338) = { 21 , 28 } ;



Line(401) = { 31 , 32 } ;  Line(402) = { 32 , 33 } ;
Line(403) = { 33 , 34 } ;  Line(404) = { 34 , 31 } ;

Line(411) = { 41 , 42 } ;  Line(412) = { 42 , 43 } ;
Line(413) = { 43 , 44 } ;  Line(414) = { 44 , 41 } ;

Line(421) = { 51 , 52 } ;  Line(422) = { 52 , 53 } ;
Line(423) = { 53 , 54 } ;  Line(424) = { 54 , 51 } ;

Line(431) = { 61 , 62 } ;  Line(432) = { 62 , 63 } ;
Line(433) = { 63 , 64 } ;  Line(434) = { 64 , 61 } ;

Line(441) = { 71 , 72 } ;  Line(442) = { 72 , 73 } ;
Line(443) = { 73 , 74 } ;  Line(444) = { 74 , 71 } ;

Line(451) = { 81 , 82 } ;  Line(452) = { 82 , 83 } ;
Line(453) = { 83 , 84 } ;  Line(454) = { 84 , 81 } ;




Line(501) = { 31 , 39 } ;  Line(502) = { 41 , 39 } ;
Line(503) = { 34 , 39 } ;  Line(504) = { 44 , 39 } ;
Line(511) = { 32 , 38 } ;  Line(512) = { 42 , 38 } ;
Line(513) = { 33 , 38 } ;  Line(514) = { 43 , 38 } ;

Line(521) = { 51 , 59 } ;  Line(522) = { 61 , 59 } ;
Line(523) = { 54 , 59 } ;  Line(524) = { 64 , 59 } ;
Line(531) = { 52 , 58 } ;  Line(532) = { 62 , 58 } ;
Line(533) = { 53 , 58 } ;  Line(534) = { 63 , 58 } ;

Line(541) = { 71 , 79 } ;  Line(542) = { 81 , 79 } ;
Line(543) = { 74 , 79 } ;  Line(544) = { 84 , 79 } ;
Line(551) = { 72 , 78 } ;  Line(552) = { 82 , 78 } ;
Line(553) = { 73 , 78 } ;  Line(554) = { 83 , 78 } ;

