ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('CATIA V6 STEP','CAx-IF Rec.Pracs.--- Model Styling and Organization---1.4--- 2014-01-23'),'2;1');

FILE_NAME('D:\\Downloads\\Step Files\\cubez.stp','2017-05-22T11:55:54+00:00',('none'),('none'),'3DEXPERIENCE Platform3DEXPERIENCE R2016x HotFix 9','3DEXPERIENCE Platform STEP AP214','none');

FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));

ENDSEC;
DATA;
#16=PRODUCT('','','',(#2)) ;
#2=PRODUCT_CONTEXT(' ',#1,'mechanical') ;
#1=APPLICATION_CONTEXT('automotive design') ;
#18=PRODUCT_DEFINITION('',' ',#17,#3) ;
#3=PRODUCT_DEFINITION_CONTEXT('part definition',#1,' ') ;
#19=PRODUCT_DEFINITION_SHAPE(' ',' ',#18) ;
#216=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION(' ',(#78,#170,#202,#29),#12) ;
#217=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION(' ',(#213),#12) ;
#13=SHAPE_REPRESENTATION(' ',(#15),#12) ;
#15=AXIS2_PLACEMENT_3D('',#14,$,$) ;
#34=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#31,#32,#33) ;
#82=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#79,#80,#81) ;
#113=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#110,#111,#112) ;
#144=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#141,#142,#143) ;
#174=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#171,#172,#173) ;
#186=AXIS2_PLACEMENT_3D('Plane Axis2P3D',#183,#184,#185) ;
#208=AXIS2_PLACEMENT_3D('Absolute Axis System',#205,#206,#207) ;
#14=CARTESIAN_POINT('',(0.,0.,0.)) ;
#31=CARTESIAN_POINT('Axis2P3D Location',(0.,-50.,50.)) ;
#36=CARTESIAN_POINT('Line Origine',(0.,50.,50.)) ;
#40=CARTESIAN_POINT('Vertex',(-50.,50.,50.)) ;
#42=CARTESIAN_POINT('Vertex',(50.,50.,50.)) ;
#45=CARTESIAN_POINT('Line Origine',(-50.,0.,50.)) ;
#49=CARTESIAN_POINT('Vertex',(-50.,-50.,50.)) ;
#52=CARTESIAN_POINT('Line Origine',(0.,-50.,50.)) ;
#56=CARTESIAN_POINT('Vertex',(50.,-50.,50.)) ;
#59=CARTESIAN_POINT('Line Origine',(50.,0.,50.)) ;
#79=CARTESIAN_POINT('Axis2P3D Location',(0.,-50.,-50.)) ;
#84=CARTESIAN_POINT('Line Origine',(-50.,-50.,7.1054273576E-015)) ;
#88=CARTESIAN_POINT('Vertex',(-50.,-50.,-50.)) ;
#91=CARTESIAN_POINT('Line Origine',(0.,-50.,-50.)) ;
#95=CARTESIAN_POINT('Vertex',(50.,-50.,-50.)) ;
#98=CARTESIAN_POINT('Line Origine',(50.,-50.,7.1054273576E-015)) ;
#110=CARTESIAN_POINT('Axis2P3D Location',(0.,50.,-50.)) ;
#115=CARTESIAN_POINT('Line Origine',(-50.,0.,-50.)) ;
#119=CARTESIAN_POINT('Vertex',(-50.,50.,-50.)) ;
#122=CARTESIAN_POINT('Line Origine',(0.,50.,-50.)) ;
#126=CARTESIAN_POINT('Vertex',(50.,50.,-50.)) ;
#129=CARTESIAN_POINT('Line Origine',(50.,0.,-50.)) ;
#141=CARTESIAN_POINT('Axis2P3D Location',(0.,50.,50.)) ;
#146=CARTESIAN_POINT('Line Origine',(-50.,50.,7.1054273576E-015)) ;
#151=CARTESIAN_POINT('Line Origine',(50.,50.,7.1054273576E-015)) ;
#171=CARTESIAN_POINT('Axis2P3D Location',(-50.,0.,0.)) ;
#183=CARTESIAN_POINT('Axis2P3D Location',(50.,0.,0.)) ;
#205=CARTESIAN_POINT('Axis2P3D Location',(0.,0.,0.)) ;
#32=DIRECTION('Axis2P3D Direction',(0.,0.,-1.)) ;
#33=DIRECTION('Axis2P3D XDirection',(0.,1.,0.)) ;
#37=DIRECTION('Vector Direction',(1.,0.,0.)) ;
#46=DIRECTION('Vector Direction',(0.,1.,0.)) ;
#53=DIRECTION('Vector Direction',(1.,0.,0.)) ;
#60=DIRECTION('Vector Direction',(0.,1.,0.)) ;
#80=DIRECTION('Axis2P3D Direction',(0.,1.,0.)) ;
#81=DIRECTION('Axis2P3D XDirection',(0.,0.,1.)) ;
#85=DIRECTION('Vector Direction',(0.,0.,1.)) ;
#92=DIRECTION('Vector Direction',(1.,0.,0.)) ;
#99=DIRECTION('Vector Direction',(0.,0.,1.)) ;
#111=DIRECTION('Axis2P3D Direction',(-0.,0.,1.)) ;
#112=DIRECTION('Axis2P3D XDirection',(0.,-1.,0.)) ;
#116=DIRECTION('Vector Direction',(0.,-1.,0.)) ;
#123=DIRECTION('Vector Direction',(1.,0.,0.)) ;
#130=DIRECTION('Vector Direction',(0.,-1.,0.)) ;
#142=DIRECTION('Axis2P3D Direction',(0.,-1.,0.)) ;
#143=DIRECTION('Axis2P3D XDirection',(0.,0.,-1.)) ;
#147=DIRECTION('Vector Direction',(0.,0.,-1.)) ;
#152=DIRECTION('Vector Direction',(0.,0.,-1.)) ;
#172=DIRECTION('Axis2P3D Direction',(1.,0.,0.)) ;
#173=DIRECTION('Axis2P3D XDirection',(0.,1.,0.)) ;
#184=DIRECTION('Axis2P3D Direction',(1.,0.,0.)) ;
#185=DIRECTION('Axis2P3D XDirection',(0.,1.,0.)) ;
#206=DIRECTION('Axis2P3D Direction',(0.,0.,1.)) ;
#207=DIRECTION('Axis2P3D XDirection',(1.,0.,0.)) ;
#6=PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#16)) ;
#5=PRODUCT_CATEGORY('part','specification') ;
#29=STYLED_ITEM(' ',(#28),#21) ;
#213=STYLED_ITEM(' ',(#212),#208) ;
#28=PRESENTATION_STYLE_ASSIGNMENT((#27)) ;
#77=PRESENTATION_STYLE_ASSIGNMENT((#76)) ;
#169=PRESENTATION_STYLE_ASSIGNMENT((#168)) ;
#201=PRESENTATION_STYLE_ASSIGNMENT((#200)) ;
#212=PRESENTATION_STYLE_ASSIGNMENT((#211)) ;
#211=CURVE_STYLE(' ',#209,POSITIVE_LENGTH_MEASURE(0.129999995232),#210) ;
#27=SURFACE_STYLE_USAGE(.BOTH.,#26) ;
#76=SURFACE_STYLE_USAGE(.BOTH.,#75) ;
#168=SURFACE_STYLE_USAGE(.BOTH.,#167) ;
#200=SURFACE_STYLE_USAGE(.BOTH.,#199) ;
#26=SURFACE_SIDE_STYLE(' ',(#25)) ;
#75=SURFACE_SIDE_STYLE(' ',(#74)) ;
#167=SURFACE_SIDE_STYLE(' ',(#166)) ;
#199=SURFACE_SIDE_STYLE(' ',(#198)) ;
#25=SURFACE_STYLE_FILL_AREA(#24) ;
#74=SURFACE_STYLE_FILL_AREA(#73) ;
#166=SURFACE_STYLE_FILL_AREA(#165) ;
#198=SURFACE_STYLE_FILL_AREA(#197) ;
#24=FILL_AREA_STYLE(' ',(#23)) ;
#73=FILL_AREA_STYLE(' ',(#72)) ;
#165=FILL_AREA_STYLE(' ',(#164)) ;
#197=FILL_AREA_STYLE(' ',(#196)) ;
#23=FILL_AREA_STYLE_COLOUR(' ',#22) ;
#72=FILL_AREA_STYLE_COLOUR(' ',#71) ;
#164=FILL_AREA_STYLE_COLOUR(' ',#163) ;
#196=FILL_AREA_STYLE_COLOUR(' ',#195) ;
#38=VECTOR('Line Direction',#37,1.) ;
#47=VECTOR('Line Direction',#46,1.) ;
#54=VECTOR('Line Direction',#53,1.) ;
#61=VECTOR('Line Direction',#60,1.) ;
#86=VECTOR('Line Direction',#85,1.) ;
#93=VECTOR('Line Direction',#92,1.) ;
#100=VECTOR('Line Direction',#99,1.) ;
#117=VECTOR('Line Direction',#116,1.) ;
#124=VECTOR('Line Direction',#123,1.) ;
#131=VECTOR('Line Direction',#130,1.) ;
#148=VECTOR('Line Direction',#147,1.) ;
#153=VECTOR('Line Direction',#152,1.) ;
#11=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.005),#8,'distance_accuracy_value','CONFUSED CURVE UNCERTAINTY') ;
#204=SHAPE_REPRESENTATION_RELATIONSHIP(' ',' ',#13,#203) ;
#65=ORIENTED_EDGE('',*,*,#44,.F.) ;
#66=ORIENTED_EDGE('',*,*,#51,.T.) ;
#67=ORIENTED_EDGE('',*,*,#58,.T.) ;
#68=ORIENTED_EDGE('',*,*,#63,.F.) ;
#104=ORIENTED_EDGE('',*,*,#58,.F.) ;
#105=ORIENTED_EDGE('',*,*,#90,.T.) ;
#106=ORIENTED_EDGE('',*,*,#97,.T.) ;
#107=ORIENTED_EDGE('',*,*,#102,.F.) ;
#135=ORIENTED_EDGE('',*,*,#97,.F.) ;
#136=ORIENTED_EDGE('',*,*,#121,.T.) ;
#137=ORIENTED_EDGE('',*,*,#128,.T.) ;
#138=ORIENTED_EDGE('',*,*,#133,.F.) ;
#157=ORIENTED_EDGE('',*,*,#128,.F.) ;
#158=ORIENTED_EDGE('',*,*,#150,.T.) ;
#159=ORIENTED_EDGE('',*,*,#44,.T.) ;
#160=ORIENTED_EDGE('',*,*,#155,.F.) ;
#177=ORIENTED_EDGE('',*,*,#150,.F.) ;
#178=ORIENTED_EDGE('',*,*,#121,.F.) ;
#179=ORIENTED_EDGE('',*,*,#90,.F.) ;
#180=ORIENTED_EDGE('',*,*,#51,.F.) ;
#189=ORIENTED_EDGE('',*,*,#63,.T.) ;
#190=ORIENTED_EDGE('',*,*,#102,.T.) ;
#191=ORIENTED_EDGE('',*,*,#133,.T.) ;
#192=ORIENTED_EDGE('',*,*,#155,.T.) ;
#30=CLOSED_SHELL('Closed Shell',(#70,#109,#140,#162,#182,#194)) ;
#203=ADVANCED_BREP_SHAPE_REPRESENTATION('NONE',(#21),#12) ;
#70=ADVANCED_FACE('PartBody',(#69),#35,.F.) ;
#109=ADVANCED_FACE('PartBody',(#108),#83,.F.) ;
#140=ADVANCED_FACE('PartBody',(#139),#114,.F.) ;
#162=ADVANCED_FACE('PartBody',(#161),#145,.F.) ;
#182=ADVANCED_FACE('PartBody',(#181),#175,.F.) ;
#194=ADVANCED_FACE('PartBody',(#193),#187,.T.) ;
#4=APPLICATION_PROTOCOL_DEFINITION('international standard','automotive_design',2001,#1) ;
#21=MANIFOLD_SOLID_BREP('PartBody',#30) ;
#22=COLOUR_RGB('Colour',0.882352941176,0.882352941176,0.882352941176) ;
#195=COLOUR_RGB('Colour',0.,0.501960784314,1.) ;
#214=CONSTRUCTIVE_GEOMETRY_REPRESENTATION('supplemental geometry',(#208),#12) ;
#215=CONSTRUCTIVE_GEOMETRY_REPRESENTATION_RELATIONSHIP('supplemental geometry','',#13,#214) ;
#78=OVER_RIDING_STYLED_ITEM(' ',(#77),#70,#29) ;
#170=OVER_RIDING_STYLED_ITEM(' ',(#169),#162,#29) ;
#202=OVER_RIDING_STYLED_ITEM(' ',(#201),#194,#29) ;
#71=DRAUGHTING_PRE_DEFINED_COLOUR('green') ;
#163=DRAUGHTING_PRE_DEFINED_COLOUR('red') ;
#210=DRAUGHTING_PRE_DEFINED_COLOUR('white') ;
#209=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous') ;
#44=EDGE_CURVE('',#41,#43,#39,.T.) ;
#51=EDGE_CURVE('',#41,#50,#48,.F.) ;
#58=EDGE_CURVE('',#50,#57,#55,.T.) ;
#63=EDGE_CURVE('',#43,#57,#62,.F.) ;
#90=EDGE_CURVE('',#50,#89,#87,.F.) ;
#97=EDGE_CURVE('',#89,#96,#94,.T.) ;
#102=EDGE_CURVE('',#57,#96,#101,.F.) ;
#121=EDGE_CURVE('',#89,#120,#118,.F.) ;
#128=EDGE_CURVE('',#120,#127,#125,.T.) ;
#133=EDGE_CURVE('',#96,#127,#132,.F.) ;
#150=EDGE_CURVE('',#120,#41,#149,.F.) ;
#155=EDGE_CURVE('',#127,#43,#154,.F.) ;
#64=EDGE_LOOP('',(#65,#66,#67,#68)) ;
#103=EDGE_LOOP('',(#104,#105,#106,#107)) ;
#134=EDGE_LOOP('',(#135,#136,#137,#138)) ;
#156=EDGE_LOOP('',(#157,#158,#159,#160)) ;
#176=EDGE_LOOP('',(#177,#178,#179,#180)) ;
#188=EDGE_LOOP('',(#189,#190,#191,#192)) ;
#69=FACE_OUTER_BOUND('',#64,.T.) ;
#108=FACE_OUTER_BOUND('',#103,.T.) ;
#139=FACE_OUTER_BOUND('',#134,.T.) ;
#161=FACE_OUTER_BOUND('',#156,.T.) ;
#181=FACE_OUTER_BOUND('',#176,.T.) ;
#193=FACE_OUTER_BOUND('',#188,.T.) ;
#39=LINE('Line',#36,#38) ;
#48=LINE('Line',#45,#47) ;
#55=LINE('Line',#52,#54) ;
#62=LINE('Line',#59,#61) ;
#87=LINE('Line',#84,#86) ;
#94=LINE('Line',#91,#93) ;
#101=LINE('Line',#98,#100) ;
#118=LINE('Line',#115,#117) ;
#125=LINE('Line',#122,#124) ;
#132=LINE('Line',#129,#131) ;
#149=LINE('Line',#146,#148) ;
#154=LINE('Line',#151,#153) ;
#35=PLANE('',#34) ;
#83=PLANE('',#82) ;
#114=PLANE('',#113) ;
#145=PLANE('',#144) ;
#175=PLANE('',#174) ;
#187=PLANE('',#186) ;
#7=PRODUCT_CATEGORY_RELATIONSHIP(' ',' ',#5,#6) ;
#17=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('1.0',' ',#16,.NOT_KNOWN.) ;
#20=SHAPE_DEFINITION_REPRESENTATION(#19,#13) ;
#41=VERTEX_POINT('',#40) ;
#43=VERTEX_POINT('',#42) ;
#50=VERTEX_POINT('',#49) ;
#57=VERTEX_POINT('',#56) ;
#89=VERTEX_POINT('',#88) ;
#96=VERTEX_POINT('',#95) ;
#120=VERTEX_POINT('',#119) ;
#127=VERTEX_POINT('',#126) ;
#8=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.)) ;
#9=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.)) ;
#10=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT()) ;
#12=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#11))GLOBAL_UNIT_ASSIGNED_CONTEXT((#8,#9,#10))REPRESENTATION_CONTEXT(' ',' ')) ;
ENDSEC;
END-ISO-10303-21;
