ISO-10303-21;
HEADER;
FILE_NAME('Torus','2024-02-02T07:43:45',('gnikit'),('Gmsh'),'Gmsh','-',
  'Unknown');
FILE_DESCRIPTION(('A simple torus geometry'),'2;1');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Open CASCADE STEP translator 7.8 1',
  'Open CASCADE STEP translator 7.8 1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#73);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17));
#17 = ADVANCED_FACE('',(#18),#31,.T.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#49,#71,#72));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#22,#24,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(1.2,-2.939152317954E-16,-4.898587196589E-17));
#24 = SEAM_CURVE('',#25,(#30,#42),.PCURVE_S1.);
#25 = CIRCLE('',#26,1.2);
#26 = AXIS2_PLACEMENT_3D('',#27,#28,#29);
#27 = CARTESIAN_POINT('',(0.,0.,-4.898587196589E-17));
#28 = DIRECTION('',(0.,0.,1.));
#29 = DIRECTION('',(1.,0.,-0.));
#30 = PCURVE('',#31,#36);
#31 = TOROIDAL_SURFACE('',#32,1.,0.2);
#32 = AXIS2_PLACEMENT_3D('',#33,#34,#35);
#33 = CARTESIAN_POINT('',(0.,0.,0.));
#34 = DIRECTION('',(0.,0.,1.));
#35 = DIRECTION('',(1.,0.,-0.));
#36 = DEFINITIONAL_REPRESENTATION('',(#37),#41);
#37 = LINE('',#38,#39);
#38 = CARTESIAN_POINT('',(0.,6.28318530718));
#39 = VECTOR('',#40,1.);
#40 = DIRECTION('',(1.,0.));
#41 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#42 = PCURVE('',#31,#43);
#43 = DEFINITIONAL_REPRESENTATION('',(#44),#48);
#44 = LINE('',#45,#46);
#45 = CARTESIAN_POINT('',(0.,0.));
#46 = VECTOR('',#47,1.);
#47 = DIRECTION('',(1.,0.));
#48 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#49 = ORIENTED_EDGE('',*,*,#50,.F.);
#50 = EDGE_CURVE('',#22,#22,#51,.T.);
#51 = SEAM_CURVE('',#52,(#57,#64),.PCURVE_S1.);
#52 = CIRCLE('',#53,0.2);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(1.,-2.449293598295E-16,0.));
#55 = DIRECTION('',(-2.449293598295E-16,-1.,0.));
#56 = DIRECTION('',(1.,-2.449293598295E-16,0.));
#57 = PCURVE('',#31,#58);
#58 = DEFINITIONAL_REPRESENTATION('',(#59),#63);
#59 = LINE('',#60,#61);
#60 = CARTESIAN_POINT('',(6.28318530718,-0.));
#61 = VECTOR('',#62,1.);
#62 = DIRECTION('',(0.,1.));
#63 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#64 = PCURVE('',#31,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(0.,-0.));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(0.,1.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = ORIENTED_EDGE('',*,*,#21,.T.);
#72 = ORIENTED_EDGE('',*,*,#50,.T.);
#73 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#77)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#74,#75,#76)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#74 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#75 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#76 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#77 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#74,
  'distance_accuracy_value','confusion accuracy');
#78 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
