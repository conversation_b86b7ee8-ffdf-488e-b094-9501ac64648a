ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('AS1 with extended valprops'),'2;1');
FILE_NAME('as1-tu.stp',
'2008-07-23T15:10:19+01:00',
('A P Ranger'),
('Theorem Solutions Ltd'),
'THEOREM SOLUTIONS GCO -> AP203 E2 PREPROCESSOR 10.0.053  ',
'UG',
'A P Ranger');
FILE_SCHEMA(('CONFIGURATION_CONTROL_3D_DESIGN_ED2_MIM_LF { 1 0 10303 403 1 1 4}'
));
ENDSEC;
DATA;
#1=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#2=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#3=(NAMED_UNIT(*)SI_UNIT($,.STERADIAN.)SOLID_ANGLE_UNIT());
#4=(LENGTH_UNIT()NAMED_UNIT(*)SI_UNIT(.MILLI.,.METRE.));
#5=(NAMED_UNIT(*)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#6=APPLICATION_CONTEXT(
'configuration controlled 3D designs of mechanical parts and assemblies');
#7=APPLICATION_PROTOCOL_DEFINITION('technical specification',
'config_control_design',2004,#6);
#8=PRODUCT_CONTEXT('',#6,'mechanical');
#9=PRODUCT_DEFINITION_CONTEXT('',#6,'design');
#10=PRODUCT('as1','as1',$,(#8));
#11=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#10));
#12=PRODUCT_RELATED_PRODUCT_CATEGORY('part','as1',(#10));
#13=PRODUCT_CATEGORY_RELATIONSHIP('','as1',#12,#11);
#14=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for as1',#10);
#15=PRODUCT_DEFINITION('design',$,#14,#9);
#16=PRODUCT('nut','nut',$,(#8));
#17=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#16));
#18=PRODUCT_RELATED_PRODUCT_CATEGORY('part','nut',(#16));
#19=PRODUCT_CATEGORY_RELATIONSHIP('','nut',#18,#17);
#20=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for nut',#16);
#21=PRODUCT_DEFINITION('design',$,#20,#9);
#22=PRODUCT('rod','rod',$,(#8));
#23=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#22));
#24=PRODUCT_RELATED_PRODUCT_CATEGORY('part','rod',(#22));
#25=PRODUCT_CATEGORY_RELATIONSHIP('','rod',#24,#23);
#26=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for rod',#22);
#27=PRODUCT_DEFINITION('design',$,#26,#9);
#28=PRODUCT('rod-assembly','rod-assembly',$,(#8));
#29=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#28));
#30=PRODUCT_RELATED_PRODUCT_CATEGORY('part','rod-assembly',(#28));
#31=PRODUCT_CATEGORY_RELATIONSHIP('','rod-assembly',#30,#29);
#32=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for rod-assembly',#28);
#33=PRODUCT_DEFINITION('design',$,#32,#9);
#34=PRODUCT('bolt','bolt',$,(#8));
#35=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#34));
#36=PRODUCT_RELATED_PRODUCT_CATEGORY('part','bolt',(#34));
#37=PRODUCT_CATEGORY_RELATIONSHIP('','bolt',#36,#35);
#38=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for bolt',#34);
#39=PRODUCT_DEFINITION('design',$,#38,#9);
#40=PRODUCT('nut-bolt-assembly','nut-bolt-assembly',$,(#8));
#41=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#40));
#42=PRODUCT_RELATED_PRODUCT_CATEGORY('part','nut-bolt-assembly',(#40));
#43=PRODUCT_CATEGORY_RELATIONSHIP('','nut-bolt-assembly',#42,#41);
#44=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for nut-bolt-assembly',
#40);
#45=PRODUCT_DEFINITION('design',$,#44,#9);
#46=PRODUCT('l-bracket','l-bracket',$,(#8));
#47=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#46));
#48=PRODUCT_RELATED_PRODUCT_CATEGORY('part','l-bracket',(#46));
#49=PRODUCT_CATEGORY_RELATIONSHIP('','l-bracket',#48,#47);
#50=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for l-bracket',#46);
#51=PRODUCT_DEFINITION('design',$,#50,#9);
#52=PRODUCT('l-bracket-assembly','l-bracket-assembly',$,(#8));
#53=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#52));
#54=PRODUCT_RELATED_PRODUCT_CATEGORY('part','l-bracket-assembly',(#52));
#55=PRODUCT_CATEGORY_RELATIONSHIP('','l-bracket-assembly',#54,#53);
#56=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for l-bracket-assembly',
#52);
#57=PRODUCT_DEFINITION('design',$,#56,#9);
#58=PRODUCT('plate','plate',$,(#8));
#59=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','',(#58));
#60=PRODUCT_RELATED_PRODUCT_CATEGORY('part','plate',(#58));
#61=PRODUCT_CATEGORY_RELATIONSHIP('','plate',#60,#59);
#62=PRODUCT_DEFINITION_FORMATION('version 0','version 0 for plate',#58);
#63=PRODUCT_DEFINITION('design',$,#62,#9);
#64=CARTESIAN_POINT('#64',(20.,0.0,3.0));
#65=VERTEX_POINT('#65',#64);
#66=CARTESIAN_POINT('#66',(0.0,0.0,3.0));
#67=VERTEX_POINT('#67',#66);
#68=CARTESIAN_POINT('#68',(20.,15.,3.0));
#69=VERTEX_POINT('#69',#68);
#70=CARTESIAN_POINT('#70',(0.0,15.,3.0));
#71=VERTEX_POINT('#71',#70);
#72=CARTESIAN_POINT('#72',(5.0,7.5,3.0));
#73=VERTEX_POINT('#73',#72);
#74=CARTESIAN_POINT('#74',(15.,7.5,3.0));
#75=VERTEX_POINT('#75',#74);
#76=CARTESIAN_POINT('#76',(20.,0.0,0.0));
#77=VERTEX_POINT('#77',#76);
#78=CARTESIAN_POINT('#78',(0.0,0.0,0.0));
#79=VERTEX_POINT('#79',#78);
#80=CARTESIAN_POINT('#80',(0.0,15.,0.0));
#81=VERTEX_POINT('#81',#80);
#82=CARTESIAN_POINT('#82',(20.,15.,0.0));
#83=VERTEX_POINT('#83',#82);
#84=CARTESIAN_POINT('#84',(15.,7.5,0.0));
#85=VERTEX_POINT('#85',#84);
#86=CARTESIAN_POINT('#86',(5.0,7.5,0.0));
#87=VERTEX_POINT('#87',#86);
#88=CARTESIAN_POINT('#88',(20.,0.0,3.0));
#89=DIRECTION('#89',(-1.,0.0,0.0));
#90=VECTOR('#90',#89,20.);
#91=LINE('#91',#88,#90);
#92=CARTESIAN_POINT('#92',(20.,15.,3.0));
#93=DIRECTION('#93',(0.0,-1.,0.0));
#94=VECTOR('#94',#93,15.);
#95=LINE('#95',#92,#94);
#96=CARTESIAN_POINT('#96',(0.0,15.,3.0));
#97=DIRECTION('#97',(1.0,0.0,0.0));
#98=VECTOR('#98',#97,20.);
#99=LINE('#99',#96,#98);
#100=CARTESIAN_POINT('#100',(0.0,0.0,3.0));
#101=DIRECTION('#101',(0.0,1.0,0.0));
#102=VECTOR('#102',#101,15.);
#103=LINE('#103',#100,#102);
#104=CARTESIAN_POINT('#104',(5.0,7.5,3.0));
#105=CARTESIAN_POINT('#105',(5.0,17.5,3.0));
#106=CARTESIAN_POINT('#106',(15.,17.5,3.0));
#107=CARTESIAN_POINT('#107',(15.,7.5,3.0));
#108=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#104,#105,#106,#107),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#108'));
#109=CARTESIAN_POINT('#109',(15.,7.5,3.0));
#110=CARTESIAN_POINT('#110',(15.,-2.5,3.0));
#111=CARTESIAN_POINT('#111',(5.0,-2.5,3.0));
#112=CARTESIAN_POINT('#112',(5.0,7.5,3.0));
#113=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#109,#110,#111,#112),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#113'));
#114=CARTESIAN_POINT('#114',(20.,0.0,0.0));
#115=DIRECTION('#115',(0.0,0.0,1.0));
#116=VECTOR('#116',#115,3.0);
#117=LINE('#117',#114,#116);
#118=CARTESIAN_POINT('#118',(0.0,0.0,0.0));
#119=DIRECTION('#119',(0.0,0.0,1.0));
#120=VECTOR('#120',#119,3.0);
#121=LINE('#121',#118,#120);
#122=CARTESIAN_POINT('#122',(0.0,0.0,0.0));
#123=DIRECTION('#123',(1.0,0.0,0.0));
#124=VECTOR('#124',#123,20.);
#125=LINE('#125',#122,#124);
#126=CARTESIAN_POINT('#126',(0.0,15.,0.0));
#127=DIRECTION('#127',(0.0,0.0,1.0));
#128=VECTOR('#128',#127,3.0);
#129=LINE('#129',#126,#128);
#130=CARTESIAN_POINT('#130',(0.0,15.,0.0));
#131=DIRECTION('#131',(0.0,-1.,0.0));
#132=VECTOR('#132',#131,15.);
#133=LINE('#133',#130,#132);
#134=CARTESIAN_POINT('#134',(20.,15.,0.0));
#135=DIRECTION('#135',(0.0,0.0,1.0));
#136=VECTOR('#136',#135,3.0);
#137=LINE('#137',#134,#136);
#138=CARTESIAN_POINT('#138',(20.,15.,0.0));
#139=DIRECTION('#139',(-1.,0.0,0.0));
#140=VECTOR('#140',#139,20.);
#141=LINE('#141',#138,#140);
#142=CARTESIAN_POINT('#142',(20.,0.0,0.0));
#143=DIRECTION('#143',(0.0,1.0,0.0));
#144=VECTOR('#144',#143,15.);
#145=LINE('#145',#142,#144);
#146=CARTESIAN_POINT('#146',(15.,7.5,0.0));
#147=CARTESIAN_POINT('#147',(15.,-2.5,0.0));
#148=CARTESIAN_POINT('#148',(5.0,-2.5,0.0));
#149=CARTESIAN_POINT('#149',(5.0,7.5,0.0));
#150=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#146,#147,#148,#149),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#150'));
#151=CARTESIAN_POINT('#151',(5.0,7.5,0.0));
#152=CARTESIAN_POINT('#152',(5.0,17.5,0.0));
#153=CARTESIAN_POINT('#153',(15.,17.5,0.0));
#154=CARTESIAN_POINT('#154',(15.,7.5,0.0));
#155=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#151,#152,#153,#154),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#155'));
#156=CARTESIAN_POINT('#156',(15.,7.5,0.0));
#157=DIRECTION('#157',(0.0,0.0,1.0));
#158=VECTOR('#158',#157,3.0);
#159=LINE('#159',#156,#158);
#160=CARTESIAN_POINT('#160',(5.0,7.5,3.0));
#161=DIRECTION('#161',(0.0,0.0,-1.));
#162=VECTOR('#162',#161,3.0);
#163=LINE('#163',#160,#162);
#164=EDGE_CURVE('#164',#65,#67,#91,.T.);
#165=EDGE_CURVE('#165',#69,#65,#95,.T.);
#166=EDGE_CURVE('#166',#71,#69,#99,.T.);
#167=EDGE_CURVE('#167',#67,#71,#103,.T.);
#168=EDGE_CURVE('#168',#73,#75,#108,.T.);
#169=EDGE_CURVE('#169',#75,#73,#113,.T.);
#170=EDGE_CURVE('#170',#77,#65,#117,.T.);
#171=EDGE_CURVE('#171',#79,#67,#121,.T.);
#172=EDGE_CURVE('#172',#79,#77,#125,.T.);
#173=EDGE_CURVE('#173',#81,#71,#129,.T.);
#174=EDGE_CURVE('#174',#81,#79,#133,.T.);
#175=EDGE_CURVE('#175',#83,#69,#137,.T.);
#176=EDGE_CURVE('#176',#83,#81,#141,.T.);
#177=EDGE_CURVE('#177',#77,#83,#145,.T.);
#178=EDGE_CURVE('#178',#85,#87,#150,.T.);
#179=EDGE_CURVE('#179',#87,#85,#155,.T.);
#180=EDGE_CURVE('#180',#85,#75,#159,.T.);
#181=EDGE_CURVE('#181',#73,#87,#163,.T.);
#182=ORIENTED_EDGE('#182',*,*,#164,.F.);
#183=ORIENTED_EDGE('#183',*,*,#165,.F.);
#184=ORIENTED_EDGE('#184',*,*,#166,.F.);
#185=ORIENTED_EDGE('#185',*,*,#167,.F.);
#186=EDGE_LOOP('#186',(#182,#183,#184,#185));
#187=FACE_OUTER_BOUND('#187',#186,.T.);
#188=ORIENTED_EDGE('#188',*,*,#168,.T.);
#189=ORIENTED_EDGE('#189',*,*,#169,.T.);
#190=EDGE_LOOP('#190',(#188,#189));
#191=FACE_BOUND('#191',#190,.T.);
#192=CARTESIAN_POINT('#192',(10.,7.5,3.0));
#193=DIRECTION('#193',(0.0,0.0,1.0));
#194=DIRECTION('#194',(1.0,0.0,0.0));
#195=AXIS2_PLACEMENT_3D('#195',#192,#193,#194);
#196=PLANE('#196',#195);
#197=ADVANCED_FACE('#197',(#187,#191),#196,.T.);
#198=ORIENTED_EDGE('#198',*,*,#170,.T.);
#199=ORIENTED_EDGE('#199',*,*,#164,.T.);
#200=ORIENTED_EDGE('#200',*,*,#171,.F.);
#201=ORIENTED_EDGE('#201',*,*,#172,.T.);
#202=EDGE_LOOP('#202',(#198,#199,#200,#201));
#203=FACE_BOUND('#203',#202,.T.);
#204=CARTESIAN_POINT('#204',(10.,0.0,0.0));
#205=DIRECTION('#205',(0.0,-1.,0.0));
#206=DIRECTION('#206',(0.0,0.0,-1.));
#207=AXIS2_PLACEMENT_3D('#207',#204,#205,#206);
#208=PLANE('#208',#207);
#209=ADVANCED_FACE('#209',(#203),#208,.T.);
#210=ORIENTED_EDGE('#210',*,*,#171,.T.);
#211=ORIENTED_EDGE('#211',*,*,#167,.T.);
#212=ORIENTED_EDGE('#212',*,*,#173,.F.);
#213=ORIENTED_EDGE('#213',*,*,#174,.T.);
#214=EDGE_LOOP('#214',(#210,#211,#212,#213));
#215=FACE_BOUND('#215',#214,.T.);
#216=CARTESIAN_POINT('#216',(0.0,7.5,0.0));
#217=DIRECTION('#217',(-1.,0.0,0.0));
#218=DIRECTION('#218',(0.0,0.0,1.0));
#219=AXIS2_PLACEMENT_3D('#219',#216,#217,#218);
#220=PLANE('#220',#219);
#221=ADVANCED_FACE('#221',(#215),#220,.T.);
#222=ORIENTED_EDGE('#222',*,*,#173,.T.);
#223=ORIENTED_EDGE('#223',*,*,#166,.T.);
#224=ORIENTED_EDGE('#224',*,*,#175,.F.);
#225=ORIENTED_EDGE('#225',*,*,#176,.T.);
#226=EDGE_LOOP('#226',(#222,#223,#224,#225));
#227=FACE_BOUND('#227',#226,.T.);
#228=CARTESIAN_POINT('#228',(10.,15.,0.0));
#229=DIRECTION('#229',(0.0,1.0,0.0));
#230=DIRECTION('#230',(0.0,0.0,1.0));
#231=AXIS2_PLACEMENT_3D('#231',#228,#229,#230);
#232=PLANE('#232',#231);
#233=ADVANCED_FACE('#233',(#227),#232,.T.);
#234=ORIENTED_EDGE('#234',*,*,#177,.F.);
#235=ORIENTED_EDGE('#235',*,*,#172,.F.);
#236=ORIENTED_EDGE('#236',*,*,#174,.F.);
#237=ORIENTED_EDGE('#237',*,*,#176,.F.);
#238=EDGE_LOOP('#238',(#234,#235,#236,#237));
#239=FACE_OUTER_BOUND('#239',#238,.T.);
#240=ORIENTED_EDGE('#240',*,*,#178,.F.);
#241=ORIENTED_EDGE('#241',*,*,#179,.F.);
#242=EDGE_LOOP('#242',(#240,#241));
#243=FACE_BOUND('#243',#242,.T.);
#244=CARTESIAN_POINT('#244',(10.,7.5,0.0));
#245=DIRECTION('#245',(0.0,0.0,-1.));
#246=DIRECTION('#246',(-1.,0.0,0.0));
#247=AXIS2_PLACEMENT_3D('#247',#244,#245,#246);
#248=PLANE('#248',#247);
#249=ADVANCED_FACE('#249',(#239,#243),#248,.T.);
#250=ORIENTED_EDGE('#250',*,*,#177,.T.);
#251=ORIENTED_EDGE('#251',*,*,#175,.T.);
#252=ORIENTED_EDGE('#252',*,*,#165,.T.);
#253=ORIENTED_EDGE('#253',*,*,#170,.F.);
#254=EDGE_LOOP('#254',(#250,#251,#252,#253));
#255=FACE_BOUND('#255',#254,.T.);
#256=CARTESIAN_POINT('#256',(20.,7.5,0.0));
#257=DIRECTION('#257',(1.0,0.0,0.0));
#258=DIRECTION('#258',(0.0,0.0,-1.));
#259=AXIS2_PLACEMENT_3D('#259',#256,#257,#258);
#260=PLANE('#260',#259);
#261=ADVANCED_FACE('#261',(#255),#260,.T.);
#262=ORIENTED_EDGE('#262',*,*,#179,.T.);
#263=ORIENTED_EDGE('#263',*,*,#180,.T.);
#264=ORIENTED_EDGE('#264',*,*,#168,.F.);
#265=ORIENTED_EDGE('#265',*,*,#181,.T.);
#266=EDGE_LOOP('#266',(#262,#263,#264,#265));
#267=FACE_BOUND('#267',#266,.T.);
#268=CARTESIAN_POINT('#268',(5.0,7.5,3.0));
#269=CARTESIAN_POINT('#269',(5.0,17.5,3.0));
#270=CARTESIAN_POINT('#270',(15.,17.5,3.0));
#271=CARTESIAN_POINT('#271',(15.,7.5,3.0));
#272=CARTESIAN_POINT('#272',(5.0,7.5,0.0));
#273=CARTESIAN_POINT('#273',(5.0,17.5,0.0));
#274=CARTESIAN_POINT('#274',(15.,17.5,0.0));
#275=CARTESIAN_POINT('#275',(15.,7.5,0.0));
#276=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#268,#269,#270,#271),(#272,#273,
#274,#275)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#276')SURFACE());
#277=ADVANCED_FACE('#277',(#267),#276,.T.);
#278=ORIENTED_EDGE('#278',*,*,#178,.T.);
#279=ORIENTED_EDGE('#279',*,*,#181,.F.);
#280=ORIENTED_EDGE('#280',*,*,#169,.F.);
#281=ORIENTED_EDGE('#281',*,*,#180,.F.);
#282=EDGE_LOOP('#282',(#278,#279,#280,#281));
#283=FACE_BOUND('#283',#282,.T.);
#284=CARTESIAN_POINT('#284',(15.,7.5,3.0));
#285=CARTESIAN_POINT('#285',(15.,-2.5,3.0));
#286=CARTESIAN_POINT('#286',(5.0,-2.5,3.0));
#287=CARTESIAN_POINT('#287',(5.0,7.5,3.0));
#288=CARTESIAN_POINT('#288',(15.,7.5,0.0));
#289=CARTESIAN_POINT('#289',(15.,-2.5,0.0));
#290=CARTESIAN_POINT('#290',(5.0,-2.5,0.0));
#291=CARTESIAN_POINT('#291',(5.0,7.5,0.0));
#292=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#284,#285,#286,#287),(#288,#289,
#290,#291)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#292')SURFACE());
#293=ADVANCED_FACE('#293',(#283),#292,.T.);
#294=CLOSED_SHELL('#294',(#197,#209,#221,#233,#249,#261,#277,#293));
#295=MANIFOLD_SOLID_BREP('#295',#294);
#296=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.1E-12),#4,
'distance_accuracy_value','EDGE CURVE AND VERTEX POINT ACCURACY');
#297=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#296))GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3))REPRESENTATION_CONTEXT('nut','3D')
);
#298=CARTESIAN_POINT('#298',(0.0,0.0,0.0));
#299=DIRECTION('#299',(1.0,0.0,0.0));
#300=DIRECTION('#300',(0.0,0.0,1.0));
#301=AXIS2_PLACEMENT_3D('#301',#298,#300,#299);
#302=ADVANCED_BREP_SHAPE_REPRESENTATION('#302',(#295,#301),#297);
#303=DERIVED_UNIT_ELEMENT(#4,3.0);
#304=DERIVED_UNIT((#303));
#305=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#304);
#306=DERIVED_UNIT_ELEMENT(#4,2.0);
#307=DERIVED_UNIT((#306));
#308=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#307);
#310=SHAPE_ASPECT('#310','solid #295',#309,.F.);
#311=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(664.476451301),
#304);
#312=REPRESENTATION('volume',(#311),#297);
#313=PROPERTY_DEFINITION('geometric validation property','volume of #295',#310);
#314=PROPERTY_DEFINITION_REPRESENTATION(#313,#312);
#315=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(747.193720347),#307);
#316=REPRESENTATION('surface area',(#315),#297);
#317=PROPERTY_DEFINITION('geometric validation property','area of #295',#310);
#318=PROPERTY_DEFINITION_REPRESENTATION(#317,#316);
#319=CARTESIAN_POINT('centre point',(10.,7.5,1.5));
#320=REPRESENTATION('centroid',(#319),#297);
#321=PROPERTY_DEFINITION('geometric validation property','centroid of #295',#310
);
#322=PROPERTY_DEFINITION_REPRESENTATION(#321,#320);
#323=SHAPE_REPRESENTATION('',(#295),#297);
#324=PROPERTY_DEFINITION('','Shape for Validation Properties',#310);
#325=SHAPE_DEFINITION_REPRESENTATION(#324,#323);
#326=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(664.476451301),
#304);
#327=REPRESENTATION('volume',(#326),#297);
#328=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #302 - nut',#309);
#329=PROPERTY_DEFINITION_REPRESENTATION(#328,#327);
#330=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(747.193720347),#307);
#331=REPRESENTATION('surface area',(#330),#297);
#332=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #302 - nut',#309);
#333=PROPERTY_DEFINITION_REPRESENTATION(#332,#331);
#334=CARTESIAN_POINT('centre point',(10.,7.5,1.5));
#335=REPRESENTATION('centroid',(#334),#297);
#336=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #302 - nut',#309);
#337=PROPERTY_DEFINITION_REPRESENTATION(#336,#335);
#338=CARTESIAN_POINT('#338',(-10.,-7.5,185.));
#339=DIRECTION('#339',(1.0,0.0,0.0));
#340=DIRECTION('#340',(0.0,0.0,1.0));
#341=AXIS2_PLACEMENT_3D('#341',#338,#340,#339);
#342=ITEM_DEFINED_TRANSFORMATION('#341','rod-assembly : nut',#301,#341);
#343=CARTESIAN_POINT('#343',(-10.,-7.5,12.));
#344=DIRECTION('#344',(1.0,0.0,0.0));
#345=DIRECTION('#345',(0.0,0.0,1.0));
#346=AXIS2_PLACEMENT_3D('#346',#343,#345,#344);
#347=ITEM_DEFINED_TRANSFORMATION('#346','rod-assembly : nut',#301,#346);
#348=CARTESIAN_POINT('#348',(5.0,0.0,200.));
#349=VERTEX_POINT('#349',#348);
#350=CARTESIAN_POINT('#350',(-5.,0.0,200.));
#351=VERTEX_POINT('#351',#350);
#352=CARTESIAN_POINT('#352',(-5.,0.0,0.0));
#353=VERTEX_POINT('#353',#352);
#354=CARTESIAN_POINT('#354',(5.0,0.0,0.0));
#355=VERTEX_POINT('#355',#354);
#356=CARTESIAN_POINT('#356',(5.0,0.0,200.));
#357=CARTESIAN_POINT('#357',(5.0,-10.,200.));
#358=CARTESIAN_POINT('#358',(-5.,-10.,200.));
#359=CARTESIAN_POINT('#359',(-5.,0.0,200.));
#360=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#356,#357,#358,#359),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#360'));
#361=CARTESIAN_POINT('#361',(-5.,0.0,200.));
#362=CARTESIAN_POINT('#362',(-5.,10.,200.));
#363=CARTESIAN_POINT('#363',(5.0,10.,200.));
#364=CARTESIAN_POINT('#364',(5.0,0.0,200.));
#365=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#361,#362,#363,#364),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#365'));
#366=CARTESIAN_POINT('#366',(-5.,0.0,0.0));
#367=CARTESIAN_POINT('#367',(-5.,-10.,0.0));
#368=CARTESIAN_POINT('#368',(5.0,-10.,0.0));
#369=CARTESIAN_POINT('#369',(5.0,0.0,0.0));
#370=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#366,#367,#368,#369),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#370'));
#371=CARTESIAN_POINT('#371',(5.0,0.0,0.0));
#372=CARTESIAN_POINT('#372',(5.0,10.,0.0));
#373=CARTESIAN_POINT('#373',(-5.,10.,0.0));
#374=CARTESIAN_POINT('#374',(-5.,0.0,0.0));
#375=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#371,#372,#373,#374),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#375'));
#376=CARTESIAN_POINT('#376',(5.0,0.0,200.));
#377=DIRECTION('#377',(0.0,0.0,-1.));
#378=VECTOR('#378',#377,200.);
#379=LINE('#379',#376,#378);
#380=CARTESIAN_POINT('#380',(-5.,0.0,0.0));
#381=DIRECTION('#381',(0.0,0.0,1.0));
#382=VECTOR('#382',#381,200.);
#383=LINE('#383',#380,#382);
#384=EDGE_CURVE('#384',#349,#351,#360,.T.);
#385=EDGE_CURVE('#385',#351,#349,#365,.T.);
#386=EDGE_CURVE('#386',#353,#355,#370,.T.);
#387=EDGE_CURVE('#387',#355,#353,#375,.T.);
#388=EDGE_CURVE('#388',#349,#355,#379,.T.);
#389=EDGE_CURVE('#389',#353,#351,#383,.T.);
#390=ORIENTED_EDGE('#390',*,*,#384,.F.);
#391=ORIENTED_EDGE('#391',*,*,#385,.F.);
#392=EDGE_LOOP('#392',(#390,#391));
#393=FACE_BOUND('#393',#392,.T.);
#394=CARTESIAN_POINT('#394',(0.0,0.0,200.));
#395=DIRECTION('#395',(0.0,0.0,1.0));
#396=DIRECTION('#396',(1.0,0.0,0.0));
#397=AXIS2_PLACEMENT_3D('#397',#394,#395,#396);
#398=PLANE('#398',#397);
#399=ADVANCED_FACE('#399',(#393),#398,.T.);
#400=ORIENTED_EDGE('#400',*,*,#386,.F.);
#401=ORIENTED_EDGE('#401',*,*,#387,.F.);
#402=EDGE_LOOP('#402',(#400,#401));
#403=FACE_BOUND('#403',#402,.T.);
#404=CARTESIAN_POINT('#404',(0.0,0.0,0.0));
#405=DIRECTION('#405',(0.0,0.0,-1.));
#406=DIRECTION('#406',(-1.,0.0,0.0));
#407=AXIS2_PLACEMENT_3D('#407',#404,#405,#406);
#408=PLANE('#408',#407);
#409=ADVANCED_FACE('#409',(#403),#408,.T.);
#410=ORIENTED_EDGE('#410',*,*,#385,.T.);
#411=ORIENTED_EDGE('#411',*,*,#388,.T.);
#412=ORIENTED_EDGE('#412',*,*,#387,.T.);
#413=ORIENTED_EDGE('#413',*,*,#389,.T.);
#414=EDGE_LOOP('#414',(#410,#411,#412,#413));
#415=FACE_BOUND('#415',#414,.T.);
#416=CARTESIAN_POINT('#416',(5.0,0.0,200.));
#417=CARTESIAN_POINT('#417',(5.0,10.,200.));
#418=CARTESIAN_POINT('#418',(-5.,10.,200.));
#419=CARTESIAN_POINT('#419',(-5.,0.0,200.));
#420=CARTESIAN_POINT('#420',(5.0,0.0,0.0));
#421=CARTESIAN_POINT('#421',(5.0,10.,0.0));
#422=CARTESIAN_POINT('#422',(-5.,10.,0.0));
#423=CARTESIAN_POINT('#423',(-5.,0.0,0.0));
#424=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#416,#417,#418,#419),(#420,#421,
#422,#423)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#424')SURFACE());
#425=ADVANCED_FACE('#425',(#415),#424,.T.);
#426=ORIENTED_EDGE('#426',*,*,#384,.T.);
#427=ORIENTED_EDGE('#427',*,*,#389,.F.);
#428=ORIENTED_EDGE('#428',*,*,#386,.T.);
#429=ORIENTED_EDGE('#429',*,*,#388,.F.);
#430=EDGE_LOOP('#430',(#426,#427,#428,#429));
#431=FACE_BOUND('#431',#430,.T.);
#432=CARTESIAN_POINT('#432',(-5.,0.0,200.));
#433=CARTESIAN_POINT('#433',(-5.,-10.,200.));
#434=CARTESIAN_POINT('#434',(5.0,-10.,200.));
#435=CARTESIAN_POINT('#435',(5.0,0.0,200.));
#436=CARTESIAN_POINT('#436',(-5.,0.0,0.0));
#437=CARTESIAN_POINT('#437',(-5.,-10.,0.0));
#438=CARTESIAN_POINT('#438',(5.0,-10.,0.0));
#439=CARTESIAN_POINT('#439',(5.0,0.0,0.0));
#440=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#432,#433,#434,#435),(#436,#437,
#438,#439)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#440')SURFACE());
#441=ADVANCED_FACE('#441',(#431),#440,.T.);
#442=CLOSED_SHELL('#442',(#399,#409,#425,#441));
#443=MANIFOLD_SOLID_BREP('#443',#442);
#444=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.1194E-12),#4,
'distance_accuracy_value','EDGE CURVE AND VERTEX POINT ACCURACY');
#445=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#444))GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3))REPRESENTATION_CONTEXT('rod','3D')
);
#446=CARTESIAN_POINT('#446',(0.0,0.0,0.0));
#447=DIRECTION('#447',(1.0,0.0,0.0));
#448=DIRECTION('#448',(0.0,0.0,1.0));
#449=AXIS2_PLACEMENT_3D('#449',#446,#448,#447);
#450=ADVANCED_BREP_SHAPE_REPRESENTATION('#450',(#443,#449),#445);
#451=DERIVED_UNIT_ELEMENT(#4,3.0);
#452=DERIVED_UNIT((#451));
#453=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#452);
#454=DERIVED_UNIT_ELEMENT(#4,2.0);
#455=DERIVED_UNIT((#454));
#456=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#455);
#458=SHAPE_ASPECT('#458','solid #443',#457,.F.);
#459=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(15705.534251651),#452);
#460=REPRESENTATION('volume',(#459),#445);
#461=PROPERTY_DEFINITION('geometric validation property','volume of #443',#458);
#462=PROPERTY_DEFINITION_REPRESENTATION(#461,#460);
#463=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(6440.022267471),#455);
#464=REPRESENTATION('surface area',(#463),#445);
#465=PROPERTY_DEFINITION('geometric validation property','area of #443',#458);
#466=PROPERTY_DEFINITION_REPRESENTATION(#465,#464);
#467=CARTESIAN_POINT('centre point',(0.0,0.0,100.));
#468=REPRESENTATION('centroid',(#467),#445);
#469=PROPERTY_DEFINITION('geometric validation property','centroid of #443',#458
);
#470=PROPERTY_DEFINITION_REPRESENTATION(#469,#468);
#471=SHAPE_REPRESENTATION('',(#443),#445);
#472=PROPERTY_DEFINITION('','Shape for Validation Properties',#458);
#473=SHAPE_DEFINITION_REPRESENTATION(#472,#471);
#474=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(15705.534251651),#452);
#475=REPRESENTATION('volume',(#474),#445);
#476=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #450 - rod',#457);
#477=PROPERTY_DEFINITION_REPRESENTATION(#476,#475);
#478=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(6440.022267471),#455);
#479=REPRESENTATION('surface area',(#478),#445);
#480=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #450 - rod',#457);
#481=PROPERTY_DEFINITION_REPRESENTATION(#480,#479);
#482=CARTESIAN_POINT('centre point',(0.0,0.0,100.));
#483=REPRESENTATION('centroid',(#482),#445);
#484=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #450 - rod',#457);
#485=PROPERTY_DEFINITION_REPRESENTATION(#484,#483);
#486=CARTESIAN_POINT('#486',(0.0,0.0,0.0));
#487=DIRECTION('#487',(1.0,0.0,0.0));
#488=DIRECTION('#488',(0.0,0.0,1.0));
#489=AXIS2_PLACEMENT_3D('#489',#486,#488,#487);
#490=ITEM_DEFINED_TRANSFORMATION('#489','rod-assembly : rod',#449,#489);
#491=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3)
)REPRESENTATION_CONTEXT('rod-assembly','3D'));
#492=CARTESIAN_POINT('#492',(0.0,0.0,0.0));
#493=DIRECTION('#493',(1.0,0.0,0.0));
#494=DIRECTION('#494',(0.0,0.0,1.0));
#495=AXIS2_PLACEMENT_3D('#495',#492,#494,#493);
#496=SHAPE_REPRESENTATION('#496',(#495,#341,#346,#489),#491);
#497=(REPRESENTATION_RELATIONSHIP('#497','rod-assembly : nut',#302,#496)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#342)
SHAPE_REPRESENTATION_RELATIONSHIP());
#498=(REPRESENTATION_RELATIONSHIP('#498','rod-assembly : nut',#302,#496)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#347)
SHAPE_REPRESENTATION_RELATIONSHIP());
#499=(REPRESENTATION_RELATIONSHIP('#499','rod-assembly : rod',#450,#496)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#490)
SHAPE_REPRESENTATION_RELATIONSHIP());
#500=DERIVED_UNIT_ELEMENT(#4,3.0);
#501=DERIVED_UNIT((#500));
#502=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#501);
#503=DERIVED_UNIT_ELEMENT(#4,2.0);
#504=DERIVED_UNIT((#503));
#505=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#504);
#507=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(17036.724276008),#501);
#508=REPRESENTATION('volume',(#507),#491);
#509=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #496 - rod-assembly',#506);
#510=PROPERTY_DEFINITION_REPRESENTATION(#509,#508);
#511=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(7934.601221856),#504);
#512=REPRESENTATION('surface area',(#511),#491);
#513=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #496 - rod-assembly',#506);
#514=PROPERTY_DEFINITION_REPRESENTATION(#513,#512);
#515=CARTESIAN_POINT('centre point',(0.0,0.0,100.));
#516=REPRESENTATION('centroid',(#515),#491);
#517=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #496 - rod-assembly',#506);
#518=PROPERTY_DEFINITION_REPRESENTATION(#517,#516);
#519=CARTESIAN_POINT('#519',(-10.,75.,60.));
#520=DIRECTION('#520',(0.0,0.0,-1.));
#521=DIRECTION('#521',(1.0,0.0,0.0));
#522=AXIS2_PLACEMENT_3D('#522',#519,#521,#520);
#523=ITEM_DEFINED_TRANSFORMATION('#522','MASTER : rod-assembly',#495,#522);
#524=CARTESIAN_POINT('#524',(7.5,0.0,3.0));
#525=VERTEX_POINT('#525',#524);
#526=CARTESIAN_POINT('#526',(-7.5,0.0,3.0));
#527=VERTEX_POINT('#527',#526);
#528=CARTESIAN_POINT('#528',(-5.,0.0,3.0));
#529=VERTEX_POINT('#529',#528);
#530=CARTESIAN_POINT('#530',(5.0,0.0,3.0));
#531=VERTEX_POINT('#531',#530);
#532=CARTESIAN_POINT('#532',(-7.5,0.0,0.0));
#533=VERTEX_POINT('#533',#532);
#534=CARTESIAN_POINT('#534',(7.5,0.0,0.0));
#535=VERTEX_POINT('#535',#534);
#536=CARTESIAN_POINT('#536',(5.0,0.0,37.));
#537=VERTEX_POINT('#537',#536);
#538=CARTESIAN_POINT('#538',(-5.,0.0,37.));
#539=VERTEX_POINT('#539',#538);
#540=CARTESIAN_POINT('#540',(7.5,0.0,3.0));
#541=CARTESIAN_POINT('#541',(7.5,-15.,3.0));
#542=CARTESIAN_POINT('#542',(-7.5,-15.,3.0));
#543=CARTESIAN_POINT('#543',(-7.5,0.0,3.0));
#544=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#540,#541,#542,#543),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#544'));
#545=CARTESIAN_POINT('#545',(-7.5,0.0,3.0));
#546=CARTESIAN_POINT('#546',(-7.5,15.,3.0));
#547=CARTESIAN_POINT('#547',(7.5,15.,3.0));
#548=CARTESIAN_POINT('#548',(7.5,0.0,3.0));
#549=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#545,#546,#547,#548),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#549'));
#550=CARTESIAN_POINT('#550',(-5.,0.0,3.0));
#551=CARTESIAN_POINT('#551',(-5.,-10.,3.0));
#552=CARTESIAN_POINT('#552',(5.0,-10.,3.0));
#553=CARTESIAN_POINT('#553',(5.0,0.0,3.0));
#554=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#550,#551,#552,#553),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#554'));
#555=CARTESIAN_POINT('#555',(5.0,0.0,3.0));
#556=CARTESIAN_POINT('#556',(5.0,10.,3.0));
#557=CARTESIAN_POINT('#557',(-5.,10.,3.0));
#558=CARTESIAN_POINT('#558',(-5.,0.0,3.0));
#559=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#555,#556,#557,#558),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#559'));
#560=CARTESIAN_POINT('#560',(-7.5,0.0,0.0));
#561=CARTESIAN_POINT('#561',(-7.5,-15.,0.0));
#562=CARTESIAN_POINT('#562',(7.5,-15.,0.0));
#563=CARTESIAN_POINT('#563',(7.5,0.0,0.0));
#564=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#560,#561,#562,#563),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#564'));
#565=CARTESIAN_POINT('#565',(7.5,0.0,0.0));
#566=CARTESIAN_POINT('#566',(7.5,15.,0.0));
#567=CARTESIAN_POINT('#567',(-7.5,15.,0.0));
#568=CARTESIAN_POINT('#568',(-7.5,0.0,0.0));
#569=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#565,#566,#567,#568),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#569'));
#570=CARTESIAN_POINT('#570',(7.5,0.0,3.0));
#571=DIRECTION('#571',(0.0,0.0,-1.));
#572=VECTOR('#572',#571,3.0);
#573=LINE('#573',#570,#572);
#574=CARTESIAN_POINT('#574',(-7.5,0.0,0.0));
#575=DIRECTION('#575',(0.0,0.0,1.0));
#576=VECTOR('#576',#575,3.0);
#577=LINE('#577',#574,#576);
#578=CARTESIAN_POINT('#578',(5.0,0.0,37.));
#579=CARTESIAN_POINT('#579',(5.0,-10.,37.));
#580=CARTESIAN_POINT('#580',(-5.,-10.,37.));
#581=CARTESIAN_POINT('#581',(-5.,0.0,37.));
#582=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#578,#579,#580,#581),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#582'));
#583=CARTESIAN_POINT('#583',(-5.,0.0,37.));
#584=CARTESIAN_POINT('#584',(-5.,10.,37.));
#585=CARTESIAN_POINT('#585',(5.0,10.,37.));
#586=CARTESIAN_POINT('#586',(5.0,0.0,37.));
#587=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#583,#584,#585,#586),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#587'));
#588=CARTESIAN_POINT('#588',(-5.,0.0,3.0));
#589=DIRECTION('#589',(0.0,0.0,1.0));
#590=VECTOR('#590',#589,34.);
#591=LINE('#591',#588,#590);
#592=CARTESIAN_POINT('#592',(5.0,0.0,37.));
#593=DIRECTION('#593',(0.0,0.0,-1.));
#594=VECTOR('#594',#593,34.);
#595=LINE('#595',#592,#594);
#596=EDGE_CURVE('#596',#525,#527,#544,.T.);
#597=EDGE_CURVE('#597',#527,#525,#549,.T.);
#598=EDGE_CURVE('#598',#529,#531,#554,.T.);
#599=EDGE_CURVE('#599',#531,#529,#559,.T.);
#600=EDGE_CURVE('#600',#533,#535,#564,.T.);
#601=EDGE_CURVE('#601',#535,#533,#569,.T.);
#602=EDGE_CURVE('#602',#525,#535,#573,.T.);
#603=EDGE_CURVE('#603',#533,#527,#577,.T.);
#604=EDGE_CURVE('#604',#537,#539,#582,.T.);
#605=EDGE_CURVE('#605',#539,#537,#587,.T.);
#606=EDGE_CURVE('#606',#529,#539,#591,.T.);
#607=EDGE_CURVE('#607',#537,#531,#595,.T.);
#608=ORIENTED_EDGE('#608',*,*,#596,.F.);
#609=ORIENTED_EDGE('#609',*,*,#597,.F.);
#610=EDGE_LOOP('#610',(#608,#609));
#611=FACE_OUTER_BOUND('#611',#610,.T.);
#612=ORIENTED_EDGE('#612',*,*,#598,.F.);
#613=ORIENTED_EDGE('#613',*,*,#599,.F.);
#614=EDGE_LOOP('#614',(#612,#613));
#615=FACE_BOUND('#615',#614,.T.);
#616=CARTESIAN_POINT('#616',(0.0,0.0,3.0));
#617=DIRECTION('#617',(0.0,0.0,1.0));
#618=DIRECTION('#618',(1.0,0.0,0.0));
#619=AXIS2_PLACEMENT_3D('#619',#616,#617,#618);
#620=PLANE('#620',#619);
#621=ADVANCED_FACE('#621',(#611,#615),#620,.T.);
#622=ORIENTED_EDGE('#622',*,*,#600,.F.);
#623=ORIENTED_EDGE('#623',*,*,#601,.F.);
#624=EDGE_LOOP('#624',(#622,#623));
#625=FACE_BOUND('#625',#624,.T.);
#626=CARTESIAN_POINT('#626',(0.0,0.0,0.0));
#627=DIRECTION('#627',(0.0,0.0,-1.));
#628=DIRECTION('#628',(-1.,0.0,0.0));
#629=AXIS2_PLACEMENT_3D('#629',#626,#627,#628);
#630=PLANE('#630',#629);
#631=ADVANCED_FACE('#631',(#625),#630,.T.);
#632=ORIENTED_EDGE('#632',*,*,#597,.T.);
#633=ORIENTED_EDGE('#633',*,*,#602,.T.);
#634=ORIENTED_EDGE('#634',*,*,#601,.T.);
#635=ORIENTED_EDGE('#635',*,*,#603,.T.);
#636=EDGE_LOOP('#636',(#632,#633,#634,#635));
#637=FACE_BOUND('#637',#636,.T.);
#638=CARTESIAN_POINT('#638',(7.5,0.0,3.0));
#639=CARTESIAN_POINT('#639',(7.5,15.,3.0));
#640=CARTESIAN_POINT('#640',(-7.5,15.,3.0));
#641=CARTESIAN_POINT('#641',(-7.5,0.0,3.0));
#642=CARTESIAN_POINT('#642',(7.5,0.0,0.0));
#643=CARTESIAN_POINT('#643',(7.5,15.,0.0));
#644=CARTESIAN_POINT('#644',(-7.5,15.,0.0));
#645=CARTESIAN_POINT('#645',(-7.5,0.0,0.0));
#646=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#638,#639,#640,#641),(#642,#643,
#644,#645)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#646')SURFACE());
#647=ADVANCED_FACE('#647',(#637),#646,.T.);
#648=ORIENTED_EDGE('#648',*,*,#604,.F.);
#649=ORIENTED_EDGE('#649',*,*,#605,.F.);
#650=EDGE_LOOP('#650',(#648,#649));
#651=FACE_BOUND('#651',#650,.T.);
#652=CARTESIAN_POINT('#652',(0.0,0.0,37.));
#653=DIRECTION('#653',(0.0,0.0,1.0));
#654=DIRECTION('#654',(1.0,0.0,0.0));
#655=AXIS2_PLACEMENT_3D('#655',#652,#653,#654);
#656=PLANE('#656',#655);
#657=ADVANCED_FACE('#657',(#651),#656,.T.);
#658=ORIENTED_EDGE('#658',*,*,#599,.T.);
#659=ORIENTED_EDGE('#659',*,*,#606,.T.);
#660=ORIENTED_EDGE('#660',*,*,#605,.T.);
#661=ORIENTED_EDGE('#661',*,*,#607,.T.);
#662=EDGE_LOOP('#662',(#658,#659,#660,#661));
#663=FACE_BOUND('#663',#662,.T.);
#664=CARTESIAN_POINT('#664',(5.0,0.0,37.));
#665=CARTESIAN_POINT('#665',(5.0,10.,37.));
#666=CARTESIAN_POINT('#666',(-5.,10.,37.));
#667=CARTESIAN_POINT('#667',(-5.,0.0,37.));
#668=CARTESIAN_POINT('#668',(5.0,0.0,3.0));
#669=CARTESIAN_POINT('#669',(5.0,10.,3.0));
#670=CARTESIAN_POINT('#670',(-5.,10.,3.0));
#671=CARTESIAN_POINT('#671',(-5.,0.0,3.0));
#672=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#664,#665,#666,#667),(#668,#669,
#670,#671)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#672')SURFACE());
#673=ADVANCED_FACE('#673',(#663),#672,.T.);
#674=ORIENTED_EDGE('#674',*,*,#596,.T.);
#675=ORIENTED_EDGE('#675',*,*,#603,.F.);
#676=ORIENTED_EDGE('#676',*,*,#600,.T.);
#677=ORIENTED_EDGE('#677',*,*,#602,.F.);
#678=EDGE_LOOP('#678',(#674,#675,#676,#677));
#679=FACE_BOUND('#679',#678,.T.);
#680=CARTESIAN_POINT('#680',(-7.5,0.0,3.0));
#681=CARTESIAN_POINT('#681',(-7.5,-15.,3.0));
#682=CARTESIAN_POINT('#682',(7.5,-15.,3.0));
#683=CARTESIAN_POINT('#683',(7.5,0.0,3.0));
#684=CARTESIAN_POINT('#684',(-7.5,0.0,0.0));
#685=CARTESIAN_POINT('#685',(-7.5,-15.,0.0));
#686=CARTESIAN_POINT('#686',(7.5,-15.,0.0));
#687=CARTESIAN_POINT('#687',(7.5,0.0,0.0));
#688=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#680,#681,#682,#683),(#684,#685,
#686,#687)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#688')SURFACE());
#689=ADVANCED_FACE('#689',(#679),#688,.T.);
#690=ORIENTED_EDGE('#690',*,*,#598,.T.);
#691=ORIENTED_EDGE('#691',*,*,#607,.F.);
#692=ORIENTED_EDGE('#692',*,*,#604,.T.);
#693=ORIENTED_EDGE('#693',*,*,#606,.F.);
#694=EDGE_LOOP('#694',(#690,#691,#692,#693));
#695=FACE_BOUND('#695',#694,.T.);
#696=CARTESIAN_POINT('#696',(-5.,0.0,37.));
#697=CARTESIAN_POINT('#697',(-5.,-10.,37.));
#698=CARTESIAN_POINT('#698',(5.0,-10.,37.));
#699=CARTESIAN_POINT('#699',(5.0,0.0,37.));
#700=CARTESIAN_POINT('#700',(-5.,0.0,3.0));
#701=CARTESIAN_POINT('#701',(-5.,-10.,3.0));
#702=CARTESIAN_POINT('#702',(5.0,-10.,3.0));
#703=CARTESIAN_POINT('#703',(5.0,0.0,3.0));
#704=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#696,#697,#698,#699),(#700,#701,
#702,#703)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#704')SURFACE());
#705=ADVANCED_FACE('#705',(#695),#704,.T.);
#706=CLOSED_SHELL('#706',(#621,#631,#647,#657,#673,#689,#705));
#707=MANIFOLD_SOLID_BREP('#707',#706);
#708=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.1E-12),#4,
'distance_accuracy_value','EDGE CURVE AND VERTEX POINT ACCURACY');
#709=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#708))GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3))REPRESENTATION_CONTEXT('bolt','3D'
));
#710=CARTESIAN_POINT('#710',(0.0,0.0,0.0));
#711=DIRECTION('#711',(1.0,0.0,0.0));
#712=DIRECTION('#712',(0.0,0.0,1.0));
#713=AXIS2_PLACEMENT_3D('#713',#710,#712,#711);
#714=ADVANCED_BREP_SHAPE_REPRESENTATION('#714',(#707,#713),#709);
#715=DERIVED_UNIT_ELEMENT(#4,3.0);
#716=DERIVED_UNIT((#715));
#717=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#716);
#718=DERIVED_UNIT_ELEMENT(#4,2.0);
#719=DERIVED_UNIT((#718));
#720=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#719);
#722=SHAPE_ASPECT('#722','solid #707',#721,.F.);
#723=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(3199.194869827)
,#716);
#724=REPRESENTATION('volume',(#723),#709);
#725=PROPERTY_DEFINITION('geometric validation property','volume of #707',#722);
#726=PROPERTY_DEFINITION_REPRESENTATION(#725,#724);
#727=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(1562.306206369),#719);
#728=REPRESENTATION('surface area',(#727),#709);
#729=PROPERTY_DEFINITION('geometric validation property','area of #707',#722);
#730=PROPERTY_DEFINITION_REPRESENTATION(#729,#728);
#731=CARTESIAN_POINT('centre point',(0.0,0.0,16.935582822));
#732=REPRESENTATION('centroid',(#731),#709);
#733=PROPERTY_DEFINITION('geometric validation property','centroid of #707',#722
);
#734=PROPERTY_DEFINITION_REPRESENTATION(#733,#732);
#735=SHAPE_REPRESENTATION('',(#707),#709);
#736=PROPERTY_DEFINITION('','Shape for Validation Properties',#722);
#737=SHAPE_DEFINITION_REPRESENTATION(#736,#735);
#738=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(3199.194869827)
,#716);
#739=REPRESENTATION('volume',(#738),#709);
#740=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #714 - bolt',#721);
#741=PROPERTY_DEFINITION_REPRESENTATION(#740,#739);
#742=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(1562.306206369),#719);
#743=REPRESENTATION('surface area',(#742),#709);
#744=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #714 - bolt',#721);
#745=PROPERTY_DEFINITION_REPRESENTATION(#744,#743);
#746=CARTESIAN_POINT('centre point',(0.0,0.0,16.935582822));
#747=REPRESENTATION('centroid',(#746),#709);
#748=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #714 - bolt',#721);
#749=PROPERTY_DEFINITION_REPRESENTATION(#748,#747);
#750=CARTESIAN_POINT('#750',(-7.5,-10.,13.));
#751=DIRECTION('#751',(0.0,-1.,0.0));
#752=DIRECTION('#752',(0.0,0.0,-1.));
#753=AXIS2_PLACEMENT_3D('#753',#750,#752,#751);
#754=ITEM_DEFINED_TRANSFORMATION('#753','nut-bolt-assembly : bolt',#713,#753);
#755=CARTESIAN_POINT('#755',(2.5,-17.5,-20.));
#756=DIRECTION('#756',(-1.,0.0,0.0));
#757=DIRECTION('#757',(0.0,0.0,-1.));
#758=AXIS2_PLACEMENT_3D('#758',#755,#757,#756);
#759=ITEM_DEFINED_TRANSFORMATION('#758','nut-bolt-assembly : nut',#301,#758);
#760=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3)
)REPRESENTATION_CONTEXT('nut-bolt-assembly','3D'));
#761=CARTESIAN_POINT('#761',(0.0,0.0,0.0));
#762=DIRECTION('#762',(1.0,0.0,0.0));
#763=DIRECTION('#763',(0.0,0.0,1.0));
#764=AXIS2_PLACEMENT_3D('#764',#761,#763,#762);
#765=SHAPE_REPRESENTATION('#765',(#764,#753,#758),#760);
#766=(REPRESENTATION_RELATIONSHIP('#766','nut-bolt-assembly : bolt',#714,#765)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#754)
SHAPE_REPRESENTATION_RELATIONSHIP());
#767=(REPRESENTATION_RELATIONSHIP('#767','nut-bolt-assembly : nut',#302,#765)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#759)
SHAPE_REPRESENTATION_RELATIONSHIP());
#768=DERIVED_UNIT_ELEMENT(#4,3.0);
#769=DERIVED_UNIT((#768));
#770=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#769);
#771=DERIVED_UNIT_ELEMENT(#4,2.0);
#772=DERIVED_UNIT((#771));
#773=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#772);
#775=MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(3864.878001149)
,#769);
#776=REPRESENTATION('volume',(#775),#760);
#777=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #765 - nut-bolt-assembly',#774);
#778=PROPERTY_DEFINITION_REPRESENTATION(#777,#776);
#779=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(2310.110489696),#772);
#780=REPRESENTATION('surface area',(#779),#760);
#781=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #765 - nut-bolt-assembly',#774);
#782=PROPERTY_DEFINITION_REPRESENTATION(#781,#780);
#783=CARTESIAN_POINT('centre point',(-7.5,-10.,-6.954942431));
#784=REPRESENTATION('centroid',(#783),#760);
#785=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #765 - nut-bolt-assembly',#774);
#786=PROPERTY_DEFINITION_REPRESENTATION(#785,#784);
#787=CARTESIAN_POINT('#787',(27.5,-40.,0.0));
#788=DIRECTION('#788',(1.0,0.0,0.0));
#789=DIRECTION('#789',(0.0,0.0,1.0));
#790=AXIS2_PLACEMENT_3D('#790',#787,#789,#788);
#791=ITEM_DEFINED_TRANSFORMATION('#790','l-bracket-assembly : nut-bolt-assembly'
,#764,#790);
#792=CARTESIAN_POINT('#792',(50.,-52.990381057,0.0));
#793=DIRECTION('#793',(1.0,0.0,0.0));
#794=DIRECTION('#794',(0.0,0.0,1.0));
#795=AXIS2_PLACEMENT_3D('#795',#792,#794,#793);
#796=ITEM_DEFINED_TRANSFORMATION('#795','l-bracket-assembly : nut-bolt-assembly'
,#764,#795);
#797=CARTESIAN_POINT('#797',(50.,-27.009618943,0.0));
#798=DIRECTION('#798',(1.0,0.0,0.0));
#799=DIRECTION('#799',(0.0,0.0,1.0));
#800=AXIS2_PLACEMENT_3D('#800',#797,#799,#798);
#801=ITEM_DEFINED_TRANSFORMATION('#800','l-bracket-assembly : nut-bolt-assembly'
,#764,#800);
#802=CARTESIAN_POINT('#802',(0.0,0.0,100.));
#803=VERTEX_POINT('#803',#802);
#804=CARTESIAN_POINT('#804',(0.0,0.0,0.0));
#805=VERTEX_POINT('#805',#804);
#806=CARTESIAN_POINT('#806',(0.0,60.,100.));
#807=VERTEX_POINT('#807',#806);
#808=CARTESIAN_POINT('#808',(0.0,60.,0.0));
#809=VERTEX_POINT('#809',#808);
#810=CARTESIAN_POINT('#810',(0.0,40.,45.));
#811=VERTEX_POINT('#811',#810);
#812=CARTESIAN_POINT('#812',(0.0,40.,55.));
#813=VERTEX_POINT('#813',#812);
#814=CARTESIAN_POINT('#814',(50.,0.0,100.));
#815=VERTEX_POINT('#815',#814);
#816=CARTESIAN_POINT('#816',(50.,0.0,0.0));
#817=VERTEX_POINT('#817',#816);
#818=CARTESIAN_POINT('#818',(42.5,0.0,42.009618943));
#819=VERTEX_POINT('#819',#818);
#820=CARTESIAN_POINT('#820',(42.5,0.0,32.009618943));
#821=VERTEX_POINT('#821',#820);
#822=CARTESIAN_POINT('#822',(42.5,0.0,67.990381057));
#823=VERTEX_POINT('#823',#822);
#824=CARTESIAN_POINT('#824',(42.5,0.0,57.990381057));
#825=VERTEX_POINT('#825',#824);
#826=CARTESIAN_POINT('#826',(20.,0.0,55.));
#827=VERTEX_POINT('#827',#826);
#828=CARTESIAN_POINT('#828',(20.,0.0,45.));
#829=VERTEX_POINT('#829',#828);
#830=CARTESIAN_POINT('#830',(50.,10.,100.));
#831=VERTEX_POINT('#831',#830);
#832=CARTESIAN_POINT('#832',(50.,10.,0.0));
#833=VERTEX_POINT('#833',#832);
#834=CARTESIAN_POINT('#834',(10.,10.,100.));
#835=VERTEX_POINT('#835',#834);
#836=CARTESIAN_POINT('#836',(10.,10.,0.0));
#837=VERTEX_POINT('#837',#836);
#838=CARTESIAN_POINT('#838',(42.5,10.,32.009618943));
#839=VERTEX_POINT('#839',#838);
#840=CARTESIAN_POINT('#840',(42.5,10.,42.009618943));
#841=VERTEX_POINT('#841',#840);
#842=CARTESIAN_POINT('#842',(42.5,10.,57.990381057));
#843=VERTEX_POINT('#843',#842);
#844=CARTESIAN_POINT('#844',(42.5,10.,67.990381057));
#845=VERTEX_POINT('#845',#844);
#846=CARTESIAN_POINT('#846',(20.,10.,45.));
#847=VERTEX_POINT('#847',#846);
#848=CARTESIAN_POINT('#848',(20.,10.,55.));
#849=VERTEX_POINT('#849',#848);
#850=CARTESIAN_POINT('#850',(10.,60.,100.));
#851=VERTEX_POINT('#851',#850);
#852=CARTESIAN_POINT('#852',(10.,60.,0.0));
#853=VERTEX_POINT('#853',#852);
#854=CARTESIAN_POINT('#854',(10.,40.,55.));
#855=VERTEX_POINT('#855',#854);
#856=CARTESIAN_POINT('#856',(10.,40.,45.));
#857=VERTEX_POINT('#857',#856);
#858=CARTESIAN_POINT('#858',(0.0,0.0,100.));
#859=DIRECTION('#859',(0.0,0.0,-1.));
#860=VECTOR('#860',#859,100.);
#861=LINE('#861',#858,#860);
#862=CARTESIAN_POINT('#862',(0.0,60.,100.));
#863=DIRECTION('#863',(0.0,-1.,0.0));
#864=VECTOR('#864',#863,60.);
#865=LINE('#865',#862,#864);
#866=CARTESIAN_POINT('#866',(0.0,60.,100.));
#867=DIRECTION('#867',(0.0,0.0,-1.));
#868=VECTOR('#868',#867,100.);
#869=LINE('#869',#866,#868);
#870=CARTESIAN_POINT('#870',(0.0,60.,0.0));
#871=DIRECTION('#871',(0.0,-1.,0.0));
#872=VECTOR('#872',#871,60.);
#873=LINE('#873',#870,#872);
#874=CARTESIAN_POINT('#874',(0.0,40.,45.));
#875=CARTESIAN_POINT('#875',(0.0,50.,45.));
#876=CARTESIAN_POINT('#876',(0.0,50.,55.));
#877=CARTESIAN_POINT('#877',(0.0,40.,55.));
#878=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#874,#875,#876,#877),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#878'));
#879=CARTESIAN_POINT('#879',(0.0,40.,55.));
#880=CARTESIAN_POINT('#880',(0.0,30.,55.));
#881=CARTESIAN_POINT('#881',(0.0,30.,45.));
#882=CARTESIAN_POINT('#882',(0.0,40.,45.));
#883=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#879,#880,#881,#882),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#883'));
#884=CARTESIAN_POINT('#884',(50.,0.0,100.));
#885=DIRECTION('#885',(0.0,0.0,-1.));
#886=VECTOR('#886',#885,100.);
#887=LINE('#887',#884,#886);
#888=CARTESIAN_POINT('#888',(0.0,0.0,100.));
#889=DIRECTION('#889',(1.0,0.0,0.0));
#890=VECTOR('#890',#889,50.);
#891=LINE('#891',#888,#890);
#892=CARTESIAN_POINT('#892',(0.0,0.0,0.0));
#893=DIRECTION('#893',(1.0,0.0,0.0));
#894=VECTOR('#894',#893,50.);
#895=LINE('#895',#892,#894);
#896=CARTESIAN_POINT('#896',(42.5,0.0,42.009618943));
#897=CARTESIAN_POINT('#897',(52.5,0.0,42.009618943));
#898=CARTESIAN_POINT('#898',(52.5,0.0,32.009618943));
#899=CARTESIAN_POINT('#899',(42.5,0.0,32.009618943));
#900=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#896,#897,#898,#899),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#900'));
#901=CARTESIAN_POINT('#901',(42.5,0.0,32.009618943));
#902=CARTESIAN_POINT('#902',(32.5,0.0,32.009618943));
#903=CARTESIAN_POINT('#903',(32.5,0.0,42.009618943));
#904=CARTESIAN_POINT('#904',(42.5,0.0,42.009618943));
#905=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#901,#902,#903,#904),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#905'));
#906=CARTESIAN_POINT('#906',(42.5,0.0,67.990381057));
#907=CARTESIAN_POINT('#907',(52.5,0.0,67.990381057));
#908=CARTESIAN_POINT('#908',(52.5,0.0,57.990381057));
#909=CARTESIAN_POINT('#909',(42.5,0.0,57.990381057));
#910=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#906,#907,#908,#909),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#910'));
#911=CARTESIAN_POINT('#911',(42.5,0.0,57.990381057));
#912=CARTESIAN_POINT('#912',(32.5,0.0,57.990381057));
#913=CARTESIAN_POINT('#913',(32.5,0.0,67.990381057));
#914=CARTESIAN_POINT('#914',(42.5,0.0,67.990381057));
#915=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#911,#912,#913,#914),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#915'));
#916=CARTESIAN_POINT('#916',(20.,0.0,55.));
#917=CARTESIAN_POINT('#917',(30.,0.0,55.));
#918=CARTESIAN_POINT('#918',(30.,0.0,45.));
#919=CARTESIAN_POINT('#919',(20.,0.0,45.));
#920=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#916,#917,#918,#919),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#920'));
#921=CARTESIAN_POINT('#921',(20.,0.0,45.));
#922=CARTESIAN_POINT('#922',(10.,0.0,45.));
#923=CARTESIAN_POINT('#923',(10.,0.0,55.));
#924=CARTESIAN_POINT('#924',(20.,0.0,55.));
#925=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#921,#922,#923,#924),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#925'));
#926=CARTESIAN_POINT('#926',(50.,10.,100.));
#927=DIRECTION('#927',(0.0,0.0,-1.));
#928=VECTOR('#928',#927,100.);
#929=LINE('#929',#926,#928);
#930=CARTESIAN_POINT('#930',(50.,0.0,100.));
#931=DIRECTION('#931',(0.0,1.0,0.0));
#932=VECTOR('#932',#931,10.);
#933=LINE('#933',#930,#932);
#934=CARTESIAN_POINT('#934',(50.,0.0,0.0));
#935=DIRECTION('#935',(0.0,1.0,0.0));
#936=VECTOR('#936',#935,10.);
#937=LINE('#937',#934,#936);
#938=CARTESIAN_POINT('#938',(10.,10.,100.));
#939=DIRECTION('#939',(0.0,0.0,-1.));
#940=VECTOR('#940',#939,100.);
#941=LINE('#941',#938,#940);
#942=CARTESIAN_POINT('#942',(50.,10.,100.));
#943=DIRECTION('#943',(-1.,0.0,0.0));
#944=VECTOR('#944',#943,40.);
#945=LINE('#945',#942,#944);
#946=CARTESIAN_POINT('#946',(50.,10.,0.0));
#947=DIRECTION('#947',(-1.,0.0,0.0));
#948=VECTOR('#948',#947,40.);
#949=LINE('#949',#946,#948);
#950=CARTESIAN_POINT('#950',(42.5,10.,32.009618943));
#951=CARTESIAN_POINT('#951',(52.5,10.,32.009618943));
#952=CARTESIAN_POINT('#952',(52.5,10.,42.009618943));
#953=CARTESIAN_POINT('#953',(42.5,10.,42.009618943));
#954=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#950,#951,#952,#953),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#954'));
#955=CARTESIAN_POINT('#955',(42.5,10.,42.009618943));
#956=CARTESIAN_POINT('#956',(32.5,10.,42.009618943));
#957=CARTESIAN_POINT('#957',(32.5,10.,32.009618943));
#958=CARTESIAN_POINT('#958',(42.5,10.,32.009618943));
#959=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#955,#956,#957,#958),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#959'));
#960=CARTESIAN_POINT('#960',(42.5,10.,57.990381057));
#961=CARTESIAN_POINT('#961',(52.5,10.,57.990381057));
#962=CARTESIAN_POINT('#962',(52.5,10.,67.990381057));
#963=CARTESIAN_POINT('#963',(42.5,10.,67.990381057));
#964=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#960,#961,#962,#963),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#964'));
#965=CARTESIAN_POINT('#965',(42.5,10.,67.990381057));
#966=CARTESIAN_POINT('#966',(32.5,10.,67.990381057));
#967=CARTESIAN_POINT('#967',(32.5,10.,57.990381057));
#968=CARTESIAN_POINT('#968',(42.5,10.,57.990381057));
#969=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#965,#966,#967,#968),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#969'));
#970=CARTESIAN_POINT('#970',(20.,10.,45.));
#971=CARTESIAN_POINT('#971',(30.,10.,45.));
#972=CARTESIAN_POINT('#972',(30.,10.,55.));
#973=CARTESIAN_POINT('#973',(20.,10.,55.));
#974=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#970,#971,#972,#973),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#974'));
#975=CARTESIAN_POINT('#975',(20.,10.,55.));
#976=CARTESIAN_POINT('#976',(10.,10.,55.));
#977=CARTESIAN_POINT('#977',(10.,10.,45.));
#978=CARTESIAN_POINT('#978',(20.,10.,45.));
#979=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#975,#976,#977,#978),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#979'));
#980=CARTESIAN_POINT('#980',(10.,60.,100.));
#981=DIRECTION('#981',(0.0,0.0,-1.));
#982=VECTOR('#982',#981,100.);
#983=LINE('#983',#980,#982);
#984=CARTESIAN_POINT('#984',(10.,10.,100.));
#985=DIRECTION('#985',(0.0,1.0,0.0));
#986=VECTOR('#986',#985,50.);
#987=LINE('#987',#984,#986);
#988=CARTESIAN_POINT('#988',(10.,10.,0.0));
#989=DIRECTION('#989',(0.0,1.0,0.0));
#990=VECTOR('#990',#989,50.);
#991=LINE('#991',#988,#990);
#992=CARTESIAN_POINT('#992',(10.,40.,55.));
#993=CARTESIAN_POINT('#993',(10.,50.,55.));
#994=CARTESIAN_POINT('#994',(10.,50.,45.));
#995=CARTESIAN_POINT('#995',(10.,40.,45.));
#996=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#992,#993,#994,#995),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#996'));
#997=CARTESIAN_POINT('#997',(10.,40.,45.));
#998=CARTESIAN_POINT('#998',(10.,30.,45.));
#999=CARTESIAN_POINT('#999',(10.,30.,55.));
#1000=CARTESIAN_POINT('#1000',(10.,40.,55.));
#1001=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#997,#998,#999,#1000),.UNSPECIFIED.,.F.,
.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1001'));
#1002=CARTESIAN_POINT('#1002',(10.,60.,100.));
#1003=DIRECTION('#1003',(-1.,0.0,0.0));
#1004=VECTOR('#1004',#1003,10.);
#1005=LINE('#1005',#1002,#1004);
#1006=CARTESIAN_POINT('#1006',(10.,60.,0.0));
#1007=DIRECTION('#1007',(-1.,0.0,0.0));
#1008=VECTOR('#1008',#1007,10.);
#1009=LINE('#1009',#1006,#1008);
#1010=CARTESIAN_POINT('#1010',(10.,40.,45.));
#1011=DIRECTION('#1011',(-1.,0.0,0.0));
#1012=VECTOR('#1012',#1011,10.);
#1013=LINE('#1013',#1010,#1012);
#1014=CARTESIAN_POINT('#1014',(0.0,40.,55.));
#1015=DIRECTION('#1015',(1.0,0.0,0.0));
#1016=VECTOR('#1016',#1015,10.);
#1017=LINE('#1017',#1014,#1016);
#1018=CARTESIAN_POINT('#1018',(20.,10.,55.));
#1019=DIRECTION('#1019',(0.0,-1.,0.0));
#1020=VECTOR('#1020',#1019,10.);
#1021=LINE('#1021',#1018,#1020);
#1022=CARTESIAN_POINT('#1022',(20.,0.0,45.));
#1023=DIRECTION('#1023',(0.0,1.0,0.0));
#1024=VECTOR('#1024',#1023,10.);
#1025=LINE('#1025',#1022,#1024);
#1026=CARTESIAN_POINT('#1026',(42.5,10.,67.990381057));
#1027=DIRECTION('#1027',(0.0,-1.,0.0));
#1028=VECTOR('#1028',#1027,10.);
#1029=LINE('#1029',#1026,#1028);
#1030=CARTESIAN_POINT('#1030',(42.5,0.0,57.990381057));
#1031=DIRECTION('#1031',(0.0,1.0,0.0));
#1032=VECTOR('#1032',#1031,10.);
#1033=LINE('#1033',#1030,#1032);
#1034=CARTESIAN_POINT('#1034',(42.5,10.,42.009618943));
#1035=DIRECTION('#1035',(0.0,-1.,0.0));
#1036=VECTOR('#1036',#1035,10.);
#1037=LINE('#1037',#1034,#1036);
#1038=CARTESIAN_POINT('#1038',(42.5,0.0,32.009618943));
#1039=DIRECTION('#1039',(0.0,1.0,0.0));
#1040=VECTOR('#1040',#1039,10.);
#1041=LINE('#1041',#1038,#1040);
#1042=EDGE_CURVE('#1042',#803,#805,#861,.T.);
#1043=EDGE_CURVE('#1043',#807,#803,#865,.T.);
#1044=EDGE_CURVE('#1044',#807,#809,#869,.T.);
#1045=EDGE_CURVE('#1045',#809,#805,#873,.T.);
#1046=EDGE_CURVE('#1046',#811,#813,#878,.T.);
#1047=EDGE_CURVE('#1047',#813,#811,#883,.T.);
#1048=EDGE_CURVE('#1048',#815,#817,#887,.T.);
#1049=EDGE_CURVE('#1049',#803,#815,#891,.T.);
#1050=EDGE_CURVE('#1050',#805,#817,#895,.T.);
#1051=EDGE_CURVE('#1051',#819,#821,#900,.T.);
#1052=EDGE_CURVE('#1052',#821,#819,#905,.T.);
#1053=EDGE_CURVE('#1053',#823,#825,#910,.T.);
#1054=EDGE_CURVE('#1054',#825,#823,#915,.T.);
#1055=EDGE_CURVE('#1055',#827,#829,#920,.T.);
#1056=EDGE_CURVE('#1056',#829,#827,#925,.T.);
#1057=EDGE_CURVE('#1057',#831,#833,#929,.T.);
#1058=EDGE_CURVE('#1058',#815,#831,#933,.T.);
#1059=EDGE_CURVE('#1059',#817,#833,#937,.T.);
#1060=EDGE_CURVE('#1060',#835,#837,#941,.T.);
#1061=EDGE_CURVE('#1061',#831,#835,#945,.T.);
#1062=EDGE_CURVE('#1062',#833,#837,#949,.T.);
#1063=EDGE_CURVE('#1063',#839,#841,#954,.T.);
#1064=EDGE_CURVE('#1064',#841,#839,#959,.T.);
#1065=EDGE_CURVE('#1065',#843,#845,#964,.T.);
#1066=EDGE_CURVE('#1066',#845,#843,#969,.T.);
#1067=EDGE_CURVE('#1067',#847,#849,#974,.T.);
#1068=EDGE_CURVE('#1068',#849,#847,#979,.T.);
#1069=EDGE_CURVE('#1069',#851,#853,#983,.T.);
#1070=EDGE_CURVE('#1070',#835,#851,#987,.T.);
#1071=EDGE_CURVE('#1071',#837,#853,#991,.T.);
#1072=EDGE_CURVE('#1072',#855,#857,#996,.T.);
#1073=EDGE_CURVE('#1073',#857,#855,#1001,.T.);
#1074=EDGE_CURVE('#1074',#851,#807,#1005,.T.);
#1075=EDGE_CURVE('#1075',#853,#809,#1009,.T.);
#1076=EDGE_CURVE('#1076',#857,#811,#1013,.T.);
#1077=EDGE_CURVE('#1077',#813,#855,#1017,.T.);
#1078=EDGE_CURVE('#1078',#849,#827,#1021,.T.);
#1079=EDGE_CURVE('#1079',#829,#847,#1025,.T.);
#1080=EDGE_CURVE('#1080',#845,#823,#1029,.T.);
#1081=EDGE_CURVE('#1081',#825,#843,#1033,.T.);
#1082=EDGE_CURVE('#1082',#841,#819,#1037,.T.);
#1083=EDGE_CURVE('#1083',#821,#839,#1041,.T.);
#1084=ORIENTED_EDGE('#1084',*,*,#1042,.F.);
#1085=ORIENTED_EDGE('#1085',*,*,#1043,.F.);
#1086=ORIENTED_EDGE('#1086',*,*,#1044,.T.);
#1087=ORIENTED_EDGE('#1087',*,*,#1045,.T.);
#1088=EDGE_LOOP('#1088',(#1084,#1085,#1086,#1087));
#1089=FACE_OUTER_BOUND('#1089',#1088,.T.);
#1090=ORIENTED_EDGE('#1090',*,*,#1046,.T.);
#1091=ORIENTED_EDGE('#1091',*,*,#1047,.T.);
#1092=EDGE_LOOP('#1092',(#1090,#1091));
#1093=FACE_BOUND('#1093',#1092,.T.);
#1094=CARTESIAN_POINT('#1094',(0.0,60.,100.));
#1095=DIRECTION('#1095',(-1.,0.0,0.0));
#1096=DIRECTION('#1096',(0.0,0.0,1.0));
#1097=AXIS2_PLACEMENT_3D('#1097',#1094,#1095,#1096);
#1098=PLANE('#1098',#1097);
#1099=ADVANCED_FACE('#1099',(#1089,#1093),#1098,.T.);
#1100=ORIENTED_EDGE('#1100',*,*,#1048,.F.);
#1101=ORIENTED_EDGE('#1101',*,*,#1049,.F.);
#1102=ORIENTED_EDGE('#1102',*,*,#1042,.T.);
#1103=ORIENTED_EDGE('#1103',*,*,#1050,.T.);
#1104=EDGE_LOOP('#1104',(#1100,#1101,#1102,#1103));
#1105=FACE_OUTER_BOUND('#1105',#1104,.T.);
#1106=ORIENTED_EDGE('#1106',*,*,#1051,.T.);
#1107=ORIENTED_EDGE('#1107',*,*,#1052,.T.);
#1108=EDGE_LOOP('#1108',(#1106,#1107));
#1109=FACE_BOUND('#1109',#1108,.T.);
#1110=ORIENTED_EDGE('#1110',*,*,#1053,.T.);
#1111=ORIENTED_EDGE('#1111',*,*,#1054,.T.);
#1112=EDGE_LOOP('#1112',(#1110,#1111));
#1113=FACE_BOUND('#1113',#1112,.T.);
#1114=ORIENTED_EDGE('#1114',*,*,#1055,.T.);
#1115=ORIENTED_EDGE('#1115',*,*,#1056,.T.);
#1116=EDGE_LOOP('#1116',(#1114,#1115));
#1117=FACE_BOUND('#1117',#1116,.T.);
#1118=CARTESIAN_POINT('#1118',(0.0,0.0,100.));
#1119=DIRECTION('#1119',(0.0,-1.,0.0));
#1120=DIRECTION('#1120',(0.0,0.0,-1.));
#1121=AXIS2_PLACEMENT_3D('#1121',#1118,#1119,#1120);
#1122=PLANE('#1122',#1121);
#1123=ADVANCED_FACE('#1123',(#1105,#1109,#1113,#1117),#1122,.T.);
#1124=ORIENTED_EDGE('#1124',*,*,#1057,.F.);
#1125=ORIENTED_EDGE('#1125',*,*,#1058,.F.);
#1126=ORIENTED_EDGE('#1126',*,*,#1048,.T.);
#1127=ORIENTED_EDGE('#1127',*,*,#1059,.T.);
#1128=EDGE_LOOP('#1128',(#1124,#1125,#1126,#1127));
#1129=FACE_BOUND('#1129',#1128,.T.);
#1130=CARTESIAN_POINT('#1130',(50.,0.0,100.));
#1131=DIRECTION('#1131',(1.0,0.0,0.0));
#1132=DIRECTION('#1132',(0.0,0.0,-1.));
#1133=AXIS2_PLACEMENT_3D('#1133',#1130,#1131,#1132);
#1134=PLANE('#1134',#1133);
#1135=ADVANCED_FACE('#1135',(#1129),#1134,.T.);
#1136=ORIENTED_EDGE('#1136',*,*,#1060,.F.);
#1137=ORIENTED_EDGE('#1137',*,*,#1061,.F.);
#1138=ORIENTED_EDGE('#1138',*,*,#1057,.T.);
#1139=ORIENTED_EDGE('#1139',*,*,#1062,.T.);
#1140=EDGE_LOOP('#1140',(#1136,#1137,#1138,#1139));
#1141=FACE_OUTER_BOUND('#1141',#1140,.T.);
#1142=ORIENTED_EDGE('#1142',*,*,#1063,.T.);
#1143=ORIENTED_EDGE('#1143',*,*,#1064,.T.);
#1144=EDGE_LOOP('#1144',(#1142,#1143));
#1145=FACE_BOUND('#1145',#1144,.T.);
#1146=ORIENTED_EDGE('#1146',*,*,#1065,.T.);
#1147=ORIENTED_EDGE('#1147',*,*,#1066,.T.);
#1148=EDGE_LOOP('#1148',(#1146,#1147));
#1149=FACE_BOUND('#1149',#1148,.T.);
#1150=ORIENTED_EDGE('#1150',*,*,#1067,.T.);
#1151=ORIENTED_EDGE('#1151',*,*,#1068,.T.);
#1152=EDGE_LOOP('#1152',(#1150,#1151));
#1153=FACE_BOUND('#1153',#1152,.T.);
#1154=CARTESIAN_POINT('#1154',(50.,10.,100.));
#1155=DIRECTION('#1155',(0.0,1.0,0.0));
#1156=DIRECTION('#1156',(0.0,0.0,1.0));
#1157=AXIS2_PLACEMENT_3D('#1157',#1154,#1155,#1156);
#1158=PLANE('#1158',#1157);
#1159=ADVANCED_FACE('#1159',(#1141,#1145,#1149,#1153),#1158,.T.);
#1160=ORIENTED_EDGE('#1160',*,*,#1069,.F.);
#1161=ORIENTED_EDGE('#1161',*,*,#1070,.F.);
#1162=ORIENTED_EDGE('#1162',*,*,#1060,.T.);
#1163=ORIENTED_EDGE('#1163',*,*,#1071,.T.);
#1164=EDGE_LOOP('#1164',(#1160,#1161,#1162,#1163));
#1165=FACE_OUTER_BOUND('#1165',#1164,.T.);
#1166=ORIENTED_EDGE('#1166',*,*,#1072,.T.);
#1167=ORIENTED_EDGE('#1167',*,*,#1073,.T.);
#1168=EDGE_LOOP('#1168',(#1166,#1167));
#1169=FACE_BOUND('#1169',#1168,.T.);
#1170=CARTESIAN_POINT('#1170',(10.,10.,100.));
#1171=DIRECTION('#1171',(1.0,0.0,0.0));
#1172=DIRECTION('#1172',(0.0,0.0,-1.));
#1173=AXIS2_PLACEMENT_3D('#1173',#1170,#1171,#1172);
#1174=PLANE('#1174',#1173);
#1175=ADVANCED_FACE('#1175',(#1165,#1169),#1174,.T.);
#1176=ORIENTED_EDGE('#1176',*,*,#1074,.F.);
#1177=ORIENTED_EDGE('#1177',*,*,#1069,.T.);
#1178=ORIENTED_EDGE('#1178',*,*,#1075,.T.);
#1179=ORIENTED_EDGE('#1179',*,*,#1044,.F.);
#1180=EDGE_LOOP('#1180',(#1176,#1177,#1178,#1179));
#1181=FACE_BOUND('#1181',#1180,.T.);
#1182=CARTESIAN_POINT('#1182',(10.,60.,100.));
#1183=DIRECTION('#1183',(0.0,1.0,0.0));
#1184=DIRECTION('#1184',(0.0,0.0,1.0));
#1185=AXIS2_PLACEMENT_3D('#1185',#1182,#1183,#1184);
#1186=PLANE('#1186',#1185);
#1187=ADVANCED_FACE('#1187',(#1181),#1186,.T.);
#1188=ORIENTED_EDGE('#1188',*,*,#1043,.T.);
#1189=ORIENTED_EDGE('#1189',*,*,#1049,.T.);
#1190=ORIENTED_EDGE('#1190',*,*,#1058,.T.);
#1191=ORIENTED_EDGE('#1191',*,*,#1061,.T.);
#1192=ORIENTED_EDGE('#1192',*,*,#1070,.T.);
#1193=ORIENTED_EDGE('#1193',*,*,#1074,.T.);
#1194=EDGE_LOOP('#1194',(#1188,#1189,#1190,#1191,#1192,#1193));
#1195=FACE_BOUND('#1195',#1194,.T.);
#1196=CARTESIAN_POINT('#1196',(0.0,0.0,100.));
#1197=DIRECTION('#1197',(0.0,0.0,1.0));
#1198=DIRECTION('#1198',(1.0,0.0,0.0));
#1199=AXIS2_PLACEMENT_3D('#1199',#1196,#1197,#1198);
#1200=PLANE('#1200',#1199);
#1201=ADVANCED_FACE('#1201',(#1195),#1200,.T.);
#1202=ORIENTED_EDGE('#1202',*,*,#1045,.F.);
#1203=ORIENTED_EDGE('#1203',*,*,#1075,.F.);
#1204=ORIENTED_EDGE('#1204',*,*,#1071,.F.);
#1205=ORIENTED_EDGE('#1205',*,*,#1062,.F.);
#1206=ORIENTED_EDGE('#1206',*,*,#1059,.F.);
#1207=ORIENTED_EDGE('#1207',*,*,#1050,.F.);
#1208=EDGE_LOOP('#1208',(#1202,#1203,#1204,#1205,#1206,#1207));
#1209=FACE_BOUND('#1209',#1208,.T.);
#1210=CARTESIAN_POINT('#1210',(0.0,0.0,0.0));
#1211=DIRECTION('#1211',(0.0,0.0,-1.));
#1212=DIRECTION('#1212',(-1.,0.0,0.0));
#1213=AXIS2_PLACEMENT_3D('#1213',#1210,#1211,#1212);
#1214=PLANE('#1214',#1213);
#1215=ADVANCED_FACE('#1215',(#1209),#1214,.T.);
#1216=ORIENTED_EDGE('#1216',*,*,#1073,.F.);
#1217=ORIENTED_EDGE('#1217',*,*,#1076,.T.);
#1218=ORIENTED_EDGE('#1218',*,*,#1047,.F.);
#1219=ORIENTED_EDGE('#1219',*,*,#1077,.T.);
#1220=EDGE_LOOP('#1220',(#1216,#1217,#1218,#1219));
#1221=FACE_BOUND('#1221',#1220,.T.);
#1222=CARTESIAN_POINT('#1222',(10.,40.,45.));
#1223=CARTESIAN_POINT('#1223',(10.,30.,45.));
#1224=CARTESIAN_POINT('#1224',(10.,30.,55.));
#1225=CARTESIAN_POINT('#1225',(10.,40.,55.));
#1226=CARTESIAN_POINT('#1226',(0.0,40.,45.));
#1227=CARTESIAN_POINT('#1227',(0.0,30.,45.));
#1228=CARTESIAN_POINT('#1228',(0.0,30.,55.));
#1229=CARTESIAN_POINT('#1229',(0.0,40.,55.));
#1230=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1222,#1223,#1224,#1225),(#1226,
#1227,#1228,#1229)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1230')SURFACE());
#1231=ADVANCED_FACE('#1231',(#1221),#1230,.T.);
#1232=ORIENTED_EDGE('#1232',*,*,#1068,.F.);
#1233=ORIENTED_EDGE('#1233',*,*,#1078,.T.);
#1234=ORIENTED_EDGE('#1234',*,*,#1056,.F.);
#1235=ORIENTED_EDGE('#1235',*,*,#1079,.T.);
#1236=EDGE_LOOP('#1236',(#1232,#1233,#1234,#1235));
#1237=FACE_BOUND('#1237',#1236,.T.);
#1238=CARTESIAN_POINT('#1238',(20.,10.,55.));
#1239=CARTESIAN_POINT('#1239',(10.,10.,55.));
#1240=CARTESIAN_POINT('#1240',(10.,10.,45.));
#1241=CARTESIAN_POINT('#1241',(20.,10.,45.));
#1242=CARTESIAN_POINT('#1242',(20.,0.0,55.));
#1243=CARTESIAN_POINT('#1243',(10.,0.0,55.));
#1244=CARTESIAN_POINT('#1244',(10.,0.0,45.));
#1245=CARTESIAN_POINT('#1245',(20.,0.0,45.));
#1246=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1238,#1239,#1240,#1241),(#1242,
#1243,#1244,#1245)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1246')SURFACE());
#1247=ADVANCED_FACE('#1247',(#1237),#1246,.T.);
#1248=ORIENTED_EDGE('#1248',*,*,#1066,.F.);
#1249=ORIENTED_EDGE('#1249',*,*,#1080,.T.);
#1250=ORIENTED_EDGE('#1250',*,*,#1054,.F.);
#1251=ORIENTED_EDGE('#1251',*,*,#1081,.T.);
#1252=EDGE_LOOP('#1252',(#1248,#1249,#1250,#1251));
#1253=FACE_BOUND('#1253',#1252,.T.);
#1254=CARTESIAN_POINT('#1254',(42.5,10.,67.990381057));
#1255=CARTESIAN_POINT('#1255',(32.5,10.,67.990381057));
#1256=CARTESIAN_POINT('#1256',(32.5,10.,57.990381057));
#1257=CARTESIAN_POINT('#1257',(42.5,10.,57.990381057));
#1258=CARTESIAN_POINT('#1258',(42.5,0.0,67.990381057));
#1259=CARTESIAN_POINT('#1259',(32.5,0.0,67.990381057));
#1260=CARTESIAN_POINT('#1260',(32.5,0.0,57.990381057));
#1261=CARTESIAN_POINT('#1261',(42.5,0.0,57.990381057));
#1262=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1254,#1255,#1256,#1257),(#1258,
#1259,#1260,#1261)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1262')SURFACE());
#1263=ADVANCED_FACE('#1263',(#1253),#1262,.T.);
#1264=ORIENTED_EDGE('#1264',*,*,#1064,.F.);
#1265=ORIENTED_EDGE('#1265',*,*,#1082,.T.);
#1266=ORIENTED_EDGE('#1266',*,*,#1052,.F.);
#1267=ORIENTED_EDGE('#1267',*,*,#1083,.T.);
#1268=EDGE_LOOP('#1268',(#1264,#1265,#1266,#1267));
#1269=FACE_BOUND('#1269',#1268,.T.);
#1270=CARTESIAN_POINT('#1270',(42.5,10.,42.009618943));
#1271=CARTESIAN_POINT('#1271',(32.5,10.,42.009618943));
#1272=CARTESIAN_POINT('#1272',(32.5,10.,32.009618943));
#1273=CARTESIAN_POINT('#1273',(42.5,10.,32.009618943));
#1274=CARTESIAN_POINT('#1274',(42.5,0.0,42.009618943));
#1275=CARTESIAN_POINT('#1275',(32.5,0.0,42.009618943));
#1276=CARTESIAN_POINT('#1276',(32.5,0.0,32.009618943));
#1277=CARTESIAN_POINT('#1277',(42.5,0.0,32.009618943));
#1278=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1270,#1271,#1272,#1273),(#1274,
#1275,#1276,#1277)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1278')SURFACE());
#1279=ADVANCED_FACE('#1279',(#1269),#1278,.T.);
#1280=ORIENTED_EDGE('#1280',*,*,#1072,.F.);
#1281=ORIENTED_EDGE('#1281',*,*,#1077,.F.);
#1282=ORIENTED_EDGE('#1282',*,*,#1046,.F.);
#1283=ORIENTED_EDGE('#1283',*,*,#1076,.F.);
#1284=EDGE_LOOP('#1284',(#1280,#1281,#1282,#1283));
#1285=FACE_BOUND('#1285',#1284,.T.);
#1286=CARTESIAN_POINT('#1286',(10.,40.,55.));
#1287=CARTESIAN_POINT('#1287',(10.,50.,55.));
#1288=CARTESIAN_POINT('#1288',(10.,50.,45.));
#1289=CARTESIAN_POINT('#1289',(10.,40.,45.));
#1290=CARTESIAN_POINT('#1290',(0.0,40.,55.));
#1291=CARTESIAN_POINT('#1291',(0.0,50.,55.));
#1292=CARTESIAN_POINT('#1292',(0.0,50.,45.));
#1293=CARTESIAN_POINT('#1293',(0.0,40.,45.));
#1294=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1286,#1287,#1288,#1289),(#1290,
#1291,#1292,#1293)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1294')SURFACE());
#1295=ADVANCED_FACE('#1295',(#1285),#1294,.T.);
#1296=ORIENTED_EDGE('#1296',*,*,#1067,.F.);
#1297=ORIENTED_EDGE('#1297',*,*,#1079,.F.);
#1298=ORIENTED_EDGE('#1298',*,*,#1055,.F.);
#1299=ORIENTED_EDGE('#1299',*,*,#1078,.F.);
#1300=EDGE_LOOP('#1300',(#1296,#1297,#1298,#1299));
#1301=FACE_BOUND('#1301',#1300,.T.);
#1302=CARTESIAN_POINT('#1302',(20.,10.,45.));
#1303=CARTESIAN_POINT('#1303',(30.,10.,45.));
#1304=CARTESIAN_POINT('#1304',(30.,10.,55.));
#1305=CARTESIAN_POINT('#1305',(20.,10.,55.));
#1306=CARTESIAN_POINT('#1306',(20.,0.0,45.));
#1307=CARTESIAN_POINT('#1307',(30.,0.0,45.));
#1308=CARTESIAN_POINT('#1308',(30.,0.0,55.));
#1309=CARTESIAN_POINT('#1309',(20.,0.0,55.));
#1310=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1302,#1303,#1304,#1305),(#1306,
#1307,#1308,#1309)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1310')SURFACE());
#1311=ADVANCED_FACE('#1311',(#1301),#1310,.T.);
#1312=ORIENTED_EDGE('#1312',*,*,#1065,.F.);
#1313=ORIENTED_EDGE('#1313',*,*,#1081,.F.);
#1314=ORIENTED_EDGE('#1314',*,*,#1053,.F.);
#1315=ORIENTED_EDGE('#1315',*,*,#1080,.F.);
#1316=EDGE_LOOP('#1316',(#1312,#1313,#1314,#1315));
#1317=FACE_BOUND('#1317',#1316,.T.);
#1318=CARTESIAN_POINT('#1318',(42.5,10.,57.990381057));
#1319=CARTESIAN_POINT('#1319',(52.5,10.,57.990381057));
#1320=CARTESIAN_POINT('#1320',(52.5,10.,67.990381057));
#1321=CARTESIAN_POINT('#1321',(42.5,10.,67.990381057));
#1322=CARTESIAN_POINT('#1322',(42.5,0.0,57.990381057));
#1323=CARTESIAN_POINT('#1323',(52.5,0.0,57.990381057));
#1324=CARTESIAN_POINT('#1324',(52.5,0.0,67.990381057));
#1325=CARTESIAN_POINT('#1325',(42.5,0.0,67.990381057));
#1326=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1318,#1319,#1320,#1321),(#1322,
#1323,#1324,#1325)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1326')SURFACE());
#1327=ADVANCED_FACE('#1327',(#1317),#1326,.T.);
#1328=ORIENTED_EDGE('#1328',*,*,#1063,.F.);
#1329=ORIENTED_EDGE('#1329',*,*,#1083,.F.);
#1330=ORIENTED_EDGE('#1330',*,*,#1051,.F.);
#1331=ORIENTED_EDGE('#1331',*,*,#1082,.F.);
#1332=EDGE_LOOP('#1332',(#1328,#1329,#1330,#1331));
#1333=FACE_BOUND('#1333',#1332,.T.);
#1334=CARTESIAN_POINT('#1334',(42.5,10.,32.009618943));
#1335=CARTESIAN_POINT('#1335',(52.5,10.,32.009618943));
#1336=CARTESIAN_POINT('#1336',(52.5,10.,42.009618943));
#1337=CARTESIAN_POINT('#1337',(42.5,10.,42.009618943));
#1338=CARTESIAN_POINT('#1338',(42.5,0.0,32.009618943));
#1339=CARTESIAN_POINT('#1339',(52.5,0.0,32.009618943));
#1340=CARTESIAN_POINT('#1340',(52.5,0.0,42.009618943));
#1341=CARTESIAN_POINT('#1341',(42.5,0.0,42.009618943));
#1342=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1334,#1335,#1336,#1337),(#1338,
#1339,#1340,#1341)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1342')SURFACE());
#1343=ADVANCED_FACE('#1343',(#1333),#1342,.T.);
#1344=CLOSED_SHELL('#1344',(#1099,#1123,#1135,#1159,#1175,#1187,#1201,#1215,
#1231,#1247,#1263,#1279,#1295,#1311,#1327,#1343));
#1345=MANIFOLD_SOLID_BREP('#1345',#1344);
#1346=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.329E-12),#4,
'distance_accuracy_value','EDGE CURVE AND VERTEX POINT ACCURACY');
#1347=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#1346))GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3))REPRESENTATION_CONTEXT(
'l-bracket','3D'));
#1348=CARTESIAN_POINT('#1348',(0.0,0.0,0.0));
#1349=DIRECTION('#1349',(1.0,0.0,0.0));
#1350=DIRECTION('#1350',(0.0,0.0,1.0));
#1351=AXIS2_PLACEMENT_3D('#1351',#1348,#1350,#1349);
#1352=ADVANCED_BREP_SHAPE_REPRESENTATION('#1352',(#1345,#1351),#1347);
#1353=DERIVED_UNIT_ELEMENT(#4,3.0);
#1354=DERIVED_UNIT((#1353));
#1355=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#1354);
#1356=DERIVED_UNIT_ELEMENT(#4,2.0);
#1357=DERIVED_UNIT((#1356));
#1358=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#1357);
#1360=SHAPE_ASPECT('#1360','solid #1345',#1359,.F.);
#1361=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(96858.893149642),#1354);
#1362=REPRESENTATION('volume',(#1361),#1347);
#1363=PROPERTY_DEFINITION('geometric validation property','volume of #1345',
#1360);
#1364=PROPERTY_DEFINITION_REPRESENTATION(#1363,#1362);
#1365=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(24628.538517154),#1357);
#1366=REPRESENTATION('surface area',(#1365),#1347);
#1367=PROPERTY_DEFINITION('geometric validation property','area of #1345',#1360)
;
#1368=PROPERTY_DEFINITION_REPRESENTATION(#1367,#1366);
#1369=CARTESIAN_POINT('centre point',(14.59461849,20.20264746,50.000000769));
#1370=REPRESENTATION('centroid',(#1369),#1347);
#1371=PROPERTY_DEFINITION('geometric validation property','centroid of #1345',
#1360);
#1372=PROPERTY_DEFINITION_REPRESENTATION(#1371,#1370);
#1373=SHAPE_REPRESENTATION('',(#1345),#1347);
#1374=PROPERTY_DEFINITION('','Shape for Validation Properties',#1360);
#1375=SHAPE_DEFINITION_REPRESENTATION(#1374,#1373);
#1376=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(96858.893149642),#1354);
#1377=REPRESENTATION('volume',(#1376),#1347);
#1378=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #1352 - l-bracket',#1359);
#1379=PROPERTY_DEFINITION_REPRESENTATION(#1378,#1377);
#1380=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(24628.538517154),#1357);
#1381=REPRESENTATION('surface area',(#1380),#1347);
#1382=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #1352 - l-bracket',#1359);
#1383=PROPERTY_DEFINITION_REPRESENTATION(#1382,#1381);
#1384=CARTESIAN_POINT('centre point',(14.59461849,20.20264746,50.000000769));
#1385=REPRESENTATION('centroid',(#1384),#1347);
#1386=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #1352 - l-bracket',#1359);
#1387=PROPERTY_DEFINITION_REPRESENTATION(#1386,#1385);
#1388=CARTESIAN_POINT('#1388',(0.0,0.0,0.0));
#1389=DIRECTION('#1389',(1.0,0.0,0.0));
#1390=DIRECTION('#1390',(0.0,-1.,0.0));
#1391=AXIS2_PLACEMENT_3D('#1391',#1388,#1390,#1389);
#1392=ITEM_DEFINED_TRANSFORMATION('#1391','l-bracket-assembly : l-bracket',#1351
,#1391);
#1393=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('l-bracket-assembly','3D'));
#1394=CARTESIAN_POINT('#1394',(0.0,0.0,0.0));
#1395=DIRECTION('#1395',(1.0,0.0,0.0));
#1396=DIRECTION('#1396',(0.0,0.0,1.0));
#1397=AXIS2_PLACEMENT_3D('#1397',#1394,#1396,#1395);
#1398=SHAPE_REPRESENTATION('#1398',(#1397,#790,#795,#800,#1391),#1393);
#1399=(REPRESENTATION_RELATIONSHIP('#1399',
'l-bracket-assembly : nut-bolt-assembly',#765,#1398)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#791)
SHAPE_REPRESENTATION_RELATIONSHIP());
#1400=(REPRESENTATION_RELATIONSHIP('#1400',
'l-bracket-assembly : nut-bolt-assembly',#765,#1398)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#796)
SHAPE_REPRESENTATION_RELATIONSHIP());
#1401=(REPRESENTATION_RELATIONSHIP('#1401',
'l-bracket-assembly : nut-bolt-assembly',#765,#1398)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#801)
SHAPE_REPRESENTATION_RELATIONSHIP());
#1402=(REPRESENTATION_RELATIONSHIP('#1402','l-bracket-assembly : l-bracket',
#1352,#1398)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1392)
SHAPE_REPRESENTATION_RELATIONSHIP());
#1403=DERIVED_UNIT_ELEMENT(#4,3.0);
#1404=DERIVED_UNIT((#1403));
#1405=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#1404);
#1406=DERIVED_UNIT_ELEMENT(#4,2.0);
#1407=DERIVED_UNIT((#1406));
#1408=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#1407);
#1410=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(108453.041496513),#1404);
#1411=REPRESENTATION('volume',(#1410),#1393);
#1412=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #1398 - l-bracket-assembly',#1409);
#1413=PROPERTY_DEFINITION_REPRESENTATION(#1412,#1411);
#1414=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(31558.650064561),#1407);
#1415=REPRESENTATION('surface area',(#1414),#1393);
#1416=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #1398 - l-bracket-assembly',#1409);
#1417=PROPERTY_DEFINITION_REPRESENTATION(#1416,#1415);
#1418=CARTESIAN_POINT('centre point',(16.776093787,-50.,17.299312811));
#1419=REPRESENTATION('centroid',(#1418),#1393);
#1420=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #1398 - l-bracket-assembly',#1409);
#1421=PROPERTY_DEFINITION_REPRESENTATION(#1420,#1419);
#1422=CARTESIAN_POINT('#1422',(5.0,125.,20.));
#1423=DIRECTION('#1423',(1.0,0.0,0.0));
#1424=DIRECTION('#1424',(0.0,0.0,1.0));
#1425=AXIS2_PLACEMENT_3D('#1425',#1422,#1424,#1423);
#1426=ITEM_DEFINED_TRANSFORMATION('#1425','MASTER : l-bracket-assembly',#1397,
#1425);
#1427=CARTESIAN_POINT('#1427',(180.,0.0,20.));
#1428=VERTEX_POINT('#1428',#1427);
#1429=CARTESIAN_POINT('#1429',(0.0,0.0,20.));
#1430=VERTEX_POINT('#1430',#1429);
#1431=CARTESIAN_POINT('#1431',(180.,150.,20.));
#1432=VERTEX_POINT('#1432',#1431);
#1433=CARTESIAN_POINT('#1433',(0.0,150.,20.));
#1434=VERTEX_POINT('#1434',#1433);
#1435=CARTESIAN_POINT('#1435',(42.5,87.990381057,20.));
#1436=VERTEX_POINT('#1436',#1435);
#1437=CARTESIAN_POINT('#1437',(52.5,87.990381057,20.));
#1438=VERTEX_POINT('#1438',#1437);
#1439=CARTESIAN_POINT('#1439',(42.5,62.009618943,20.));
#1440=VERTEX_POINT('#1440',#1439);
#1441=CARTESIAN_POINT('#1441',(52.5,62.009618943,20.));
#1442=VERTEX_POINT('#1442',#1441);
#1443=CARTESIAN_POINT('#1443',(127.5,62.009618943,20.));
#1444=VERTEX_POINT('#1444',#1443);
#1445=CARTESIAN_POINT('#1445',(137.5,62.009618943,20.));
#1446=VERTEX_POINT('#1446',#1445);
#1447=CARTESIAN_POINT('#1447',(127.5,87.990381057,20.));
#1448=VERTEX_POINT('#1448',#1447);
#1449=CARTESIAN_POINT('#1449',(137.5,87.990381057,20.));
#1450=VERTEX_POINT('#1450',#1449);
#1451=CARTESIAN_POINT('#1451',(20.,75.,20.));
#1452=VERTEX_POINT('#1452',#1451);
#1453=CARTESIAN_POINT('#1453',(30.,75.,20.));
#1454=VERTEX_POINT('#1454',#1453);
#1455=CARTESIAN_POINT('#1455',(150.,75.,20.));
#1456=VERTEX_POINT('#1456',#1455);
#1457=CARTESIAN_POINT('#1457',(160.,75.,20.));
#1458=VERTEX_POINT('#1458',#1457);
#1459=CARTESIAN_POINT('#1459',(180.,0.0,0.0));
#1460=VERTEX_POINT('#1460',#1459);
#1461=CARTESIAN_POINT('#1461',(0.0,0.0,0.0));
#1462=VERTEX_POINT('#1462',#1461);
#1463=CARTESIAN_POINT('#1463',(0.0,150.,0.0));
#1464=VERTEX_POINT('#1464',#1463);
#1465=CARTESIAN_POINT('#1465',(180.,150.,0.0));
#1466=VERTEX_POINT('#1466',#1465);
#1467=CARTESIAN_POINT('#1467',(52.5,87.990381057,0.0));
#1468=VERTEX_POINT('#1468',#1467);
#1469=CARTESIAN_POINT('#1469',(42.5,87.990381057,0.0));
#1470=VERTEX_POINT('#1470',#1469);
#1471=CARTESIAN_POINT('#1471',(52.5,62.009618943,0.0));
#1472=VERTEX_POINT('#1472',#1471);
#1473=CARTESIAN_POINT('#1473',(42.5,62.009618943,0.0));
#1474=VERTEX_POINT('#1474',#1473);
#1475=CARTESIAN_POINT('#1475',(137.5,62.009618943,0.0));
#1476=VERTEX_POINT('#1476',#1475);
#1477=CARTESIAN_POINT('#1477',(127.5,62.009618943,0.0));
#1478=VERTEX_POINT('#1478',#1477);
#1479=CARTESIAN_POINT('#1479',(137.5,87.990381057,0.0));
#1480=VERTEX_POINT('#1480',#1479);
#1481=CARTESIAN_POINT('#1481',(127.5,87.990381057,0.0));
#1482=VERTEX_POINT('#1482',#1481);
#1483=CARTESIAN_POINT('#1483',(30.,75.,0.0));
#1484=VERTEX_POINT('#1484',#1483);
#1485=CARTESIAN_POINT('#1485',(20.,75.,0.0));
#1486=VERTEX_POINT('#1486',#1485);
#1487=CARTESIAN_POINT('#1487',(160.,75.,0.0));
#1488=VERTEX_POINT('#1488',#1487);
#1489=CARTESIAN_POINT('#1489',(150.,75.,0.0));
#1490=VERTEX_POINT('#1490',#1489);
#1491=CARTESIAN_POINT('#1491',(180.,0.0,20.));
#1492=DIRECTION('#1492',(-1.,0.0,0.0));
#1493=VECTOR('#1493',#1492,180.);
#1494=LINE('#1494',#1491,#1493);
#1495=CARTESIAN_POINT('#1495',(180.,150.,20.));
#1496=DIRECTION('#1496',(0.0,-1.,0.0));
#1497=VECTOR('#1497',#1496,150.);
#1498=LINE('#1498',#1495,#1497);
#1499=CARTESIAN_POINT('#1499',(0.0,150.,20.));
#1500=DIRECTION('#1500',(1.0,0.0,0.0));
#1501=VECTOR('#1501',#1500,180.);
#1502=LINE('#1502',#1499,#1501);
#1503=CARTESIAN_POINT('#1503',(0.0,0.0,20.));
#1504=DIRECTION('#1504',(0.0,1.0,0.0));
#1505=VECTOR('#1505',#1504,150.);
#1506=LINE('#1506',#1503,#1505);
#1507=CARTESIAN_POINT('#1507',(42.5,87.990381057,20.));
#1508=CARTESIAN_POINT('#1508',(42.5,97.990381057,20.));
#1509=CARTESIAN_POINT('#1509',(52.5,97.990381057,20.));
#1510=CARTESIAN_POINT('#1510',(52.5,87.990381057,20.));
#1511=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1507,#1508,#1509,#1510),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1511'));
#1512=CARTESIAN_POINT('#1512',(52.5,87.990381057,20.));
#1513=CARTESIAN_POINT('#1513',(52.5,77.990381057,20.));
#1514=CARTESIAN_POINT('#1514',(42.5,77.990381057,20.));
#1515=CARTESIAN_POINT('#1515',(42.5,87.990381057,20.));
#1516=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1512,#1513,#1514,#1515),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1516'));
#1517=CARTESIAN_POINT('#1517',(42.5,62.009618943,20.));
#1518=CARTESIAN_POINT('#1518',(42.5,72.009618943,20.));
#1519=CARTESIAN_POINT('#1519',(52.5,72.009618943,20.));
#1520=CARTESIAN_POINT('#1520',(52.5,62.009618943,20.));
#1521=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1517,#1518,#1519,#1520),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1521'));
#1522=CARTESIAN_POINT('#1522',(52.5,62.009618943,20.));
#1523=CARTESIAN_POINT('#1523',(52.5,52.009618943,20.));
#1524=CARTESIAN_POINT('#1524',(42.5,52.009618943,20.));
#1525=CARTESIAN_POINT('#1525',(42.5,62.009618943,20.));
#1526=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1522,#1523,#1524,#1525),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1526'));
#1527=CARTESIAN_POINT('#1527',(127.5,62.009618943,20.));
#1528=CARTESIAN_POINT('#1528',(127.5,72.009618943,20.));
#1529=CARTESIAN_POINT('#1529',(137.5,72.009618943,20.));
#1530=CARTESIAN_POINT('#1530',(137.5,62.009618943,20.));
#1531=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1527,#1528,#1529,#1530),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1531'));
#1532=CARTESIAN_POINT('#1532',(137.5,62.009618943,20.));
#1533=CARTESIAN_POINT('#1533',(137.5,52.009618943,20.));
#1534=CARTESIAN_POINT('#1534',(127.5,52.009618943,20.));
#1535=CARTESIAN_POINT('#1535',(127.5,62.009618943,20.));
#1536=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1532,#1533,#1534,#1535),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1536'));
#1537=CARTESIAN_POINT('#1537',(127.5,87.990381057,20.));
#1538=CARTESIAN_POINT('#1538',(127.5,97.990381057,20.));
#1539=CARTESIAN_POINT('#1539',(137.5,97.990381057,20.));
#1540=CARTESIAN_POINT('#1540',(137.5,87.990381057,20.));
#1541=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1537,#1538,#1539,#1540),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1541'));
#1542=CARTESIAN_POINT('#1542',(137.5,87.990381057,20.));
#1543=CARTESIAN_POINT('#1543',(137.5,77.990381057,20.));
#1544=CARTESIAN_POINT('#1544',(127.5,77.990381057,20.));
#1545=CARTESIAN_POINT('#1545',(127.5,87.990381057,20.));
#1546=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1542,#1543,#1544,#1545),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1546'));
#1547=CARTESIAN_POINT('#1547',(20.,75.,20.));
#1548=CARTESIAN_POINT('#1548',(20.,85.,20.));
#1549=CARTESIAN_POINT('#1549',(30.,85.,20.));
#1550=CARTESIAN_POINT('#1550',(30.,75.,20.));
#1551=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1547,#1548,#1549,#1550),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1551'));
#1552=CARTESIAN_POINT('#1552',(30.,75.,20.));
#1553=CARTESIAN_POINT('#1553',(30.,65.,20.));
#1554=CARTESIAN_POINT('#1554',(20.,65.,20.));
#1555=CARTESIAN_POINT('#1555',(20.,75.,20.));
#1556=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1552,#1553,#1554,#1555),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1556'));
#1557=CARTESIAN_POINT('#1557',(150.,75.,20.));
#1558=CARTESIAN_POINT('#1558',(150.,85.,20.));
#1559=CARTESIAN_POINT('#1559',(160.,85.,20.));
#1560=CARTESIAN_POINT('#1560',(160.,75.,20.));
#1561=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1557,#1558,#1559,#1560),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1561'));
#1562=CARTESIAN_POINT('#1562',(160.,75.,20.));
#1563=CARTESIAN_POINT('#1563',(160.,65.,20.));
#1564=CARTESIAN_POINT('#1564',(150.,65.,20.));
#1565=CARTESIAN_POINT('#1565',(150.,75.,20.));
#1566=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1562,#1563,#1564,#1565),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1566'));
#1567=CARTESIAN_POINT('#1567',(180.,0.0,0.0));
#1568=DIRECTION('#1568',(0.0,0.0,1.0));
#1569=VECTOR('#1569',#1568,20.);
#1570=LINE('#1570',#1567,#1569);
#1571=CARTESIAN_POINT('#1571',(0.0,0.0,0.0));
#1572=DIRECTION('#1572',(0.0,0.0,1.0));
#1573=VECTOR('#1573',#1572,20.);
#1574=LINE('#1574',#1571,#1573);
#1575=CARTESIAN_POINT('#1575',(0.0,0.0,0.0));
#1576=DIRECTION('#1576',(1.0,0.0,0.0));
#1577=VECTOR('#1577',#1576,180.);
#1578=LINE('#1578',#1575,#1577);
#1579=CARTESIAN_POINT('#1579',(0.0,150.,0.0));
#1580=DIRECTION('#1580',(0.0,0.0,1.0));
#1581=VECTOR('#1581',#1580,20.);
#1582=LINE('#1582',#1579,#1581);
#1583=CARTESIAN_POINT('#1583',(0.0,150.,0.0));
#1584=DIRECTION('#1584',(0.0,-1.,0.0));
#1585=VECTOR('#1585',#1584,150.);
#1586=LINE('#1586',#1583,#1585);
#1587=CARTESIAN_POINT('#1587',(180.,150.,0.0));
#1588=DIRECTION('#1588',(0.0,0.0,1.0));
#1589=VECTOR('#1589',#1588,20.);
#1590=LINE('#1590',#1587,#1589);
#1591=CARTESIAN_POINT('#1591',(180.,150.,0.0));
#1592=DIRECTION('#1592',(-1.,0.0,0.0));
#1593=VECTOR('#1593',#1592,180.);
#1594=LINE('#1594',#1591,#1593);
#1595=CARTESIAN_POINT('#1595',(180.,0.0,0.0));
#1596=DIRECTION('#1596',(0.0,1.0,0.0));
#1597=VECTOR('#1597',#1596,150.);
#1598=LINE('#1598',#1595,#1597);
#1599=CARTESIAN_POINT('#1599',(52.5,87.990381057,0.0));
#1600=CARTESIAN_POINT('#1600',(52.5,77.990381057,0.0));
#1601=CARTESIAN_POINT('#1601',(42.5,77.990381057,0.0));
#1602=CARTESIAN_POINT('#1602',(42.5,87.990381057,0.0));
#1603=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1599,#1600,#1601,#1602),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1603'));
#1604=CARTESIAN_POINT('#1604',(42.5,87.990381057,0.0));
#1605=CARTESIAN_POINT('#1605',(42.5,97.990381057,0.0));
#1606=CARTESIAN_POINT('#1606',(52.5,97.990381057,0.0));
#1607=CARTESIAN_POINT('#1607',(52.5,87.990381057,0.0));
#1608=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1604,#1605,#1606,#1607),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1608'));
#1609=CARTESIAN_POINT('#1609',(52.5,62.009618943,0.0));
#1610=CARTESIAN_POINT('#1610',(52.5,52.009618943,0.0));
#1611=CARTESIAN_POINT('#1611',(42.5,52.009618943,0.0));
#1612=CARTESIAN_POINT('#1612',(42.5,62.009618943,0.0));
#1613=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1609,#1610,#1611,#1612),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1613'));
#1614=CARTESIAN_POINT('#1614',(42.5,62.009618943,0.0));
#1615=CARTESIAN_POINT('#1615',(42.5,72.009618943,0.0));
#1616=CARTESIAN_POINT('#1616',(52.5,72.009618943,0.0));
#1617=CARTESIAN_POINT('#1617',(52.5,62.009618943,0.0));
#1618=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1614,#1615,#1616,#1617),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1618'));
#1619=CARTESIAN_POINT('#1619',(137.5,62.009618943,0.0));
#1620=CARTESIAN_POINT('#1620',(137.5,52.009618943,0.0));
#1621=CARTESIAN_POINT('#1621',(127.5,52.009618943,0.0));
#1622=CARTESIAN_POINT('#1622',(127.5,62.009618943,0.0));
#1623=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1619,#1620,#1621,#1622),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1623'));
#1624=CARTESIAN_POINT('#1624',(127.5,62.009618943,0.0));
#1625=CARTESIAN_POINT('#1625',(127.5,72.009618943,0.0));
#1626=CARTESIAN_POINT('#1626',(137.5,72.009618943,0.0));
#1627=CARTESIAN_POINT('#1627',(137.5,62.009618943,0.0));
#1628=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1624,#1625,#1626,#1627),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1628'));
#1629=CARTESIAN_POINT('#1629',(137.5,87.990381057,0.0));
#1630=CARTESIAN_POINT('#1630',(137.5,77.990381057,0.0));
#1631=CARTESIAN_POINT('#1631',(127.5,77.990381057,0.0));
#1632=CARTESIAN_POINT('#1632',(127.5,87.990381057,0.0));
#1633=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1629,#1630,#1631,#1632),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1633'));
#1634=CARTESIAN_POINT('#1634',(127.5,87.990381057,0.0));
#1635=CARTESIAN_POINT('#1635',(127.5,97.990381057,0.0));
#1636=CARTESIAN_POINT('#1636',(137.5,97.990381057,0.0));
#1637=CARTESIAN_POINT('#1637',(137.5,87.990381057,0.0));
#1638=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1634,#1635,#1636,#1637),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1638'));
#1639=CARTESIAN_POINT('#1639',(30.,75.,0.0));
#1640=CARTESIAN_POINT('#1640',(30.,65.,0.0));
#1641=CARTESIAN_POINT('#1641',(20.,65.,0.0));
#1642=CARTESIAN_POINT('#1642',(20.,75.,0.0));
#1643=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1639,#1640,#1641,#1642),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1643'));
#1644=CARTESIAN_POINT('#1644',(20.,75.,0.0));
#1645=CARTESIAN_POINT('#1645',(20.,85.,0.0));
#1646=CARTESIAN_POINT('#1646',(30.,85.,0.0));
#1647=CARTESIAN_POINT('#1647',(30.,75.,0.0));
#1648=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1644,#1645,#1646,#1647),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1648'));
#1649=CARTESIAN_POINT('#1649',(160.,75.,0.0));
#1650=CARTESIAN_POINT('#1650',(160.,65.,0.0));
#1651=CARTESIAN_POINT('#1651',(150.,65.,0.0));
#1652=CARTESIAN_POINT('#1652',(150.,75.,0.0));
#1653=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1649,#1650,#1651,#1652),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1653'));
#1654=CARTESIAN_POINT('#1654',(150.,75.,0.0));
#1655=CARTESIAN_POINT('#1655',(150.,85.,0.0));
#1656=CARTESIAN_POINT('#1656',(160.,85.,0.0));
#1657=CARTESIAN_POINT('#1657',(160.,75.,0.0));
#1658=(BOUNDED_CURVE()B_SPLINE_CURVE(3,(#1654,#1655,#1656,#1657),.UNSPECIFIED.,
.F.,.F.)B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.0,0.5),.UNSPECIFIED.)CURVE()
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_CURVE((1.0,0.333333333333,
0.333333333333,1.0))REPRESENTATION_ITEM('#1658'));
#1659=CARTESIAN_POINT('#1659',(160.,75.,0.0));
#1660=DIRECTION('#1660',(0.0,0.0,1.0));
#1661=VECTOR('#1661',#1660,20.);
#1662=LINE('#1662',#1659,#1661);
#1663=CARTESIAN_POINT('#1663',(150.,75.,20.));
#1664=DIRECTION('#1664',(0.0,0.0,-1.));
#1665=VECTOR('#1665',#1664,20.);
#1666=LINE('#1666',#1663,#1665);
#1667=CARTESIAN_POINT('#1667',(30.,75.,0.0));
#1668=DIRECTION('#1668',(0.0,0.0,1.0));
#1669=VECTOR('#1669',#1668,20.);
#1670=LINE('#1670',#1667,#1669);
#1671=CARTESIAN_POINT('#1671',(20.,75.,20.));
#1672=DIRECTION('#1672',(0.0,0.0,-1.));
#1673=VECTOR('#1673',#1672,20.);
#1674=LINE('#1674',#1671,#1673);
#1675=CARTESIAN_POINT('#1675',(137.5,87.990381057,0.0));
#1676=DIRECTION('#1676',(0.0,0.0,1.0));
#1677=VECTOR('#1677',#1676,20.);
#1678=LINE('#1678',#1675,#1677);
#1679=CARTESIAN_POINT('#1679',(127.5,87.990381057,20.));
#1680=DIRECTION('#1680',(0.0,0.0,-1.));
#1681=VECTOR('#1681',#1680,20.);
#1682=LINE('#1682',#1679,#1681);
#1683=CARTESIAN_POINT('#1683',(137.5,62.009618943,0.0));
#1684=DIRECTION('#1684',(0.0,0.0,1.0));
#1685=VECTOR('#1685',#1684,20.);
#1686=LINE('#1686',#1683,#1685);
#1687=CARTESIAN_POINT('#1687',(127.5,62.009618943,20.));
#1688=DIRECTION('#1688',(0.0,0.0,-1.));
#1689=VECTOR('#1689',#1688,20.);
#1690=LINE('#1690',#1687,#1689);
#1691=CARTESIAN_POINT('#1691',(52.5,62.009618943,0.0));
#1692=DIRECTION('#1692',(0.0,0.0,1.0));
#1693=VECTOR('#1693',#1692,20.);
#1694=LINE('#1694',#1691,#1693);
#1695=CARTESIAN_POINT('#1695',(42.5,62.009618943,20.));
#1696=DIRECTION('#1696',(0.0,0.0,-1.));
#1697=VECTOR('#1697',#1696,20.);
#1698=LINE('#1698',#1695,#1697);
#1699=CARTESIAN_POINT('#1699',(52.5,87.990381057,0.0));
#1700=DIRECTION('#1700',(0.0,0.0,1.0));
#1701=VECTOR('#1701',#1700,20.);
#1702=LINE('#1702',#1699,#1701);
#1703=CARTESIAN_POINT('#1703',(42.5,87.990381057,20.));
#1704=DIRECTION('#1704',(0.0,0.0,-1.));
#1705=VECTOR('#1705',#1704,20.);
#1706=LINE('#1706',#1703,#1705);
#1707=EDGE_CURVE('#1707',#1428,#1430,#1494,.T.);
#1708=EDGE_CURVE('#1708',#1432,#1428,#1498,.T.);
#1709=EDGE_CURVE('#1709',#1434,#1432,#1502,.T.);
#1710=EDGE_CURVE('#1710',#1430,#1434,#1506,.T.);
#1711=EDGE_CURVE('#1711',#1436,#1438,#1511,.T.);
#1712=EDGE_CURVE('#1712',#1438,#1436,#1516,.T.);
#1713=EDGE_CURVE('#1713',#1440,#1442,#1521,.T.);
#1714=EDGE_CURVE('#1714',#1442,#1440,#1526,.T.);
#1715=EDGE_CURVE('#1715',#1444,#1446,#1531,.T.);
#1716=EDGE_CURVE('#1716',#1446,#1444,#1536,.T.);
#1717=EDGE_CURVE('#1717',#1448,#1450,#1541,.T.);
#1718=EDGE_CURVE('#1718',#1450,#1448,#1546,.T.);
#1719=EDGE_CURVE('#1719',#1452,#1454,#1551,.T.);
#1720=EDGE_CURVE('#1720',#1454,#1452,#1556,.T.);
#1721=EDGE_CURVE('#1721',#1456,#1458,#1561,.T.);
#1722=EDGE_CURVE('#1722',#1458,#1456,#1566,.T.);
#1723=EDGE_CURVE('#1723',#1460,#1428,#1570,.T.);
#1724=EDGE_CURVE('#1724',#1462,#1430,#1574,.T.);
#1725=EDGE_CURVE('#1725',#1462,#1460,#1578,.T.);
#1726=EDGE_CURVE('#1726',#1464,#1434,#1582,.T.);
#1727=EDGE_CURVE('#1727',#1464,#1462,#1586,.T.);
#1728=EDGE_CURVE('#1728',#1466,#1432,#1590,.T.);
#1729=EDGE_CURVE('#1729',#1466,#1464,#1594,.T.);
#1730=EDGE_CURVE('#1730',#1460,#1466,#1598,.T.);
#1731=EDGE_CURVE('#1731',#1468,#1470,#1603,.T.);
#1732=EDGE_CURVE('#1732',#1470,#1468,#1608,.T.);
#1733=EDGE_CURVE('#1733',#1472,#1474,#1613,.T.);
#1734=EDGE_CURVE('#1734',#1474,#1472,#1618,.T.);
#1735=EDGE_CURVE('#1735',#1476,#1478,#1623,.T.);
#1736=EDGE_CURVE('#1736',#1478,#1476,#1628,.T.);
#1737=EDGE_CURVE('#1737',#1480,#1482,#1633,.T.);
#1738=EDGE_CURVE('#1738',#1482,#1480,#1638,.T.);
#1739=EDGE_CURVE('#1739',#1484,#1486,#1643,.T.);
#1740=EDGE_CURVE('#1740',#1486,#1484,#1648,.T.);
#1741=EDGE_CURVE('#1741',#1488,#1490,#1653,.T.);
#1742=EDGE_CURVE('#1742',#1490,#1488,#1658,.T.);
#1743=EDGE_CURVE('#1743',#1488,#1458,#1662,.T.);
#1744=EDGE_CURVE('#1744',#1456,#1490,#1666,.T.);
#1745=EDGE_CURVE('#1745',#1484,#1454,#1670,.T.);
#1746=EDGE_CURVE('#1746',#1452,#1486,#1674,.T.);
#1747=EDGE_CURVE('#1747',#1480,#1450,#1678,.T.);
#1748=EDGE_CURVE('#1748',#1448,#1482,#1682,.T.);
#1749=EDGE_CURVE('#1749',#1476,#1446,#1686,.T.);
#1750=EDGE_CURVE('#1750',#1444,#1478,#1690,.T.);
#1751=EDGE_CURVE('#1751',#1472,#1442,#1694,.T.);
#1752=EDGE_CURVE('#1752',#1440,#1474,#1698,.T.);
#1753=EDGE_CURVE('#1753',#1468,#1438,#1702,.T.);
#1754=EDGE_CURVE('#1754',#1436,#1470,#1706,.T.);
#1755=ORIENTED_EDGE('#1755',*,*,#1707,.F.);
#1756=ORIENTED_EDGE('#1756',*,*,#1708,.F.);
#1757=ORIENTED_EDGE('#1757',*,*,#1709,.F.);
#1758=ORIENTED_EDGE('#1758',*,*,#1710,.F.);
#1759=EDGE_LOOP('#1759',(#1755,#1756,#1757,#1758));
#1760=FACE_OUTER_BOUND('#1760',#1759,.T.);
#1761=ORIENTED_EDGE('#1761',*,*,#1711,.T.);
#1762=ORIENTED_EDGE('#1762',*,*,#1712,.T.);
#1763=EDGE_LOOP('#1763',(#1761,#1762));
#1764=FACE_BOUND('#1764',#1763,.T.);
#1765=ORIENTED_EDGE('#1765',*,*,#1713,.T.);
#1766=ORIENTED_EDGE('#1766',*,*,#1714,.T.);
#1767=EDGE_LOOP('#1767',(#1765,#1766));
#1768=FACE_BOUND('#1768',#1767,.T.);
#1769=ORIENTED_EDGE('#1769',*,*,#1715,.T.);
#1770=ORIENTED_EDGE('#1770',*,*,#1716,.T.);
#1771=EDGE_LOOP('#1771',(#1769,#1770));
#1772=FACE_BOUND('#1772',#1771,.T.);
#1773=ORIENTED_EDGE('#1773',*,*,#1717,.T.);
#1774=ORIENTED_EDGE('#1774',*,*,#1718,.T.);
#1775=EDGE_LOOP('#1775',(#1773,#1774));
#1776=FACE_BOUND('#1776',#1775,.T.);
#1777=ORIENTED_EDGE('#1777',*,*,#1719,.T.);
#1778=ORIENTED_EDGE('#1778',*,*,#1720,.T.);
#1779=EDGE_LOOP('#1779',(#1777,#1778));
#1780=FACE_BOUND('#1780',#1779,.T.);
#1781=ORIENTED_EDGE('#1781',*,*,#1721,.T.);
#1782=ORIENTED_EDGE('#1782',*,*,#1722,.T.);
#1783=EDGE_LOOP('#1783',(#1781,#1782));
#1784=FACE_BOUND('#1784',#1783,.T.);
#1785=CARTESIAN_POINT('#1785',(90.,75.,20.));
#1786=DIRECTION('#1786',(0.0,0.0,1.0));
#1787=DIRECTION('#1787',(1.0,0.0,0.0));
#1788=AXIS2_PLACEMENT_3D('#1788',#1785,#1786,#1787);
#1789=PLANE('#1789',#1788);
#1790=ADVANCED_FACE('#1790',(#1760,#1764,#1768,#1772,#1776,#1780,#1784),#1789,
.T.);
#1791=ORIENTED_EDGE('#1791',*,*,#1723,.T.);
#1792=ORIENTED_EDGE('#1792',*,*,#1707,.T.);
#1793=ORIENTED_EDGE('#1793',*,*,#1724,.F.);
#1794=ORIENTED_EDGE('#1794',*,*,#1725,.T.);
#1795=EDGE_LOOP('#1795',(#1791,#1792,#1793,#1794));
#1796=FACE_BOUND('#1796',#1795,.T.);
#1797=CARTESIAN_POINT('#1797',(90.,0.0,0.0));
#1798=DIRECTION('#1798',(0.0,-1.,0.0));
#1799=DIRECTION('#1799',(0.0,0.0,-1.));
#1800=AXIS2_PLACEMENT_3D('#1800',#1797,#1798,#1799);
#1801=PLANE('#1801',#1800);
#1802=ADVANCED_FACE('#1802',(#1796),#1801,.T.);
#1803=ORIENTED_EDGE('#1803',*,*,#1724,.T.);
#1804=ORIENTED_EDGE('#1804',*,*,#1710,.T.);
#1805=ORIENTED_EDGE('#1805',*,*,#1726,.F.);
#1806=ORIENTED_EDGE('#1806',*,*,#1727,.T.);
#1807=EDGE_LOOP('#1807',(#1803,#1804,#1805,#1806));
#1808=FACE_BOUND('#1808',#1807,.T.);
#1809=CARTESIAN_POINT('#1809',(0.0,75.,0.0));
#1810=DIRECTION('#1810',(-1.,0.0,0.0));
#1811=DIRECTION('#1811',(0.0,0.0,1.0));
#1812=AXIS2_PLACEMENT_3D('#1812',#1809,#1810,#1811);
#1813=PLANE('#1813',#1812);
#1814=ADVANCED_FACE('#1814',(#1808),#1813,.T.);
#1815=ORIENTED_EDGE('#1815',*,*,#1726,.T.);
#1816=ORIENTED_EDGE('#1816',*,*,#1709,.T.);
#1817=ORIENTED_EDGE('#1817',*,*,#1728,.F.);
#1818=ORIENTED_EDGE('#1818',*,*,#1729,.T.);
#1819=EDGE_LOOP('#1819',(#1815,#1816,#1817,#1818));
#1820=FACE_BOUND('#1820',#1819,.T.);
#1821=CARTESIAN_POINT('#1821',(90.,150.,0.0));
#1822=DIRECTION('#1822',(0.0,1.0,0.0));
#1823=DIRECTION('#1823',(0.0,0.0,1.0));
#1824=AXIS2_PLACEMENT_3D('#1824',#1821,#1822,#1823);
#1825=PLANE('#1825',#1824);
#1826=ADVANCED_FACE('#1826',(#1820),#1825,.T.);
#1827=ORIENTED_EDGE('#1827',*,*,#1730,.F.);
#1828=ORIENTED_EDGE('#1828',*,*,#1725,.F.);
#1829=ORIENTED_EDGE('#1829',*,*,#1727,.F.);
#1830=ORIENTED_EDGE('#1830',*,*,#1729,.F.);
#1831=EDGE_LOOP('#1831',(#1827,#1828,#1829,#1830));
#1832=FACE_OUTER_BOUND('#1832',#1831,.T.);
#1833=ORIENTED_EDGE('#1833',*,*,#1731,.F.);
#1834=ORIENTED_EDGE('#1834',*,*,#1732,.F.);
#1835=EDGE_LOOP('#1835',(#1833,#1834));
#1836=FACE_BOUND('#1836',#1835,.T.);
#1837=ORIENTED_EDGE('#1837',*,*,#1733,.F.);
#1838=ORIENTED_EDGE('#1838',*,*,#1734,.F.);
#1839=EDGE_LOOP('#1839',(#1837,#1838));
#1840=FACE_BOUND('#1840',#1839,.T.);
#1841=ORIENTED_EDGE('#1841',*,*,#1735,.F.);
#1842=ORIENTED_EDGE('#1842',*,*,#1736,.F.);
#1843=EDGE_LOOP('#1843',(#1841,#1842));
#1844=FACE_BOUND('#1844',#1843,.T.);
#1845=ORIENTED_EDGE('#1845',*,*,#1737,.F.);
#1846=ORIENTED_EDGE('#1846',*,*,#1738,.F.);
#1847=EDGE_LOOP('#1847',(#1845,#1846));
#1848=FACE_BOUND('#1848',#1847,.T.);
#1849=ORIENTED_EDGE('#1849',*,*,#1739,.F.);
#1850=ORIENTED_EDGE('#1850',*,*,#1740,.F.);
#1851=EDGE_LOOP('#1851',(#1849,#1850));
#1852=FACE_BOUND('#1852',#1851,.T.);
#1853=ORIENTED_EDGE('#1853',*,*,#1741,.F.);
#1854=ORIENTED_EDGE('#1854',*,*,#1742,.F.);
#1855=EDGE_LOOP('#1855',(#1853,#1854));
#1856=FACE_BOUND('#1856',#1855,.T.);
#1857=CARTESIAN_POINT('#1857',(90.,75.,0.0));
#1858=DIRECTION('#1858',(0.0,0.0,-1.));
#1859=DIRECTION('#1859',(-1.,0.0,0.0));
#1860=AXIS2_PLACEMENT_3D('#1860',#1857,#1858,#1859);
#1861=PLANE('#1861',#1860);
#1862=ADVANCED_FACE('#1862',(#1832,#1836,#1840,#1844,#1848,#1852,#1856),#1861,
.T.);
#1863=ORIENTED_EDGE('#1863',*,*,#1730,.T.);
#1864=ORIENTED_EDGE('#1864',*,*,#1728,.T.);
#1865=ORIENTED_EDGE('#1865',*,*,#1708,.T.);
#1866=ORIENTED_EDGE('#1866',*,*,#1723,.F.);
#1867=EDGE_LOOP('#1867',(#1863,#1864,#1865,#1866));
#1868=FACE_BOUND('#1868',#1867,.T.);
#1869=CARTESIAN_POINT('#1869',(180.,75.,0.0));
#1870=DIRECTION('#1870',(1.0,0.0,0.0));
#1871=DIRECTION('#1871',(0.0,0.0,-1.));
#1872=AXIS2_PLACEMENT_3D('#1872',#1869,#1870,#1871);
#1873=PLANE('#1873',#1872);
#1874=ADVANCED_FACE('#1874',(#1868),#1873,.T.);
#1875=ORIENTED_EDGE('#1875',*,*,#1742,.T.);
#1876=ORIENTED_EDGE('#1876',*,*,#1743,.T.);
#1877=ORIENTED_EDGE('#1877',*,*,#1721,.F.);
#1878=ORIENTED_EDGE('#1878',*,*,#1744,.T.);
#1879=EDGE_LOOP('#1879',(#1875,#1876,#1877,#1878));
#1880=FACE_BOUND('#1880',#1879,.T.);
#1881=CARTESIAN_POINT('#1881',(150.,75.,20.));
#1882=CARTESIAN_POINT('#1882',(150.,85.,20.));
#1883=CARTESIAN_POINT('#1883',(160.,85.,20.));
#1884=CARTESIAN_POINT('#1884',(160.,75.,20.));
#1885=CARTESIAN_POINT('#1885',(150.,75.,0.0));
#1886=CARTESIAN_POINT('#1886',(150.,85.,0.0));
#1887=CARTESIAN_POINT('#1887',(160.,85.,0.0));
#1888=CARTESIAN_POINT('#1888',(160.,75.,0.0));
#1889=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1881,#1882,#1883,#1884),(#1885,
#1886,#1887,#1888)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1889')SURFACE());
#1890=ADVANCED_FACE('#1890',(#1880),#1889,.T.);
#1891=ORIENTED_EDGE('#1891',*,*,#1740,.T.);
#1892=ORIENTED_EDGE('#1892',*,*,#1745,.T.);
#1893=ORIENTED_EDGE('#1893',*,*,#1719,.F.);
#1894=ORIENTED_EDGE('#1894',*,*,#1746,.T.);
#1895=EDGE_LOOP('#1895',(#1891,#1892,#1893,#1894));
#1896=FACE_BOUND('#1896',#1895,.T.);
#1897=CARTESIAN_POINT('#1897',(20.,75.,20.));
#1898=CARTESIAN_POINT('#1898',(20.,85.,20.));
#1899=CARTESIAN_POINT('#1899',(30.,85.,20.));
#1900=CARTESIAN_POINT('#1900',(30.,75.,20.));
#1901=CARTESIAN_POINT('#1901',(20.,75.,0.0));
#1902=CARTESIAN_POINT('#1902',(20.,85.,0.0));
#1903=CARTESIAN_POINT('#1903',(30.,85.,0.0));
#1904=CARTESIAN_POINT('#1904',(30.,75.,0.0));
#1905=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1897,#1898,#1899,#1900),(#1901,
#1902,#1903,#1904)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1905')SURFACE());
#1906=ADVANCED_FACE('#1906',(#1896),#1905,.T.);
#1907=ORIENTED_EDGE('#1907',*,*,#1738,.T.);
#1908=ORIENTED_EDGE('#1908',*,*,#1747,.T.);
#1909=ORIENTED_EDGE('#1909',*,*,#1717,.F.);
#1910=ORIENTED_EDGE('#1910',*,*,#1748,.T.);
#1911=EDGE_LOOP('#1911',(#1907,#1908,#1909,#1910));
#1912=FACE_BOUND('#1912',#1911,.T.);
#1913=CARTESIAN_POINT('#1913',(127.5,87.990381057,20.));
#1914=CARTESIAN_POINT('#1914',(127.5,97.990381057,20.));
#1915=CARTESIAN_POINT('#1915',(137.5,97.990381057,20.));
#1916=CARTESIAN_POINT('#1916',(137.5,87.990381057,20.));
#1917=CARTESIAN_POINT('#1917',(127.5,87.990381057,0.0));
#1918=CARTESIAN_POINT('#1918',(127.5,97.990381057,0.0));
#1919=CARTESIAN_POINT('#1919',(137.5,97.990381057,0.0));
#1920=CARTESIAN_POINT('#1920',(137.5,87.990381057,0.0));
#1921=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1913,#1914,#1915,#1916),(#1917,
#1918,#1919,#1920)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1921')SURFACE());
#1922=ADVANCED_FACE('#1922',(#1912),#1921,.T.);
#1923=ORIENTED_EDGE('#1923',*,*,#1736,.T.);
#1924=ORIENTED_EDGE('#1924',*,*,#1749,.T.);
#1925=ORIENTED_EDGE('#1925',*,*,#1715,.F.);
#1926=ORIENTED_EDGE('#1926',*,*,#1750,.T.);
#1927=EDGE_LOOP('#1927',(#1923,#1924,#1925,#1926));
#1928=FACE_BOUND('#1928',#1927,.T.);
#1929=CARTESIAN_POINT('#1929',(127.5,62.009618943,20.));
#1930=CARTESIAN_POINT('#1930',(127.5,72.009618943,20.));
#1931=CARTESIAN_POINT('#1931',(137.5,72.009618943,20.));
#1932=CARTESIAN_POINT('#1932',(137.5,62.009618943,20.));
#1933=CARTESIAN_POINT('#1933',(127.5,62.009618943,0.0));
#1934=CARTESIAN_POINT('#1934',(127.5,72.009618943,0.0));
#1935=CARTESIAN_POINT('#1935',(137.5,72.009618943,0.0));
#1936=CARTESIAN_POINT('#1936',(137.5,62.009618943,0.0));
#1937=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1929,#1930,#1931,#1932),(#1933,
#1934,#1935,#1936)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1937')SURFACE());
#1938=ADVANCED_FACE('#1938',(#1928),#1937,.T.);
#1939=ORIENTED_EDGE('#1939',*,*,#1734,.T.);
#1940=ORIENTED_EDGE('#1940',*,*,#1751,.T.);
#1941=ORIENTED_EDGE('#1941',*,*,#1713,.F.);
#1942=ORIENTED_EDGE('#1942',*,*,#1752,.T.);
#1943=EDGE_LOOP('#1943',(#1939,#1940,#1941,#1942));
#1944=FACE_BOUND('#1944',#1943,.T.);
#1945=CARTESIAN_POINT('#1945',(42.5,62.009618943,20.));
#1946=CARTESIAN_POINT('#1946',(42.5,72.009618943,20.));
#1947=CARTESIAN_POINT('#1947',(52.5,72.009618943,20.));
#1948=CARTESIAN_POINT('#1948',(52.5,62.009618943,20.));
#1949=CARTESIAN_POINT('#1949',(42.5,62.009618943,0.0));
#1950=CARTESIAN_POINT('#1950',(42.5,72.009618943,0.0));
#1951=CARTESIAN_POINT('#1951',(52.5,72.009618943,0.0));
#1952=CARTESIAN_POINT('#1952',(52.5,62.009618943,0.0));
#1953=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1945,#1946,#1947,#1948),(#1949,
#1950,#1951,#1952)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1953')SURFACE());
#1954=ADVANCED_FACE('#1954',(#1944),#1953,.T.);
#1955=ORIENTED_EDGE('#1955',*,*,#1732,.T.);
#1956=ORIENTED_EDGE('#1956',*,*,#1753,.T.);
#1957=ORIENTED_EDGE('#1957',*,*,#1711,.F.);
#1958=ORIENTED_EDGE('#1958',*,*,#1754,.T.);
#1959=EDGE_LOOP('#1959',(#1955,#1956,#1957,#1958));
#1960=FACE_BOUND('#1960',#1959,.T.);
#1961=CARTESIAN_POINT('#1961',(42.5,87.990381057,20.));
#1962=CARTESIAN_POINT('#1962',(42.5,97.990381057,20.));
#1963=CARTESIAN_POINT('#1963',(52.5,97.990381057,20.));
#1964=CARTESIAN_POINT('#1964',(52.5,87.990381057,20.));
#1965=CARTESIAN_POINT('#1965',(42.5,87.990381057,0.0));
#1966=CARTESIAN_POINT('#1966',(42.5,97.990381057,0.0));
#1967=CARTESIAN_POINT('#1967',(52.5,97.990381057,0.0));
#1968=CARTESIAN_POINT('#1968',(52.5,87.990381057,0.0));
#1969=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1961,#1962,#1963,#1964),(#1965,
#1966,#1967,#1968)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1969')SURFACE());
#1970=ADVANCED_FACE('#1970',(#1960),#1969,.T.);
#1971=ORIENTED_EDGE('#1971',*,*,#1741,.T.);
#1972=ORIENTED_EDGE('#1972',*,*,#1744,.F.);
#1973=ORIENTED_EDGE('#1973',*,*,#1722,.F.);
#1974=ORIENTED_EDGE('#1974',*,*,#1743,.F.);
#1975=EDGE_LOOP('#1975',(#1971,#1972,#1973,#1974));
#1976=FACE_BOUND('#1976',#1975,.T.);
#1977=CARTESIAN_POINT('#1977',(160.,75.,20.));
#1978=CARTESIAN_POINT('#1978',(160.,65.,20.));
#1979=CARTESIAN_POINT('#1979',(150.,65.,20.));
#1980=CARTESIAN_POINT('#1980',(150.,75.,20.));
#1981=CARTESIAN_POINT('#1981',(160.,75.,0.0));
#1982=CARTESIAN_POINT('#1982',(160.,65.,0.0));
#1983=CARTESIAN_POINT('#1983',(150.,65.,0.0));
#1984=CARTESIAN_POINT('#1984',(150.,75.,0.0));
#1985=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1977,#1978,#1979,#1980),(#1981,
#1982,#1983,#1984)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#1985')SURFACE());
#1986=ADVANCED_FACE('#1986',(#1976),#1985,.T.);
#1987=ORIENTED_EDGE('#1987',*,*,#1739,.T.);
#1988=ORIENTED_EDGE('#1988',*,*,#1746,.F.);
#1989=ORIENTED_EDGE('#1989',*,*,#1720,.F.);
#1990=ORIENTED_EDGE('#1990',*,*,#1745,.F.);
#1991=EDGE_LOOP('#1991',(#1987,#1988,#1989,#1990));
#1992=FACE_BOUND('#1992',#1991,.T.);
#1993=CARTESIAN_POINT('#1993',(30.,75.,20.));
#1994=CARTESIAN_POINT('#1994',(30.,65.,20.));
#1995=CARTESIAN_POINT('#1995',(20.,65.,20.));
#1996=CARTESIAN_POINT('#1996',(20.,75.,20.));
#1997=CARTESIAN_POINT('#1997',(30.,75.,0.0));
#1998=CARTESIAN_POINT('#1998',(30.,65.,0.0));
#1999=CARTESIAN_POINT('#1999',(20.,65.,0.0));
#2000=CARTESIAN_POINT('#2000',(20.,75.,0.0));
#2001=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#1993,#1994,#1995,#1996),(#1997,
#1998,#1999,#2000)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#2001')SURFACE());
#2002=ADVANCED_FACE('#2002',(#1992),#2001,.T.);
#2003=ORIENTED_EDGE('#2003',*,*,#1737,.T.);
#2004=ORIENTED_EDGE('#2004',*,*,#1748,.F.);
#2005=ORIENTED_EDGE('#2005',*,*,#1718,.F.);
#2006=ORIENTED_EDGE('#2006',*,*,#1747,.F.);
#2007=EDGE_LOOP('#2007',(#2003,#2004,#2005,#2006));
#2008=FACE_BOUND('#2008',#2007,.T.);
#2009=CARTESIAN_POINT('#2009',(137.5,87.990381057,20.));
#2010=CARTESIAN_POINT('#2010',(137.5,77.990381057,20.));
#2011=CARTESIAN_POINT('#2011',(127.5,77.990381057,20.));
#2012=CARTESIAN_POINT('#2012',(127.5,87.990381057,20.));
#2013=CARTESIAN_POINT('#2013',(137.5,87.990381057,0.0));
#2014=CARTESIAN_POINT('#2014',(137.5,77.990381057,0.0));
#2015=CARTESIAN_POINT('#2015',(127.5,77.990381057,0.0));
#2016=CARTESIAN_POINT('#2016',(127.5,87.990381057,0.0));
#2017=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#2009,#2010,#2011,#2012),(#2013,
#2014,#2015,#2016)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#2017')SURFACE());
#2018=ADVANCED_FACE('#2018',(#2008),#2017,.T.);
#2019=ORIENTED_EDGE('#2019',*,*,#1735,.T.);
#2020=ORIENTED_EDGE('#2020',*,*,#1750,.F.);
#2021=ORIENTED_EDGE('#2021',*,*,#1716,.F.);
#2022=ORIENTED_EDGE('#2022',*,*,#1749,.F.);
#2023=EDGE_LOOP('#2023',(#2019,#2020,#2021,#2022));
#2024=FACE_BOUND('#2024',#2023,.T.);
#2025=CARTESIAN_POINT('#2025',(137.5,62.009618943,20.));
#2026=CARTESIAN_POINT('#2026',(137.5,52.009618943,20.));
#2027=CARTESIAN_POINT('#2027',(127.5,52.009618943,20.));
#2028=CARTESIAN_POINT('#2028',(127.5,62.009618943,20.));
#2029=CARTESIAN_POINT('#2029',(137.5,62.009618943,0.0));
#2030=CARTESIAN_POINT('#2030',(137.5,52.009618943,0.0));
#2031=CARTESIAN_POINT('#2031',(127.5,52.009618943,0.0));
#2032=CARTESIAN_POINT('#2032',(127.5,62.009618943,0.0));
#2033=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#2025,#2026,#2027,#2028),(#2029,
#2030,#2031,#2032)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#2033')SURFACE());
#2034=ADVANCED_FACE('#2034',(#2024),#2033,.T.);
#2035=ORIENTED_EDGE('#2035',*,*,#1733,.T.);
#2036=ORIENTED_EDGE('#2036',*,*,#1752,.F.);
#2037=ORIENTED_EDGE('#2037',*,*,#1714,.F.);
#2038=ORIENTED_EDGE('#2038',*,*,#1751,.F.);
#2039=EDGE_LOOP('#2039',(#2035,#2036,#2037,#2038));
#2040=FACE_BOUND('#2040',#2039,.T.);
#2041=CARTESIAN_POINT('#2041',(52.5,62.009618943,20.));
#2042=CARTESIAN_POINT('#2042',(52.5,52.009618943,20.));
#2043=CARTESIAN_POINT('#2043',(42.5,52.009618943,20.));
#2044=CARTESIAN_POINT('#2044',(42.5,62.009618943,20.));
#2045=CARTESIAN_POINT('#2045',(52.5,62.009618943,0.0));
#2046=CARTESIAN_POINT('#2046',(52.5,52.009618943,0.0));
#2047=CARTESIAN_POINT('#2047',(42.5,52.009618943,0.0));
#2048=CARTESIAN_POINT('#2048',(42.5,62.009618943,0.0));
#2049=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#2041,#2042,#2043,#2044),(#2045,
#2046,#2047,#2048)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#2049')SURFACE());
#2050=ADVANCED_FACE('#2050',(#2040),#2049,.T.);
#2051=ORIENTED_EDGE('#2051',*,*,#1731,.T.);
#2052=ORIENTED_EDGE('#2052',*,*,#1754,.F.);
#2053=ORIENTED_EDGE('#2053',*,*,#1712,.F.);
#2054=ORIENTED_EDGE('#2054',*,*,#1753,.F.);
#2055=EDGE_LOOP('#2055',(#2051,#2052,#2053,#2054));
#2056=FACE_BOUND('#2056',#2055,.T.);
#2057=CARTESIAN_POINT('#2057',(52.5,87.990381057,20.));
#2058=CARTESIAN_POINT('#2058',(52.5,77.990381057,20.));
#2059=CARTESIAN_POINT('#2059',(42.5,77.990381057,20.));
#2060=CARTESIAN_POINT('#2060',(42.5,87.990381057,20.));
#2061=CARTESIAN_POINT('#2061',(52.5,87.990381057,0.0));
#2062=CARTESIAN_POINT('#2062',(52.5,77.990381057,0.0));
#2063=CARTESIAN_POINT('#2063',(42.5,77.990381057,0.0));
#2064=CARTESIAN_POINT('#2064',(42.5,87.990381057,0.0));
#2065=(BOUNDED_SURFACE()B_SPLINE_SURFACE(1,3,((#2057,#2058,#2059,#2060),(#2061,
#2062,#2063,#2064)),.UNSPECIFIED.,.F.,.F.,.F.)B_SPLINE_SURFACE_WITH_KNOTS((2,2),
(4,4),(0.000998003992,0.999001996008),(0.0,0.5),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()RATIONAL_B_SPLINE_SURFACE(((1.0,0.333333333333,
0.333333333333,1.0),(1.0,0.333333333333,0.333333333333,1.0)))REPRESENTATION_ITEM
('#2065')SURFACE());
#2066=ADVANCED_FACE('#2066',(#2056),#2065,.T.);
#2067=CLOSED_SHELL('#2067',(#1790,#1802,#1814,#1826,#1862,#1874,#1890,#1906,
#1922,#1938,#1954,#1970,#1986,#2002,#2018,#2034,#2050,#2066));
#2068=MANIFOLD_SOLID_BREP('#2068',#2067);
#2069=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.5405E-12),#4,
'distance_accuracy_value','EDGE CURVE AND VERTEX POINT ACCURACY');
#2070=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((
#2069))GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3))REPRESENTATION_CONTEXT('plate',
'3D'));
#2071=CARTESIAN_POINT('#2071',(0.0,0.0,0.0));
#2072=DIRECTION('#2072',(1.0,0.0,0.0));
#2073=DIRECTION('#2073',(0.0,0.0,1.0));
#2074=AXIS2_PLACEMENT_3D('#2074',#2071,#2073,#2072);
#2075=ADVANCED_BREP_SHAPE_REPRESENTATION('#2075',(#2068,#2074),#2070);
#2076=DERIVED_UNIT_ELEMENT(#4,3.0);
#2077=DERIVED_UNIT((#2076));
#2078=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#2077);
#2079=DERIVED_UNIT_ELEMENT(#4,2.0);
#2080=DERIVED_UNIT((#2079));
#2081=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#2080);
#2083=SHAPE_ASPECT('#2083','solid #2068',#2082,.F.);
#2084=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(530576.67944901),#2077);
#2085=REPRESENTATION('volume',(#2084),#2070);
#2086=PROPERTY_DEFINITION('geometric validation property','volume of #2068',
#2083);
#2087=PROPERTY_DEFINITION_REPRESENTATION(#2086,#2085);
#2088=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(70027.709746208),#2080);
#2089=REPRESENTATION('surface area',(#2088),#2070);
#2090=PROPERTY_DEFINITION('geometric validation property','area of #2068',#2083)
;
#2091=PROPERTY_DEFINITION_REPRESENTATION(#2090,#2089);
#2092=CARTESIAN_POINT('centre point',(90.,75.,10.));
#2093=REPRESENTATION('centroid',(#2092),#2070);
#2094=PROPERTY_DEFINITION('geometric validation property','centroid of #2068',
#2083);
#2095=PROPERTY_DEFINITION_REPRESENTATION(#2094,#2093);
#2096=SHAPE_REPRESENTATION('',(#2068),#2070);
#2097=PROPERTY_DEFINITION('','Shape for Validation Properties',#2083);
#2098=SHAPE_DEFINITION_REPRESENTATION(#2097,#2096);
#2099=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(530576.67944901),#2077);
#2100=REPRESENTATION('volume',(#2099),#2070);
#2101=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #2075 - plate',#2082);
#2102=PROPERTY_DEFINITION_REPRESENTATION(#2101,#2100);
#2103=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(70027.709746208),#2080);
#2104=REPRESENTATION('surface area',(#2103),#2070);
#2105=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #2075 - plate',#2082);
#2106=PROPERTY_DEFINITION_REPRESENTATION(#2105,#2104);
#2107=CARTESIAN_POINT('centre point',(90.,75.,10.));
#2108=REPRESENTATION('centroid',(#2107),#2070);
#2109=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #2075 - plate',#2082);
#2110=PROPERTY_DEFINITION_REPRESENTATION(#2109,#2108);
#2111=CARTESIAN_POINT('#2111',(0.0,0.0,0.0));
#2112=DIRECTION('#2112',(1.0,0.0,0.0));
#2113=DIRECTION('#2113',(0.0,0.0,1.0));
#2114=AXIS2_PLACEMENT_3D('#2114',#2111,#2113,#2112);
#2115=ITEM_DEFINED_TRANSFORMATION('#2114','MASTER : plate',#2074,#2114);
#2116=CARTESIAN_POINT('#2116',(175.,25.,20.));
#2117=DIRECTION('#2117',(-1.,0.0,0.0));
#2118=DIRECTION('#2118',(0.0,0.0,1.0));
#2119=AXIS2_PLACEMENT_3D('#2119',#2116,#2118,#2117);
#2120=ITEM_DEFINED_TRANSFORMATION('#2119','MASTER : l-bracket-assembly',#1397,
#2119);
#2121=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('as1','3D'));
#2122=CARTESIAN_POINT('#2122',(0.0,0.0,0.0));
#2123=DIRECTION('#2123',(1.0,0.0,0.0));
#2124=DIRECTION('#2124',(0.0,0.0,1.0));
#2125=AXIS2_PLACEMENT_3D('#2125',#2122,#2124,#2123);
#2126=SHAPE_REPRESENTATION('#2126',(#2125,#522,#1425,#2114,#2119),#2121);
#2127=(REPRESENTATION_RELATIONSHIP('#2127','MASTER : rod-assembly',#496,#2126)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#523)
SHAPE_REPRESENTATION_RELATIONSHIP());
#2128=(REPRESENTATION_RELATIONSHIP('#2128','MASTER : l-bracket-assembly',#1398,
#2126)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1426)
SHAPE_REPRESENTATION_RELATIONSHIP());
#2129=(REPRESENTATION_RELATIONSHIP('#2129','MASTER : plate',#2075,#2126)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2115)
SHAPE_REPRESENTATION_RELATIONSHIP());
#2130=(REPRESENTATION_RELATIONSHIP('#2130','MASTER : l-bracket-assembly',#1398,
#2126)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#2120)
SHAPE_REPRESENTATION_RELATIONSHIP());
#2131=DERIVED_UNIT_ELEMENT(#4,3.0);
#2132=DERIVED_UNIT((#2131));
#2133=NAME_ATTRIBUTE('CUBIC MILLIMETRE',#2132);
#2134=DERIVED_UNIT_ELEMENT(#4,2.0);
#2135=DERIVED_UNIT((#2134));
#2136=NAME_ATTRIBUTE('SQUARE MILLIMETRE',#2135);
#2138=MEASURE_REPRESENTATION_ITEM('volume measure',
VOLUME_MEASURE(764518.031066584),#2132);
#2139=REPRESENTATION('volume',(#2138),#2121);
#2140=PROPERTY_DEFINITION('geometric validation property',
'volume of shape rep #2126 - as1',#2137);
#2141=PROPERTY_DEFINITION_REPRESENTATION(#2140,#2139);
#2142=MEASURE_REPRESENTATION_ITEM('surface area measure',
AREA_MEASURE(141079.335225521),#2135);
#2143=REPRESENTATION('surface area',(#2142),#2121);
#2144=PROPERTY_DEFINITION('geometric validation property',
'area of shape rep #2126 - as1',#2137);
#2145=PROPERTY_DEFINITION_REPRESENTATION(#2144,#2143);
#2146=CARTESIAN_POINT('centre point',(90.,74.999999986,18.859468252));
#2147=REPRESENTATION('centroid',(#2146),#2121);
#2148=PROPERTY_DEFINITION('geometric validation property',
'centroid of shape rep #2126 - as1',#2137);
#2149=PROPERTY_DEFINITION_REPRESENTATION(#2148,#2147);
#2137=PRODUCT_DEFINITION_SHAPE('',$,#15);
#2150=SHAPE_DEFINITION_REPRESENTATION(#2137,#2126);
#309=PRODUCT_DEFINITION_SHAPE('',$,#21);
#2151=SHAPE_DEFINITION_REPRESENTATION(#309,#302);
#457=PRODUCT_DEFINITION_SHAPE('',$,#27);
#2152=SHAPE_DEFINITION_REPRESENTATION(#457,#450);
#506=PRODUCT_DEFINITION_SHAPE('',$,#33);
#2153=SHAPE_DEFINITION_REPRESENTATION(#506,#496);
#721=PRODUCT_DEFINITION_SHAPE('',$,#39);
#2154=SHAPE_DEFINITION_REPRESENTATION(#721,#714);
#774=PRODUCT_DEFINITION_SHAPE('',$,#45);
#2155=SHAPE_DEFINITION_REPRESENTATION(#774,#765);
#1359=PRODUCT_DEFINITION_SHAPE('',$,#51);
#2156=SHAPE_DEFINITION_REPRESENTATION(#1359,#1352);
#1409=PRODUCT_DEFINITION_SHAPE('',$,#57);
#2157=SHAPE_DEFINITION_REPRESENTATION(#1409,#1398);
#2082=PRODUCT_DEFINITION_SHAPE('',$,#63);
#2158=SHAPE_DEFINITION_REPRESENTATION(#2082,#2075);
#2159=PRESENTATION_LAYER_ASSIGNMENT('256','layer 256',(#295,#443,#707,#1345,
#2068));
#2160=DRAUGHTING_PRE_DEFINED_COLOUR('red');
#2161=FILL_AREA_STYLE_COLOUR('',#2160);
#2162=FILL_AREA_STYLE('',(#2161));
#2163=SURFACE_STYLE_FILL_AREA(#2162);
#2164=SURFACE_SIDE_STYLE('',(#2163));
#2165=SURFACE_STYLE_USAGE(.BOTH.,#2164);
#2166=PRESENTATION_STYLE_ASSIGNMENT((#2165));
#2167=STYLED_ITEM('',(#2166),#295);
#2168=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('Presentation Context for nut','3D'));
#2169=CARTESIAN_POINT('#2169',(0.0,0.0,0.0));
#2170=DIRECTION('#2170',(1.0,0.0,0.0));
#2171=DIRECTION('#2171',(0.0,0.0,1.0));
#2172=AXIS2_PLACEMENT_3D('#2172',#2169,#2171,#2170);
#2173=REPRESENTATION_MAP(#301,#302);
#2174=MAPPED_ITEM('#2174',#2173,#2172);
#2175=DRAUGHTING_MODEL('#2175',(#2167,#2172,#2174),#2168);
#2176=COLOUR_RGB('',1.0,0.56862745098,0.0);
#2177=FILL_AREA_STYLE_COLOUR('',#2176);
#2178=FILL_AREA_STYLE('',(#2177));
#2179=SURFACE_STYLE_FILL_AREA(#2178);
#2180=SURFACE_SIDE_STYLE('',(#2179));
#2181=SURFACE_STYLE_USAGE(.BOTH.,#2180);
#2182=PRESENTATION_STYLE_ASSIGNMENT((#2181));
#2183=STYLED_ITEM('',(#2182),#443);
#2184=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('Presentation Context for rod','3D'));
#2185=CARTESIAN_POINT('#2185',(0.0,0.0,0.0));
#2186=DIRECTION('#2186',(1.0,0.0,0.0));
#2187=DIRECTION('#2187',(0.0,0.0,1.0));
#2188=AXIS2_PLACEMENT_3D('#2188',#2185,#2187,#2186);
#2189=REPRESENTATION_MAP(#449,#450);
#2190=MAPPED_ITEM('#2190',#2189,#2188);
#2191=DRAUGHTING_MODEL('#2191',(#2183,#2188,#2190),#2184);
#2192=DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#2193=FILL_AREA_STYLE_COLOUR('',#2192);
#2194=FILL_AREA_STYLE('',(#2193));
#2195=SURFACE_STYLE_FILL_AREA(#2194);
#2196=SURFACE_SIDE_STYLE('',(#2195));
#2197=SURFACE_STYLE_USAGE(.BOTH.,#2196);
#2198=PRESENTATION_STYLE_ASSIGNMENT((#2197));
#2199=STYLED_ITEM('',(#2198),#707);
#2200=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('Presentation Context for bolt','3D'));
#2201=CARTESIAN_POINT('#2201',(0.0,0.0,0.0));
#2202=DIRECTION('#2202',(1.0,0.0,0.0));
#2203=DIRECTION('#2203',(0.0,0.0,1.0));
#2204=AXIS2_PLACEMENT_3D('#2204',#2201,#2203,#2202);
#2205=REPRESENTATION_MAP(#713,#714);
#2206=MAPPED_ITEM('#2206',#2205,#2204);
#2207=DRAUGHTING_MODEL('#2207',(#2199,#2204,#2206),#2200);
#2208=DRAUGHTING_PRE_DEFINED_COLOUR('green');
#2209=FILL_AREA_STYLE_COLOUR('',#2208);
#2210=FILL_AREA_STYLE('',(#2209));
#2211=SURFACE_STYLE_FILL_AREA(#2210);
#2212=SURFACE_SIDE_STYLE('',(#2211));
#2213=SURFACE_STYLE_USAGE(.BOTH.,#2212);
#2214=PRESENTATION_STYLE_ASSIGNMENT((#2213));
#2215=STYLED_ITEM('',(#2214),#1345);
#2216=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('Presentation Context for l-bracket','3D'));
#2217=CARTESIAN_POINT('#2217',(0.0,0.0,0.0));
#2218=DIRECTION('#2218',(1.0,0.0,0.0));
#2219=DIRECTION('#2219',(0.0,0.0,1.0));
#2220=AXIS2_PLACEMENT_3D('#2220',#2217,#2219,#2218);
#2221=REPRESENTATION_MAP(#1351,#1352);
#2222=MAPPED_ITEM('#2222',#2221,#2220);
#2223=DRAUGHTING_MODEL('#2223',(#2215,#2220,#2222),#2216);
#2224=COLOUR_RGB('',0.780392156863,0.780392156863,0.0);
#2225=FILL_AREA_STYLE_COLOUR('',#2224);
#2226=FILL_AREA_STYLE('',(#2225));
#2227=SURFACE_STYLE_FILL_AREA(#2226);
#2228=SURFACE_SIDE_STYLE('',(#2227));
#2229=SURFACE_STYLE_USAGE(.BOTH.,#2228);
#2230=PRESENTATION_STYLE_ASSIGNMENT((#2229));
#2231=STYLED_ITEM('',(#2230),#2068);
#2232=(GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNIT_ASSIGNED_CONTEXT((#4,#5,#3
))REPRESENTATION_CONTEXT('Presentation Context for plate','3D'));
#2233=CARTESIAN_POINT('#2233',(0.0,0.0,0.0));
#2234=DIRECTION('#2234',(1.0,0.0,0.0));
#2235=DIRECTION('#2235',(0.0,0.0,1.0));
#2236=AXIS2_PLACEMENT_3D('#2236',#2233,#2235,#2234);
#2237=REPRESENTATION_MAP(#2074,#2075);
#2238=MAPPED_ITEM('#2238',#2237,#2236);
#2239=DRAUGHTING_MODEL('#2239',(#2231,#2236,#2238),#2232);
#2240=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT::1','','NUT::1',#33,#21,'NUT::1');
#2241=PRODUCT_DEFINITION_SHAPE('NUT::1',$,#2240);
#2242=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#497,#2241);
#2243=CARTESIAN_POINT('centre point',(0.0,0.0,186.5));
#2244=REPRESENTATION('centroid',(#2243),#491);
#2245=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT::1',#2241);
#2246=PROPERTY_DEFINITION_REPRESENTATION(#2245,#2244);
#2247=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT::2','','NUT::2',#33,#21,'NUT::2');
#2248=PRODUCT_DEFINITION_SHAPE('NUT::2',$,#2247);
#2249=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#498,#2248);
#2250=CARTESIAN_POINT('centre point',(0.0,0.0,13.5));
#2251=REPRESENTATION('centroid',(#2250),#491);
#2252=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT::2',#2248);
#2253=PROPERTY_DEFINITION_REPRESENTATION(#2252,#2251);
#2254=NEXT_ASSEMBLY_USAGE_OCCURRENCE('ROD','','ROD',#33,#27,'ROD');
#2255=PRODUCT_DEFINITION_SHAPE('ROD',$,#2254);
#2256=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#499,#2255);
#2257=CARTESIAN_POINT('centre point',(0.0,0.0,100.));
#2258=REPRESENTATION('centroid',(#2257),#491);
#2259=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - ROD',#2255);
#2260=PROPERTY_DEFINITION_REPRESENTATION(#2259,#2258);
#2261=NEXT_ASSEMBLY_USAGE_OCCURRENCE('ROD-ASSEMBLY','','ROD-ASSEMBLY',#15,#33,
'ROD-ASSEMBLY');
#2262=PRODUCT_DEFINITION_SHAPE('ROD-ASSEMBLY',$,#2261);
#2263=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2127,#2262);
#2264=CARTESIAN_POINT('centre point',(90.,75.,60.));
#2265=REPRESENTATION('centroid',(#2264),#2121);
#2266=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - ROD-ASSEMBLY',#2262);
#2267=PROPERTY_DEFINITION_REPRESENTATION(#2266,#2265);
#2268=NEXT_ASSEMBLY_USAGE_OCCURRENCE('BOLT','','BOLT',#45,#39,'BOLT');
#2269=PRODUCT_DEFINITION_SHAPE('BOLT',$,#2268);
#2270=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#766,#2269);
#2271=CARTESIAN_POINT('centre point',(-7.5,-10.,-3.935582568));
#2272=REPRESENTATION('centroid',(#2271),#760);
#2273=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - BOLT',#2269);
#2274=PROPERTY_DEFINITION_REPRESENTATION(#2273,#2272);
#2275=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT::3','','NUT::3',#45,#21,'NUT::3');
#2276=PRODUCT_DEFINITION_SHAPE('NUT::3',$,#2275);
#2277=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#767,#2276);
#2278=CARTESIAN_POINT('centre point',(-7.5,-10.,-21.5));
#2279=REPRESENTATION('centroid',(#2278),#760);
#2280=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT::3',#2276);
#2281=PROPERTY_DEFINITION_REPRESENTATION(#2280,#2279);
#2282=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT-BOLT-ASSEMBLY::1','',
'NUT-BOLT-ASSEMBLY::1',#57,#45,'NUT-BOLT-ASSEMBLY::1');
#2283=PRODUCT_DEFINITION_SHAPE('NUT-BOLT-ASSEMBLY::1',$,#2282);
#2284=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1399,#2283);
#2285=CARTESIAN_POINT('centre point',(20.,-50.,-6.954942431));
#2286=REPRESENTATION('centroid',(#2285),#1393);
#2287=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT-BOLT-ASSEMBLY::1',#2283);
#2288=PROPERTY_DEFINITION_REPRESENTATION(#2287,#2286);
#2289=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT-BOLT-ASSEMBLY::2','',
'NUT-BOLT-ASSEMBLY::2',#57,#45,'NUT-BOLT-ASSEMBLY::2');
#2290=PRODUCT_DEFINITION_SHAPE('NUT-BOLT-ASSEMBLY::2',$,#2289);
#2291=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1400,#2290);
#2292=CARTESIAN_POINT('centre point',(42.5,-62.990381057,-6.954942431));
#2293=REPRESENTATION('centroid',(#2292),#1393);
#2294=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT-BOLT-ASSEMBLY::2',#2290);
#2295=PROPERTY_DEFINITION_REPRESENTATION(#2294,#2293);
#2296=NEXT_ASSEMBLY_USAGE_OCCURRENCE('NUT-BOLT-ASSEMBLY::3','',
'NUT-BOLT-ASSEMBLY::3',#57,#45,'NUT-BOLT-ASSEMBLY::3');
#2297=PRODUCT_DEFINITION_SHAPE('NUT-BOLT-ASSEMBLY::3',$,#2296);
#2298=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1401,#2297);
#2299=CARTESIAN_POINT('centre point',(42.5,-37.009618943,-6.954942431));
#2300=REPRESENTATION('centroid',(#2299),#1393);
#2301=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - NUT-BOLT-ASSEMBLY::3',#2297);
#2302=PROPERTY_DEFINITION_REPRESENTATION(#2301,#2300);
#2303=NEXT_ASSEMBLY_USAGE_OCCURRENCE('L-BRACKET','','L-BRACKET',#57,#51,
'L-BRACKET');
#2304=PRODUCT_DEFINITION_SHAPE('L-BRACKET',$,#2303);
#2305=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1402,#2304);
#2306=CARTESIAN_POINT('centre point',(14.594563778,-50.,20.2027181));
#2307=REPRESENTATION('centroid',(#2306),#1393);
#2308=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - L-BRACKET',#2304);
#2309=PROPERTY_DEFINITION_REPRESENTATION(#2308,#2307);
#2310=NEXT_ASSEMBLY_USAGE_OCCURRENCE('L-BRACKET-ASSEMBLY::1','',
'L-BRACKET-ASSEMBLY::1',#15,#57,'L-BRACKET-ASSEMBLY::1');
#2311=PRODUCT_DEFINITION_SHAPE('L-BRACKET-ASSEMBLY::1',$,#2310);
#2312=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2128,#2311);
#2313=CARTESIAN_POINT('centre point',(21.776093787,75.,37.299312811));
#2314=REPRESENTATION('centroid',(#2313),#2121);
#2315=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - L-BRACKET-ASSEMBLY::1',#2311);
#2316=PROPERTY_DEFINITION_REPRESENTATION(#2315,#2314);
#2317=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PLATE','','PLATE',#15,#63,'PLATE');
#2318=PRODUCT_DEFINITION_SHAPE('PLATE',$,#2317);
#2319=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2129,#2318);
#2320=CARTESIAN_POINT('centre point',(90.,74.999999979,10.));
#2321=REPRESENTATION('centroid',(#2320),#2121);
#2322=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - PLATE',#2318);
#2323=PROPERTY_DEFINITION_REPRESENTATION(#2322,#2321);
#2324=NEXT_ASSEMBLY_USAGE_OCCURRENCE('L-BRACKET-ASSEMBLY::2','',
'L-BRACKET-ASSEMBLY::2',#15,#57,'L-BRACKET-ASSEMBLY::2');
#2325=PRODUCT_DEFINITION_SHAPE('L-BRACKET-ASSEMBLY::2',$,#2324);
#2326=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#2130,#2325);
#2327=CARTESIAN_POINT('centre point',(158.223906213,75.,37.299312811));
#2328=REPRESENTATION('centroid',(#2327),#2121);
#2329=PROPERTY_DEFINITION('geometric validation property',
'centroid of instance - L-BRACKET-ASSEMBLY::2',#2325);
#2330=PROPERTY_DEFINITION_REPRESENTATION(#2329,#2328);
#2331=PROPERTY_DEFINITION('assembly validation property','',#15);
#2332=VALUE_REPRESENTATION_ITEM('number of children',COUNT_MEASURE(4.0));
#2333=REPRESENTATION('number of children',(#2332),#2121);
#2334=PROPERTY_DEFINITION_REPRESENTATION(#2331,#2333);
#2335=PROPERTY_DEFINITION('assembly validation property',
'notional solids centroid',#2137);
#2336=CARTESIAN_POINT('centre point',(47.5,61.25,30.));
#2337=REPRESENTATION('notional solids centroid',(#2336),#2121);
#2338=PROPERTY_DEFINITION_REPRESENTATION(#2335,#2337);
#2339=PROPERTY_DEFINITION('assembly validation property','',#33);
#2340=VALUE_REPRESENTATION_ITEM('number of children',COUNT_MEASURE(3.0));
#2341=REPRESENTATION('number of children',(#2340),#491);
#2342=PROPERTY_DEFINITION_REPRESENTATION(#2339,#2341);
#2343=PROPERTY_DEFINITION('assembly validation property',
'notional solids centroid',#506);
#2344=CARTESIAN_POINT('centre point',(3.333333333,5.0,75.666666667));
#2345=REPRESENTATION('notional solids centroid',(#2344),#491);
#2346=PROPERTY_DEFINITION_REPRESENTATION(#2343,#2345);
#2347=PROPERTY_DEFINITION('assembly validation property','',#45);
#2348=VALUE_REPRESENTATION_ITEM('number of children',COUNT_MEASURE(2.0));
#2349=REPRESENTATION('number of children',(#2348),#760);
#2350=PROPERTY_DEFINITION_REPRESENTATION(#2347,#2349);
#2351=PROPERTY_DEFINITION('assembly validation property',
'notional solids centroid',#774);
#2352=CARTESIAN_POINT('centre point',(-12.5,-13.75,-13.5));
#2353=REPRESENTATION('notional solids centroid',(#2352),#760);
#2354=PROPERTY_DEFINITION_REPRESENTATION(#2351,#2353);
#2355=PROPERTY_DEFINITION('assembly validation property','',#57);
#2356=VALUE_REPRESENTATION_ITEM('number of children',COUNT_MEASURE(4.0));
#2357=REPRESENTATION('number of children',(#2356),#1393);
#2358=PROPERTY_DEFINITION_REPRESENTATION(#2355,#2357);
#2359=PROPERTY_DEFINITION('assembly validation property',
'notional solids centroid',#1409);
#2360=CARTESIAN_POINT('centre point',(41.875,-25.,10.));
#2361=REPRESENTATION('notional solids centroid',(#2360),#1393);
#2362=PROPERTY_DEFINITION_REPRESENTATION(#2359,#2361);
ENDSEC;
END-ISO-10303-21;
