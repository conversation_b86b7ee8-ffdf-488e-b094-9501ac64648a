import gmsh
import sys

gmsh.initialize(sys.argv)

# this example shows how alternative non-plane geometries can be used in the
# built-in kernel, useful e.g. for ocean modelling

gmsh.model.geo.addGeometry("PolarSphere", [0,0,0,1], tag=1)

gmsh.model.geo.addPointOnGeometry(1, -0.192501, 0.354324, tag=3)
gmsh.model.geo.addPointOnGeometry(1, -0.203205, 0.349771, tag=4)
gmsh.model.geo.addPointOnGeometry(1, -0.202949, 0.349357, tag=5)
gmsh.model.geo.addPointOnGeometry(1, -0.193411, 0.342772, tag=6)
gmsh.model.geo.addPointOnGeometry(1, 0.0988705, -0.0890255, tag=7)
gmsh.model.geo.addPointOnGeometry(1, 0.104863, -0.080801, tag=8)
gmsh.model.geo.addPointOnGeometry(1, 0.0963105, -0.0753115, tag=9)
gmsh.model.geo.addPointOnGeometry(1, 0.0921205, -0.08456, tag=10)
gmsh.model.geo.addPointOnGeometry(1, -0.473756, -0.116656, tag=11)
gmsh.model.geo.addPointOnGeometry(1, -0.483641, -0.124205, tag=12)
gmsh.model.geo.addPointOnGeometry(1, -0.484734, -0.129884, tag=13)
gmsh.model.geo.addPointOnGeometry(1, -0.472311, -0.128963, tag=14)
gmsh.model.geo.addPointOnGeometry(1, 0.36271, -0.388383, tag=15)
gmsh.model.geo.addPointOnGeometry(1, 0.36578, -0.400897, tag=16)
gmsh.model.geo.addPointOnGeometry(1, 0.370419, -0.397228, tag=17)
gmsh.model.geo.addPointOnGeometry(1, 0.375171, -0.385212, tag=18)
gmsh.model.geo.addPointOnGeometry(1, 0.0268657, 0.159489, tag=19)
gmsh.model.geo.addPointOnGeometry(1, 0.0195572, 0.159404, tag=20)
gmsh.model.geo.addPointOnGeometry(1, 0.0189496, 0.149178, tag=21)
gmsh.model.geo.addPointOnGeometry(1, 0.0235173, 0.140042, tag=22)
gmsh.model.geo.addPointOnGeometry(1, 0.0278574, 0.149291, tag=23)
gmsh.model.geo.addPointOnGeometry(1, 0.0146395, 0.148359, tag=24)
gmsh.model.geo.addPointOnGeometry(1, 0.0137733, 0.157928, tag=25)
gmsh.model.geo.addPointOnGeometry(1, 0.00563075, 0.151714, tag=26)
gmsh.model.geo.addPointOnGeometry(1, 0.000966875, 0.142622, tag=27)
gmsh.model.geo.addPointOnGeometry(1, 0.0105109, 0.139022, tag=28)
gmsh.model.geo.addPointOnGeometry(1, 0.431623, -0.688045, tag=29)
gmsh.model.geo.addPointOnGeometry(1, 0.438862, -0.70313, tag=30)
gmsh.model.geo.addPointOnGeometry(1, 0.441195, -0.70606, tag=31)
gmsh.model.geo.addPointOnGeometry(1, 0.448101, -0.690685, tag=32)
gmsh.model.geo.addPointOnGeometry(1, -0.0997495, -0.137292, tag=33)
gmsh.model.geo.addPointOnGeometry(1, -0.0920615, -0.144132, tag=34)
gmsh.model.geo.addPointOnGeometry(1, -0.091229, -0.133888, tag=35)
gmsh.model.geo.addPointOnGeometry(1, -0.0852685, -0.125553, tag=36)
gmsh.model.geo.addPointOnGeometry(1, -0.095412, -0.124167, tag=37)
gmsh.model.geo.addPointOnGeometry(1, -0.0980435, -0.134085, tag=38)
gmsh.model.geo.addPointOnGeometry(1, -0.465075, -0.069084, tag=39)
gmsh.model.geo.addPointOnGeometry(1, -0.471801, -0.070089, tag=40)
gmsh.model.geo.addPointOnGeometry(1, -0.463215, -0.0788145, tag=41)
gmsh.model.geo.addPointOnGeometry(1, -0.451215, -0.076901, tag=42)
gmsh.model.geo.addPointOnGeometry(1, -0.453612, -0.065043, tag=43)
gmsh.model.geo.addPointOnGeometry(1, 0.056777, 0.118775, tag=44)
gmsh.model.geo.addPointOnGeometry(1, 0.0467583, 0.120526, tag=45)
gmsh.model.geo.addPointOnGeometry(1, 0.0385371, 0.126515, tag=46)
gmsh.model.geo.addPointOnGeometry(1, 0.0373494, 0.116422, tag=47)
gmsh.model.geo.addPointOnGeometry(1, 0.0463851, 0.1118, tag=48)
gmsh.model.geo.addPointOnGeometry(1, 0.056521, 0.111229, tag=49)
gmsh.model.geo.addPointOnGeometry(1, -0.0143839, 0.16419, tag=50)
gmsh.model.geo.addPointOnGeometry(1, -0.00997395, 0.154931, tag=51)
gmsh.model.geo.addPointOnGeometry(1, -0.0111115, 0.144767, tag=52)
gmsh.model.geo.addPointOnGeometry(1, -0.00180002, 0.148973, tag=53)
gmsh.model.geo.addPointOnGeometry(1, 0.000324027, 0.158988, tag=54)
gmsh.model.geo.addPointOnGeometry(1, -0.0069292, 0.166253, tag=55)
gmsh.model.geo.addPointOnGeometry(1, 0.0070838, 0.0960695, tag=56)
gmsh.model.geo.addPointOnGeometry(1, 0.000641925, 0.103849, tag=57)
gmsh.model.geo.addPointOnGeometry(1, -0.00576835, 0.0960435, tag=58)
gmsh.model.geo.addPointOnGeometry(1, -0.0028407, 0.086394, tag=59)
gmsh.model.geo.addPointOnGeometry(1, 0.00237201, 0.0777795, tag=60)
gmsh.model.geo.addPointOnGeometry(1, 0.0071041, 0.0866665, tag=61)
gmsh.model.geo.addPointOnGeometry(1, -0.0321427, 0.223762, tag=62)
gmsh.model.geo.addPointOnGeometry(1, -0.0228856, 0.218809, tag=63)
gmsh.model.geo.addPointOnGeometry(1, -0.0142591, 0.22479, tag=64)
gmsh.model.geo.addPointOnGeometry(1, -0.0180399, 0.234619, tag=65)
gmsh.model.geo.addPointOnGeometry(1, -0.0265167, 0.228353, tag=66)
gmsh.model.geo.addPointOnGeometry(1, -0.0360002, 0.232958, tag=67)
gmsh.model.geo.addPointOnGeometry(1, -0.0369471, 0.233137, tag=68)
gmsh.model.geo.addPointOnGeometry(1, 0.218043, 0.303729, tag=69)
gmsh.model.geo.addPointOnGeometry(1, 0.213005, 0.313976, tag=70)
gmsh.model.geo.addPointOnGeometry(1, 0.210623, 0.302827, tag=71)
gmsh.model.geo.addPointOnGeometry(1, 0.211377, 0.291524, tag=72)
gmsh.model.geo.addPointOnGeometry(1, 0.2174, 0.281983, tag=73)
gmsh.model.geo.addPointOnGeometry(1, 0.218227, 0.293256, tag=74)
gmsh.model.geo.addPointOnGeometry(1, -0.100175, -0.0334128, tag=75)
gmsh.model.geo.addPointOnGeometry(1, -0.090101, -0.0326662, tag=76)
gmsh.model.geo.addPointOnGeometry(1, -0.083669, -0.024898, tag=77)
gmsh.model.geo.addPointOnGeometry(1, -0.089853, -0.0169376, tag=78)
gmsh.model.geo.addPointOnGeometry(1, -0.0987015, -0.0217936, tag=79)
gmsh.model.geo.addPointOnGeometry(1, -0.102163, -0.0312904, tag=80)
gmsh.model.geo.addPointOnGeometry(1, -0.109815, -0.0303298, tag=81)
gmsh.model.geo.addPointOnGeometry(1, 0.396907, -0.753705, tag=82)
gmsh.model.geo.addPointOnGeometry(1, 0.401796, -0.75566, tag=83)
gmsh.model.geo.addPointOnGeometry(1, 0.404688, -0.738695, tag=84)
gmsh.model.geo.addPointOnGeometry(1, 0.411748, -0.723215, tag=85)
gmsh.model.geo.addPointOnGeometry(1, 0.402538, -0.737495, tag=86)
gmsh.model.geo.addPointOnGeometry(1, 0.34603, -0.578985, tag=87)
gmsh.model.geo.addPointOnGeometry(1, 0.343859, -0.56469, tag=88)
gmsh.model.geo.addPointOnGeometry(1, 0.339684, -0.551035, tag=89)
gmsh.model.geo.addPointOnGeometry(1, 0.32818, -0.559355, tag=90)
gmsh.model.geo.addPointOnGeometry(1, 0.332908, -0.572845, tag=91)
gmsh.model.geo.addPointOnGeometry(1, 0.345075, -0.580695, tag=92)
gmsh.model.geo.addPointOnGeometry(1, 0.226924, -0.671235, tag=93)
gmsh.model.geo.addPointOnGeometry(1, 0.234977, -0.682415, tag=94)
gmsh.model.geo.addPointOnGeometry(1, 0.247905, -0.67445, tag=95)
gmsh.model.geo.addPointOnGeometry(1, 0.250756, -0.65965, tag=96)
gmsh.model.geo.addPointOnGeometry(1, 0.23582, -0.659205, tag=97)
gmsh.model.geo.addPointOnGeometry(1, -0.0550305, -0.112902, tag=98)
gmsh.model.geo.addPointOnGeometry(1, -0.0448983, -0.112268, tag=99)
gmsh.model.geo.addPointOnGeometry(1, -0.0539575, -0.107699, tag=100)
gmsh.model.geo.addPointOnGeometry(1, -0.0640075, -0.106279, tag=101)
gmsh.model.geo.addPointOnGeometry(1, -0.073793, -0.109025, tag=102)
gmsh.model.geo.addPointOnGeometry(1, -0.0821775, -0.114811, tag=103)
gmsh.model.geo.addPointOnGeometry(1, -0.0739375, -0.119521, tag=104)
gmsh.model.geo.addPointOnGeometry(1, -0.0650675, -0.114513, tag=105)
gmsh.model.geo.addPointOnGeometry(1, 0.61693, -0.794725, tag=106)
gmsh.model.geo.addPointOnGeometry(1, 0.6189, -0.792185, tag=107)
gmsh.model.geo.addPointOnGeometry(1, 0.61011, -0.774315, tag=108)
gmsh.model.geo.addPointOnGeometry(1, 0.59354, -0.763965, tag=109)
gmsh.model.geo.addPointOnGeometry(1, 0.60191, -0.78163, tag=110)
gmsh.model.geo.addPointOnGeometry(1, 0.0697695, 0.118383, tag=111)
gmsh.model.geo.addPointOnGeometry(1, 0.0783785, 0.112932, tag=112)
gmsh.model.geo.addPointOnGeometry(1, 0.083981, 0.121464, tag=113)
gmsh.model.geo.addPointOnGeometry(1, 0.0919425, 0.127894, tag=114)
gmsh.model.geo.addPointOnGeometry(1, 0.0911055, 0.13812, tag=115)
gmsh.model.geo.addPointOnGeometry(1, 0.080845, 0.137807, tag=116)
gmsh.model.geo.addPointOnGeometry(1, 0.0799855, 0.136702, tag=117)
gmsh.model.geo.addPointOnGeometry(1, 0.0705425, 0.132745, tag=118)
gmsh.model.geo.addPointOnGeometry(1, 0.062928, 0.12594, tag=119)
gmsh.model.geo.addPointOnGeometry(1, 0.367654, -0.416038, tag=120)
gmsh.model.geo.addPointOnGeometry(1, 0.364326, -0.403452, tag=121)
gmsh.model.geo.addPointOnGeometry(1, 0.351417, -0.403641, tag=122)
gmsh.model.geo.addPointOnGeometry(1, 0.344465, -0.41449, tag=123)
gmsh.model.geo.addPointOnGeometry(1, 0.357145, -0.417173, tag=124)
gmsh.model.geo.addPointOnGeometry(1, 0.362621, -0.429057, tag=125)
gmsh.model.geo.addPointOnGeometry(1, 0.368699, -0.429138, tag=126)
gmsh.model.geo.addPointOnGeometry(1, -0.0236358, 0.131058, tag=127)
gmsh.model.geo.addPointOnGeometry(1, -0.0172388, 0.123156, tag=128)
gmsh.model.geo.addPointOnGeometry(1, -0.00823225, 0.127861, tag=129)
gmsh.model.geo.addPointOnGeometry(1, 0.000695355, 0.123013, tag=130)
gmsh.model.geo.addPointOnGeometry(1, 0.0070753, 0.115127, tag=131)
gmsh.model.geo.addPointOnGeometry(1, 0.00474773, 0.125002, tag=132)
gmsh.model.geo.addPointOnGeometry(1, 0.00496561, 0.131666, tag=133)
gmsh.model.geo.addPointOnGeometry(1, -0.00414834, 0.136202, tag=134)
gmsh.model.geo.addPointOnGeometry(1, -0.0142674, 0.135044, tag=135)
gmsh.model.geo.addPointOnGeometry(1, 0.650705, -0.82524, tag=136)
gmsh.model.geo.addPointOnGeometry(1, 0.64955, -0.831435, tag=137)
gmsh.model.geo.addPointOnGeometry(1, 0.668805, -0.822605, tag=138)
gmsh.model.geo.addPointOnGeometry(1, 0.687495, -0.81241, tag=139)
gmsh.model.geo.addPointOnGeometry(1, 0.666355, -0.811185, tag=140)
gmsh.model.geo.addPointOnGeometry(1, 0.95995, -0.496396, tag=141)
gmsh.model.geo.addPointOnGeometry(1, 0.957135, -0.487717, tag=142)
gmsh.model.geo.addPointOnGeometry(1, 0.937295, -0.495718, tag=143)
gmsh.model.geo.addPointOnGeometry(1, 0.91931, -0.506815, tag=144)
gmsh.model.geo.addPointOnGeometry(1, 0.938525, -0.497967, tag=145)
gmsh.model.geo.addPointOnGeometry(1, 0.630325, -0.976385, tag=146)
gmsh.model.geo.addPointOnGeometry(1, 0.63028, -0.970535, tag=147)
gmsh.model.geo.addPointOnGeometry(1, 0.61239, -0.98566, tag=148)
gmsh.model.geo.addPointOnGeometry(1, 0.590755, -0.994645, tag=149)
gmsh.model.geo.addPointOnGeometry(1, 0.61425, -0.993635, tag=150)
gmsh.model.geo.addPointOnGeometry(1, -0.309957, 0.0389143, tag=151)
gmsh.model.geo.addPointOnGeometry(1, -0.317069, 0.04731, tag=152)
gmsh.model.geo.addPointOnGeometry(1, -0.323207, 0.056501, tag=153)
gmsh.model.geo.addPointOnGeometry(1, -0.333519, 0.0523695, tag=154)
gmsh.model.geo.addPointOnGeometry(1, -0.339693, 0.061673, tag=155)
gmsh.model.geo.addPointOnGeometry(1, -0.34366, 0.051199, tag=156)
gmsh.model.geo.addPointOnGeometry(1, -0.342332, 0.0480932, tag=157)
gmsh.model.geo.addPointOnGeometry(1, -0.338276, 0.037678, tag=158)
gmsh.model.geo.addPointOnGeometry(1, -0.327504, 0.0349081, tag=159)
gmsh.model.geo.addPointOnGeometry(1, -0.317281, 0.0307116, tag=160)
gmsh.model.geo.addPointOnGeometry(1, -0.192378, 0.079943, tag=161)
gmsh.model.geo.addPointOnGeometry(1, -0.197085, 0.089274, tag=162)
gmsh.model.geo.addPointOnGeometry(1, -0.20413, 0.081521, tag=163)
gmsh.model.geo.addPointOnGeometry(1, -0.213546, 0.086184, tag=164)
gmsh.model.geo.addPointOnGeometry(1, -0.221537, 0.0793085, tag=165)
gmsh.model.geo.addPointOnGeometry(1, -0.2224, 0.068796, tag=166)
gmsh.model.geo.addPointOnGeometry(1, -0.222581, 0.068053, tag=167)
gmsh.model.geo.addPointOnGeometry(1, -0.21982, 0.057892, tag=168)
gmsh.model.geo.addPointOnGeometry(1, -0.21163, 0.0513275, tag=169)
gmsh.model.geo.addPointOnGeometry(1, -0.202692, 0.0567565, tag=170)
gmsh.model.geo.addPointOnGeometry(1, -0.202128, 0.0671905, tag=171)
gmsh.model.geo.addPointOnGeometry(1, -0.202574, 0.0776435, tag=172)
gmsh.model.geo.addPointOnGeometry(1, -0.124482, -0.87141, tag=173)
gmsh.model.geo.addPointOnGeometry(1, -0.129431, -0.85451, tag=174)
gmsh.model.geo.addPointOnGeometry(1, -0.135947, -0.83844, tag=175)
gmsh.model.geo.addPointOnGeometry(1, -0.150669, -0.847555, tag=176)
gmsh.model.geo.addPointOnGeometry(1, -0.155293, -0.864495, tag=177)
gmsh.model.geo.addPointOnGeometry(1, -0.15503, -0.882365, tag=178)
gmsh.model.geo.addPointOnGeometry(1, -0.152362, -0.88747, tag=179)
gmsh.model.geo.addPointOnGeometry(1, -0.134324, -0.886355, tag=180)
gmsh.model.geo.addPointOnGeometry(1, 0.330194, -0.260902, tag=181)
gmsh.model.geo.addPointOnGeometry(1, 0.337672, -0.270056, tag=182)
gmsh.model.geo.addPointOnGeometry(1, 0.33539, -0.281729, tag=183)
gmsh.model.geo.addPointOnGeometry(1, 0.344655, -0.28931, tag=184)
gmsh.model.geo.addPointOnGeometry(1, 0.339527, -0.278487, tag=185)
gmsh.model.geo.addPointOnGeometry(1, 0.34801, -0.270093, tag=186)
gmsh.model.geo.addPointOnGeometry(1, 0.356783, -0.266331, tag=187)
gmsh.model.geo.addPointOnGeometry(1, 0.352602, -0.255148, tag=188)
gmsh.model.geo.addPointOnGeometry(1, 0.356254, -0.243845, tag=189)
gmsh.model.geo.addPointOnGeometry(1, 0.34507, -0.247715, tag=190)
gmsh.model.geo.addPointOnGeometry(1, 0.334115, -0.25204, tag=191)
gmsh.model.geo.addPointOnGeometry(1, 0.322402, -0.252142, tag=192)
gmsh.model.geo.addPointOnGeometry(1, -0.198604, 0.291557, tag=193)
gmsh.model.geo.addPointOnGeometry(1, -0.193098, 0.301382, tag=194)
gmsh.model.geo.addPointOnGeometry(1, -0.196062, 0.312307, tag=195)
gmsh.model.geo.addPointOnGeometry(1, -0.195506, 0.323688, tag=196)
gmsh.model.geo.addPointOnGeometry(1, -0.206145, 0.327964, tag=197)
gmsh.model.geo.addPointOnGeometry(1, -0.215257, 0.320951, tag=198)
gmsh.model.geo.addPointOnGeometry(1, -0.224807, 0.323463, tag=199)
gmsh.model.geo.addPointOnGeometry(1, -0.225882, 0.311996, tag=200)
gmsh.model.geo.addPointOnGeometry(1, -0.235035, 0.319003, tag=201)
gmsh.model.geo.addPointOnGeometry(1, -0.234107, 0.307508, tag=202)
gmsh.model.geo.addPointOnGeometry(1, -0.222665, 0.30832, tag=203)
gmsh.model.geo.addPointOnGeometry(1, -0.21423, 0.300645, tag=204)
gmsh.model.geo.addPointOnGeometry(1, -0.202963, 0.301965, tag=205)
gmsh.model.geo.addPointOnGeometry(1, 0.97166, -0.515345, tag=206)
gmsh.model.geo.addPointOnGeometry(1, 0.956175, -0.499901, tag=207)
gmsh.model.geo.addPointOnGeometry(1, 0.95686, -0.52165, tag=208)
gmsh.model.geo.addPointOnGeometry(1, 0.95665, -0.54364, tag=209)
gmsh.model.geo.addPointOnGeometry(1, 0.947465, -0.563775, tag=210)
gmsh.model.geo.addPointOnGeometry(1, 0.96771, -0.55443, tag=211)
gmsh.model.geo.addPointOnGeometry(1, 0.971845, -0.53756, tag=212)
gmsh.model.geo.addPointOnGeometry(1, 0.214812, 0.0283932, tag=213)
gmsh.model.geo.addPointOnGeometry(1, 0.208914, 0.0370319, tag=214)
gmsh.model.geo.addPointOnGeometry(1, 0.20179, 0.0294096, tag=215)
gmsh.model.geo.addPointOnGeometry(1, 0.209232, 0.0221022, tag=216)
gmsh.model.geo.addPointOnGeometry(1, 0.199543, 0.0182617, tag=217)
gmsh.model.geo.addPointOnGeometry(1, 0.19299, 0.0102011, tag=218)
gmsh.model.geo.addPointOnGeometry(1, 0.186393, 0.00221277, tag=219)
gmsh.model.geo.addPointOnGeometry(1, 0.22124, 0.000183722, tag=220)
gmsh.model.geo.addPointOnGeometry(1, 0.211107, 0.00281108, tag=221)
gmsh.model.geo.addPointOnGeometry(1, 0.216116, 0.0119919, tag=222)
gmsh.model.geo.addPointOnGeometry(1, 0.222739, 0.0201202, tag=223)
gmsh.model.geo.addPointOnGeometry(1, 0.22512, 0.030355, tag=224)
gmsh.model.geo.addPointOnGeometry(1, -0.219196, 0.66962, tag=225)
gmsh.model.geo.addPointOnGeometry(1, -0.204306, 0.670855, tag=226)
gmsh.model.geo.addPointOnGeometry(1, -0.213832, 0.682465, tag=227)
gmsh.model.geo.addPointOnGeometry(1, -0.201109, 0.69068, tag=228)
gmsh.model.geo.addPointOnGeometry(1, -0.216313, 0.691075, tag=229)
gmsh.model.geo.addPointOnGeometry(1, -0.23155, 0.692405, tag=230)
gmsh.model.geo.addPointOnGeometry(1, -0.234745, 0.68758, tag=231)
gmsh.model.geo.addPointOnGeometry(1, -0.246353, 0.677705, tag=232)
gmsh.model.geo.addPointOnGeometry(1, -0.260912, 0.673305, tag=233)
gmsh.model.geo.addPointOnGeometry(1, -0.247436, 0.66642, tag=234)
gmsh.model.geo.addPointOnGeometry(1, -0.232761, 0.66332, tag=235)
gmsh.model.geo.addPointOnGeometry(1, 0.67908, -0.970455, tag=236)
gmsh.model.geo.addPointOnGeometry(1, 0.68954, -0.94897, tag=237)
gmsh.model.geo.addPointOnGeometry(1, 0.70116, -0.928375, tag=238)
gmsh.model.geo.addPointOnGeometry(1, 0.680565, -0.939695, tag=239)
gmsh.model.geo.addPointOnGeometry(1, 0.66928, -0.9604, tag=240)
gmsh.model.geo.addPointOnGeometry(1, 0.657485, -0.981105, tag=241)
gmsh.model.geo.addPointOnGeometry(1, 0.669835, -0.992805, tag=242)
gmsh.model.geo.addPointOnGeometry(1, 0.264666, -0.20788, tag=243)
gmsh.model.geo.addPointOnGeometry(1, 0.273357, -0.214897, tag=244)
gmsh.model.geo.addPointOnGeometry(1, 0.282989, -0.220708, tag=245)
gmsh.model.geo.addPointOnGeometry(1, 0.29205, -0.227509, tag=246)
gmsh.model.geo.addPointOnGeometry(1, 0.301367, -0.234102, tag=247)
gmsh.model.geo.addPointOnGeometry(1, 0.310006, -0.241694, tag=248)
gmsh.model.geo.addPointOnGeometry(1, 0.318645, -0.24894, tag=249)
gmsh.model.geo.addPointOnGeometry(1, 0.322249, -0.237893, tag=250)
gmsh.model.geo.addPointOnGeometry(1, 0.31096, -0.235391, tag=251)
gmsh.model.geo.addPointOnGeometry(1, 0.301303, -0.229189, tag=252)
gmsh.model.geo.addPointOnGeometry(1, 0.301244, -0.217781, tag=253)
gmsh.model.geo.addPointOnGeometry(1, 0.290943, -0.213037, tag=254)
gmsh.model.geo.addPointOnGeometry(1, 0.280782, -0.208181, tag=255)
gmsh.model.geo.addPointOnGeometry(1, 0.271197, -0.202417, tag=256)
gmsh.model.geo.addPointOnGeometry(1, 0.261345, -0.197286, tag=257)
gmsh.model.geo.addPointOnGeometry(1, 0.077429, 0.139269, tag=258)
gmsh.model.geo.addPointOnGeometry(1, 0.0701055, 0.146453, tag=259)
gmsh.model.geo.addPointOnGeometry(1, 0.079273, 0.151099, tag=260)
gmsh.model.geo.addPointOnGeometry(1, 0.0698575, 0.155254, tag=261)
gmsh.model.geo.addPointOnGeometry(1, 0.080115, 0.15618, tag=262)
gmsh.model.geo.addPointOnGeometry(1, 0.0769285, 0.165997, tag=263)
gmsh.model.geo.addPointOnGeometry(1, 0.0740875, 0.169904, tag=264)
gmsh.model.geo.addPointOnGeometry(1, 0.066274, 0.176691, tag=265)
gmsh.model.geo.addPointOnGeometry(1, 0.0559255, 0.176907, tag=266)
gmsh.model.geo.addPointOnGeometry(1, 0.0459945, 0.179802, tag=267)
gmsh.model.geo.addPointOnGeometry(1, 0.037746, 0.173583, tag=268)
gmsh.model.geo.addPointOnGeometry(1, 0.0408673, 0.163767, tag=269)
gmsh.model.geo.addPointOnGeometry(1, 0.0407431, 0.153498, tag=270)
gmsh.model.geo.addPointOnGeometry(1, 0.0407623, 0.143261, tag=271)
gmsh.model.geo.addPointOnGeometry(1, 0.0478513, 0.150647, tag=272)
gmsh.model.geo.addPointOnGeometry(1, 0.0509145, 0.140878, tag=273)
gmsh.model.geo.addPointOnGeometry(1, 0.061098, 0.139915, tag=274)
gmsh.model.geo.addPointOnGeometry(1, 0.0690135, 0.133435, tag=275)
gmsh.model.geo.addPointOnGeometry(1, -0.0217622, 0.0930765, tag=276)
gmsh.model.geo.addPointOnGeometry(1, -0.0283523, 0.0854405, tag=277)
gmsh.model.geo.addPointOnGeometry(1, -0.0298998, 0.075486, tag=278)
gmsh.model.geo.addPointOnGeometry(1, -0.0305187, 0.065445, tag=279)
gmsh.model.geo.addPointOnGeometry(1, -0.0266121, 0.05619, tag=280)
gmsh.model.geo.addPointOnGeometry(1, -0.0166043, 0.0569625, tag=281)
gmsh.model.geo.addPointOnGeometry(1, -0.0134165, 0.058545, tag=282)
gmsh.model.geo.addPointOnGeometry(1, -0.0111013, 0.068317, tag=283)
gmsh.model.geo.addPointOnGeometry(1, -0.00119833, 0.0700285, tag=284)
gmsh.model.geo.addPointOnGeometry(1, -0.00109426, 0.0800845, tag=285)
gmsh.model.geo.addPointOnGeometry(1, -0.0111395, 0.0794595, tag=286)
gmsh.model.geo.addPointOnGeometry(1, -0.013992, 0.0891205, tag=287)
gmsh.model.geo.addPointOnGeometry(1, -0.0133445, 0.099191, tag=288)
gmsh.model.geo.addPointOnGeometry(1, -0.00976845, 0.108648, tag=289)
gmsh.model.geo.addPointOnGeometry(1, -0.00159309, 0.114621, tag=290)
gmsh.model.geo.addPointOnGeometry(1, -0.0102939, 0.119827, tag=291)
gmsh.model.geo.addPointOnGeometry(1, -0.0204042, 0.118973, tag=292)
gmsh.model.geo.addPointOnGeometry(1, -0.0151567, 0.110302, tag=293)
gmsh.model.geo.addPointOnGeometry(1, -0.0223253, 0.103162, tag=294)
gmsh.model.geo.addPointOnGeometry(1, 1.95413, -0.210729, tag=295)
gmsh.model.geo.addPointOnGeometry(1, 1.98912, -0.185842, tag=296)
gmsh.model.geo.addPointOnGeometry(1, 1.94069, -0.186417, tag=297)
gmsh.model.geo.addPointOnGeometry(1, 1.90731, -0.22011, tag=298)
gmsh.model.geo.addPointOnGeometry(1, 1.4266, -0.35238, tag=299)
gmsh.model.geo.addPointOnGeometry(1, 1.40069, -0.369908, tag=300)
gmsh.model.geo.addPointOnGeometry(1, 1.37894, -0.39168, tag=301)
gmsh.model.geo.addPointOnGeometry(1, 1.40823, -0.381766, tag=302)
gmsh.model.geo.addPointOnGeometry(1, 1.43291, -0.362095, tag=303)
gmsh.model.geo.addPointOnGeometry(1, 1.45436, -0.338248, tag=304)
gmsh.model.geo.addPointOnGeometry(1, 1.45377, -0.335606, tag=305)
gmsh.model.geo.addPointOnGeometry(1, 0.484513, -0.71829, tag=306)
gmsh.model.geo.addPointOnGeometry(1, 0.470336, -0.728575, tag=307)
gmsh.model.geo.addPointOnGeometry(1, 0.467198, -0.74593, tag=308)
gmsh.model.geo.addPointOnGeometry(1, 0.477077, -0.73126, tag=309)
gmsh.model.geo.addPointOnGeometry(1, 0.494046, -0.726325, tag=310)
gmsh.model.geo.addPointOnGeometry(1, 0.503435, -0.741535, tag=311)
gmsh.model.geo.addPointOnGeometry(1, 0.51775, -0.7394, tag=312)
gmsh.model.geo.addPointOnGeometry(1, 0.514255, -0.72174, tag=313)
gmsh.model.geo.addPointOnGeometry(1, 0.52494, -0.707495, tag=314)
gmsh.model.geo.addPointOnGeometry(1, 0.511705, -0.69588, tag=315)
gmsh.model.geo.addPointOnGeometry(1, 0.497, -0.68672, tag=316)
gmsh.model.geo.addPointOnGeometry(1, 0.487586, -0.701165, tag=317)
gmsh.model.geo.addPointOnGeometry(1, 0.376464, -0.64376, tag=318)
gmsh.model.geo.addPointOnGeometry(1, 0.379681, -0.6591, tag=319)
gmsh.model.geo.addPointOnGeometry(1, 0.392726, -0.668185, tag=320)
gmsh.model.geo.addPointOnGeometry(1, 0.408199, -0.672645, tag=321)
gmsh.model.geo.addPointOnGeometry(1, 0.422867, -0.665755, tag=322)
gmsh.model.geo.addPointOnGeometry(1, 0.438993, -0.66356, tag=323)
gmsh.model.geo.addPointOnGeometry(1, 0.445057, -0.659825, tag=324)
gmsh.model.geo.addPointOnGeometry(1, 0.430521, -0.65262, tag=325)
gmsh.model.geo.addPointOnGeometry(1, 0.415254, -0.65767, tag=326)
gmsh.model.geo.addPointOnGeometry(1, 0.400014, -0.652945, tag=327)
gmsh.model.geo.addPointOnGeometry(1, 0.397974, -0.63732, tag=328)
gmsh.model.geo.addPointOnGeometry(1, 0.395054, -0.62206, tag=329)
gmsh.model.geo.addPointOnGeometry(1, 0.385839, -0.60982, tag=330)
gmsh.model.geo.addPointOnGeometry(1, 0.371455, -0.614685, tag=331)
gmsh.model.geo.addPointOnGeometry(1, 0.369767, -0.629835, tag=332)
gmsh.model.geo.addPointOnGeometry(1, -0.33454, 0.0273099, tag=333)
gmsh.model.geo.addPointOnGeometry(1, -0.345126, 0.0308558, tag=334)
gmsh.model.geo.addPointOnGeometry(1, -0.349763, 0.0206403, tag=335)
gmsh.model.geo.addPointOnGeometry(1, -0.3564, 0.0297279, tag=336)
gmsh.model.geo.addPointOnGeometry(1, -0.356481, 0.0184515, tag=337)
gmsh.model.geo.addPointOnGeometry(1, -0.35623, 0.00718155, tag=338)
gmsh.model.geo.addPointOnGeometry(1, -0.35621, -1.2116e-05, tag=339)
gmsh.model.geo.addPointOnGeometry(1, -0.346361, -0.0054177, tag=340)
gmsh.model.geo.addPointOnGeometry(1, -0.335586, -0.0083361, tag=341)
gmsh.model.geo.addPointOnGeometry(1, -0.328946, 0.00056479, tag=342)
gmsh.model.geo.addPointOnGeometry(1, -0.319122, 0.00562485, tag=343)
gmsh.model.geo.addPointOnGeometry(1, -0.308891, 0.0096292, tag=344)
gmsh.model.geo.addPointOnGeometry(1, -0.304833, 0.0197933, tag=345)
gmsh.model.geo.addPointOnGeometry(1, -0.297463, 0.0117488, tag=346)
gmsh.model.geo.addPointOnGeometry(1, -0.289955, 0.0196034, tag=347)
gmsh.model.geo.addPointOnGeometry(1, -0.279997, 0.0238281, tag=348)
gmsh.model.geo.addPointOnGeometry(1, -0.289768, 0.0284724, tag=349)
gmsh.model.geo.addPointOnGeometry(1, -0.300632, 0.0290583, tag=350)
gmsh.model.geo.addPointOnGeometry(1, -0.311243, 0.0263689, tag=351)
gmsh.model.geo.addPointOnGeometry(1, -0.320095, 0.0198349, tag=352)
gmsh.model.geo.addPointOnGeometry(1, -0.330752, 0.0168635, tag=353)
gmsh.model.geo.addPointOnGeometry(1, -0.101768, 0.66564, tag=354)
gmsh.model.geo.addPointOnGeometry(1, -0.116281, 0.666775, tag=355)
gmsh.model.geo.addPointOnGeometry(1, -0.130882, 0.66699, tag=356)
gmsh.model.geo.addPointOnGeometry(1, -0.142568, 0.6759, tag=357)
gmsh.model.geo.addPointOnGeometry(1, -0.154976, 0.68405, tag=358)
gmsh.model.geo.addPointOnGeometry(1, -0.169328, 0.679985, tag=359)
gmsh.model.geo.addPointOnGeometry(1, -0.181585, 0.677385, tag=360)
gmsh.model.geo.addPointOnGeometry(1, -0.169585, 0.668655, tag=361)
gmsh.model.geo.addPointOnGeometry(1, -0.155187, 0.665615, tag=362)
gmsh.model.geo.addPointOnGeometry(1, -0.14143, 0.660675, tag=363)
gmsh.model.geo.addPointOnGeometry(1, -0.127298, 0.65732, tag=364)
gmsh.model.geo.addPointOnGeometry(1, -0.113644, 0.652635, tag=365)
gmsh.model.geo.addPointOnGeometry(1, -0.0992695, 0.65282, tag=366)
gmsh.model.geo.addPointOnGeometry(1, -0.0852135, 0.655795, tag=367)
gmsh.model.geo.addPointOnGeometry(1, -0.071835, 0.661115, tag=368)
gmsh.model.geo.addPointOnGeometry(1, -0.0624155, 0.672125, tag=369)
gmsh.model.geo.addPointOnGeometry(1, -0.0763565, 0.667995, tag=370)
gmsh.model.geo.addPointOnGeometry(1, -0.088427, 0.66, tag=371)
gmsh.model.geo.addPointOnGeometry(1, 0.349771, -0.382915, tag=372)
gmsh.model.geo.addPointOnGeometry(1, 0.348315, -0.395565, tag=373)
gmsh.model.geo.addPointOnGeometry(1, 0.359003, -0.388543, tag=374)
gmsh.model.geo.addPointOnGeometry(1, 0.365018, -0.37727, tag=375)
gmsh.model.geo.addPointOnGeometry(1, 0.373366, -0.367632, tag=376)
gmsh.model.geo.addPointOnGeometry(1, 0.382726, -0.376383, tag=377)
gmsh.model.geo.addPointOnGeometry(1, 0.384812, -0.371585, tag=378)
gmsh.model.geo.addPointOnGeometry(1, 0.379744, -0.359832, tag=379)
gmsh.model.geo.addPointOnGeometry(1, 0.389183, -0.351269, tag=380)
gmsh.model.geo.addPointOnGeometry(1, 0.391797, -0.338824, tag=381)
gmsh.model.geo.addPointOnGeometry(1, 0.396761, -0.327174, tag=382)
gmsh.model.geo.addPointOnGeometry(1, 0.388464, -0.317714, tag=383)
gmsh.model.geo.addPointOnGeometry(1, 0.380235, -0.308362, tag=384)
gmsh.model.geo.addPointOnGeometry(1, 0.374856, -0.297252, tag=385)
gmsh.model.geo.addPointOnGeometry(1, 0.367426, -0.287535, tag=386)
gmsh.model.geo.addPointOnGeometry(1, 0.355781, -0.284147, tag=387)
gmsh.model.geo.addPointOnGeometry(1, 0.351493, -0.295452, tag=388)
gmsh.model.geo.addPointOnGeometry(1, 0.361304, -0.302648, tag=389)
gmsh.model.geo.addPointOnGeometry(1, 0.367045, -0.313499, tag=390)
gmsh.model.geo.addPointOnGeometry(1, 0.370038, -0.325511, tag=391)
gmsh.model.geo.addPointOnGeometry(1, 0.367762, -0.337762, tag=392)
gmsh.model.geo.addPointOnGeometry(1, 0.36567, -0.350116, tag=393)
gmsh.model.geo.addPointOnGeometry(1, 0.364761, -0.362689, tag=394)
gmsh.model.geo.addPointOnGeometry(1, 0.356177, -0.371981, tag=395)
gmsh.model.geo.addPointOnGeometry(1, 0.346218, -1.08872, tag=396)
gmsh.model.geo.addPointOnGeometry(1, 0.368594, -1.0831, tag=397)
gmsh.model.geo.addPointOnGeometry(1, 0.391333, -1.07886, tag=398)
gmsh.model.geo.addPointOnGeometry(1, 0.414596, -1.07865, tag=399)
gmsh.model.geo.addPointOnGeometry(1, 0.436804, -1.07138, tag=400)
gmsh.model.geo.addPointOnGeometry(1, 0.458416, -1.06242, tag=401)
gmsh.model.geo.addPointOnGeometry(1, 0.472822, -1.06185, tag=402)
gmsh.model.geo.addPointOnGeometry(1, 0.451561, -1.05229, tag=403)
gmsh.model.geo.addPointOnGeometry(1, 0.430868, -1.04244, tag=404)
gmsh.model.geo.addPointOnGeometry(1, 0.408722, -1.04735, tag=405)
gmsh.model.geo.addPointOnGeometry(1, 0.388854, -1.05828, tag=406)
gmsh.model.geo.addPointOnGeometry(1, 0.366959, -1.06425, tag=407)
gmsh.model.geo.addPointOnGeometry(1, 0.344573, -1.06143, tag=408)
gmsh.model.geo.addPointOnGeometry(1, 0.322477, -1.06524, tag=409)
gmsh.model.geo.addPointOnGeometry(1, 0.304433, -1.07864, tag=410)
gmsh.model.geo.addPointOnGeometry(1, 0.323355, -1.09128, tag=411)
gmsh.model.geo.addPointOnGeometry(1, -0.0471086, 0.221832, tag=412)
gmsh.model.geo.addPointOnGeometry(1, -0.0572035, 0.218896, tag=413)
gmsh.model.geo.addPointOnGeometry(1, -0.0676995, 0.219604, tag=414)
gmsh.model.geo.addPointOnGeometry(1, -0.075522, 0.226682, tag=415)
gmsh.model.geo.addPointOnGeometry(1, -0.0859095, 0.228716, tag=416)
gmsh.model.geo.addPointOnGeometry(1, -0.0965075, 0.229164, tag=417)
gmsh.model.geo.addPointOnGeometry(1, -0.101581, 0.228146, tag=418)
gmsh.model.geo.addPointOnGeometry(1, -0.0928545, 0.222125, tag=419)
gmsh.model.geo.addPointOnGeometry(1, -0.102861, 0.218685, tag=420)
gmsh.model.geo.addPointOnGeometry(1, -0.0988035, 0.208937, tag=421)
gmsh.model.geo.addPointOnGeometry(1, -0.089652, 0.203758, tag=422)
gmsh.model.geo.addPointOnGeometry(1, -0.0803335, 0.198968, tag=423)
gmsh.model.geo.addPointOnGeometry(1, -0.0867755, 0.19074, tag=424)
gmsh.model.geo.addPointOnGeometry(1, -0.0937375, 0.198548, tag=425)
gmsh.model.geo.addPointOnGeometry(1, -0.099679, 0.189925, tag=426)
gmsh.model.geo.addPointOnGeometry(1, -0.09675, 0.179905, tag=427)
gmsh.model.geo.addPointOnGeometry(1, -0.086823, 0.183051, tag=428)
gmsh.model.geo.addPointOnGeometry(1, -0.0780745, 0.17744, tag=429)
gmsh.model.geo.addPointOnGeometry(1, -0.068069, 0.174737, tag=430)
gmsh.model.geo.addPointOnGeometry(1, -0.069276, 0.16447, tag=431)
gmsh.model.geo.addPointOnGeometry(1, -0.059409, 0.167486, tag=432)
gmsh.model.geo.addPointOnGeometry(1, -0.055672, 0.157889, tag=433)
gmsh.model.geo.addPointOnGeometry(1, -0.0456773, 0.15552, tag=434)
gmsh.model.geo.addPointOnGeometry(1, -0.0381818, 0.148529, tag=435)
gmsh.model.geo.addPointOnGeometry(1, -0.030394, 0.155185, tag=436)
gmsh.model.geo.addPointOnGeometry(1, -0.0244127, 0.146877, tag=437)
gmsh.model.geo.addPointOnGeometry(1, -0.0144346, 0.144674, tag=438)
gmsh.model.geo.addPointOnGeometry(1, -0.0139011, 0.154887, tag=439)
gmsh.model.geo.addPointOnGeometry(1, -0.0149856, 0.165088, tag=440)
gmsh.model.geo.addPointOnGeometry(1, -0.00504375, 0.167697, tag=441)
gmsh.model.geo.addPointOnGeometry(1, -0.012015, 0.175274, tag=442)
gmsh.model.geo.addPointOnGeometry(1, -0.02232, 0.17564, tag=443)
gmsh.model.geo.addPointOnGeometry(1, -0.0325399, 0.174246, tag=444)
gmsh.model.geo.addPointOnGeometry(1, -0.0428538, 0.17457, tag=445)
gmsh.model.geo.addPointOnGeometry(1, -0.0504015, 0.181638, tag=446)
gmsh.model.geo.addPointOnGeometry(1, -0.058885, 0.187607, tag=447)
gmsh.model.geo.addPointOnGeometry(1, -0.059269, 0.198008, tag=448)
gmsh.model.geo.addPointOnGeometry(1, -0.061589, 0.208197, tag=449)
gmsh.model.geo.addPointOnGeometry(1, -0.052077, 0.212586, tag=450)
gmsh.model.geo.addPointOnGeometry(1, 1.90865, -1.17872, tag=451)
gmsh.model.geo.addPointOnGeometry(1, 1.85502, -1.15408, tag=452)
gmsh.model.geo.addPointOnGeometry(1, 1.83765, -1.20949, tag=453)
gmsh.model.geo.addPointOnGeometry(1, 1.7934, -1.24709, tag=454)
gmsh.model.geo.addPointOnGeometry(1, 1.84198, -1.2806, tag=455)
gmsh.model.geo.addPointOnGeometry(1, 1.90066, -1.29953, tag=456)
gmsh.model.geo.addPointOnGeometry(1, 1.92179, -1.29631, tag=457)
gmsh.model.geo.addPointOnGeometry(1, 1.93466, -1.23444, tag=458)
gmsh.model.geo.addPointOnGeometry(1, 0.496486, -0.85223, tag=459)
gmsh.model.geo.addPointOnGeometry(1, 0.499037, -0.871975, tag=460)
gmsh.model.geo.addPointOnGeometry(1, 0.50101, -0.89216, tag=461)
gmsh.model.geo.addPointOnGeometry(1, 0.508075, -0.911605, tag=462)
gmsh.model.geo.addPointOnGeometry(1, 0.523695, -0.925795, tag=463)
gmsh.model.geo.addPointOnGeometry(1, 0.534795, -0.944255, tag=464)
gmsh.model.geo.addPointOnGeometry(1, 0.537045, -0.95003, tag=465)
gmsh.model.geo.addPointOnGeometry(1, 0.559065, -0.95097, tag=466)
gmsh.model.geo.addPointOnGeometry(1, 0.546965, -0.93268, tag=467)
gmsh.model.geo.addPointOnGeometry(1, 0.53625, -0.914085, tag=468)
gmsh.model.geo.addPointOnGeometry(1, 0.557555, -0.915615, tag=469)
gmsh.model.geo.addPointOnGeometry(1, 0.578965, -0.912835, tag=470)
gmsh.model.geo.addPointOnGeometry(1, 0.56604, -0.89571, tag=471)
gmsh.model.geo.addPointOnGeometry(1, 0.548195, -0.884585, tag=472)
gmsh.model.geo.addPointOnGeometry(1, 0.55022, -0.86402, tag=473)
gmsh.model.geo.addPointOnGeometry(1, 0.558425, -0.845365, tag=474)
gmsh.model.geo.addPointOnGeometry(1, 0.541715, -0.85684, tag=475)
gmsh.model.geo.addPointOnGeometry(1, 0.529395, -0.87304, tag=476)
gmsh.model.geo.addPointOnGeometry(1, 0.509355, -0.87659, tag=477)
gmsh.model.geo.addPointOnGeometry(1, 0.500875, -0.85839, tag=478)
gmsh.model.geo.addPointOnGeometry(1, 0.516705, -0.846405, tag=479)
gmsh.model.geo.addPointOnGeometry(1, 0.53342, -0.83573, tag=480)
gmsh.model.geo.addPointOnGeometry(1, 0.551725, -0.828015, tag=481)
gmsh.model.geo.addPointOnGeometry(1, 0.56093, -0.810475, tag=482)
gmsh.model.geo.addPointOnGeometry(1, 0.54413, -0.820785, tag=483)
gmsh.model.geo.addPointOnGeometry(1, 0.52659, -0.8297, tag=484)
gmsh.model.geo.addPointOnGeometry(1, 0.508155, -0.83641, tag=485)
gmsh.model.geo.addPointOnGeometry(1, 0.177858, -0.99563, tag=486)
gmsh.model.geo.addPointOnGeometry(1, 0.190691, -1.01151, tag=487)
gmsh.model.geo.addPointOnGeometry(1, 0.20368, -1.02774, tag=488)
gmsh.model.geo.addPointOnGeometry(1, 0.220861, -1.04005, tag=489)
gmsh.model.geo.addPointOnGeometry(1, 0.238565, -1.05221, tag=490)
gmsh.model.geo.addPointOnGeometry(1, 0.258977, -1.05977, tag=491)
gmsh.model.geo.addPointOnGeometry(1, 0.265529, -1.06495, tag=492)
gmsh.model.geo.addPointOnGeometry(1, 0.287457, -1.06801, tag=493)
gmsh.model.geo.addPointOnGeometry(1, 0.298397, -1.04885, tag=494)
gmsh.model.geo.addPointOnGeometry(1, 0.293617, -1.02772, tag=495)
gmsh.model.geo.addPointOnGeometry(1, 0.283943, -1.00886, tag=496)
gmsh.model.geo.addPointOnGeometry(1, 0.264602, -1.00106, tag=497)
gmsh.model.geo.addPointOnGeometry(1, 0.249213, -0.98744, tag=498)
gmsh.model.geo.addPointOnGeometry(1, 0.231706, -0.977305, tag=499)
gmsh.model.geo.addPointOnGeometry(1, 0.21798, -0.96287, tag=500)
gmsh.model.geo.addPointOnGeometry(1, 0.201267, -0.95261, tag=501)
gmsh.model.geo.addPointOnGeometry(1, 0.184371, -0.94316, tag=502)
gmsh.model.geo.addPointOnGeometry(1, 0.165669, -0.93898, tag=503)
gmsh.model.geo.addPointOnGeometry(1, 0.150285, -0.927895, tag=504)
gmsh.model.geo.addPointOnGeometry(1, 0.132929, -0.920805, tag=505)
gmsh.model.geo.addPointOnGeometry(1, 0.121391, -0.906335, tag=506)
gmsh.model.geo.addPointOnGeometry(1, 0.103056, -0.907, tag=507)
gmsh.model.geo.addPointOnGeometry(1, 0.085532, -0.90183, tag=508)
gmsh.model.geo.addPointOnGeometry(1, 0.0917145, -0.91913, tag=509)
gmsh.model.geo.addPointOnGeometry(1, 0.106381, -0.930655, tag=510)
gmsh.model.geo.addPointOnGeometry(1, 0.121915, -0.94141, tag=511)
gmsh.model.geo.addPointOnGeometry(1, 0.1368, -0.95345, tag=512)
gmsh.model.geo.addPointOnGeometry(1, 0.150817, -0.9669, tag=513)
gmsh.model.geo.addPointOnGeometry(1, 0.162236, -0.98302, tag=514)
gmsh.model.geo.addPointOnGeometry(1, 0.355532, -0.971865, tag=515)
gmsh.model.geo.addPointOnGeometry(1, 0.37121, -0.98569, tag=516)
gmsh.model.geo.addPointOnGeometry(1, 0.390838, -0.97797, tag=517)
gmsh.model.geo.addPointOnGeometry(1, 0.411739, -0.97477, tag=518)
gmsh.model.geo.addPointOnGeometry(1, 0.431943, -0.96828, tag=519)
gmsh.model.geo.addPointOnGeometry(1, 0.452846, -0.97276, tag=520)
gmsh.model.geo.addPointOnGeometry(1, 0.453332, -0.972135, tag=521)
gmsh.model.geo.addPointOnGeometry(1, 0.465993, -0.954885, tag=522)
gmsh.model.geo.addPointOnGeometry(1, 0.466395, -0.9338, tag=523)
gmsh.model.geo.addPointOnGeometry(1, 0.460744, -0.9139, tag=524)
gmsh.model.geo.addPointOnGeometry(1, 0.46761, -0.89476, tag=525)
gmsh.model.geo.addPointOnGeometry(1, 0.459597, -0.87644, tag=526)
gmsh.model.geo.addPointOnGeometry(1, 0.475001, -0.86407, tag=527)
gmsh.model.geo.addPointOnGeometry(1, 0.456922, -0.856565, tag=528)
gmsh.model.geo.addPointOnGeometry(1, 0.442992, -0.84328, tag=529)
gmsh.model.geo.addPointOnGeometry(1, 0.433056, -0.82721, tag=530)
gmsh.model.geo.addPointOnGeometry(1, 0.433494, -0.80865, tag=531)
gmsh.model.geo.addPointOnGeometry(1, 0.445164, -0.79448, tag=532)
gmsh.model.geo.addPointOnGeometry(1, 0.426999, -0.79597, tag=533)
gmsh.model.geo.addPointOnGeometry(1, 0.409603, -0.79116, tag=534)
gmsh.model.geo.addPointOnGeometry(1, 0.396388, -0.803355, tag=535)
gmsh.model.geo.addPointOnGeometry(1, 0.390616, -0.82056, tag=536)
gmsh.model.geo.addPointOnGeometry(1, 0.382641, -0.8371, tag=537)
gmsh.model.geo.addPointOnGeometry(1, 0.375083, -0.85409, tag=538)
gmsh.model.geo.addPointOnGeometry(1, 0.369074, -0.871935, tag=539)
gmsh.model.geo.addPointOnGeometry(1, 0.353541, -0.88289, tag=540)
gmsh.model.geo.addPointOnGeometry(1, 0.348927, -0.901525, tag=541)
gmsh.model.geo.addPointOnGeometry(1, 0.332579, -0.91195, tag=542)
gmsh.model.geo.addPointOnGeometry(1, 0.318182, -0.925095, tag=543)
gmsh.model.geo.addPointOnGeometry(1, 0.327287, -0.942635, tag=544)
gmsh.model.geo.addPointOnGeometry(1, 0.340861, -0.95752, tag=545)
gmsh.model.geo.addPointOnGeometry(1, 2.12476, -0.107842, tag=546)
gmsh.model.geo.addPointOnGeometry(1, 2.08115, -0.0754635, tag=547)
gmsh.model.geo.addPointOnGeometry(1, 2.03348, -0.0538055, tag=548)
gmsh.model.geo.addPointOnGeometry(1, 2.04879, -0.103219, tag=549)
gmsh.model.geo.addPointOnGeometry(1, 2.016, -0.142883, tag=550)
gmsh.model.geo.addPointOnGeometry(1, 1.99009, -0.185146, tag=551)
gmsh.model.geo.addPointOnGeometry(1, 1.99433, -0.18209, tag=552)
gmsh.model.geo.addPointOnGeometry(1, 2.04525, -0.18682, tag=553)
gmsh.model.geo.addPointOnGeometry(1, 2.08793, -0.21843, tag=554)
gmsh.model.geo.addPointOnGeometry(1, 2.13339, -0.187514, tag=555)
gmsh.model.geo.addPointOnGeometry(1, 2.18589, -0.193676, tag=556)
gmsh.model.geo.addPointOnGeometry(1, 2.17268, -0.137431, tag=557)
gmsh.model.geo.addPointOnGeometry(1, -0.138105, 0.17657, tag=558)
gmsh.model.geo.addPointOnGeometry(1, -0.146291, 0.183188, tag=559)
gmsh.model.geo.addPointOnGeometry(1, -0.155904, 0.187588, tag=560)
gmsh.model.geo.addPointOnGeometry(1, -0.166021, 0.190808, tag=561)
gmsh.model.geo.addPointOnGeometry(1, -0.176636, 0.18987, tag=562)
gmsh.model.geo.addPointOnGeometry(1, -0.186308, 0.185336, tag=563)
gmsh.model.geo.addPointOnGeometry(1, -0.19046, 0.188481, tag=564)
gmsh.model.geo.addPointOnGeometry(1, -0.192137, 0.177909, tag=565)
gmsh.model.geo.addPointOnGeometry(1, -0.184324, 0.170659, tag=566)
gmsh.model.geo.addPointOnGeometry(1, -0.177898, 0.16222, tag=567)
gmsh.model.geo.addPointOnGeometry(1, -0.177404, 0.151669, tag=568)
gmsh.model.geo.addPointOnGeometry(1, -0.168376, 0.146265, tag=569)
gmsh.model.geo.addPointOnGeometry(1, -0.168788, 0.13579, tag=570)
gmsh.model.geo.addPointOnGeometry(1, -0.171699, 0.125742, tag=571)
gmsh.model.geo.addPointOnGeometry(1, -0.170411, 0.115384, tag=572)
gmsh.model.geo.addPointOnGeometry(1, -0.165371, 0.10628, tag=573)
gmsh.model.geo.addPointOnGeometry(1, -0.165541, 0.0959035, tag=574)
gmsh.model.geo.addPointOnGeometry(1, -0.16831, 0.0859185, tag=575)
gmsh.model.geo.addPointOnGeometry(1, -0.165529, 0.075955, tag=576)
gmsh.model.geo.addPointOnGeometry(1, -0.162564, 0.0660695, tag=577)
gmsh.model.geo.addPointOnGeometry(1, -0.156305, 0.0742545, tag=578)
gmsh.model.geo.addPointOnGeometry(1, -0.150824, 0.082972, tag=579)
gmsh.model.geo.addPointOnGeometry(1, -0.149651, 0.0727515, tag=580)
gmsh.model.geo.addPointOnGeometry(1, -0.139844, 0.075784, tag=581)
gmsh.model.geo.addPointOnGeometry(1, -0.148355, 0.070053, tag=582)
gmsh.model.geo.addPointOnGeometry(1, -0.158407, 0.067883, tag=583)
gmsh.model.geo.addPointOnGeometry(1, -0.151352, 0.0604045, tag=584)
gmsh.model.geo.addPointOnGeometry(1, -0.141745, 0.063992, tag=585)
gmsh.model.geo.addPointOnGeometry(1, -0.132094, 0.0673855, tag=586)
gmsh.model.geo.addPointOnGeometry(1, -0.131062, 0.057225, tag=587)
gmsh.model.geo.addPointOnGeometry(1, -0.129955, 0.0470873, tag=588)
gmsh.model.geo.addPointOnGeometry(1, -0.119788, 0.0466021, tag=589)
gmsh.model.geo.addPointOnGeometry(1, -0.109691, 0.0455319, tag=590)
gmsh.model.geo.addPointOnGeometry(1, -0.108244, 0.0355, tag=591)
gmsh.model.geo.addPointOnGeometry(1, -0.0982895, 0.0373247, tag=592)
gmsh.model.geo.addPointOnGeometry(1, -0.089436, 0.0324622, tag=593)
gmsh.model.geo.addPointOnGeometry(1, -0.079975, 0.0289785, tag=594)
gmsh.model.geo.addPointOnGeometry(1, -0.075194, 0.0201196, tag=595)
gmsh.model.geo.addPointOnGeometry(1, -0.0710585, 0.0292914, tag=596)
gmsh.model.geo.addPointOnGeometry(1, -0.0616305, 0.0327989, tag=597)
gmsh.model.geo.addPointOnGeometry(1, -0.0540475, 0.0262138, tag=598)
gmsh.model.geo.addPointOnGeometry(1, -0.0511135, 0.0358135, tag=599)
gmsh.model.geo.addPointOnGeometry(1, -0.0423212, 0.0406696, tag=600)
gmsh.model.geo.addPointOnGeometry(1, -0.0480106, 0.0489494, tag=601)
gmsh.model.geo.addPointOnGeometry(1, -0.0414152, 0.056531, tag=602)
gmsh.model.geo.addPointOnGeometry(1, -0.0339199, 0.0632265, tag=603)
gmsh.model.geo.addPointOnGeometry(1, -0.0337238, 0.0732835, tag=604)
gmsh.model.geo.addPointOnGeometry(1, -0.038728, 0.0820265, tag=605)
gmsh.model.geo.addPointOnGeometry(1, -0.0341667, 0.091025, tag=606)
gmsh.model.geo.addPointOnGeometry(1, -0.0340872, 0.101129, tag=607)
gmsh.model.geo.addPointOnGeometry(1, -0.0440882, 0.099604, tag=608)
gmsh.model.geo.addPointOnGeometry(1, -0.0366658, 0.106488, tag=609)
gmsh.model.geo.addPointOnGeometry(1, -0.0445223, 0.112895, tag=610)
gmsh.model.geo.addPointOnGeometry(1, -0.0537405, 0.108653, tag=611)
gmsh.model.geo.addPointOnGeometry(1, -0.0636525, 0.106465, tag=612)
gmsh.model.geo.addPointOnGeometry(1, -0.0724815, 0.111504, tag=613)
gmsh.model.geo.addPointOnGeometry(1, -0.0803, 0.118039, tag=614)
gmsh.model.geo.addPointOnGeometry(1, -0.0879445, 0.12482, tag=615)
gmsh.model.geo.addPointOnGeometry(1, -0.092035, 0.134218, tag=616)
gmsh.model.geo.addPointOnGeometry(1, -0.099513, 0.127189, tag=617)
gmsh.model.geo.addPointOnGeometry(1, -0.108157, 0.132746, tag=618)
gmsh.model.geo.addPointOnGeometry(1, -0.0988245, 0.137078, tag=619)
gmsh.model.geo.addPointOnGeometry(1, -0.109036, 0.138417, tag=620)
gmsh.model.geo.addPointOnGeometry(1, -0.118548, 0.142437, tag=621)
gmsh.model.geo.addPointOnGeometry(1, -0.115233, 0.152246, tag=622)
gmsh.model.geo.addPointOnGeometry(1, -0.118359, 0.162149, tag=623)
gmsh.model.geo.addPointOnGeometry(1, -0.125202, 0.170012, tag=624)
gmsh.model.geo.addPointOnGeometry(1, -0.127823, 0.159914, tag=625)
gmsh.model.geo.addPointOnGeometry(1, -0.12638, 0.170248, tag=626)
gmsh.model.geo.addPointOnGeometry(1, -0.136057, 0.166289, tag=627)
gmsh.model.geo.addPointOnGeometry(1, -1.1011, 2.77067, tag=628)
gmsh.model.geo.addPointOnGeometry(1, -1.00488, 2.79157, tag=629)
gmsh.model.geo.addPointOnGeometry(1, -1.04245, 2.88535, tag=630)
gmsh.model.geo.addPointOnGeometry(1, -0.97058, 2.96278, tag=631)
gmsh.model.geo.addPointOnGeometry(1, -1.07893, 2.96667, tag=632)
gmsh.model.geo.addPointOnGeometry(1, -1.18141, 2.92762, tag=633)
gmsh.model.geo.addPointOnGeometry(1, -1.25355, 2.91373, tag=634)
gmsh.model.geo.addPointOnGeometry(1, -1.1766, 2.83869, tag=635)
gmsh.model.geo.addPointOnGeometry(1, -1.02331, -1.00116, tag=636)
gmsh.model.geo.addPointOnGeometry(1, -1.05272, -1.01062, tag=637)
gmsh.model.geo.addPointOnGeometry(1, -1.0834, -1.01862, tag=638)
gmsh.model.geo.addPointOnGeometry(1, -1.09883, -1.0473, tag=639)
gmsh.model.geo.addPointOnGeometry(1, -1.12061, -1.07282, tag=640)
gmsh.model.geo.addPointOnGeometry(1, -1.12535, -1.10699, tag=641)
gmsh.model.geo.addPointOnGeometry(1, -1.12043, -1.12043, tag=642)
gmsh.model.geo.addPointOnGeometry(1, -1.09035, -1.13828, tag=643)
gmsh.model.geo.addPointOnGeometry(1, -1.0562, -1.14343, tag=644)
gmsh.model.geo.addPointOnGeometry(1, -1.02681, -1.12687, tag=645)
gmsh.model.geo.addPointOnGeometry(1, -0.99988, -1.10822, tag=646)
gmsh.model.geo.addPointOnGeometry(1, -0.972, -1.09285, tag=647)
gmsh.model.geo.addPointOnGeometry(1, -0.945605, -1.07667, tag=648)
gmsh.model.geo.addPointOnGeometry(1, -0.92003, -1.06075, tag=649)
gmsh.model.geo.addPointOnGeometry(1, -0.895085, -1.04532, tag=650)
gmsh.model.geo.addPointOnGeometry(1, -0.872655, -1.02766, tag=651)
gmsh.model.geo.addPointOnGeometry(1, -0.85468, -1.00645, tag=652)
gmsh.model.geo.addPointOnGeometry(1, -0.82945, -0.99649, tag=653)
gmsh.model.geo.addPointOnGeometry(1, -0.815605, -0.973925, tag=654)
gmsh.model.geo.addPointOnGeometry(1, -0.80925, -0.948875, tag=655)
gmsh.model.geo.addPointOnGeometry(1, -0.83482, -0.95228, tag=656)
gmsh.model.geo.addPointOnGeometry(1, -0.860835, -0.95608, tag=657)
gmsh.model.geo.addPointOnGeometry(1, -0.887615, -0.95706, tag=658)
gmsh.model.geo.addPointOnGeometry(1, -0.914685, -0.96071, tag=659)
gmsh.model.geo.addPointOnGeometry(1, -0.933935, -0.94095, tag=660)
gmsh.model.geo.addPointOnGeometry(1, -0.961775, -0.941125, tag=661)
gmsh.model.geo.addPointOnGeometry(1, -0.98707, -0.954225, tag=662)
gmsh.model.geo.addPointOnGeometry(1, -1.00429, -0.977865, tag=663)
gmsh.model.geo.addPointOnGeometry(1, 2.28484, -0.404444, tag=664)
gmsh.model.geo.addPointOnGeometry(1, 2.30753, -0.464951, tag=665)
gmsh.model.geo.addPointOnGeometry(1, 2.34692, -0.51863, tag=666)
gmsh.model.geo.addPointOnGeometry(1, 2.39547, -0.56791, tag=667)
gmsh.model.geo.addPointOnGeometry(1, 2.44291, -0.514465, tag=668)
gmsh.model.geo.addPointOnGeometry(1, 2.46569, -0.44558, tag=669)
gmsh.model.geo.addPointOnGeometry(1, 2.45318, -0.432524, tag=670)
gmsh.model.geo.addPointOnGeometry(1, 2.40004, -0.386101, tag=671)
gmsh.model.geo.addPointOnGeometry(1, 2.34192, -0.35163, tag=672)
gmsh.model.geo.addPointOnGeometry(1, 2.32462, -0.288485, tag=673)
gmsh.model.geo.addPointOnGeometry(1, 2.26743, -0.260959, tag=674)
gmsh.model.geo.addPointOnGeometry(1, 2.21711, -0.226719, tag=675)
gmsh.model.geo.addPointOnGeometry(1, 2.17183, -0.26418, tag=676)
gmsh.model.geo.addPointOnGeometry(1, 2.20281, -0.31403, tag=677)
gmsh.model.geo.addPointOnGeometry(1, 2.25007, -0.352081, tag=678)
gmsh.model.geo.addPointOnGeometry(1, 0.946135, -0.652425, tag=679)
gmsh.model.geo.addPointOnGeometry(1, 0.967765, -0.643615, tag=680)
gmsh.model.geo.addPointOnGeometry(1, 0.9914, -0.6415, tag=681)
gmsh.model.geo.addPointOnGeometry(1, 1.01367, -0.632275, tag=682)
gmsh.model.geo.addPointOnGeometry(1, 1.02859, -0.61309, tag=683)
gmsh.model.geo.addPointOnGeometry(1, 1.04766, -0.59779, tag=684)
gmsh.model.geo.addPointOnGeometry(1, 1.03367, -0.596825, tag=685)
gmsh.model.geo.addPointOnGeometry(1, 1.01005, -0.60124, tag=686)
gmsh.model.geo.addPointOnGeometry(1, 0.988775, -0.611605, tag=687)
gmsh.model.geo.addPointOnGeometry(1, 0.96561, -0.6142, tag=688)
gmsh.model.geo.addPointOnGeometry(1, 0.95041, -0.59714, tag=689)
gmsh.model.geo.addPointOnGeometry(1, 0.92932, -0.604825, tag=690)
gmsh.model.geo.addPointOnGeometry(1, 0.911195, -0.61765, tag=691)
gmsh.model.geo.addPointOnGeometry(1, 0.8896, -0.613995, tag=692)
gmsh.model.geo.addPointOnGeometry(1, 0.86935, -0.62137, tag=693)
gmsh.model.geo.addPointOnGeometry(1, 0.8517, -0.6334, tag=694)
gmsh.model.geo.addPointOnGeometry(1, 0.83485, -0.646275, tag=695)
gmsh.model.geo.addPointOnGeometry(1, 0.81618, -0.656005, tag=696)
gmsh.model.geo.addPointOnGeometry(1, 0.79892, -0.667805, tag=697)
gmsh.model.geo.addPointOnGeometry(1, 0.78139, -0.678965, tag=698)
gmsh.model.geo.addPointOnGeometry(1, 0.76297, -0.688265, tag=699)
gmsh.model.geo.addPointOnGeometry(1, 0.758625, -0.70847, tag=700)
gmsh.model.geo.addPointOnGeometry(1, 0.754495, -0.72895, tag=701)
gmsh.model.geo.addPointOnGeometry(1, 0.74956, -0.74949, tag=702)
gmsh.model.geo.addPointOnGeometry(1, 0.728595, -0.747405, tag=703)
gmsh.model.geo.addPointOnGeometry(1, 0.713865, -0.73289, tag=704)
gmsh.model.geo.addPointOnGeometry(1, 0.693775, -0.73613, tag=705)
gmsh.model.geo.addPointOnGeometry(1, 0.67635, -0.74633, tag=706)
gmsh.model.geo.addPointOnGeometry(1, 0.671505, -0.766005, tag=707)
gmsh.model.geo.addPointOnGeometry(1, 0.69185, -0.768795, tag=708)
gmsh.model.geo.addPointOnGeometry(1, 0.71039, -0.759455, tag=709)
gmsh.model.geo.addPointOnGeometry(1, 0.702935, -0.778995, tag=710)
gmsh.model.geo.addPointOnGeometry(1, 0.724095, -0.779095, tag=711)
gmsh.model.geo.addPointOnGeometry(1, 0.74265, -0.768495, tag=712)
gmsh.model.geo.addPointOnGeometry(1, 0.76338, -0.76267, tag=713)
gmsh.model.geo.addPointOnGeometry(1, 0.781435, -0.75064, tag=714)
gmsh.model.geo.addPointOnGeometry(1, 0.800565, -0.74016, tag=715)
gmsh.model.geo.addPointOnGeometry(1, 0.822565, -0.73859, tag=716)
gmsh.model.geo.addPointOnGeometry(1, 0.844885, -0.740705, tag=717)
gmsh.model.geo.addPointOnGeometry(1, 0.865265, -0.7511, tag=718)
gmsh.model.geo.addPointOnGeometry(1, 0.886625, -0.74192, tag=719)
gmsh.model.geo.addPointOnGeometry(1, 0.910015, -0.739135, tag=720)
gmsh.model.geo.addPointOnGeometry(1, 0.92644, -0.72195, tag=721)
gmsh.model.geo.addPointOnGeometry(1, 0.938455, -0.70145, tag=722)
gmsh.model.geo.addPointOnGeometry(1, 0.915205, -0.705125, tag=723)
gmsh.model.geo.addPointOnGeometry(1, 0.92923, -0.68646, tag=724)
gmsh.model.geo.addPointOnGeometry(1, 0.92589, -0.663535, tag=725)
gmsh.model.geo.addPointOnGeometry(1, 1.53191, -1.29712, tag=726)
gmsh.model.geo.addPointOnGeometry(1, 1.58269, -1.30322, tag=727)
gmsh.model.geo.addPointOnGeometry(1, 1.62463, -1.27196, tag=728)
gmsh.model.geo.addPointOnGeometry(1, 1.67165, -1.24742, tag=729)
gmsh.model.geo.addPointOnGeometry(1, 1.68203, -1.19541, tag=730)
gmsh.model.geo.addPointOnGeometry(1, 1.72917, -1.17094, tag=731)
gmsh.model.geo.addPointOnGeometry(1, 1.74393, -1.16398, tag=732)
gmsh.model.geo.addPointOnGeometry(1, 1.73595, -1.11136, tag=733)
gmsh.model.geo.addPointOnGeometry(1, 1.74486, -1.06004, tag=734)
gmsh.model.geo.addPointOnGeometry(1, 1.74598, -1.00888, tag=735)
gmsh.model.geo.addPointOnGeometry(1, 1.70595, -0.97947, tag=736)
gmsh.model.geo.addPointOnGeometry(1, 1.6766, -0.941705, tag=737)
gmsh.model.geo.addPointOnGeometry(1, 1.64655, -0.906685, tag=738)
gmsh.model.geo.addPointOnGeometry(1, 1.62083, -0.87027, tag=739)
gmsh.model.geo.addPointOnGeometry(1, 1.60162, -0.83157, tag=740)
gmsh.model.geo.addPointOnGeometry(1, 1.57417, -0.799945, tag=741)
gmsh.model.geo.addPointOnGeometry(1, 1.54181, -0.775635, tag=742)
gmsh.model.geo.addPointOnGeometry(1, 1.51219, -0.75006, tag=743)
gmsh.model.geo.addPointOnGeometry(1, 1.47521, -0.74188, tag=744)
gmsh.model.geo.addPointOnGeometry(1, 1.4403, -0.73061, tag=745)
gmsh.model.geo.addPointOnGeometry(1, 1.40656, -0.71953, tag=746)
gmsh.model.geo.addPointOnGeometry(1, 1.37328, -0.72887, tag=747)
gmsh.model.geo.addPointOnGeometry(1, 1.34125, -0.739725, tag=748)
gmsh.model.geo.addPointOnGeometry(1, 1.3091, -0.73235, tag=749)
gmsh.model.geo.addPointOnGeometry(1, 1.2858, -0.75482, tag=750)
gmsh.model.geo.addPointOnGeometry(1, 1.25466, -0.74839, tag=751)
gmsh.model.geo.addPointOnGeometry(1, 1.22385, -0.74564, tag=752)
gmsh.model.geo.addPointOnGeometry(1, 1.19843, -0.76223, tag=753)
gmsh.model.geo.addPointOnGeometry(1, 1.17137, -0.775075, tag=754)
gmsh.model.geo.addPointOnGeometry(1, 1.14256, -0.76949, tag=755)
gmsh.model.geo.addPointOnGeometry(1, 1.1178, -0.75521, tag=756)
gmsh.model.geo.addPointOnGeometry(1, 1.09, -0.75313, tag=757)
gmsh.model.geo.addPointOnGeometry(1, 1.06453, -0.74356, tag=758)
gmsh.model.geo.addPointOnGeometry(1, 1.0436, -0.76024, tag=759)
gmsh.model.geo.addPointOnGeometry(1, 1.01873, -0.75154, tag=760)
gmsh.model.geo.addPointOnGeometry(1, 0.993575, -0.746095, tag=761)
gmsh.model.geo.addPointOnGeometry(1, 0.970265, -0.736685, tag=762)
gmsh.model.geo.addPointOnGeometry(1, 0.973405, -0.761545, tag=763)
gmsh.model.geo.addPointOnGeometry(1, 0.988095, -0.782485, tag=764)
gmsh.model.geo.addPointOnGeometry(1, 1.0081, -0.79943, tag=765)
gmsh.model.geo.addPointOnGeometry(1, 1.02805, -0.81747, tag=766)
gmsh.model.geo.addPointOnGeometry(1, 1.04565, -0.838745, tag=767)
gmsh.model.geo.addPointOnGeometry(1, 1.05567, -0.865215, tag=768)
gmsh.model.geo.addPointOnGeometry(1, 1.03468, -0.88461, tag=769)
gmsh.model.geo.addPointOnGeometry(1, 1.00748, -0.89254, tag=770)
gmsh.model.geo.addPointOnGeometry(1, 0.980545, -0.899865, tag=771)
gmsh.model.geo.addPointOnGeometry(1, 0.954555, -0.908985, tag=772)
gmsh.model.geo.addPointOnGeometry(1, 0.927745, -0.91332, tag=773)
gmsh.model.geo.addPointOnGeometry(1, 0.918845, -0.888215, tag=774)
gmsh.model.geo.addPointOnGeometry(1, 0.913425, -0.862725, tag=775)
gmsh.model.geo.addPointOnGeometry(1, 0.8881, -0.86641, tag=776)
gmsh.model.geo.addPointOnGeometry(1, 0.867365, -0.880975, tag=777)
gmsh.model.geo.addPointOnGeometry(1, 0.84546, -0.89346, tag=778)
gmsh.model.geo.addPointOnGeometry(1, 0.82144, -0.900365, tag=779)
gmsh.model.geo.addPointOnGeometry(1, 0.826825, -0.924905, tag=780)
gmsh.model.geo.addPointOnGeometry(1, 0.80977, -0.94377, tag=781)
gmsh.model.geo.addPointOnGeometry(1, 0.81717, -0.968445, tag=782)
gmsh.model.geo.addPointOnGeometry(1, 0.816955, -0.99476, tag=783)
gmsh.model.geo.addPointOnGeometry(1, 0.810735, -1.0208, tag=784)
gmsh.model.geo.addPointOnGeometry(1, 0.78433, -1.0166, tag=785)
gmsh.model.geo.addPointOnGeometry(1, 0.76227, -1.03123, tag=786)
gmsh.model.geo.addPointOnGeometry(1, 0.75001, -1.05483, tag=787)
gmsh.model.geo.addPointOnGeometry(1, 0.7468, -1.08165, tag=788)
gmsh.model.geo.addPointOnGeometry(1, 0.74511, -1.10917, tag=789)
gmsh.model.geo.addPointOnGeometry(1, 0.754025, -1.13594, tag=790)
gmsh.model.geo.addPointOnGeometry(1, 0.729, -1.12282, tag=791)
gmsh.model.geo.addPointOnGeometry(1, 0.72353, -1.15049, tag=792)
gmsh.model.geo.addPointOnGeometry(1, 0.73422, -1.17729, tag=793)
gmsh.model.geo.addPointOnGeometry(1, 0.733285, -1.20688, tag=794)
gmsh.model.geo.addPointOnGeometry(1, 0.71717, -1.23235, tag=795)
gmsh.model.geo.addPointOnGeometry(1, 0.692545, -1.25013, tag=796)
gmsh.model.geo.addPointOnGeometry(1, 0.675585, -1.27564, tag=797)
gmsh.model.geo.addPointOnGeometry(1, 0.64951, -1.29218, tag=798)
gmsh.model.geo.addPointOnGeometry(1, 0.633845, -1.31913, tag=799)
gmsh.model.geo.addPointOnGeometry(1, 0.616755, -1.34579, tag=800)
gmsh.model.geo.addPointOnGeometry(1, 0.602985, -1.37492, tag=801)
gmsh.model.geo.addPointOnGeometry(1, 0.61211, -1.40667, tag=802)
gmsh.model.geo.addPointOnGeometry(1, 0.62946, -1.43598, tag=803)
gmsh.model.geo.addPointOnGeometry(1, 0.6568, -1.45795, tag=804)
gmsh.model.geo.addPointOnGeometry(1, 0.660405, -1.4939, tag=805)
gmsh.model.geo.addPointOnGeometry(1, 0.684475, -1.52234, tag=806)
gmsh.model.geo.addPointOnGeometry(1, 0.715965, -1.54435, tag=807)
gmsh.model.geo.addPointOnGeometry(1, 0.737005, -1.57795, tag=808)
gmsh.model.geo.addPointOnGeometry(1, 0.766125, -1.60683, tag=809)
gmsh.model.geo.addPointOnGeometry(1, 0.791225, -1.64104, tag=810)
gmsh.model.geo.addPointOnGeometry(1, 0.80096, -1.68392, tag=811)
gmsh.model.geo.addPointOnGeometry(1, 0.8331, -1.71624, tag=812)
gmsh.model.geo.addPointOnGeometry(1, 0.8792, -1.709, tag=813)
gmsh.model.geo.addPointOnGeometry(1, 0.912645, -1.67643, tag=814)
gmsh.model.geo.addPointOnGeometry(1, 0.933445, -1.63546, tag=815)
gmsh.model.geo.addPointOnGeometry(1, 0.96782, -1.60594, tag=816)
gmsh.model.geo.addPointOnGeometry(1, 1.00755, -1.58437, tag=817)
gmsh.model.geo.addPointOnGeometry(1, 1.03894, -1.55202, tag=818)
gmsh.model.geo.addPointOnGeometry(1, 1.04835, -1.50873, tag=819)
gmsh.model.geo.addPointOnGeometry(1, 1.06567, -1.46898, tag=820)
gmsh.model.geo.addPointOnGeometry(1, 1.09942, -1.44249, tag=821)
gmsh.model.geo.addPointOnGeometry(1, 1.1223, -1.4065, tag=822)
gmsh.model.geo.addPointOnGeometry(1, 1.14756, -1.37272, tag=823)
gmsh.model.geo.addPointOnGeometry(1, 1.17674, -1.34258, tag=824)
gmsh.model.geo.addPointOnGeometry(1, 1.2176, -1.33196, tag=825)
gmsh.model.geo.addPointOnGeometry(1, 1.25401, -1.30962, tag=826)
gmsh.model.geo.addPointOnGeometry(1, 1.29678, -1.31773, tag=827)
gmsh.model.geo.addPointOnGeometry(1, 1.3404, -1.32831, tag=828)
gmsh.model.geo.addPointOnGeometry(1, 1.36375, -1.28934, tag=829)
gmsh.model.geo.addPointOnGeometry(1, 1.36133, -1.24479, tag=830)
gmsh.model.geo.addPointOnGeometry(1, 1.393, -1.27656, tag=831)
gmsh.model.geo.addPointOnGeometry(1, 1.40839, -1.32042, tag=832)
gmsh.model.geo.addPointOnGeometry(1, 1.40977, -1.27376, tag=833)
gmsh.model.geo.addPointOnGeometry(1, 1.44874, -1.30001, tag=834)
gmsh.model.geo.addPointOnGeometry(1, 1.48874, -1.27324, tag=835)
gmsh.model.geo.addPointOnGeometry(1, -0.7038, 2.45018, tag=836)
gmsh.model.geo.addPointOnGeometry(1, -0.74965, 2.51183, tag=837)
gmsh.model.geo.addPointOnGeometry(1, -0.723955, 2.58807, tag=838)
gmsh.model.geo.addPointOnGeometry(1, -0.76779, 2.66022, tag=839)
gmsh.model.geo.addPointOnGeometry(1, -0.80263, 2.74225, tag=840)
gmsh.model.geo.addPointOnGeometry(1, -0.87934, 2.79617, tag=841)
gmsh.model.geo.addPointOnGeometry(1, -0.855855, 2.86358, tag=842)
gmsh.model.geo.addPointOnGeometry(1, -0.9316, 2.80104, tag=843)
gmsh.model.geo.addPointOnGeometry(1, -0.936065, 2.90093, tag=844)
gmsh.model.geo.addPointOnGeometry(1, -0.97799, 2.80939, tag=845)
gmsh.model.geo.addPointOnGeometry(1, -1.03582, 2.73162, tag=846)
gmsh.model.geo.addPointOnGeometry(1, -1.00697, 2.64358, tag=847)
gmsh.model.geo.addPointOnGeometry(1, -1.00346, 2.55596, tag=848)
gmsh.model.geo.addPointOnGeometry(1, -1.02176, 2.47448, tag=849)
gmsh.model.geo.addPointOnGeometry(1, -1.05428, 2.40116, tag=850)
gmsh.model.geo.addPointOnGeometry(1, -1.04354, 2.32508, tag=851)
gmsh.model.geo.addPointOnGeometry(1, -0.96958, 2.32046, tag=852)
gmsh.model.geo.addPointOnGeometry(1, -0.947455, 2.25252, tag=853)
gmsh.model.geo.addPointOnGeometry(1, -0.994975, 2.2024, tag=854)
gmsh.model.geo.addPointOnGeometry(1, -0.98285, 2.13666, tag=855)
gmsh.model.geo.addPointOnGeometry(1, -0.9956, 2.07383, tag=856)
gmsh.model.geo.addPointOnGeometry(1, -0.946585, 2.03642, tag=857)
gmsh.model.geo.addPointOnGeometry(1, -0.91989, 1.98368, tag=858)
gmsh.model.geo.addPointOnGeometry(1, -0.97724, 1.97394, tag=859)
gmsh.model.geo.addPointOnGeometry(1, -1.01541, 1.93021, tag=860)
gmsh.model.geo.addPointOnGeometry(1, -0.99442, 1.87791, tag=861)
gmsh.model.geo.addPointOnGeometry(1, -1.02108, 1.83032, tag=862)
gmsh.model.geo.addPointOnGeometry(1, -1.06132, 1.79476, tag=863)
gmsh.model.geo.addPointOnGeometry(1, -1.09466, 1.75344, tag=864)
gmsh.model.geo.addPointOnGeometry(1, -1.10055, 1.70186, tag=865)
gmsh.model.geo.addPointOnGeometry(1, -1.086, 1.65393, tag=866)
gmsh.model.geo.addPointOnGeometry(1, -1.04728, 1.62511, tag=867)
gmsh.model.geo.addPointOnGeometry(1, -1.00051, 1.62237, tag=868)
gmsh.model.geo.addPointOnGeometry(1, -0.971525, 1.58733, tag=869)
gmsh.model.geo.addPointOnGeometry(1, -1.01205, 1.60771, tag=870)
gmsh.model.geo.addPointOnGeometry(1, -1.05585, 1.59272, tag=871)
gmsh.model.geo.addPointOnGeometry(1, -1.09708, 1.57095, tag=872)
gmsh.model.geo.addPointOnGeometry(1, -1.12024, 1.5308, tag=873)
gmsh.model.geo.addPointOnGeometry(1, -1.11908, 1.48553, tag=874)
gmsh.model.geo.addPointOnGeometry(1, -1.11231, 1.44218, tag=875)
gmsh.model.geo.addPointOnGeometry(1, -1.09717, 1.40252, tag=876)
gmsh.model.geo.addPointOnGeometry(1, -1.09412, 1.36153, tag=877)
gmsh.model.geo.addPointOnGeometry(1, -1.11342, 1.39796, tag=878)
gmsh.model.geo.addPointOnGeometry(1, -1.11818, 1.35681, tag=879)
gmsh.model.geo.addPointOnGeometry(1, -1.10708, 1.31811, tag=880)
gmsh.model.geo.addPointOnGeometry(1, -1.10545, 1.27904, tag=881)
gmsh.model.geo.addPointOnGeometry(1, -1.09425, 1.24273, tag=882)
gmsh.model.geo.addPointOnGeometry(1, -1.06919, 1.21577, tag=883)
gmsh.model.geo.addPointOnGeometry(1, -1.04524, 1.18937, tag=884)
gmsh.model.geo.addPointOnGeometry(1, -1.05276, 1.15544, tag=885)
gmsh.model.geo.addPointOnGeometry(1, -1.05941, 1.12197, tag=886)
gmsh.model.geo.addPointOnGeometry(1, -1.07719, 1.09336, tag=887)
gmsh.model.geo.addPointOnGeometry(1, -1.07761, 1.06016, tag=888)
gmsh.model.geo.addPointOnGeometry(1, -1.10014, 1.03626, tag=889)
gmsh.model.geo.addPointOnGeometry(1, -1.12125, 1.01113, tag=890)
gmsh.model.geo.addPointOnGeometry(1, -1.11829, 0.97882, tag=891)
gmsh.model.geo.addPointOnGeometry(1, -1.10191, 0.95175, tag=892)
gmsh.model.geo.addPointOnGeometry(1, -1.092, 0.92257, tag=893)
gmsh.model.geo.addPointOnGeometry(1, -1.07765, 0.89618, tag=894)
gmsh.model.geo.addPointOnGeometry(1, -1.06232, 0.871255, tag=895)
gmsh.model.geo.addPointOnGeometry(1, -1.04384, 0.84957, tag=896)
gmsh.model.geo.addPointOnGeometry(1, -1.02526, 0.828965, tag=897)
gmsh.model.geo.addPointOnGeometry(1, -1.0033, 0.813205, tag=898)
gmsh.model.geo.addPointOnGeometry(1, -0.983685, 0.795625, tag=899)
gmsh.model.geo.addPointOnGeometry(1, -0.98351, 0.769825, tag=900)
gmsh.model.geo.addPointOnGeometry(1, -0.973825, 0.74642, tag=901)
gmsh.model.geo.addPointOnGeometry(1, -0.966255, 0.722795, tag=902)
gmsh.model.geo.addPointOnGeometry(1, -0.963425, 0.6986, tag=903)
gmsh.model.geo.addPointOnGeometry(1, -0.956895, 0.675565, tag=904)
gmsh.model.geo.addPointOnGeometry(1, -0.94342, 0.656355, tag=905)
gmsh.model.geo.addPointOnGeometry(1, -0.923645, 0.64472, tag=906)
gmsh.model.geo.addPointOnGeometry(1, -0.902635, 0.636825, tag=907)
gmsh.model.geo.addPointOnGeometry(1, -0.88228, 0.645385, tag=908)
gmsh.model.geo.addPointOnGeometry(1, -0.86454, 0.65819, tag=909)
gmsh.model.geo.addPointOnGeometry(1, -0.84355, 0.663535, tag=910)
gmsh.model.geo.addPointOnGeometry(1, -0.82286, 0.668935, tag=911)
gmsh.model.geo.addPointOnGeometry(1, -0.80333, 0.677035, tag=912)
gmsh.model.geo.addPointOnGeometry(1, -0.79037, 0.69362, tag=913)
gmsh.model.geo.addPointOnGeometry(1, -0.7746, 0.70754, tag=914)
gmsh.model.geo.addPointOnGeometry(1, -0.75662, 0.71828, tag=915)
gmsh.model.geo.addPointOnGeometry(1, -0.751995, 0.738765, tag=916)
gmsh.model.geo.addPointOnGeometry(1, -0.736325, 0.72495, tag=917)
gmsh.model.geo.addPointOnGeometry(1, -0.716635, 0.73094, tag=918)
gmsh.model.geo.addPointOnGeometry(1, -0.69818, 0.739655, tag=919)
gmsh.model.geo.addPointOnGeometry(1, -0.679955, 0.74857, tag=920)
gmsh.model.geo.addPointOnGeometry(1, -0.6809, 0.768935, tag=921)
gmsh.model.geo.addPointOnGeometry(1, -0.676305, 0.78909, tag=922)
gmsh.model.geo.addPointOnGeometry(1, -0.671075, 0.76916, tag=923)
gmsh.model.geo.addPointOnGeometry(1, -0.651465, 0.764105, tag=924)
gmsh.model.geo.addPointOnGeometry(1, -0.637295, 0.778365, tag=925)
gmsh.model.geo.addPointOnGeometry(1, -0.63017, 0.79729, tag=926)
gmsh.model.geo.addPointOnGeometry(1, -0.628185, 0.77723, tag=927)
gmsh.model.geo.addPointOnGeometry(1, -0.62884, 0.7574, tag=928)
gmsh.model.geo.addPointOnGeometry(1, -0.612925, 0.74612, tag=929)
gmsh.model.geo.addPointOnGeometry(1, -0.59633, 0.73655, tag=930)
gmsh.model.geo.addPointOnGeometry(1, -0.58162, 0.72483, tag=931)
gmsh.model.geo.addPointOnGeometry(1, -0.56313, 0.72621, tag=932)
gmsh.model.geo.addPointOnGeometry(1, -0.544845, 0.727825, tag=933)
gmsh.model.geo.addPointOnGeometry(1, -0.52712, 0.731955, tag=934)
gmsh.model.geo.addPointOnGeometry(1, -0.51167, 0.741435, tag=935)
gmsh.model.geo.addPointOnGeometry(1, -0.496816, 0.75181, tag=936)
gmsh.model.geo.addPointOnGeometry(1, -0.478993, 0.754695, tag=937)
gmsh.model.geo.addPointOnGeometry(1, -0.461295, 0.75208, tag=938)
gmsh.model.geo.addPointOnGeometry(1, -0.444845, 0.745655, tag=939)
gmsh.model.geo.addPointOnGeometry(1, -0.427382, 0.746015, tag=940)
gmsh.model.geo.addPointOnGeometry(1, -0.411008, 0.740485, tag=941)
gmsh.model.geo.addPointOnGeometry(1, -0.393929, 0.741555, tag=942)
gmsh.model.geo.addPointOnGeometry(1, -0.377373, 0.737875, tag=943)
gmsh.model.geo.addPointOnGeometry(1, -0.362672, 0.74616, tag=944)
gmsh.model.geo.addPointOnGeometry(1, -0.353038, 0.760115, tag=945)
gmsh.model.geo.addPointOnGeometry(1, -0.336107, 0.759145, tag=946)
gmsh.model.geo.addPointOnGeometry(1, -0.320877, 0.766475, tag=947)
gmsh.model.geo.addPointOnGeometry(1, -0.304068, 0.765395, tag=948)
gmsh.model.geo.addPointOnGeometry(1, -0.287344, 0.76492, tag=949)
gmsh.model.geo.addPointOnGeometry(1, -0.273339, 0.774045, tag=950)
gmsh.model.geo.addPointOnGeometry(1, -0.263151, 0.787425, tag=951)
gmsh.model.geo.addPointOnGeometry(1, -0.274238, 0.80035, tag=952)
gmsh.model.geo.addPointOnGeometry(1, -0.262039, 0.788465, tag=953)
gmsh.model.geo.addPointOnGeometry(1, -0.257467, 0.772335, tag=954)
gmsh.model.geo.addPointOnGeometry(1, -0.241652, 0.777475, tag=955)
gmsh.model.geo.addPointOnGeometry(1, -0.228627, 0.78789, tag=956)
gmsh.model.geo.addPointOnGeometry(1, -0.213932, 0.79596, tag=957)
gmsh.model.geo.addPointOnGeometry(1, -0.208945, 0.81212, tag=958)
gmsh.model.geo.addPointOnGeometry(1, -0.203101, 0.828245, tag=959)
gmsh.model.geo.addPointOnGeometry(1, -0.199453, 0.845265, tag=960)
gmsh.model.geo.addPointOnGeometry(1, -0.184093, 0.837, tag=961)
gmsh.model.geo.addPointOnGeometry(1, -0.16806, 0.830595, tag=962)
gmsh.model.geo.addPointOnGeometry(1, -0.151087, 0.83323, tag=963)
gmsh.model.geo.addPointOnGeometry(1, -0.137139, 0.843355, tag=964)
gmsh.model.geo.addPointOnGeometry(1, -0.119873, 0.84282, tag=965)
gmsh.model.geo.addPointOnGeometry(1, -0.104234, 0.835735, tag=966)
gmsh.model.geo.addPointOnGeometry(1, -0.092753, 0.823225, tag=967)
gmsh.model.geo.addPointOnGeometry(1, -0.088999, 0.806925, tag=968)
gmsh.model.geo.addPointOnGeometry(1, -0.089627, 0.79048, tag=969)
gmsh.model.geo.addPointOnGeometry(1, -0.0908815, 0.774325, tag=970)
gmsh.model.geo.addPointOnGeometry(1, -0.0877845, 0.75867, tag=971)
gmsh.model.geo.addPointOnGeometry(1, -0.073319, 0.75238, tag=972)
gmsh.model.geo.addPointOnGeometry(1, -0.057614, 0.75253, tag=973)
gmsh.model.geo.addPointOnGeometry(1, -0.0421587, 0.75535, tag=974)
gmsh.model.geo.addPointOnGeometry(1, -0.0264479, 0.75597, tag=975)
gmsh.model.geo.addPointOnGeometry(1, -0.0222705, 0.74093, tag=976)
gmsh.model.geo.addPointOnGeometry(1, -0.0237102, 0.725615, tag=977)
gmsh.model.geo.addPointOnGeometry(1, -0.0302156, 0.711905, tag=978)
gmsh.model.geo.addPointOnGeometry(1, -0.0311935, 0.696965, tag=979)
gmsh.model.geo.addPointOnGeometry(1, -0.0389904, 0.684405, tag=980)
gmsh.model.geo.addPointOnGeometry(1, -0.0251922, 0.67945, tag=981)
gmsh.model.geo.addPointOnGeometry(1, -0.0108746, 0.682515, tag=982)
gmsh.model.geo.addPointOnGeometry(1, 0.00325215, 0.68653, tag=983)
gmsh.model.geo.addPointOnGeometry(1, 0.00591575, 0.701105, tag=984)
gmsh.model.geo.addPointOnGeometry(1, 0.0148585, 0.71315, tag=985)
gmsh.model.geo.addPointOnGeometry(1, 0.0294932, 0.716945, tag=986)
gmsh.model.geo.addPointOnGeometry(1, 0.044439, 0.719565, tag=987)
gmsh.model.geo.addPointOnGeometry(1, 0.0595865, 0.71834, tag=988)
gmsh.model.geo.addPointOnGeometry(1, 0.073108, 0.71149, tag=989)
gmsh.model.geo.addPointOnGeometry(1, 0.0788355, 0.6976, tag=990)
gmsh.model.geo.addPointOnGeometry(1, 0.0865275, 0.6849, tag=991)
gmsh.model.geo.addPointOnGeometry(1, 0.090691, 0.67083, tag=992)
gmsh.model.geo.addPointOnGeometry(1, 0.0894295, 0.6564, tag=993)
gmsh.model.geo.addPointOnGeometry(1, 0.0880665, 0.64217, tag=994)
gmsh.model.geo.addPointOnGeometry(1, 0.085012, 0.62839, tag=995)
gmsh.model.geo.addPointOnGeometry(1, 0.0792425, 0.615705, tag=996)
gmsh.model.geo.addPointOnGeometry(1, 0.077653, 0.602025, tag=997)
gmsh.model.geo.addPointOnGeometry(1, 0.0679255, 0.59249, tag=998)
gmsh.model.geo.addPointOnGeometry(1, 0.0552335, 0.58783, tag=999)
gmsh.model.geo.addPointOnGeometry(1, 0.043862, 0.580665, tag=1000)
gmsh.model.geo.addPointOnGeometry(1, 0.0305119, 0.57976, tag=1001)
gmsh.model.geo.addPointOnGeometry(1, 0.0171462, 0.58011, tag=1002)
gmsh.model.geo.addPointOnGeometry(1, 0.00497601, 0.585715, tag=1003)
gmsh.model.geo.addPointOnGeometry(1, -0.00819575, 0.588425, tag=1004)
gmsh.model.geo.addPointOnGeometry(1, -0.00196848, 0.576565, tag=1005)
gmsh.model.geo.addPointOnGeometry(1, -0.0148709, 0.57331, tag=1006)
gmsh.model.geo.addPointOnGeometry(1, -0.0280621, 0.57175, tag=1007)
gmsh.model.geo.addPointOnGeometry(1, -0.0412571, 0.573335, tag=1008)
gmsh.model.geo.addPointOnGeometry(1, -0.0542185, 0.576445, tag=1009)
gmsh.model.geo.addPointOnGeometry(1, -0.067297, 0.57925, tag=1010)
gmsh.model.geo.addPointOnGeometry(1, -0.0760475, 0.58949, tag=1011)
gmsh.model.geo.addPointOnGeometry(1, -0.0785785, 0.602865, tag=1012)
gmsh.model.geo.addPointOnGeometry(1, -0.0876765, 0.613195, tag=1013)
gmsh.model.geo.addPointOnGeometry(1, -0.0980385, 0.62247, tag=1014)
gmsh.model.geo.addPointOnGeometry(1, -0.107046, 0.61186, tag=1015)
gmsh.model.geo.addPointOnGeometry(1, -0.102208, 0.598965, tag=1016)
gmsh.model.geo.addPointOnGeometry(1, -0.0953365, 0.58721, tag=1017)
gmsh.model.geo.addPointOnGeometry(1, -0.089256, 0.575195, tag=1018)
gmsh.model.geo.addPointOnGeometry(1, -0.0842745, 0.56285, tag=1019)
gmsh.model.geo.addPointOnGeometry(1, -0.0854255, 0.549735, tag=1020)
gmsh.model.geo.addPointOnGeometry(1, -0.093724, 0.539665, tag=1021)
gmsh.model.geo.addPointOnGeometry(1, -0.101442, 0.52926, tag=1022)
gmsh.model.geo.addPointOnGeometry(1, -0.110898, 0.52053, tag=1023)
gmsh.model.geo.addPointOnGeometry(1, -0.118365, 0.51015, tag=1024)
gmsh.model.geo.addPointOnGeometry(1, -0.124939, 0.49929, tag=1025)
gmsh.model.geo.addPointOnGeometry(1, -0.121903, 0.487076, tag=1026)
gmsh.model.geo.addPointOnGeometry(1, -0.113808, 0.477596, tag=1027)
gmsh.model.geo.addPointOnGeometry(1, -0.111811, 0.465407, tag=1028)
gmsh.model.geo.addPointOnGeometry(1, -0.121089, 0.473543, tag=1029)
gmsh.model.geo.addPointOnGeometry(1, -0.120632, 0.461221, tag=1030)
gmsh.model.geo.addPointOnGeometry(1, -0.128125, 0.451545, tag=1031)
gmsh.model.geo.addPointOnGeometry(1, -0.127553, 0.43941, tag=1032)
gmsh.model.geo.addPointOnGeometry(1, -0.136799, 0.431648, tag=1033)
gmsh.model.geo.addPointOnGeometry(1, -0.146837, 0.425006, tag=1034)
gmsh.model.geo.addPointOnGeometry(1, -0.143792, 0.413431, tag=1035)
gmsh.model.geo.addPointOnGeometry(1, -0.144629, 0.401592, tag=1036)
gmsh.model.geo.addPointOnGeometry(1, -0.151034, 0.391691, tag=1037)
gmsh.model.geo.addPointOnGeometry(1, -0.160749, 0.385077, tag=1038)
gmsh.model.geo.addPointOnGeometry(1, -0.168382, 0.376183, tag=1039)
gmsh.model.geo.addPointOnGeometry(1, -0.172908, 0.365431, tag=1040)
gmsh.model.geo.addPointOnGeometry(1, -0.173347, 0.377101, tag=1041)
gmsh.model.geo.addPointOnGeometry(1, -0.171813, 0.388765, tag=1042)
gmsh.model.geo.addPointOnGeometry(1, -0.181971, 0.382757, tag=1043)
gmsh.model.geo.addPointOnGeometry(1, -0.187663, 0.372458, tag=1044)
gmsh.model.geo.addPointOnGeometry(1, -0.195778, 0.363996, tag=1045)
gmsh.model.geo.addPointOnGeometry(1, -0.184288, 0.361903, tag=1046)
gmsh.model.geo.addPointOnGeometry(1, -0.172684, 0.362698, tag=1047)
gmsh.model.geo.addPointOnGeometry(1, -0.165293, 0.353797, tag=1048)
gmsh.model.geo.addPointOnGeometry(1, -0.154017, 0.351543, tag=1049)
gmsh.model.geo.addPointOnGeometry(1, -0.162703, 0.344067, tag=1050)
gmsh.model.geo.addPointOnGeometry(1, -0.15262, 0.338716, tag=1051)
gmsh.model.geo.addPointOnGeometry(1, -0.144095, 0.346275, tag=1052)
gmsh.model.geo.addPointOnGeometry(1, -0.137024, 0.355253, tag=1053)
gmsh.model.geo.addPointOnGeometry(1, -0.132969, 0.365998, tag=1054)
gmsh.model.geo.addPointOnGeometry(1, -0.134114, 0.354577, tag=1055)
gmsh.model.geo.addPointOnGeometry(1, -0.138706, 0.344135, tag=1056)
gmsh.model.geo.addPointOnGeometry(1, -0.143611, 0.3339, tag=1057)
gmsh.model.geo.addPointOnGeometry(1, -0.152877, 0.327408, tag=1058)
gmsh.model.geo.addPointOnGeometry(1, -0.163153, 0.322686, tag=1059)
gmsh.model.geo.addPointOnGeometry(1, -0.173468, 0.318048, tag=1060)
gmsh.model.geo.addPointOnGeometry(1, -0.181603, 0.310201, tag=1061)
gmsh.model.geo.addPointOnGeometry(1, -0.183742, 0.299144, tag=1062)
gmsh.model.geo.addPointOnGeometry(1, -0.191131, 0.290698, tag=1063)
gmsh.model.geo.addPointOnGeometry(1, -0.190341, 0.279549, tag=1064)
gmsh.model.geo.addPointOnGeometry(1, -0.180661, 0.274094, tag=1065)
gmsh.model.geo.addPointOnGeometry(1, -0.169621, 0.274797, tag=1066)
gmsh.model.geo.addPointOnGeometry(1, -0.165253, 0.284962, tag=1067)
gmsh.model.geo.addPointOnGeometry(1, -0.169233, 0.274639, tag=1068)
gmsh.model.geo.addPointOnGeometry(1, -0.159177, 0.270149, tag=1069)
gmsh.model.geo.addPointOnGeometry(1, -0.148273, 0.268992, tag=1070)
gmsh.model.geo.addPointOnGeometry(1, -0.138174, 0.264844, tag=1071)
gmsh.model.geo.addPointOnGeometry(1, -0.134739, 0.254539, tag=1072)
gmsh.model.geo.addPointOnGeometry(1, -0.126071, 0.248094, tag=1073)
gmsh.model.geo.addPointOnGeometry(1, -0.117295, 0.241888, tag=1074)
gmsh.model.geo.addPointOnGeometry(1, -0.114521, 0.252269, tag=1075)
gmsh.model.geo.addPointOnGeometry(1, -0.109374, 0.261748, tag=1076)
gmsh.model.geo.addPointOnGeometry(1, -0.0986965, 0.260192, tag=1077)
gmsh.model.geo.addPointOnGeometry(1, -0.0914245, 0.252278, tag=1078)
gmsh.model.geo.addPointOnGeometry(1, -0.0903, 0.241645, tag=1079)
gmsh.model.geo.addPointOnGeometry(1, -0.0796895, 0.242666, tag=1080)
gmsh.model.geo.addPointOnGeometry(1, -0.0713045, 0.236132, tag=1081)
gmsh.model.geo.addPointOnGeometry(1, -0.061303, 0.239677, tag=1082)
gmsh.model.geo.addPointOnGeometry(1, -0.0507545, 0.240809, tag=1083)
gmsh.model.geo.addPointOnGeometry(1, -0.0545895, 0.250725, tag=1084)
gmsh.model.geo.addPointOnGeometry(1, -0.057363, 0.261047, tag=1085)
gmsh.model.geo.addPointOnGeometry(1, -0.0563575, 0.271741, tag=1086)
gmsh.model.geo.addPointOnGeometry(1, -0.06389, 0.279477, tag=1087)
gmsh.model.geo.addPointOnGeometry(1, -0.0691055, 0.288995, tag=1088)
gmsh.model.geo.addPointOnGeometry(1, -0.069082, 0.29991, tag=1089)
gmsh.model.geo.addPointOnGeometry(1, -0.062867, 0.308951, tag=1090)
gmsh.model.geo.addPointOnGeometry(1, -0.062423, 0.319971, tag=1091)
gmsh.model.geo.addPointOnGeometry(1, -0.065074, 0.33075, tag=1092)
gmsh.model.geo.addPointOnGeometry(1, -0.0677935, 0.34159, tag=1093)
gmsh.model.geo.addPointOnGeometry(1, -0.0577685, 0.346637, tag=1094)
gmsh.model.geo.addPointOnGeometry(1, -0.0501705, 0.338402, tag=1095)
gmsh.model.geo.addPointOnGeometry(1, -0.0454189, 0.328329, tag=1096)
gmsh.model.geo.addPointOnGeometry(1, -0.0426719, 0.317613, tag=1097)
gmsh.model.geo.addPointOnGeometry(1, -0.0338967, 0.310974, tag=1098)
gmsh.model.geo.addPointOnGeometry(1, -0.0231464, 0.308791, tag=1099)
gmsh.model.geo.addPointOnGeometry(1, -0.0128878, 0.304974, tag=1100)
gmsh.model.geo.addPointOnGeometry(1, -0.00486551, 0.297577, tag=1101)
gmsh.model.geo.addPointOnGeometry(1, 0.00517675, 0.293394, tag=1102)
gmsh.model.geo.addPointOnGeometry(1, 0.0139657, 0.287041, tag=1103)
gmsh.model.geo.addPointOnGeometry(1, 0.0204901, 0.278428, tag=1104)
gmsh.model.geo.addPointOnGeometry(1, 0.0226394, 0.267895, tag=1105)
gmsh.model.geo.addPointOnGeometry(1, 0.0188267, 0.257901, tag=1106)
gmsh.model.geo.addPointOnGeometry(1, 0.0138178, 0.248509, tag=1107)
gmsh.model.geo.addPointOnGeometry(1, 0.00579805, 0.241574, tag=1108)
gmsh.model.geo.addPointOnGeometry(1, 0.0110256, 0.232395, tag=1109)
gmsh.model.geo.addPointOnGeometry(1, 0.000655325, 0.234311, tag=1110)
gmsh.model.geo.addPointOnGeometry(1, -0.00788575, 0.228143, tag=1111)
gmsh.model.geo.addPointOnGeometry(1, -0.00354544, 0.218582, tag=1112)
gmsh.model.geo.addPointOnGeometry(1, -0.0132915, 0.214756, tag=1113)
gmsh.model.geo.addPointOnGeometry(1, -0.0223821, 0.209594, tag=1114)
gmsh.model.geo.addPointOnGeometry(1, -0.0289497, 0.201491, tag=1115)
gmsh.model.geo.addPointOnGeometry(1, -0.0268031, 0.191321, tag=1116)
gmsh.model.geo.addPointOnGeometry(1, -0.0247868, 0.181165, tag=1117)
gmsh.model.geo.addPointOnGeometry(1, -0.015101, 0.177586, tag=1118)
gmsh.model.geo.addPointOnGeometry(1, -0.0141893, 0.187882, tag=1119)
gmsh.model.geo.addPointOnGeometry(1, -0.0121949, 0.198064, tag=1120)
gmsh.model.geo.addPointOnGeometry(1, -0.0068883, 0.189147, tag=1121)
gmsh.model.geo.addPointOnGeometry(1, 0.00230525, 0.184394, tag=1122)
gmsh.model.geo.addPointOnGeometry(1, 0.00737745, 0.175402, tag=1123)
gmsh.model.geo.addPointOnGeometry(1, 0.00839005, 0.16516, tag=1124)
gmsh.model.geo.addPointOnGeometry(1, 0.0185781, 0.163842, tag=1125)
gmsh.model.geo.addPointOnGeometry(1, 0.0198206, 0.174056, tag=1126)
gmsh.model.geo.addPointOnGeometry(1, 0.0118849, 0.18065, tag=1127)
gmsh.model.geo.addPointOnGeometry(1, 0.0139555, 0.190788, tag=1128)
gmsh.model.geo.addPointOnGeometry(1, 0.0183659, 0.20019, tag=1129)
gmsh.model.geo.addPointOnGeometry(1, 0.0221888, 0.190533, tag=1130)
gmsh.model.geo.addPointOnGeometry(1, 0.0319361, 0.194094, tag=1131)
gmsh.model.geo.addPointOnGeometry(1, 0.0421031, 0.191967, tag=1132)
gmsh.model.geo.addPointOnGeometry(1, 0.0496632, 0.18486, tag=1133)
gmsh.model.geo.addPointOnGeometry(1, 0.0584125, 0.17931, tag=1134)
gmsh.model.geo.addPointOnGeometry(1, 0.0601025, 0.189547, tag=1135)
gmsh.model.geo.addPointOnGeometry(1, 0.061696, 0.199841, tag=1136)
gmsh.model.geo.addPointOnGeometry(1, 0.06266, 0.189467, tag=1137)
gmsh.model.geo.addPointOnGeometry(1, 0.0710135, 0.183284, tag=1138)
gmsh.model.geo.addPointOnGeometry(1, 0.080698, 0.179526, tag=1139)
gmsh.model.geo.addPointOnGeometry(1, 0.078691, 0.169353, tag=1140)
gmsh.model.geo.addPointOnGeometry(1, 0.087016, 0.163211, tag=1141)
gmsh.model.geo.addPointOnGeometry(1, 0.0920745, 0.154201, tag=1142)
gmsh.model.geo.addPointOnGeometry(1, 0.101821, 0.150788, tag=1143)
gmsh.model.geo.addPointOnGeometry(1, 0.105892, 0.141302, tag=1144)
gmsh.model.geo.addPointOnGeometry(1, 0.115247, 0.136954, tag=1145)
gmsh.model.geo.addPointOnGeometry(1, 0.124596, 0.13257, tag=1146)
gmsh.model.geo.addPointOnGeometry(1, 0.134388, 0.129247, tag=1147)
gmsh.model.geo.addPointOnGeometry(1, 0.137055, 0.119258, tag=1148)
gmsh.model.geo.addPointOnGeometry(1, 0.140222, 0.109432, tag=1149)
gmsh.model.geo.addPointOnGeometry(1, 0.144872, 0.100226, tag=1150)
gmsh.model.geo.addPointOnGeometry(1, 0.147492, 0.090259, tag=1151)
gmsh.model.geo.addPointOnGeometry(1, 0.152208, 0.081104, tag=1152)
gmsh.model.geo.addPointOnGeometry(1, 0.150741, 0.070921, tag=1153)
gmsh.model.geo.addPointOnGeometry(1, 0.156916, 0.0627, tag=1154)
gmsh.model.geo.addPointOnGeometry(1, 0.16469, 0.0559515, tag=1155)
gmsh.model.geo.addPointOnGeometry(1, 0.174524, 0.052829, tag=1156)
gmsh.model.geo.addPointOnGeometry(1, 0.180999, 0.0447672, tag=1157)
gmsh.model.geo.addPointOnGeometry(1, 0.189042, 0.051307, tag=1158)
gmsh.model.geo.addPointOnGeometry(1, 0.193926, 0.060487, tag=1159)
gmsh.model.geo.addPointOnGeometry(1, 0.196383, 0.070618, tag=1160)
gmsh.model.geo.addPointOnGeometry(1, 0.202399, 0.062083, tag=1161)
gmsh.model.geo.addPointOnGeometry(1, 0.203099, 0.051663, tag=1162)
gmsh.model.geo.addPointOnGeometry(1, 0.21112, 0.0449595, tag=1163)
gmsh.model.geo.addPointOnGeometry(1, 0.21829, 0.05261, tag=1164)
gmsh.model.geo.addPointOnGeometry(1, 0.216633, 0.062986, tag=1165)
gmsh.model.geo.addPointOnGeometry(1, 0.21084, 0.071746, tag=1166)
gmsh.model.geo.addPointOnGeometry(1, 0.220102, 0.076733, tag=1167)
gmsh.model.geo.addPointOnGeometry(1, 0.228394, 0.0701975, tag=1168)
gmsh.model.geo.addPointOnGeometry(1, 0.23612, 0.0629635, tag=1169)
gmsh.model.geo.addPointOnGeometry(1, 0.246531, 0.0608615, tag=1170)
gmsh.model.geo.addPointOnGeometry(1, 0.249892, 0.070978, tag=1171)
gmsh.model.geo.addPointOnGeometry(1, 0.256564, 0.0793405, tag=1172)
gmsh.model.geo.addPointOnGeometry(1, 0.264188, 0.0869155, tag=1173)
gmsh.model.geo.addPointOnGeometry(1, 0.261321, 0.097303, tag=1174)
gmsh.model.geo.addPointOnGeometry(1, 0.257532, 0.107393, tag=1175)
gmsh.model.geo.addPointOnGeometry(1, 0.268049, 0.109898, tag=1176)
gmsh.model.geo.addPointOnGeometry(1, 0.278387, 0.106559, tag=1177)
gmsh.model.geo.addPointOnGeometry(1, 0.288172, 0.10173, tag=1178)
gmsh.model.geo.addPointOnGeometry(1, 0.295446, 0.093548, tag=1179)
gmsh.model.geo.addPointOnGeometry(1, 0.291691, 0.103844, tag=1180)
gmsh.model.geo.addPointOnGeometry(1, 0.283497, 0.111098, tag=1181)
gmsh.model.geo.addPointOnGeometry(1, 0.273736, 0.115963, tag=1182)
gmsh.model.geo.addPointOnGeometry(1, 0.263426, 0.119375, tag=1183)
gmsh.model.geo.addPointOnGeometry(1, 0.253541, 0.123768, tag=1184)
gmsh.model.geo.addPointOnGeometry(1, 0.243221, 0.120701, tag=1185)
gmsh.model.geo.addPointOnGeometry(1, 0.232639, 0.122381, tag=1186)
gmsh.model.geo.addPointOnGeometry(1, 0.222395, 0.125374, tag=1187)
gmsh.model.geo.addPointOnGeometry(1, 0.232938, 0.12707, tag=1188)
gmsh.model.geo.addPointOnGeometry(1, 0.22973, 0.137289, tag=1189)
gmsh.model.geo.addPointOnGeometry(1, 0.219134, 0.135873, tag=1190)
gmsh.model.geo.addPointOnGeometry(1, 0.216887, 0.146309, tag=1191)
gmsh.model.geo.addPointOnGeometry(1, 0.216425, 0.156999, tag=1192)
gmsh.model.geo.addPointOnGeometry(1, 0.210773, 0.166105, tag=1193)
gmsh.model.geo.addPointOnGeometry(1, 0.203624, 0.174093, tag=1194)
gmsh.model.geo.addPointOnGeometry(1, 0.206584, 0.184421, tag=1195)
gmsh.model.geo.addPointOnGeometry(1, 0.207165, 0.195193, tag=1196)
gmsh.model.geo.addPointOnGeometry(1, 0.196537, 0.193352, tag=1197)
gmsh.model.geo.addPointOnGeometry(1, 0.198643, 0.203931, tag=1198)
gmsh.model.geo.addPointOnGeometry(1, 0.202793, 0.213945, tag=1199)
gmsh.model.geo.addPointOnGeometry(1, 0.20238, 0.224831, tag=1200)
gmsh.model.geo.addPointOnGeometry(1, 0.204019, 0.235652, tag=1201)
gmsh.model.geo.addPointOnGeometry(1, 0.206968, 0.246252, tag=1202)
gmsh.model.geo.addPointOnGeometry(1, 0.206136, 0.257282, tag=1203)
gmsh.model.geo.addPointOnGeometry(1, 0.211192, 0.26719, tag=1204)
gmsh.model.geo.addPointOnGeometry(1, 0.212104, 0.278346, tag=1205)
gmsh.model.geo.addPointOnGeometry(1, 0.209466, 0.289282, tag=1206)
gmsh.model.geo.addPointOnGeometry(1, 0.207332, 0.300382, tag=1207)
gmsh.model.geo.addPointOnGeometry(1, 0.202427, 0.310622, tag=1208)
gmsh.model.geo.addPointOnGeometry(1, 0.20419, 0.3219, tag=1209)
gmsh.model.geo.addPointOnGeometry(1, 0.211785, 0.330533, tag=1210)
gmsh.model.geo.addPointOnGeometry(1, 0.210985, 0.319058, tag=1211)
gmsh.model.geo.addPointOnGeometry(1, 0.220762, 0.32512, tag=1212)
gmsh.model.geo.addPointOnGeometry(1, 0.226109, 0.335404, tag=1213)
gmsh.model.geo.addPointOnGeometry(1, 0.233106, 0.344765, tag=1214)
gmsh.model.geo.addPointOnGeometry(1, 0.240566, 0.353885, tag=1215)
gmsh.model.geo.addPointOnGeometry(1, 0.248452, 0.362773, tag=1216)
gmsh.model.geo.addPointOnGeometry(1, 0.252837, 0.373929, tag=1217)
gmsh.model.geo.addPointOnGeometry(1, 0.261457, 0.38241, tag=1218)
gmsh.model.geo.addPointOnGeometry(1, 0.263818, 0.394378, tag=1219)
gmsh.model.geo.addPointOnGeometry(1, 0.263932, 0.406679, tag=1220)
gmsh.model.geo.addPointOnGeometry(1, 0.265958, 0.418918, tag=1221)
gmsh.model.geo.addPointOnGeometry(1, 0.266865, 0.431404, tag=1222)
gmsh.model.geo.addPointOnGeometry(1, 0.265503, 0.443954, tag=1223)
gmsh.model.geo.addPointOnGeometry(1, 0.263085, 0.456449, tag=1224)
gmsh.model.geo.addPointOnGeometry(1, 0.254798, 0.466204, tag=1225)
gmsh.model.geo.addPointOnGeometry(1, 0.249224, 0.477797, tag=1226)
gmsh.model.geo.addPointOnGeometry(1, 0.250472, 0.490708, tag=1227)
gmsh.model.geo.addPointOnGeometry(1, 0.250143, 0.503805, tag=1228)
gmsh.model.geo.addPointOnGeometry(1, 0.249945, 0.517035, tag=1229)
gmsh.model.geo.addPointOnGeometry(1, 0.246786, 0.530015, tag=1230)
gmsh.model.geo.addPointOnGeometry(1, 0.242686, 0.54285, tag=1231)
gmsh.model.geo.addPointOnGeometry(1, 0.253842, 0.55064, tag=1232)
gmsh.model.geo.addPointOnGeometry(1, 0.247577, 0.562855, tag=1233)
gmsh.model.geo.addPointOnGeometry(1, 0.238807, 0.57354, tag=1234)
gmsh.model.geo.addPointOnGeometry(1, 0.238297, 0.58747, tag=1235)
gmsh.model.geo.addPointOnGeometry(1, 0.235338, 0.60125, tag=1236)
gmsh.model.geo.addPointOnGeometry(1, 0.22795, 0.613405, tag=1237)
gmsh.model.geo.addPointOnGeometry(1, 0.226319, 0.599305, tag=1238)
gmsh.model.geo.addPointOnGeometry(1, 0.227644, 0.585345, tag=1239)
gmsh.model.geo.addPointOnGeometry(1, 0.227607, 0.57148, tag=1240)
gmsh.model.geo.addPointOnGeometry(1, 0.232384, 0.558615, tag=1241)
gmsh.model.geo.addPointOnGeometry(1, 0.233217, 0.545055, tag=1242)
gmsh.model.geo.addPointOnGeometry(1, 0.236717, 0.532065, tag=1243)
gmsh.model.geo.addPointOnGeometry(1, 0.23814, 0.518815, tag=1244)
gmsh.model.geo.addPointOnGeometry(1, 0.233964, 0.50631, tag=1245)
gmsh.model.geo.addPointOnGeometry(1, 0.224637, 0.51556, tag=1246)
gmsh.model.geo.addPointOnGeometry(1, 0.222768, 0.528655, tag=1247)
gmsh.model.geo.addPointOnGeometry(1, 0.221541, 0.541955, tag=1248)
gmsh.model.geo.addPointOnGeometry(1, 0.218313, 0.55506, tag=1249)
gmsh.model.geo.addPointOnGeometry(1, 0.213298, 0.56772, tag=1250)
gmsh.model.geo.addPointOnGeometry(1, 0.208017, 0.580405, tag=1251)
gmsh.model.geo.addPointOnGeometry(1, 0.206844, 0.594235, tag=1252)
gmsh.model.geo.addPointOnGeometry(1, 0.197415, 0.604585, tag=1253)
gmsh.model.geo.addPointOnGeometry(1, 0.192396, 0.61778, tag=1254)
gmsh.model.geo.addPointOnGeometry(1, 0.186652, 0.63083, tag=1255)
gmsh.model.geo.addPointOnGeometry(1, 0.181137, 0.644135, tag=1256)
gmsh.model.geo.addPointOnGeometry(1, 0.17936, 0.658595, tag=1257)
gmsh.model.geo.addPointOnGeometry(1, 0.18738, 0.67098, tag=1258)
gmsh.model.geo.addPointOnGeometry(1, 0.18283, 0.685215, tag=1259)
gmsh.model.geo.addPointOnGeometry(1, 0.171555, 0.69523, tag=1260)
gmsh.model.geo.addPointOnGeometry(1, 0.161589, 0.706695, tag=1261)
gmsh.model.geo.addPointOnGeometry(1, 0.148305, 0.714265, tag=1262)
gmsh.model.geo.addPointOnGeometry(1, 0.137296, 0.72501, tag=1263)
gmsh.model.geo.addPointOnGeometry(1, 0.124455, 0.733675, tag=1264)
gmsh.model.geo.addPointOnGeometry(1, 0.110826, 0.741225, tag=1265)
gmsh.model.geo.addPointOnGeometry(1, 0.0969555, 0.748485, tag=1266)
gmsh.model.geo.addPointOnGeometry(1, 0.082034, 0.753435, tag=1267)
gmsh.model.geo.addPointOnGeometry(1, 0.067294, 0.74805, tag=1268)
gmsh.model.geo.addPointOnGeometry(1, 0.05213, 0.75197, tag=1269)
gmsh.model.geo.addPointOnGeometry(1, 0.0401649, 0.762215, tag=1270)
gmsh.model.geo.addPointOnGeometry(1, 0.0293273, 0.773865, tag=1271)
gmsh.model.geo.addPointOnGeometry(1, 0.015618, 0.78223, tag=1272)
gmsh.model.geo.addPointOnGeometry(1, -0.000136873, 0.785775, tag=1273)
gmsh.model.geo.addPointOnGeometry(1, -0.0154763, 0.791035, tag=1274)
gmsh.model.geo.addPointOnGeometry(1, -0.0316662, 0.789605, tag=1275)
gmsh.model.geo.addPointOnGeometry(1, -0.041052, 0.802995, tag=1276)
gmsh.model.geo.addPointOnGeometry(1, -0.053303, 0.81414, tag=1277)
gmsh.model.geo.addPointOnGeometry(1, -0.062948, 0.82787, tag=1278)
gmsh.model.geo.addPointOnGeometry(1, -0.071769, 0.842425, tag=1279)
gmsh.model.geo.addPointOnGeometry(1, -0.088831, 0.844425, tag=1280)
gmsh.model.geo.addPointOnGeometry(1, -0.10023, 0.85748, tag=1281)
gmsh.model.geo.addPointOnGeometry(1, -0.117632, 0.85605, tag=1282)
gmsh.model.geo.addPointOnGeometry(1, -0.132888, 0.864745, tag=1283)
gmsh.model.geo.addPointOnGeometry(1, -0.150568, 0.86507, tag=1284)
gmsh.model.geo.addPointOnGeometry(1, -0.15139, 0.84753, tag=1285)
gmsh.model.geo.addPointOnGeometry(1, -0.167574, 0.84118, tag=1286)
gmsh.model.geo.addPointOnGeometry(1, -0.17729, 0.85573, tag=1287)
gmsh.model.geo.addPointOnGeometry(1, -0.191137, 0.866845, tag=1288)
gmsh.model.geo.addPointOnGeometry(1, -0.199228, 0.882965, tag=1289)
gmsh.model.geo.addPointOnGeometry(1, -0.202466, 0.90104, tag=1290)
gmsh.model.geo.addPointOnGeometry(1, -0.206533, 0.919295, tag=1291)
gmsh.model.geo.addPointOnGeometry(1, -0.197556, 0.936055, tag=1292)
gmsh.model.geo.addPointOnGeometry(1, -0.186216, 0.951645, tag=1293)
gmsh.model.geo.addPointOnGeometry(1, -0.175232, 0.967805, tag=1294)
gmsh.model.geo.addPointOnGeometry(1, -0.170422, 0.987065, tag=1295)
gmsh.model.geo.addPointOnGeometry(1, -0.161373, 1.00512, tag=1296)
gmsh.model.geo.addPointOnGeometry(1, -0.165541, 1.02527, tag=1297)
gmsh.model.geo.addPointOnGeometry(1, -0.186261, 1.02759, tag=1298)
gmsh.model.geo.addPointOnGeometry(1, -0.178326, 1.04713, tag=1299)
gmsh.model.geo.addPointOnGeometry(1, -0.164852, 1.0638, tag=1300)
gmsh.model.geo.addPointOnGeometry(1, -0.171284, 1.08466, tag=1301)
gmsh.model.geo.addPointOnGeometry(1, -0.18344, 1.10334, tag=1302)
gmsh.model.geo.addPointOnGeometry(1, -0.202954, 1.11488, tag=1303)
gmsh.model.geo.addPointOnGeometry(1, -0.218398, 1.13202, tag=1304)
gmsh.model.geo.addPointOnGeometry(1, -0.232811, 1.15063, tag=1305)
gmsh.model.geo.addPointOnGeometry(1, -0.246595, 1.17033, tag=1306)
gmsh.model.geo.addPointOnGeometry(1, -0.260679, 1.19047, tag=1307)
gmsh.model.geo.addPointOnGeometry(1, -0.27871, 1.20794, tag=1308)
gmsh.model.geo.addPointOnGeometry(1, -0.29731, 1.22558, tag=1309)
gmsh.model.geo.addPointOnGeometry(1, -0.308963, 1.24909, tag=1310)
gmsh.model.geo.addPointOnGeometry(1, -0.331203, 1.26407, tag=1311)
gmsh.model.geo.addPointOnGeometry(1, -0.356591, 1.27408, tag=1312)
gmsh.model.geo.addPointOnGeometry(1, -0.383708, 1.27961, tag=1313)
gmsh.model.geo.addPointOnGeometry(1, -0.411301, 1.28447, tag=1314)
gmsh.model.geo.addPointOnGeometry(1, -0.437173, 1.29631, tag=1315)
gmsh.model.geo.addPointOnGeometry(1, -0.464883, 1.3047, tag=1316)
gmsh.model.geo.addPointOnGeometry(1, -0.478707, 1.33086, tag=1317)
gmsh.model.geo.addPointOnGeometry(1, -0.489437, 1.35935, tag=1318)
gmsh.model.geo.addPointOnGeometry(1, -0.500985, 1.38847, tag=1319)
gmsh.model.geo.addPointOnGeometry(1, -0.50863, 1.41981, tag=1320)
gmsh.model.geo.addPointOnGeometry(1, -0.51407, 1.4526, tag=1321)
gmsh.model.geo.addPointOnGeometry(1, -0.527215, 1.48426, tag=1322)
gmsh.model.geo.addPointOnGeometry(1, -0.533865, 1.51899, tag=1323)
gmsh.model.geo.addPointOnGeometry(1, -0.53774, 1.55528, tag=1324)
gmsh.model.geo.addPointOnGeometry(1, -0.54041, 1.59287, tag=1325)
gmsh.model.geo.addPointOnGeometry(1, -0.551125, 1.63031, tag=1326)
gmsh.model.geo.addPointOnGeometry(1, -0.552265, 1.67058, tag=1327)
gmsh.model.geo.addPointOnGeometry(1, -0.570495, 1.70807, tag=1328)
gmsh.model.geo.addPointOnGeometry(1, -0.58012, 1.75019, tag=1329)
gmsh.model.geo.addPointOnGeometry(1, -0.583045, 1.79489, tag=1330)
gmsh.model.geo.addPointOnGeometry(1, -0.583845, 1.84135, tag=1331)
gmsh.model.geo.addPointOnGeometry(1, -0.583145, 1.88955, tag=1332)
gmsh.model.geo.addPointOnGeometry(1, -0.567425, 1.93692, tag=1333)
gmsh.model.geo.addPointOnGeometry(1, -0.5869, 1.9849, tag=1334)
gmsh.model.geo.addPointOnGeometry(1, -0.61053, 2.0334, tag=1335)
gmsh.model.geo.addPointOnGeometry(1, -0.606235, 2.08943, tag=1336)
gmsh.model.geo.addPointOnGeometry(1, -0.62951, 2.14321, tag=1337)
gmsh.model.geo.addPointOnGeometry(1, -0.68325, 2.17178, tag=1338)
gmsh.model.geo.addPointOnGeometry(1, -0.684065, 2.23499, tag=1339)
gmsh.model.geo.addPointOnGeometry(1, -0.70813, 2.29664, tag=1340)
gmsh.model.geo.addPointOnGeometry(1, -0.698865, 2.36529, tag=1341)
gmsh.model.geo.addPointOnGeometry(1, -0.642575, 2.40938, tag=1342)
gmsh.model.geo.addPointOnGeometry(1, 0.261646, -0.0462448, tag=1343)
gmsh.model.geo.addPointOnGeometry(1, 0.257583, -0.0363529, tag=1344)
gmsh.model.geo.addPointOnGeometry(1, 0.250365, -0.0285113, tag=1345)
gmsh.model.geo.addPointOnGeometry(1, 0.246844, -0.018488, tag=1346)
gmsh.model.geo.addPointOnGeometry(1, 0.244275, -0.0081982, tag=1347)
gmsh.model.geo.addPointOnGeometry(1, 0.234033, -0.00557375, tag=1348)
gmsh.model.geo.addPointOnGeometry(1, 0.224404, -0.0098255, tag=1349)
gmsh.model.geo.addPointOnGeometry(1, 0.185382, 0, tag=1350)
gmsh.model.geo.addPointOnGeometry(1, 0.179695, -0.0086374, tag=1351)
gmsh.model.geo.addPointOnGeometry(1, 0.176208, -0.0183496, tag=1352)
gmsh.model.geo.addPointOnGeometry(1, 0.173261, -0.0282307, tag=1353)
gmsh.model.geo.addPointOnGeometry(1, 0.183426, -0.0300543, tag=1354)
gmsh.model.geo.addPointOnGeometry(1, 0.175981, -0.0372229, tag=1355)
gmsh.model.geo.addPointOnGeometry(1, 0.173928, -0.0473414, tag=1356)
gmsh.model.geo.addPointOnGeometry(1, 0.172528, -0.057574, tag=1357)
gmsh.model.geo.addPointOnGeometry(1, 0.162314, -0.0590155, tag=1358)
gmsh.model.geo.addPointOnGeometry(1, 0.153641, -0.0645495, tag=1359)
gmsh.model.geo.addPointOnGeometry(1, 0.151116, -0.0745155, tag=1360)
gmsh.model.geo.addPointOnGeometry(1, 0.142495, -0.0801075, tag=1361)
gmsh.model.geo.addPointOnGeometry(1, 0.132337, -0.0815135, tag=1362)
gmsh.model.geo.addPointOnGeometry(1, 0.132919, -0.091749, tag=1363)
gmsh.model.geo.addPointOnGeometry(1, 0.122782, -0.090251, tag=1364)
gmsh.model.geo.addPointOnGeometry(1, 0.117848, -0.099218, tag=1365)
gmsh.model.geo.addPointOnGeometry(1, 0.121211, -0.108904, tag=1366)
gmsh.model.geo.addPointOnGeometry(1, 0.114963, -0.117053, tag=1367)
gmsh.model.geo.addPointOnGeometry(1, 0.112027, -0.126903, tag=1368)
gmsh.model.geo.addPointOnGeometry(1, 0.101783, -0.126107, tag=1369)
gmsh.model.geo.addPointOnGeometry(1, 0.0959615, -0.117675, tag=1370)
gmsh.model.geo.addPointOnGeometry(1, 0.08574, -0.117649, tag=1371)
gmsh.model.geo.addPointOnGeometry(1, 0.0809045, -0.126651, tag=1372)
gmsh.model.geo.addPointOnGeometry(1, 0.0712105, -0.129898, tag=1373)
gmsh.model.geo.addPointOnGeometry(1, 0.060998, -0.129864, tag=1374)
gmsh.model.geo.addPointOnGeometry(1, 0.050825, -0.130627, tag=1375)
gmsh.model.geo.addPointOnGeometry(1, 0.0459654, -0.139602, tag=1376)
gmsh.model.geo.addPointOnGeometry(1, 0.0470436, -0.129456, tag=1377)
gmsh.model.geo.addPointOnGeometry(1, 0.051595, -0.120349, tag=1378)
gmsh.model.geo.addPointOnGeometry(1, 0.0479279, -0.110874, tag=1379)
gmsh.model.geo.addPointOnGeometry(1, 0.0378116, -0.110168, tag=1380)
gmsh.model.geo.addPointOnGeometry(1, 0.0289544, -0.105257, tag=1381)
gmsh.model.geo.addPointOnGeometry(1, 0.0219452, -0.112565, tag=1382)
gmsh.model.geo.addPointOnGeometry(1, 0.0163592, -0.121028, tag=1383)
gmsh.model.geo.addPointOnGeometry(1, 0.00628285, -0.122249, tag=1384)
gmsh.model.geo.addPointOnGeometry(1, -0.00222929, -0.127791, tag=1385)
gmsh.model.geo.addPointOnGeometry(1, -0.00967765, -0.13472, tag=1386)
gmsh.model.geo.addPointOnGeometry(1, -0.0160619, -0.142669, tag=1387)
gmsh.model.geo.addPointOnGeometry(1, -0.0240664, -0.149018, tag=1388)
gmsh.model.geo.addPointOnGeometry(1, -0.019517, -0.158194, tag=1389)
gmsh.model.geo.addPointOnGeometry(1, -0.018975, -0.168451, tag=1390)
gmsh.model.geo.addPointOnGeometry(1, -0.0234618, -0.15921, tag=1391)
gmsh.model.geo.addPointOnGeometry(1, -0.0306917, -0.151944, tag=1392)
gmsh.model.geo.addPointOnGeometry(1, -0.0352361, -0.161138, tag=1393)
gmsh.model.geo.addPointOnGeometry(1, -0.0391132, -0.151639, tag=1394)
gmsh.model.geo.addPointOnGeometry(1, -0.0468964, -0.158324, tag=1395)
gmsh.model.geo.addPointOnGeometry(1, -0.0491196, -0.168371, tag=1396)
gmsh.model.geo.addPointOnGeometry(1, -0.0446071, -0.177657, tag=1397)
gmsh.model.geo.addPointOnGeometry(1, -0.0398146, -0.186831, tag=1398)
gmsh.model.geo.addPointOnGeometry(1, -0.0480492, -0.180549, tag=1399)
gmsh.model.geo.addPointOnGeometry(1, -0.051801, -0.190216, tag=1400)
gmsh.model.geo.addPointOnGeometry(1, -0.0597535, -0.196928, tag=1401)
gmsh.model.geo.addPointOnGeometry(1, -0.07017, -0.197518, tag=1402)
gmsh.model.geo.addPointOnGeometry(1, -0.0614665, -0.191784, tag=1403)
gmsh.model.geo.addPointOnGeometry(1, -0.055126, -0.183557, tag=1404)
gmsh.model.geo.addPointOnGeometry(1, -0.0547535, -0.173214, tag=1405)
gmsh.model.geo.addPointOnGeometry(1, -0.050919, -0.163642, tag=1406)
gmsh.model.geo.addPointOnGeometry(1, -0.0491333, -0.15352, tag=1407)
gmsh.model.geo.addPointOnGeometry(1, -0.046367, -0.143658, tag=1408)
gmsh.model.geo.addPointOnGeometry(1, -0.0564505, -0.145416, tag=1409)
gmsh.model.geo.addPointOnGeometry(1, -0.0645325, -0.151733, tag=1410)
gmsh.model.geo.addPointOnGeometry(1, -0.0678085, -0.161489, tag=1411)
gmsh.model.geo.addPointOnGeometry(1, -0.0682065, -0.171805, tag=1412)
gmsh.model.geo.addPointOnGeometry(1, -0.0769865, -0.166345, tag=1413)
gmsh.model.geo.addPointOnGeometry(1, -0.0832335, -0.158117, tag=1414)
gmsh.model.geo.addPointOnGeometry(1, -0.092085, -0.163453, tag=1415)
gmsh.model.geo.addPointOnGeometry(1, -0.100964, -0.158129, tag=1416)
gmsh.model.geo.addPointOnGeometry(1, -0.111315, -0.157651, tag=1417)
gmsh.model.geo.addPointOnGeometry(1, -0.117356, -0.149226, tag=1418)
gmsh.model.geo.addPointOnGeometry(1, -0.127671, -0.148144, tag=1419)
gmsh.model.geo.addPointOnGeometry(1, -0.137736, -0.150762, tag=1420)
gmsh.model.geo.addPointOnGeometry(1, -0.141411, -0.141024, tag=1421)
gmsh.model.geo.addPointOnGeometry(1, -0.133887, -0.133874, tag=1422)
gmsh.model.geo.addPointOnGeometry(1, -0.143397, -0.138031, tag=1423)
gmsh.model.geo.addPointOnGeometry(1, -0.15027, -0.145859, tag=1424)
gmsh.model.geo.addPointOnGeometry(1, -0.158961, -0.140065, tag=1425)
gmsh.model.geo.addPointOnGeometry(1, -0.169105, -0.142662, tag=1426)
gmsh.model.geo.addPointOnGeometry(1, -0.175369, -0.134247, tag=1427)
gmsh.model.geo.addPointOnGeometry(1, -0.181957, -0.142436, tag=1428)
gmsh.model.geo.addPointOnGeometry(1, -0.184903, -0.132331, tag=1429)
gmsh.model.geo.addPointOnGeometry(1, -0.18043, -0.122834, tag=1430)
gmsh.model.geo.addPointOnGeometry(1, -0.174028, -0.114568, tag=1431)
gmsh.model.geo.addPointOnGeometry(1, -0.169207, -0.123825, tag=1432)
gmsh.model.geo.addPointOnGeometry(1, -0.164461, -0.133129, tag=1433)
gmsh.model.geo.addPointOnGeometry(1, -0.154211, -0.135085, tag=1434)
gmsh.model.geo.addPointOnGeometry(1, -0.14887, -0.126161, tag=1435)
gmsh.model.geo.addPointOnGeometry(1, -0.14911, -0.115795, tag=1436)
gmsh.model.geo.addPointOnGeometry(1, -0.149637, -0.105462, tag=1437)
gmsh.model.geo.addPointOnGeometry(1, -0.152929, -0.09567, tag=1438)
gmsh.model.geo.addPointOnGeometry(1, -0.153714, -0.0853825, tag=1439)
gmsh.model.geo.addPointOnGeometry(1, -0.147202, -0.0774115, tag=1440)
gmsh.model.geo.addPointOnGeometry(1, -0.157148, -0.0747765, tag=1441)
gmsh.model.geo.addPointOnGeometry(1, -0.160835, -0.0651565, tag=1442)
gmsh.model.geo.addPointOnGeometry(1, -0.170846, -0.0626645, tag=1443)
gmsh.model.geo.addPointOnGeometry(1, -0.178645, -0.0558725, tag=1444)
gmsh.model.geo.addPointOnGeometry(1, -0.188162, -0.0517645, tag=1445)
gmsh.model.geo.addPointOnGeometry(1, -0.198026, -0.0484758, tag=1446)
gmsh.model.geo.addPointOnGeometry(1, -0.208456, -0.0488804, tag=1447)
gmsh.model.geo.addPointOnGeometry(1, -0.217827, -0.0441969, tag=1448)
gmsh.model.geo.addPointOnGeometry(1, -0.227508, -0.0400944, tag=1449)
gmsh.model.geo.addPointOnGeometry(1, -0.236273, -0.0342191, tag=1450)
gmsh.model.geo.addPointOnGeometry(1, -0.246087, -0.0302328, tag=1451)
gmsh.model.geo.addPointOnGeometry(1, -0.253416, -0.0225306, tag=1452)
gmsh.model.geo.addPointOnGeometry(1, -0.252316, -0.0331214, tag=1453)
gmsh.model.geo.addPointOnGeometry(1, -0.256868, -0.0234858, tag=1454)
gmsh.model.geo.addPointOnGeometry(1, -0.267304, -0.0258212, tag=1455)
gmsh.model.geo.addPointOnGeometry(1, -0.277095, -0.0302566, tag=1456)
gmsh.model.geo.addPointOnGeometry(1, -0.283473, -0.0389696, tag=1457)
gmsh.model.geo.addPointOnGeometry(1, -0.275156, -0.0458561, tag=1458)
gmsh.model.geo.addPointOnGeometry(1, -0.264923, -0.0491572, tag=1459)
gmsh.model.geo.addPointOnGeometry(1, -0.274301, -0.0544215, tag=1460)
gmsh.model.geo.addPointOnGeometry(1, -0.283858, -0.059479, tag=1461)
gmsh.model.geo.addPointOnGeometry(1, -0.291972, -0.066711, tag=1462)
gmsh.model.geo.addPointOnGeometry(1, -0.302551, -0.0694595, tag=1463)
gmsh.model.geo.addPointOnGeometry(1, -0.295517, -0.0778495, tag=1464)
gmsh.model.geo.addPointOnGeometry(1, -0.286677, -0.084251, tag=1465)
gmsh.model.geo.addPointOnGeometry(1, -0.276202, -0.0813825, tag=1466)
gmsh.model.geo.addPointOnGeometry(1, -0.265627, -0.0835875, tag=1467)
gmsh.model.geo.addPointOnGeometry(1, -0.255921, -0.08822, tag=1468)
gmsh.model.geo.addPointOnGeometry(1, -0.249831, -0.07941, tag=1469)
gmsh.model.geo.addPointOnGeometry(1, -0.240066, -0.0751345, tag=1470)
gmsh.model.geo.addPointOnGeometry(1, -0.229516, -0.074038, tag=1471)
gmsh.model.geo.addPointOnGeometry(1, -0.220596, -0.079701, tag=1472)
gmsh.model.geo.addPointOnGeometry(1, -0.210781, -0.083523, tag=1473)
gmsh.model.geo.addPointOnGeometry(1, -0.20049, -0.081481, tag=1474)
gmsh.model.geo.addPointOnGeometry(1, -0.195591, -0.0907315, tag=1475)
gmsh.model.geo.addPointOnGeometry(1, -0.205791, -0.0931755, tag=1476)
gmsh.model.geo.addPointOnGeometry(1, -0.215793, -0.0898875, tag=1477)
gmsh.model.geo.addPointOnGeometry(1, -0.226106, -0.087584, tag=1478)
gmsh.model.geo.addPointOnGeometry(1, -0.235061, -0.093283, tag=1479)
gmsh.model.geo.addPointOnGeometry(1, -0.244192, -0.0987965, tag=1480)
gmsh.model.geo.addPointOnGeometry(1, -0.2442, -0.109503, tag=1481)
gmsh.model.geo.addPointOnGeometry(1, -0.236908, -0.117344, tag=1482)
gmsh.model.geo.addPointOnGeometry(1, -0.229855, -0.125381, tag=1483)
gmsh.model.geo.addPointOnGeometry(1, -0.240202, -0.128164, tag=1484)
gmsh.model.geo.addPointOnGeometry(1, -0.244864, -0.118488, tag=1485)
gmsh.model.geo.addPointOnGeometry(1, -0.251883, -0.110347, tag=1486)
gmsh.model.geo.addPointOnGeometry(1, -0.259454, -0.118028, tag=1487)
gmsh.model.geo.addPointOnGeometry(1, -0.270002, -0.12054, tag=1488)
gmsh.model.geo.addPointOnGeometry(1, -0.268163, -0.109839, tag=1489)
gmsh.model.geo.addPointOnGeometry(1, -0.278736, -0.107318, tag=1490)
gmsh.model.geo.addPointOnGeometry(1, -0.288682, -0.111838, tag=1491)
gmsh.model.geo.addPointOnGeometry(1, -0.299128, -0.108435, tag=1492)
gmsh.model.geo.addPointOnGeometry(1, -0.301484, -0.097679, tag=1493)
gmsh.model.geo.addPointOnGeometry(1, -0.30916, -0.0897705, tag=1494)
gmsh.model.geo.addPointOnGeometry(1, -0.317314, -0.0823045, tag=1495)
gmsh.model.geo.addPointOnGeometry(1, -0.312505, -0.0723525, tag=1496)
gmsh.model.geo.addPointOnGeometry(1, -0.319284, -0.0636325, tag=1497)
gmsh.model.geo.addPointOnGeometry(1, -0.3126, -0.0548535, tag=1498)
gmsh.model.geo.addPointOnGeometry(1, -0.30163, -0.0545845, tag=1499)
gmsh.model.geo.addPointOnGeometry(1, -0.290805, -0.0532505, tag=1500)
gmsh.model.geo.addPointOnGeometry(1, -0.294155, -0.0429002, tag=1501)
gmsh.model.geo.addPointOnGeometry(1, -0.305036, -0.0438064, tag=1502)
gmsh.model.geo.addPointOnGeometry(1, -0.314814, -0.0488077, tag=1503)
gmsh.model.geo.addPointOnGeometry(1, -0.324103, -0.054788, tag=1504)
gmsh.model.geo.addPointOnGeometry(1, -0.324904, -0.0437386, tag=1505)
gmsh.model.geo.addPointOnGeometry(1, -0.329337, -0.0335782, tag=1506)
gmsh.model.geo.addPointOnGeometry(1, -0.338415, -0.0271471, tag=1507)
gmsh.model.geo.addPointOnGeometry(1, -0.347912, -0.02124, tag=1508)
gmsh.model.geo.addPointOnGeometry(1, -0.35406, -0.0118351, tag=1509)
gmsh.model.geo.addPointOnGeometry(1, -0.364384, -0.00726345, tag=1510)
gmsh.model.geo.addPointOnGeometry(1, -0.370821, 0.00208725, tag=1511)
gmsh.model.geo.addPointOnGeometry(1, -0.377614, 0.011246, tag=1512)
gmsh.model.geo.addPointOnGeometry(1, -0.375211, 0.0224119, tag=1513)
gmsh.model.geo.addPointOnGeometry(1, -0.382139, 0.0315177, tag=1514)
gmsh.model.geo.addPointOnGeometry(1, -0.387379, 0.0212947, tag=1515)
gmsh.model.geo.addPointOnGeometry(1, -0.396271, 0.0139393, tag=1516)
gmsh.model.geo.addPointOnGeometry(1, -0.406705, 0.0088368, tag=1517)
gmsh.model.geo.addPointOnGeometry(1, -0.418408, 0.00894295, tag=1518)
gmsh.model.geo.addPointOnGeometry(1, -0.429847, 0.0118423, tag=1519)
gmsh.model.geo.addPointOnGeometry(1, -0.430507, 0.0236793, tag=1520)
gmsh.model.geo.addPointOnGeometry(1, -0.429031, 0.035444, tag=1521)
gmsh.model.geo.addPointOnGeometry(1, -0.426316, 0.0469755, tag=1522)
gmsh.model.geo.addPointOnGeometry(1, -0.423498, 0.0584685, tag=1523)
gmsh.model.geo.addPointOnGeometry(1, -0.428298, 0.0693085, tag=1524)
gmsh.model.geo.addPointOnGeometry(1, -0.440224, 0.068858, tag=1525)
gmsh.model.geo.addPointOnGeometry(1, -0.452261, 0.0691345, tag=1526)
gmsh.model.geo.addPointOnGeometry(1, -0.463471, 0.073815, tag=1527)
gmsh.model.geo.addPointOnGeometry(1, -0.47531, 0.077004, tag=1528)
gmsh.model.geo.addPointOnGeometry(1, -0.487591, 0.075473, tag=1529)
gmsh.model.geo.addPointOnGeometry(1, -0.492581, 0.0640645, tag=1530)
gmsh.model.geo.addPointOnGeometry(1, -0.500765, 0.0546145, tag=1531)
gmsh.model.geo.addPointOnGeometry(1, -0.502325, 0.0421719, tag=1532)
gmsh.model.geo.addPointOnGeometry(1, -0.500915, 0.0297218, tag=1533)
gmsh.model.geo.addPointOnGeometry(1, -0.499399, 0.0173049, tag=1534)
gmsh.model.geo.addPointOnGeometry(1, -0.492281, 0.0070778, tag=1535)
gmsh.model.geo.addPointOnGeometry(1, -0.481785, 0.000526585, tag=1536)
gmsh.model.geo.addPointOnGeometry(1, -0.469572, 0.0016366, tag=1537)
gmsh.model.geo.addPointOnGeometry(1, -0.459617, -0.00534585, tag=1538)
gmsh.model.geo.addPointOnGeometry(1, -0.45271, -0.0152597, tag=1539)
gmsh.model.geo.addPointOnGeometry(1, -0.445609, -0.02496, tag=1540)
gmsh.model.geo.addPointOnGeometry(1, -0.433786, -0.0232994, tag=1541)
gmsh.model.geo.addPointOnGeometry(1, -0.428794, -0.034068, tag=1542)
gmsh.model.geo.addPointOnGeometry(1, -0.431576, -0.0456053, tag=1543)
gmsh.model.geo.addPointOnGeometry(1, -0.423897, -0.054638, tag=1544)
gmsh.model.geo.addPointOnGeometry(1, -0.415754, -0.063175, tag=1545)
gmsh.model.geo.addPointOnGeometry(1, -0.417932, -0.0747585, tag=1546)
gmsh.model.geo.addPointOnGeometry(1, -0.427917, -0.0811385, tag=1547)
gmsh.model.geo.addPointOnGeometry(1, -0.434944, -0.0907865, tag=1548)
gmsh.model.geo.addPointOnGeometry(1, -0.440535, -0.101416, tag=1549)
gmsh.model.geo.addPointOnGeometry(1, -0.443368, -0.113148, tag=1550)
gmsh.model.geo.addPointOnGeometry(1, -0.449653, -0.123526, tag=1551)
gmsh.model.geo.addPointOnGeometry(1, -0.458117, -0.132348, tag=1552)
gmsh.model.geo.addPointOnGeometry(1, -0.445915, -0.133033, tag=1553)
gmsh.model.geo.addPointOnGeometry(1, -0.444268, -0.145096, tag=1554)
gmsh.model.geo.addPointOnGeometry(1, -0.435949, -0.136261, tag=1555)
gmsh.model.geo.addPointOnGeometry(1, -0.433659, -0.124419, tag=1556)
gmsh.model.geo.addPointOnGeometry(1, -0.42971, -0.113082, tag=1557)
gmsh.model.geo.addPointOnGeometry(1, -0.421846, -0.104108, tag=1558)
gmsh.model.geo.addPointOnGeometry(1, -0.414482, -0.0948275, tag=1559)
gmsh.model.geo.addPointOnGeometry(1, -0.40427, -0.0889935, tag=1560)
gmsh.model.geo.addPointOnGeometry(1, -0.403857, -0.100709, tag=1561)
gmsh.model.geo.addPointOnGeometry(1, -0.406274, -0.112213, tag=1562)
gmsh.model.geo.addPointOnGeometry(1, -0.412033, -0.122527, tag=1563)
gmsh.model.geo.addPointOnGeometry(1, -0.41592, -0.133752, tag=1564)
gmsh.model.geo.addPointOnGeometry(1, -0.419628, -0.145101, tag=1565)
gmsh.model.geo.addPointOnGeometry(1, -0.429665, -0.151722, tag=1566)
gmsh.model.geo.addPointOnGeometry(1, -0.439262, -0.15914, tag=1567)
gmsh.model.geo.addPointOnGeometry(1, -0.446567, -0.168951, tag=1568)
gmsh.model.geo.addPointOnGeometry(1, -0.448318, -0.181135, tag=1569)
gmsh.model.geo.addPointOnGeometry(1, -0.460564, -0.183075, tag=1570)
gmsh.model.geo.addPointOnGeometry(1, -0.462892, -0.195347, tag=1571)
gmsh.model.geo.addPointOnGeometry(1, -0.450675, -0.192878, tag=1572)
gmsh.model.geo.addPointOnGeometry(1, -0.441861, -0.184231, tag=1573)
gmsh.model.geo.addPointOnGeometry(1, -0.430517, -0.179648, tag=1574)
gmsh.model.geo.addPointOnGeometry(1, -0.422902, -0.18913, tag=1575)
gmsh.model.geo.addPointOnGeometry(1, -0.412458, -0.195269, tag=1576)
gmsh.model.geo.addPointOnGeometry(1, -0.41008, -0.207129, tag=1577)
gmsh.model.geo.addPointOnGeometry(1, -0.400962, -0.215072, tag=1578)
gmsh.model.geo.addPointOnGeometry(1, -0.392029, -0.207034, tag=1579)
gmsh.model.geo.addPointOnGeometry(1, -0.380811, -0.203018, tag=1580)
gmsh.model.geo.addPointOnGeometry(1, -0.369032, -0.202068, tag=1581)
gmsh.model.geo.addPointOnGeometry(1, -0.357408, -0.203651, tag=1582)
gmsh.model.geo.addPointOnGeometry(1, -0.346085, -0.200924, tag=1583)
gmsh.model.geo.addPointOnGeometry(1, -0.338126, -0.209353, tag=1584)
gmsh.model.geo.addPointOnGeometry(1, -0.3367, -0.220867, tag=1585)
gmsh.model.geo.addPointOnGeometry(1, -0.344919, -0.22915, tag=1586)
gmsh.model.geo.addPointOnGeometry(1, -0.339193, -0.239377, tag=1587)
gmsh.model.geo.addPointOnGeometry(1, -0.331356, -0.23072, tag=1588)
gmsh.model.geo.addPointOnGeometry(1, -0.320707, -0.235335, tag=1589)
gmsh.model.geo.addPointOnGeometry(1, -0.310562, -0.240888, tag=1590)
gmsh.model.geo.addPointOnGeometry(1, -0.31526, -0.251478, tag=1591)
gmsh.model.geo.addPointOnGeometry(1, -0.326681, -0.249144, tag=1592)
gmsh.model.geo.addPointOnGeometry(1, -0.329877, -0.260428, tag=1593)
gmsh.model.geo.addPointOnGeometry(1, -0.32949, -0.272219, tag=1594)
gmsh.model.geo.addPointOnGeometry(1, -0.328292, -0.284014, tag=1595)
gmsh.model.geo.addPointOnGeometry(1, -0.331689, -0.295453, tag=1596)
gmsh.model.geo.addPointOnGeometry(1, -0.343637, -0.29674, tag=1597)
gmsh.model.geo.addPointOnGeometry(1, -0.35233, -0.28837, tag=1598)
gmsh.model.geo.addPointOnGeometry(1, -0.359633, -0.278759, tag=1599)
gmsh.model.geo.addPointOnGeometry(1, -0.364474, -0.267715, tag=1600)
gmsh.model.geo.addPointOnGeometry(1, -0.364109, -0.255707, tag=1601)
gmsh.model.geo.addPointOnGeometry(1, -0.371083, -0.245964, tag=1602)
gmsh.model.geo.addPointOnGeometry(1, -0.380761, -0.238867, tag=1603)
gmsh.model.geo.addPointOnGeometry(1, -0.390848, -0.232287, tag=1604)
gmsh.model.geo.addPointOnGeometry(1, -0.395743, -0.221263, tag=1605)
gmsh.model.geo.addPointOnGeometry(1, -0.407387, -0.217989, tag=1606)
gmsh.model.geo.addPointOnGeometry(1, -0.413637, -0.207584, tag=1607)
gmsh.model.geo.addPointOnGeometry(1, -0.424127, -0.213811, tag=1608)
gmsh.model.geo.addPointOnGeometry(1, -0.434688, -0.220145, tag=1609)
gmsh.model.geo.addPointOnGeometry(1, -0.442368, -0.229919, tag=1610)
gmsh.model.geo.addPointOnGeometry(1, -0.440135, -0.242223, tag=1611)
gmsh.model.geo.addPointOnGeometry(1, -0.437533, -0.254493, tag=1612)
gmsh.model.geo.addPointOnGeometry(1, -0.427665, -0.262229, tag=1613)
gmsh.model.geo.addPointOnGeometry(1, -0.428405, -0.274761, tag=1614)
gmsh.model.geo.addPointOnGeometry(1, -0.419341, -0.283479, tag=1615)
gmsh.model.geo.addPointOnGeometry(1, -0.409901, -0.291747, tag=1616)
gmsh.model.geo.addPointOnGeometry(1, -0.41768, -0.30165, tag=1617)
gmsh.model.geo.addPointOnGeometry(1, -0.42854, -0.308276, tag=1618)
gmsh.model.geo.addPointOnGeometry(1, -0.441042, -0.311253, tag=1619)
gmsh.model.geo.addPointOnGeometry(1, -0.453418, -0.315169, tag=1620)
gmsh.model.geo.addPointOnGeometry(1, -0.466527, -0.315389, tag=1621)
gmsh.model.geo.addPointOnGeometry(1, -0.475486, -0.305717, tag=1622)
gmsh.model.geo.addPointOnGeometry(1, -0.477923, -0.292776, tag=1623)
gmsh.model.geo.addPointOnGeometry(1, -0.486446, -0.282757, tag=1624)
gmsh.model.geo.addPointOnGeometry(1, -0.496524, -0.274244, tag=1625)
gmsh.model.geo.addPointOnGeometry(1, -0.499259, -0.261333, tag=1626)
gmsh.model.geo.addPointOnGeometry(1, -0.501585, -0.248385, tag=1627)
gmsh.model.geo.addPointOnGeometry(1, -0.50396, -0.235487, tag=1628)
gmsh.model.geo.addPointOnGeometry(1, -0.50557, -0.222515, tag=1629)
gmsh.model.geo.addPointOnGeometry(1, -0.50361, -0.209649, tag=1630)
gmsh.model.geo.addPointOnGeometry(1, -0.50849, -0.197622, tag=1631)
gmsh.model.geo.addPointOnGeometry(1, -0.518865, -0.189768, tag=1632)
gmsh.model.geo.addPointOnGeometry(1, -0.53101, -0.194752, tag=1633)
gmsh.model.geo.addPointOnGeometry(1, -0.54251, -0.188172, tag=1634)
gmsh.model.geo.addPointOnGeometry(1, -0.53993, -0.175165, tag=1635)
gmsh.model.geo.addPointOnGeometry(1, -0.539495, -0.161974, tag=1636)
gmsh.model.geo.addPointOnGeometry(1, -0.537725, -0.14895, tag=1637)
gmsh.model.geo.addPointOnGeometry(1, -0.53101, -0.137748, tag=1638)
gmsh.model.geo.addPointOnGeometry(1, -0.529455, -0.124856, tag=1639)
gmsh.model.geo.addPointOnGeometry(1, -0.53078, -0.111972, tag=1640)
gmsh.model.geo.addPointOnGeometry(1, -0.52649, -0.099796, tag=1641)
gmsh.model.geo.addPointOnGeometry(1, -0.513785, -0.0982225, tag=1642)
gmsh.model.geo.addPointOnGeometry(1, -0.50189, -0.093847, tag=1643)
gmsh.model.geo.addPointOnGeometry(1, -0.489511, -0.0958945, tag=1644)
gmsh.model.geo.addPointOnGeometry(1, -0.487793, -0.0835445, tag=1645)
gmsh.model.geo.addPointOnGeometry(1, -0.494146, -0.0728115, tag=1646)
gmsh.model.geo.addPointOnGeometry(1, -0.496178, -0.060481, tag=1647)
gmsh.model.geo.addPointOnGeometry(1, -0.499472, -0.0484137, tag=1648)
gmsh.model.geo.addPointOnGeometry(1, -0.498423, -0.0359495, tag=1649)
gmsh.model.geo.addPointOnGeometry(1, -0.50167, -0.0238684, tag=1650)
gmsh.model.geo.addPointOnGeometry(1, -0.503735, -0.011508, tag=1651)
gmsh.model.geo.addPointOnGeometry(1, -0.510185, -0.00072012, tag=1652)
gmsh.model.geo.addPointOnGeometry(1, -0.514945, 0.0109784, tag=1653)
gmsh.model.geo.addPointOnGeometry(1, -0.519065, 0.022966, tag=1654)
gmsh.model.geo.addPointOnGeometry(1, -0.51675, 0.0354438, tag=1655)
gmsh.model.geo.addPointOnGeometry(1, -0.511925, 0.0471519, tag=1656)
gmsh.model.geo.addPointOnGeometry(1, -0.51921, 0.057537, tag=1657)
gmsh.model.geo.addPointOnGeometry(1, -0.52911, 0.065628, tag=1658)
gmsh.model.geo.addPointOnGeometry(1, -0.533145, 0.0778525, tag=1659)
gmsh.model.geo.addPointOnGeometry(1, -0.540805, 0.088298, tag=1660)
gmsh.model.geo.addPointOnGeometry(1, -0.55196, 0.0951125, tag=1661)
gmsh.model.geo.addPointOnGeometry(1, -0.565155, 0.095699, tag=1662)
gmsh.model.geo.addPointOnGeometry(1, -0.575855, 0.103696, tag=1663)
gmsh.model.geo.addPointOnGeometry(1, -0.58234, 0.115508, tag=1664)
gmsh.model.geo.addPointOnGeometry(1, -0.58653, 0.128411, tag=1665)
gmsh.model.geo.addPointOnGeometry(1, -0.59289, 0.140498, tag=1666)
gmsh.model.geo.addPointOnGeometry(1, -0.600845, 0.151747, tag=1667)
gmsh.model.geo.addPointOnGeometry(1, -0.61094, 0.161326, tag=1668)
gmsh.model.geo.addPointOnGeometry(1, -0.62209, 0.169918, tag=1669)
gmsh.model.geo.addPointOnGeometry(1, -0.631255, 0.18081, tag=1670)
gmsh.model.geo.addPointOnGeometry(1, -0.642205, 0.190164, tag=1671)
gmsh.model.geo.addPointOnGeometry(1, -0.652915, 0.20005, tag=1672)
gmsh.model.geo.addPointOnGeometry(1, -0.66695, 0.195521, tag=1673)
gmsh.model.geo.addPointOnGeometry(1, -0.68073, 0.20128, tag=1674)
gmsh.model.geo.addPointOnGeometry(1, -0.69581, 0.19994, tag=1675)
gmsh.model.geo.addPointOnGeometry(1, -0.709505, 0.206873, tag=1676)
gmsh.model.geo.addPointOnGeometry(1, -0.723135, 0.214418, tag=1677)
gmsh.model.geo.addPointOnGeometry(1, -0.733965, 0.225914, tag=1678)
gmsh.model.geo.addPointOnGeometry(1, -0.74998, 0.225927, tag=1679)
gmsh.model.geo.addPointOnGeometry(1, -0.759175, 0.212619, tag=1680)
gmsh.model.geo.addPointOnGeometry(1, -0.7567, 0.228663, tag=1681)
gmsh.model.geo.addPointOnGeometry(1, -0.773065, 0.229267, tag=1682)
gmsh.model.geo.addPointOnGeometry(1, -0.781245, 0.214896, tag=1683)
gmsh.model.geo.addPointOnGeometry(1, -0.79683, 0.208959, tag=1684)
gmsh.model.geo.addPointOnGeometry(1, -0.812575, 0.202827, tag=1685)
gmsh.model.geo.addPointOnGeometry(1, -0.828265, 0.19595, tag=1686)
gmsh.model.geo.addPointOnGeometry(1, -0.845545, 0.194014, tag=1687)
gmsh.model.geo.addPointOnGeometry(1, -0.860905, 0.185336, tag=1688)
gmsh.model.geo.addPointOnGeometry(1, -0.87356, 0.172759, tag=1689)
gmsh.model.geo.addPointOnGeometry(1, -0.88585, 0.159583, tag=1690)
gmsh.model.geo.addPointOnGeometry(1, -0.90047, 0.148721, tag=1691)
gmsh.model.geo.addPointOnGeometry(1, -0.91215, 0.13448, tag=1692)
gmsh.model.geo.addPointOnGeometry(1, -0.91758, 0.116765, tag=1693)
gmsh.model.geo.addPointOnGeometry(1, -0.91269, 0.0989305, tag=1694)
gmsh.model.geo.addPointOnGeometry(1, -0.910685, 0.080645, tag=1695)
gmsh.model.geo.addPointOnGeometry(1, -0.910405, 0.062304, tag=1696)
gmsh.model.geo.addPointOnGeometry(1, -0.91485, 0.0444923, tag=1697)
gmsh.model.geo.addPointOnGeometry(1, -0.91614, 0.0261428, tag=1698)
gmsh.model.geo.addPointOnGeometry(1, -0.91045, 0.00870115, tag=1699)
gmsh.model.geo.addPointOnGeometry(1, -0.90387, -0.00830035, tag=1700)
gmsh.model.geo.addPointOnGeometry(1, -0.89658, -0.0248781, tag=1701)
gmsh.model.geo.addPointOnGeometry(1, -0.89348, -0.042633, tag=1702)
gmsh.model.geo.addPointOnGeometry(1, -0.888705, -0.059956, tag=1703)
gmsh.model.geo.addPointOnGeometry(1, -0.8967, -0.0761035, tag=1704)
gmsh.model.geo.addPointOnGeometry(1, -0.911685, -0.0865075, tag=1705)
gmsh.model.geo.addPointOnGeometry(1, -0.92068, -0.102654, tag=1706)
gmsh.model.geo.addPointOnGeometry(1, -0.91724, -0.120903, tag=1707)
gmsh.model.geo.addPointOnGeometry(1, -0.914165, -0.139202, tag=1708)
gmsh.model.geo.addPointOnGeometry(1, -0.91912, -0.157156, tag=1709)
gmsh.model.geo.addPointOnGeometry(1, -0.93697, -0.163283, tag=1710)
gmsh.model.geo.addPointOnGeometry(1, -0.956195, -0.163082, tag=1711)
gmsh.model.geo.addPointOnGeometry(1, -0.97567, -0.165298, tag=1712)
gmsh.model.geo.addPointOnGeometry(1, -0.99537, -0.161959, tag=1713)
gmsh.model.geo.addPointOnGeometry(1, -1.01553, -0.164939, tag=1714)
gmsh.model.geo.addPointOnGeometry(1, -1.03112, -0.178658, tag=1715)
gmsh.model.geo.addPointOnGeometry(1, -1.04242, -0.196475, tag=1716)
gmsh.model.geo.addPointOnGeometry(1, -1.05553, -0.213425, tag=1717)
gmsh.model.geo.addPointOnGeometry(1, -1.07021, -0.229531, tag=1718)
gmsh.model.geo.addPointOnGeometry(1, -1.09025, -0.239116, tag=1719)
gmsh.model.geo.addPointOnGeometry(1, -1.10779, -0.25352, tag=1720)
gmsh.model.geo.addPointOnGeometry(1, -1.12605, -0.26775, tag=1721)
gmsh.model.geo.addPointOnGeometry(1, -1.14968, -0.269235, tag=1722)
gmsh.model.geo.addPointOnGeometry(1, -1.16885, -0.283998, tag=1723)
gmsh.model.geo.addPointOnGeometry(1, -1.19213, -0.292473, tag=1724)
gmsh.model.geo.addPointOnGeometry(1, -1.2161, -0.284291, tag=1725)
gmsh.model.geo.addPointOnGeometry(1, -1.24058, -0.275894, tag=1726)
gmsh.model.geo.addPointOnGeometry(1, -1.26706, -0.27598, tag=1727)
gmsh.model.geo.addPointOnGeometry(1, -1.29388, -0.271768, tag=1728)
gmsh.model.geo.addPointOnGeometry(1, -1.32151, -0.275289, tag=1729)
gmsh.model.geo.addPointOnGeometry(1, -1.34915, -0.282678, tag=1730)
gmsh.model.geo.addPointOnGeometry(1, -1.36968, -0.303642, tag=1731)
gmsh.model.geo.addPointOnGeometry(1, -1.39279, -0.322868, tag=1732)
gmsh.model.geo.addPointOnGeometry(1, -1.41708, -0.341867, tag=1733)
gmsh.model.geo.addPointOnGeometry(1, -1.4389, -0.364785, tag=1734)
gmsh.model.geo.addPointOnGeometry(1, -1.46835, -0.378571, tag=1735)
gmsh.model.geo.addPointOnGeometry(1, -1.50004, -0.389412, tag=1736)
gmsh.model.geo.addPointOnGeometry(1, -1.53066, -0.405425, tag=1737)
gmsh.model.geo.addPointOnGeometry(1, -1.5618, -0.422718, tag=1738)
gmsh.model.geo.addPointOnGeometry(1, -1.59271, -0.442603, tag=1739)
gmsh.model.geo.addPointOnGeometry(1, -1.61465, -0.473397, tag=1740)
gmsh.model.geo.addPointOnGeometry(1, -1.64121, -0.501795, tag=1741)
gmsh.model.geo.addPointOnGeometry(1, -1.67345, -0.52566, tag=1742)
gmsh.model.geo.addPointOnGeometry(1, -1.70071, -0.556805, tag=1743)
gmsh.model.geo.addPointOnGeometry(1, -1.74104, -0.571145, tag=1744)
gmsh.model.geo.addPointOnGeometry(1, -1.77861, -0.59473, tag=1745)
gmsh.model.geo.addPointOnGeometry(1, -1.79777, -0.636295, tag=1746)
gmsh.model.geo.addPointOnGeometry(1, -1.77448, -0.676225, tag=1747)
gmsh.model.geo.addPointOnGeometry(1, -1.74455, -0.710855, tag=1748)
gmsh.model.geo.addPointOnGeometry(1, -1.72352, -0.751115, tag=1749)
gmsh.model.geo.addPointOnGeometry(1, -1.70766, -0.79366, tag=1750)
gmsh.model.geo.addPointOnGeometry(1, -1.67757, -0.82742, tag=1751)
gmsh.model.geo.addPointOnGeometry(1, -1.64279, -0.855415, tag=1752)
gmsh.model.geo.addPointOnGeometry(1, -1.60238, -0.872275, tag=1753)
gmsh.model.geo.addPointOnGeometry(1, -1.56061, -0.88115, tag=1754)
gmsh.model.geo.addPointOnGeometry(1, -1.52029, -0.89135, tag=1755)
gmsh.model.geo.addPointOnGeometry(1, -1.47985, -0.891335, tag=1756)
gmsh.model.geo.addPointOnGeometry(1, -1.44066, -0.894375, tag=1757)
gmsh.model.geo.addPointOnGeometry(1, -1.40248, -0.896195, tag=1758)
gmsh.model.geo.addPointOnGeometry(1, -1.36742, -0.884045, tag=1759)
gmsh.model.geo.addPointOnGeometry(1, -1.33946, -0.861465, tag=1760)
gmsh.model.geo.addPointOnGeometry(1, -1.30696, -0.87456, tag=1761)
gmsh.model.geo.addPointOnGeometry(1, -1.2785, -0.89412, tag=1762)
gmsh.model.geo.addPointOnGeometry(1, -1.24564, -0.885965, tag=1763)
gmsh.model.geo.addPointOnGeometry(1, -1.2173, -0.86932, tag=1764)
gmsh.model.geo.addPointOnGeometry(1, -1.1987, -0.84337, tag=1765)
gmsh.model.geo.addPointOnGeometry(1, -1.18181, -0.817305, tag=1766)
gmsh.model.geo.addPointOnGeometry(1, -1.15156, -0.819125, tag=1767)
gmsh.model.geo.addPointOnGeometry(1, -1.12314, -0.82782, tag=1768)
gmsh.model.geo.addPointOnGeometry(1, -1.09399, -0.82819, tag=1769)
gmsh.model.geo.addPointOnGeometry(1, -1.06687, -0.83728, tag=1770)
gmsh.model.geo.addPointOnGeometry(1, -1.04263, -0.851815, tag=1771)
gmsh.model.geo.addPointOnGeometry(1, -1.01498, -0.855325, tag=1772)
gmsh.model.geo.addPointOnGeometry(1, -0.98845, -0.84892, tag=1773)
gmsh.model.geo.addPointOnGeometry(1, -0.96908, -0.83064, tag=1774)
gmsh.model.geo.addPointOnGeometry(1, -0.94993, -0.81311, tag=1775)
gmsh.model.geo.addPointOnGeometry(1, -0.93277, -0.79449, tag=1776)
gmsh.model.geo.addPointOnGeometry(1, -0.913985, -0.778435, tag=1777)
gmsh.model.geo.addPointOnGeometry(1, -0.90939, -0.754685, tag=1778)
gmsh.model.geo.addPointOnGeometry(1, -0.89737, -0.734255, tag=1779)
gmsh.model.geo.addPointOnGeometry(1, -0.87846, -0.72085, tag=1780)
gmsh.model.geo.addPointOnGeometry(1, -0.869425, -0.700045, tag=1781)
gmsh.model.geo.addPointOnGeometry(1, -0.84936, -0.690495, tag=1782)
gmsh.model.geo.addPointOnGeometry(1, -0.82805, -0.686025, tag=1783)
gmsh.model.geo.addPointOnGeometry(1, -0.80725, -0.681185, tag=1784)
gmsh.model.geo.addPointOnGeometry(1, -0.786265, -0.680555, tag=1785)
gmsh.model.geo.addPointOnGeometry(1, -0.76568, -0.68236, tag=1786)
gmsh.model.geo.addPointOnGeometry(1, -0.74541, -0.68051, tag=1787)
gmsh.model.geo.addPointOnGeometry(1, -0.72537, -0.680575, tag=1788)
gmsh.model.geo.addPointOnGeometry(1, -0.705645, -0.681725, tag=1789)
gmsh.model.geo.addPointOnGeometry(1, -0.68629, -0.684195, tag=1790)
gmsh.model.geo.addPointOnGeometry(1, -0.667675, -0.68929, tag=1791)
gmsh.model.geo.addPointOnGeometry(1, -0.64859, -0.689375, tag=1792)
gmsh.model.geo.addPointOnGeometry(1, -0.62981, -0.68794, tag=1793)
gmsh.model.geo.addPointOnGeometry(1, -0.611605, -0.68431, tag=1794)
gmsh.model.geo.addPointOnGeometry(1, -0.593845, -0.679965, tag=1795)
gmsh.model.geo.addPointOnGeometry(1, -0.57776, -0.67188, tag=1796)
gmsh.model.geo.addPointOnGeometry(1, -0.561425, -0.66503, tag=1797)
gmsh.model.geo.addPointOnGeometry(1, -0.54481, -0.6597, tag=1798)
gmsh.model.geo.addPointOnGeometry(1, -0.52972, -0.651475, tag=1799)
gmsh.model.geo.addPointOnGeometry(1, -0.516355, -0.64111, tag=1800)
gmsh.model.geo.addPointOnGeometry(1, -0.524335, -0.626415, tag=1801)
gmsh.model.geo.addPointOnGeometry(1, -0.539215, -0.618815, tag=1802)
gmsh.model.geo.addPointOnGeometry(1, -0.55287, -0.609105, tag=1803)
gmsh.model.geo.addPointOnGeometry(1, -0.568235, -0.60229, tag=1804)
gmsh.model.geo.addPointOnGeometry(1, -0.581005, -0.591275, tag=1805)
gmsh.model.geo.addPointOnGeometry(1, -0.59558, -0.582705, tag=1806)
gmsh.model.geo.addPointOnGeometry(1, -0.595385, -0.56586, tag=1807)
gmsh.model.geo.addPointOnGeometry(1, -0.58517, -0.55276, tag=1808)
gmsh.model.geo.addPointOnGeometry(1, -0.583915, -0.536425, tag=1809)
gmsh.model.geo.addPointOnGeometry(1, -0.584115, -0.52022, tag=1810)
gmsh.model.geo.addPointOnGeometry(1, -0.583455, -0.5042, tag=1811)
gmsh.model.geo.addPointOnGeometry(1, -0.583715, -0.488334, tag=1812)
gmsh.model.geo.addPointOnGeometry(1, -0.580115, -0.473052, tag=1813)
gmsh.model.geo.addPointOnGeometry(1, -0.57093, -0.460575, tag=1814)
gmsh.model.geo.addPointOnGeometry(1, -0.568465, -0.445476, tag=1815)
gmsh.model.geo.addPointOnGeometry(1, -0.56477, -0.430804, tag=1816)
gmsh.model.geo.addPointOnGeometry(1, -0.554035, -0.420411, tag=1817)
gmsh.model.geo.addPointOnGeometry(1, -0.54403, -0.409587, tag=1818)
gmsh.model.geo.addPointOnGeometry(1, -0.538535, -0.396113, tag=1819)
gmsh.model.geo.addPointOnGeometry(1, -0.53471, -0.382232, tag=1820)
gmsh.model.geo.addPointOnGeometry(1, -0.525995, -0.37098, tag=1821)
gmsh.model.geo.addPointOnGeometry(1, -0.51891, -0.358834, tag=1822)
gmsh.model.geo.addPointOnGeometry(1, -0.51245, -0.346523, tag=1823)
gmsh.model.geo.addPointOnGeometry(1, -0.504935, -0.335009, tag=1824)
gmsh.model.geo.addPointOnGeometry(1, -0.498897, -0.322819, tag=1825)
gmsh.model.geo.addPointOnGeometry(1, -0.491771, -0.3114, tag=1826)
gmsh.model.geo.addPointOnGeometry(1, -0.495283, -0.324381, tag=1827)
gmsh.model.geo.addPointOnGeometry(1, -0.499563, -0.337256, tag=1828)
gmsh.model.geo.addPointOnGeometry(1, -0.486082, -0.335782, tag=1829)
gmsh.model.geo.addPointOnGeometry(1, -0.491446, -0.348235, tag=1830)
gmsh.model.geo.addPointOnGeometry(1, -0.496439, -0.36099, tag=1831)
gmsh.model.geo.addPointOnGeometry(1, -0.50154, -0.373856, tag=1832)
gmsh.model.geo.addPointOnGeometry(1, -0.50793, -0.386305, tag=1833)
gmsh.model.geo.addPointOnGeometry(1, -0.51053, -0.400204, tag=1834)
gmsh.model.geo.addPointOnGeometry(1, -0.51467, -0.413878, tag=1835)
gmsh.model.geo.addPointOnGeometry(1, -0.52484, -0.424157, tag=1836)
gmsh.model.geo.addPointOnGeometry(1, -0.53236, -0.436725, tag=1837)
gmsh.model.geo.addPointOnGeometry(1, -0.53306, -0.45152, tag=1838)
gmsh.model.geo.addPointOnGeometry(1, -0.53548, -0.466285, tag=1839)
gmsh.model.geo.addPointOnGeometry(1, -0.5418, -0.480044, tag=1840)
gmsh.model.geo.addPointOnGeometry(1, -0.54378, -0.495241, tag=1841)
gmsh.model.geo.addPointOnGeometry(1, -0.550125, -0.5094, tag=1842)
gmsh.model.geo.addPointOnGeometry(1, -0.561095, -0.52069, tag=1843)
gmsh.model.geo.addPointOnGeometry(1, -0.56992, -0.53401, tag=1844)
gmsh.model.geo.addPointOnGeometry(1, -0.57855, -0.54775, tag=1845)
gmsh.model.geo.addPointOnGeometry(1, -0.568195, -0.56042, tag=1846)
gmsh.model.geo.addPointOnGeometry(1, -0.552645, -0.56534, tag=1847)
gmsh.model.geo.addPointOnGeometry(1, -0.539685, -0.57512, tag=1848)
gmsh.model.geo.addPointOnGeometry(1, -0.524185, -0.5797, tag=1849)
gmsh.model.geo.addPointOnGeometry(1, -0.50945, -0.58611, tag=1850)
gmsh.model.geo.addPointOnGeometry(1, -0.494012, -0.590235, tag=1851)
gmsh.model.geo.addPointOnGeometry(1, -0.479204, -0.595985, tag=1852)
gmsh.model.geo.addPointOnGeometry(1, -0.463506, -0.597635, tag=1853)
gmsh.model.geo.addPointOnGeometry(1, -0.448067, -0.59516, tag=1854)
gmsh.model.geo.addPointOnGeometry(1, -0.433252, -0.599755, tag=1855)
gmsh.model.geo.addPointOnGeometry(1, -0.41847, -0.60421, tag=1856)
gmsh.model.geo.addPointOnGeometry(1, -0.403195, -0.605705, tag=1857)
gmsh.model.geo.addPointOnGeometry(1, -0.389074, -0.60007, tag=1858)
gmsh.model.geo.addPointOnGeometry(1, -0.375195, -0.594305, tag=1859)
gmsh.model.geo.addPointOnGeometry(1, -0.360774, -0.5907, tag=1860)
gmsh.model.geo.addPointOnGeometry(1, -0.346867, -0.585895, tag=1861)
gmsh.model.geo.addPointOnGeometry(1, -0.338893, -0.573735, tag=1862)
gmsh.model.geo.addPointOnGeometry(1, -0.340665, -0.55948, tag=1863)
gmsh.model.geo.addPointOnGeometry(1, -0.350401, -0.549055, tag=1864)
gmsh.model.geo.addPointOnGeometry(1, -0.353953, -0.535325, tag=1865)
gmsh.model.geo.addPointOnGeometry(1, -0.348294, -0.522485, tag=1866)
gmsh.model.geo.addPointOnGeometry(1, -0.362249, -0.52151, tag=1867)
gmsh.model.geo.addPointOnGeometry(1, -0.375749, -0.525595, tag=1868)
gmsh.model.geo.addPointOnGeometry(1, -0.388016, -0.518475, tag=1869)
gmsh.model.geo.addPointOnGeometry(1, -0.400361, -0.51144, tag=1870)
gmsh.model.geo.addPointOnGeometry(1, -0.394789, -0.498456, tag=1871)
gmsh.model.geo.addPointOnGeometry(1, -0.402382, -0.486675, tag=1872)
gmsh.model.geo.addPointOnGeometry(1, -0.39728, -0.473739, tag=1873)
gmsh.model.geo.addPointOnGeometry(1, -0.39757, -0.459982, tag=1874)
gmsh.model.geo.addPointOnGeometry(1, -0.394603, -0.446684, tag=1875)
gmsh.model.geo.addPointOnGeometry(1, -0.392622, -0.433345, tag=1876)
gmsh.model.geo.addPointOnGeometry(1, -0.37934, -0.434938, tag=1877)
gmsh.model.geo.addPointOnGeometry(1, -0.37037, -0.444813, tag=1878)
gmsh.model.geo.addPointOnGeometry(1, -0.372888, -0.457995, tag=1879)
gmsh.model.geo.addPointOnGeometry(1, -0.374894, -0.471403, tag=1880)
gmsh.model.geo.addPointOnGeometry(1, -0.369399, -0.483919, tag=1881)
gmsh.model.geo.addPointOnGeometry(1, -0.364676, -0.496835, tag=1882)
gmsh.model.geo.addPointOnGeometry(1, -0.354063, -0.505665, tag=1883)
gmsh.model.geo.addPointOnGeometry(1, -0.340394, -0.507355, tag=1884)
gmsh.model.geo.addPointOnGeometry(1, -0.33714, -0.520755, tag=1885)
gmsh.model.geo.addPointOnGeometry(1, -0.333409, -0.534155, tag=1886)
gmsh.model.geo.addPointOnGeometry(1, -0.322454, -0.54283, tag=1887)
gmsh.model.geo.addPointOnGeometry(1, -0.310521, -0.55013, tag=1888)
gmsh.model.geo.addPointOnGeometry(1, -0.30044, -0.55987, tag=1889)
gmsh.model.geo.addPointOnGeometry(1, -0.287566, -0.565445, tag=1890)
gmsh.model.geo.addPointOnGeometry(1, -0.27468, -0.57097, tag=1891)
gmsh.model.geo.addPointOnGeometry(1, -0.261615, -0.57603, tag=1892)
gmsh.model.geo.addPointOnGeometry(1, -0.252846, -0.587, tag=1893)
gmsh.model.geo.addPointOnGeometry(1, -0.249234, -0.60069, tag=1894)
gmsh.model.geo.addPointOnGeometry(1, -0.236413, -0.60688, tag=1895)
gmsh.model.geo.addPointOnGeometry(1, -0.235037, -0.62114, tag=1896)
gmsh.model.geo.addPointOnGeometry(1, -0.237435, -0.635445, tag=1897)
gmsh.model.geo.addPointOnGeometry(1, -0.231124, -0.648695, tag=1898)
gmsh.model.geo.addPointOnGeometry(1, -0.216917, -0.652605, tag=1899)
gmsh.model.geo.addPointOnGeometry(1, -0.205922, -0.64293, tag=1900)
gmsh.model.geo.addPointOnGeometry(1, -0.202899, -0.65726, tag=1901)
gmsh.model.geo.addPointOnGeometry(1, -0.20932, -0.67063, tag=1902)
gmsh.model.geo.addPointOnGeometry(1, -0.209529, -0.68567, tag=1903)
gmsh.model.geo.addPointOnGeometry(1, -0.211998, -0.700715, tag=1904)
gmsh.model.geo.addPointOnGeometry(1, -0.214157, -0.71604, tag=1905)
gmsh.model.geo.addPointOnGeometry(1, -0.21135, -0.731475, tag=1906)
gmsh.model.geo.addPointOnGeometry(1, -0.208637, -0.747155, tag=1907)
gmsh.model.geo.addPointOnGeometry(1, -0.208558, -0.763295, tag=1908)
gmsh.model.geo.addPointOnGeometry(1, -0.207252, -0.77963, tag=1909)
gmsh.model.geo.addPointOnGeometry(1, -0.201742, -0.79531, tag=1910)
gmsh.model.geo.addPointOnGeometry(1, -0.200113, -0.812095, tag=1911)
gmsh.model.geo.addPointOnGeometry(1, -0.199297, -0.829205, tag=1912)
gmsh.model.geo.addPointOnGeometry(1, -0.191645, -0.84482, tag=1913)
gmsh.model.geo.addPointOnGeometry(1, -0.175578, -0.838095, tag=1914)
gmsh.model.geo.addPointOnGeometry(1, -0.161371, -0.82835, tag=1915)
gmsh.model.geo.addPointOnGeometry(1, -0.14655, -0.81996, tag=1916)
gmsh.model.geo.addPointOnGeometry(1, -0.145121, -0.80322, tag=1917)
gmsh.model.geo.addPointOnGeometry(1, -0.136298, -0.78923, tag=1918)
gmsh.model.geo.addPointOnGeometry(1, -0.132887, -0.773305, tag=1919)
gmsh.model.geo.addPointOnGeometry(1, -0.132393, -0.75728, tag=1920)
gmsh.model.geo.addPointOnGeometry(1, -0.122321, -0.7451, tag=1921)
gmsh.model.geo.addPointOnGeometry(1, -0.107134, -0.7413, tag=1922)
gmsh.model.geo.addPointOnGeometry(1, -0.09591, -0.73058, tag=1923)
gmsh.model.geo.addPointOnGeometry(1, -0.082603, -0.722905, tag=1924)
gmsh.model.geo.addPointOnGeometry(1, -0.0698085, -0.71465, tag=1925)
gmsh.model.geo.addPointOnGeometry(1, -0.0588625, -0.70428, tag=1926)
gmsh.model.geo.addPointOnGeometry(1, -0.0447143, -0.69944, tag=1927)
gmsh.model.geo.addPointOnGeometry(1, -0.0370785, -0.68674, tag=1928)
gmsh.model.geo.addPointOnGeometry(1, -0.0256631, -0.677535, tag=1929)
gmsh.model.geo.addPointOnGeometry(1, -0.0111695, -0.679355, tag=1930)
gmsh.model.geo.addPointOnGeometry(1, 0.00187924, -0.672865, tag=1931)
gmsh.model.geo.addPointOnGeometry(1, 0.0142019, -0.665265, tag=1932)
gmsh.model.geo.addPointOnGeometry(1, 0.0225906, -0.677105, tag=1933)
gmsh.model.geo.addPointOnGeometry(1, 0.028901, -0.69036, tag=1934)
gmsh.model.geo.addPointOnGeometry(1, 0.0411475, -0.698735, tag=1935)
gmsh.model.geo.addPointOnGeometry(1, 0.0473529, -0.71239, tag=1936)
gmsh.model.geo.addPointOnGeometry(1, 0.0568155, -0.72427, tag=1937)
gmsh.model.geo.addPointOnGeometry(1, 0.0566835, -0.73966, tag=1938)
gmsh.model.geo.addPointOnGeometry(1, 0.063279, -0.753815, tag=1939)
gmsh.model.geo.addPointOnGeometry(1, 0.076973, -0.746185, tag=1940)
gmsh.model.geo.addPointOnGeometry(1, 0.0884255, -0.735655, tag=1941)
gmsh.model.geo.addPointOnGeometry(1, 0.0992695, -0.746845, tag=1942)
gmsh.model.geo.addPointOnGeometry(1, 0.104326, -0.76181, tag=1943)
gmsh.model.geo.addPointOnGeometry(1, 0.110187, -0.77673, tag=1944)
gmsh.model.geo.addPointOnGeometry(1, 0.120107, -0.789625, tag=1945)
gmsh.model.geo.addPointOnGeometry(1, 0.123833, -0.80571, tag=1946)
gmsh.model.geo.addPointOnGeometry(1, 0.122786, -0.822455, tag=1947)
gmsh.model.geo.addPointOnGeometry(1, 0.123942, -0.839475, tag=1948)
gmsh.model.geo.addPointOnGeometry(1, 0.124766, -0.856805, tag=1949)
gmsh.model.geo.addPointOnGeometry(1, 0.140905, -0.863775, tag=1950)
gmsh.model.geo.addPointOnGeometry(1, 0.153338, -0.876495, tag=1951)
gmsh.model.geo.addPointOnGeometry(1, 0.16382, -0.89121, tag=1952)
gmsh.model.geo.addPointOnGeometry(1, 0.170262, -0.90842, tag=1953)
gmsh.model.geo.addPointOnGeometry(1, 0.181399, -0.92344, tag=1954)
gmsh.model.geo.addPointOnGeometry(1, 0.195522, -0.93615, tag=1955)
gmsh.model.geo.addPointOnGeometry(1, 0.213109, -0.943995, tag=1956)
gmsh.model.geo.addPointOnGeometry(1, 0.23242, -0.946145, tag=1957)
gmsh.model.geo.addPointOnGeometry(1, 0.227229, -0.92755, tag=1958)
gmsh.model.geo.addPointOnGeometry(1, 0.216293, -0.91207, tag=1959)
gmsh.model.geo.addPointOnGeometry(1, 0.213108, -0.89373, tag=1960)
gmsh.model.geo.addPointOnGeometry(1, 0.198524, -0.88265, tag=1961)
gmsh.model.geo.addPointOnGeometry(1, 0.182623, -0.874045, tag=1962)
gmsh.model.geo.addPointOnGeometry(1, 0.165358, -0.869305, tag=1963)
gmsh.model.geo.addPointOnGeometry(1, 0.155601, -0.854555, tag=1964)
gmsh.model.geo.addPointOnGeometry(1, 0.146559, -0.83968, tag=1965)
gmsh.model.geo.addPointOnGeometry(1, 0.133496, -0.828565, tag=1966)
gmsh.model.geo.addPointOnGeometry(1, 0.135694, -0.811795, tag=1967)
gmsh.model.geo.addPointOnGeometry(1, 0.139919, -0.79569, tag=1968)
gmsh.model.geo.addPointOnGeometry(1, 0.136778, -0.779595, tag=1969)
gmsh.model.geo.addPointOnGeometry(1, 0.151812, -0.78599, tag=1970)
gmsh.model.geo.addPointOnGeometry(1, 0.168162, -0.784445, tag=1971)
gmsh.model.geo.addPointOnGeometry(1, 0.181887, -0.793665, tag=1972)
gmsh.model.geo.addPointOnGeometry(1, 0.195933, -0.802765, tag=1973)
gmsh.model.geo.addPointOnGeometry(1, 0.2113, -0.809835, tag=1974)
gmsh.model.geo.addPointOnGeometry(1, 0.218244, -0.82552, tag=1975)
gmsh.model.geo.addPointOnGeometry(1, 0.233385, -0.817235, tag=1976)
gmsh.model.geo.addPointOnGeometry(1, 0.241678, -0.802255, tag=1977)
gmsh.model.geo.addPointOnGeometry(1, 0.253269, -0.78989, tag=1978)
gmsh.model.geo.addPointOnGeometry(1, 0.263792, -0.776785, tag=1979)
gmsh.model.geo.addPointOnGeometry(1, 0.264971, -0.76022, tag=1980)
gmsh.model.geo.addPointOnGeometry(1, 0.259888, -0.74468, tag=1981)
gmsh.model.geo.addPointOnGeometry(1, 0.252594, -0.73033, tag=1982)
gmsh.model.geo.addPointOnGeometry(1, 0.241147, -0.719345, tag=1983)
gmsh.model.geo.addPointOnGeometry(1, 0.227519, -0.711615, tag=1984)
gmsh.model.geo.addPointOnGeometry(1, 0.213622, -0.70474, tag=1985)
gmsh.model.geo.addPointOnGeometry(1, 0.202077, -0.694655, tag=1986)
gmsh.model.geo.addPointOnGeometry(1, 0.193222, -0.682385, tag=1987)
gmsh.model.geo.addPointOnGeometry(1, 0.198261, -0.668315, tag=1988)
gmsh.model.geo.addPointOnGeometry(1, 0.20493, -0.65512, tag=1989)
gmsh.model.geo.addPointOnGeometry(1, 0.2144, -0.64393, tag=1990)
gmsh.model.geo.addPointOnGeometry(1, 0.2285, -0.64009, tag=1991)
gmsh.model.geo.addPointOnGeometry(1, 0.236417, -0.6525, tag=1992)
gmsh.model.geo.addPointOnGeometry(1, 0.241522, -0.63867, tag=1993)
gmsh.model.geo.addPointOnGeometry(1, 0.25306, -0.629665, tag=1994)
gmsh.model.geo.addPointOnGeometry(1, 0.263111, -0.619125, tag=1995)
gmsh.model.geo.addPointOnGeometry(1, 0.275511, -0.61158, tag=1996)
gmsh.model.geo.addPointOnGeometry(1, 0.283626, -0.599625, tag=1997)
gmsh.model.geo.addPointOnGeometry(1, 0.295803, -0.591955, tag=1998)
gmsh.model.geo.addPointOnGeometry(1, 0.300682, -0.578495, tag=1999)
gmsh.model.geo.addPointOnGeometry(1, 0.305137, -0.565025, tag=2000)
gmsh.model.geo.addPointOnGeometry(1, 0.307821, -0.55123, tag=2001)
gmsh.model.geo.addPointOnGeometry(1, 0.309778, -0.53745, tag=2002)
gmsh.model.geo.addPointOnGeometry(1, 0.309115, -0.523695, tag=2003)
gmsh.model.geo.addPointOnGeometry(1, 0.308742, -0.51007, tag=2004)
gmsh.model.geo.addPointOnGeometry(1, 0.30964, -0.496612, tag=2005)
gmsh.model.geo.addPointOnGeometry(1, 0.297883, -0.490272, tag=2006)
gmsh.model.geo.addPointOnGeometry(1, 0.29232, -0.478283, tag=2007)
gmsh.model.geo.addPointOnGeometry(1, 0.279306, -0.479886, tag=2008)
gmsh.model.geo.addPointOnGeometry(1, 0.291287, -0.474608, tag=2009)
gmsh.model.geo.addPointOnGeometry(1, 0.279822, -0.468399, tag=2010)
gmsh.model.geo.addPointOnGeometry(1, 0.269985, -0.460036, tag=2011)
gmsh.model.geo.addPointOnGeometry(1, 0.257704, -0.456437, tag=2012)
gmsh.model.geo.addPointOnGeometry(1, 0.255435, -0.443955, tag=2013)
gmsh.model.geo.addPointOnGeometry(1, 0.259191, -0.431945, tag=2014)
gmsh.model.geo.addPointOnGeometry(1, 0.267721, -0.422778, tag=2015)
gmsh.model.geo.addPointOnGeometry(1, 0.255392, -0.420943, tag=2016)
gmsh.model.geo.addPointOnGeometry(1, 0.247061, -0.430186, tag=2017)
gmsh.model.geo.addPointOnGeometry(1, 0.235189, -0.426547, tag=2018)
gmsh.model.geo.addPointOnGeometry(1, 0.222846, -0.42677, tag=2019)
gmsh.model.geo.addPointOnGeometry(1, 0.229292, -0.416307, tag=2020)
gmsh.model.geo.addPointOnGeometry(1, 0.232285, -0.404463, tag=2021)
gmsh.model.geo.addPointOnGeometry(1, 0.235734, -0.392825, tag=2022)
gmsh.model.geo.addPointOnGeometry(1, 0.245373, -0.400224, tag=2023)
gmsh.model.geo.addPointOnGeometry(1, 0.256003, -0.394224, tag=2024)
gmsh.model.geo.addPointOnGeometry(1, 0.264964, -0.385944, tag=2025)
gmsh.model.geo.addPointOnGeometry(1, 0.275731, -0.391774, tag=2026)
gmsh.model.geo.addPointOnGeometry(1, 0.287175, -0.396404, tag=2027)
gmsh.model.geo.addPointOnGeometry(1, 0.299272, -0.399317, tag=2028)
gmsh.model.geo.addPointOnGeometry(1, 0.306045, -0.409887, tag=2029)
gmsh.model.geo.addPointOnGeometry(1, 0.312014, -0.421077, tag=2030)
gmsh.model.geo.addPointOnGeometry(1, 0.319969, -0.411137, tag=2031)
gmsh.model.geo.addPointOnGeometry(1, 0.327643, -0.401021, tag=2032)
gmsh.model.geo.addPointOnGeometry(1, 0.320789, -0.390426, tag=2033)
gmsh.model.geo.addPointOnGeometry(1, 0.311156, -0.382472, tag=2034)
gmsh.model.geo.addPointOnGeometry(1, 0.299695, -0.377794, tag=2035)
gmsh.model.geo.addPointOnGeometry(1, 0.287533, -0.376079, tag=2036)
gmsh.model.geo.addPointOnGeometry(1, 0.289197, -0.363991, tag=2037)
gmsh.model.geo.addPointOnGeometry(1, 0.291822, -0.352152, tag=2038)
gmsh.model.geo.addPointOnGeometry(1, 0.286288, -0.341461, tag=2039)
gmsh.model.geo.addPointOnGeometry(1, 0.288901, -0.329796, tag=2040)
gmsh.model.geo.addPointOnGeometry(1, 0.295722, -0.320031, tag=2041)
gmsh.model.geo.addPointOnGeometry(1, 0.302809, -0.310484, tag=2042)
gmsh.model.geo.addPointOnGeometry(1, 0.303889, -0.298685, tag=2043)
gmsh.model.geo.addPointOnGeometry(1, 0.301269, -0.287205, tag=2044)
gmsh.model.geo.addPointOnGeometry(1, 0.300098, -0.275568, tag=2045)
gmsh.model.geo.addPointOnGeometry(1, 0.297139, -0.26433, tag=2046)
gmsh.model.geo.addPointOnGeometry(1, 0.293239, -0.253467, tag=2047)
gmsh.model.geo.addPointOnGeometry(1, 0.290517, -0.242327, tag=2048)
gmsh.model.geo.addPointOnGeometry(1, 0.283322, -0.233497, tag=2049)
gmsh.model.geo.addPointOnGeometry(1, 0.274655, -0.226233, tag=2050)
gmsh.model.geo.addPointOnGeometry(1, 0.269449, -0.216284, tag=2051)
gmsh.model.geo.addPointOnGeometry(1, 0.259724, -0.210813, tag=2052)
gmsh.model.geo.addPointOnGeometry(1, 0.249091, -0.207677, tag=2053)
gmsh.model.geo.addPointOnGeometry(1, 0.246651, -0.218474, tag=2054)
gmsh.model.geo.addPointOnGeometry(1, 0.236501, -0.222893, tag=2055)
gmsh.model.geo.addPointOnGeometry(1, 0.225494, -0.223666, tag=2056)
gmsh.model.geo.addPointOnGeometry(1, 0.22466, -0.212715, tag=2057)
gmsh.model.geo.addPointOnGeometry(1, 0.224561, -0.20178, tag=2058)
gmsh.model.geo.addPointOnGeometry(1, 0.222623, -0.191068, tag=2059)
gmsh.model.geo.addPointOnGeometry(1, 0.22035, -0.180473, tag=2060)
gmsh.model.geo.addPointOnGeometry(1, 0.218474, -0.169843, tag=2061)
gmsh.model.geo.addPointOnGeometry(1, 0.222328, -0.159798, tag=2062)
gmsh.model.geo.addPointOnGeometry(1, 0.227777, -0.150534, tag=2063)
gmsh.model.geo.addPointOnGeometry(1, 0.235188, -0.142744, tag=2064)
gmsh.model.geo.addPointOnGeometry(1, 0.238527, -0.132525, tag=2065)
gmsh.model.geo.addPointOnGeometry(1, 0.247609, -0.126757, tag=2066)
gmsh.model.geo.addPointOnGeometry(1, 0.249866, -0.116229, tag=2067)
gmsh.model.geo.addPointOnGeometry(1, 0.239846, -0.112388, tag=2068)
gmsh.model.geo.addPointOnGeometry(1, 0.235596, -0.102588, tag=2069)
gmsh.model.geo.addPointOnGeometry(1, 0.233768, -0.092099, tag=2070)
gmsh.model.geo.addPointOnGeometry(1, 0.243252, -0.087254, tag=2071)
gmsh.model.geo.addPointOnGeometry(1, 0.241142, -0.0768105, tag=2072)
gmsh.model.geo.addPointOnGeometry(1, 0.234048, -0.06891, tag=2073)
gmsh.model.geo.addPointOnGeometry(1, 0.244417, -0.071218, tag=2074)
gmsh.model.geo.addPointOnGeometry(1, 0.250472, -0.0800035, tag=2075)
gmsh.model.geo.addPointOnGeometry(1, 0.256507, -0.0888565, tag=2076)
gmsh.model.geo.addPointOnGeometry(1, 0.262288, -0.097932, tag=2077)
gmsh.model.geo.addPointOnGeometry(1, 0.265804, -0.108148, tag=2078)
gmsh.model.geo.addPointOnGeometry(1, 0.271019, -0.11766, tag=2079)
gmsh.model.geo.addPointOnGeometry(1, 0.278334, -0.125745, tag=2080)
gmsh.model.geo.addPointOnGeometry(1, 0.288181, -0.130575, tag=2081)
gmsh.model.geo.addPointOnGeometry(1, 0.298871, -0.133316, tag=2082)
gmsh.model.geo.addPointOnGeometry(1, 0.309602, -0.13619, tag=2083)
gmsh.model.geo.addPointOnGeometry(1, 0.320319, -0.139382, tag=2084)
gmsh.model.geo.addPointOnGeometry(1, 0.320496, -0.128177, tag=2085)
gmsh.model.geo.addPointOnGeometry(1, 0.310993, -0.122337, tag=2086)
gmsh.model.geo.addPointOnGeometry(1, 0.3063, -0.112287, tag=2087)
gmsh.model.geo.addPointOnGeometry(1, 0.303476, -0.10161, tag=2088)
gmsh.model.geo.addPointOnGeometry(1, 0.29372, -0.0965475, tag=2089)
gmsh.model.geo.addPointOnGeometry(1, 0.288612, -0.086881, tag=2090)
gmsh.model.geo.addPointOnGeometry(1, 0.278157, -0.0838845, tag=2091)
gmsh.model.geo.addPointOnGeometry(1, 0.267373, -0.0847205, tag=2092)
gmsh.model.geo.addPointOnGeometry(1, 0.259451, -0.0774395, tag=2093)
gmsh.model.geo.addPointOnGeometry(1, 0.257715, -0.0668595, tag=2094)
gmsh.model.geo.addPointOnGeometry(1, 0.257614, -0.056158, tag=2095)
gmsh.model.geo.addPointOnGeometry(1, 5.93015, 5.03115, tag=2096)
gmsh.model.geo.addPointOnGeometry(1, 5.35695, 5.1681, tag=2097)
gmsh.model.geo.addPointOnGeometry(1, 4.8625, 5.41275, tag=2098)
gmsh.model.geo.addPointOnGeometry(1, 4.37056, 5.602, tag=2099)
gmsh.model.geo.addPointOnGeometry(1, 3.88248, 5.5448, tag=2100)
gmsh.model.geo.addPointOnGeometry(1, 3.61838, 5.948, tag=2101)
gmsh.model.geo.addPointOnGeometry(1, 3.26166, 6.30505, tag=2102)
gmsh.model.geo.addPointOnGeometry(1, 2.81599, 6.5682, tag=2103)
gmsh.model.geo.addPointOnGeometry(1, 2.45716, 6.96935, tag=2104)
gmsh.model.geo.addPointOnGeometry(1, 1.97101, 7.259, tag=2105)
gmsh.model.geo.addPointOnGeometry(1, 1.51713, 6.95575, tag=2106)
gmsh.model.geo.addPointOnGeometry(1, 1.55719, 6.4731, tag=2107)
gmsh.model.geo.addPointOnGeometry(1, 1.26645, 6.15775, tag=2108)
gmsh.model.geo.addPointOnGeometry(1, 0.866395, 6.22125, tag=2109)
gmsh.model.geo.addPointOnGeometry(1, 0.54857, 6.49515, tag=2110)
gmsh.model.geo.addPointOnGeometry(1, 0.11802, 6.46465, tag=2111)
gmsh.model.geo.addPointOnGeometry(1, -0.275897, 6.66405, tag=2112)
gmsh.model.geo.addPointOnGeometry(1, -0.71961, 6.58005, tag=2113)
gmsh.model.geo.addPointOnGeometry(1, -1.17162, 6.5733, tag=2114)
gmsh.model.geo.addPointOnGeometry(1, -1.59163, 6.4082, tag=2115)
gmsh.model.geo.addPointOnGeometry(1, -1.99432, 6.2263, tag=2116)
gmsh.model.geo.addPointOnGeometry(1, -1.6144, 6.0483, tag=2117)
gmsh.model.geo.addPointOnGeometry(1, -1.47092, 5.69815, tag=2118)
gmsh.model.geo.addPointOnGeometry(1, -1.53568, 5.3659, tag=2119)
gmsh.model.geo.addPointOnGeometry(1, -1.69825, 5.10175, tag=2120)
gmsh.model.geo.addPointOnGeometry(1, -1.99562, 5.0491, tag=2121)
gmsh.model.geo.addPointOnGeometry(1, -2.00049, 4.75864, tag=2122)
gmsh.model.geo.addPointOnGeometry(1, -1.95049, 4.50008, tag=2123)
gmsh.model.geo.addPointOnGeometry(1, -1.97438, 4.26089, tag=2124)
gmsh.model.geo.addPointOnGeometry(1, -2.0493, 4.0506, tag=2125)
gmsh.model.geo.addPointOnGeometry(1, -2.12156, 3.85366, tag=2126)
gmsh.model.geo.addPointOnGeometry(1, -2.2627, 3.71021, tag=2127)
gmsh.model.geo.addPointOnGeometry(1, -2.30519, 3.91341, tag=2128)
gmsh.model.geo.addPointOnGeometry(1, -2.33287, 4.13754, tag=2129)
gmsh.model.geo.addPointOnGeometry(1, -2.47857, 4.33766, tag=2130)
gmsh.model.geo.addPointOnGeometry(1, -2.69839, 4.49824, tag=2131)
gmsh.model.geo.addPointOnGeometry(1, -2.72586, 4.79653, tag=2132)
gmsh.model.geo.addPointOnGeometry(1, -2.87315, 5.0956, tag=2133)
gmsh.model.geo.addPointOnGeometry(1, -3.09601, 5.3959, tag=2134)
gmsh.model.geo.addPointOnGeometry(1, -3.34689, 5.7376, tag=2135)
gmsh.model.geo.addPointOnGeometry(1, -3.46032, 6.2061, tag=2136)
gmsh.model.geo.addPointOnGeometry(1, -3.73246, 6.6893, tag=2137)
gmsh.model.geo.addPointOnGeometry(1, -4.35055, 6.80705, tag=2138)
gmsh.model.geo.addPointOnGeometry(1, -5.0437, 6.91515, tag=2139)
gmsh.model.geo.addPointOnGeometry(1, -5.8227, 6.85935, tag=2140)
gmsh.model.geo.addPointOnGeometry(1, -6.6923, 6.8274, tag=2141)
gmsh.model.geo.addPointOnGeometry(1, -7.2376, 6.093, tag=2142)
gmsh.model.geo.addPointOnGeometry(1, -7.5611, 5.27215, tag=2143)
gmsh.model.geo.addPointOnGeometry(1, -7.45235, 4.46818, tag=2144)
gmsh.model.geo.addPointOnGeometry(1, -7.3134, 3.75638, tag=2145)
gmsh.model.geo.addPointOnGeometry(1, -6.9815, 3.20704, tag=2146)
gmsh.model.geo.addPointOnGeometry(1, -6.6617, 2.74304, tag=2147)
gmsh.model.geo.addPointOnGeometry(1, -6.4031, 2.31431, tag=2148)
gmsh.model.geo.addPointOnGeometry(1, -6.23745, 1.89042, tag=2149)
gmsh.model.geo.addPointOnGeometry(1, -6.10895, 1.49052, tag=2150)
gmsh.model.geo.addPointOnGeometry(1, -5.8823, 1.17641, tag=2151)
gmsh.model.geo.addPointOnGeometry(1, -5.7971, 0.825075, tag=2152)
gmsh.model.geo.addPointOnGeometry(1, -5.75925, 0.478722, tag=2153)
gmsh.model.geo.addPointOnGeometry(1, -5.64735, 0.16123, tag=2154)
gmsh.model.geo.addPointOnGeometry(1, -5.37375, 0.0078962, tag=2155)
gmsh.model.geo.addPointOnGeometry(1, -5.48635, -0.01229, tag=2156)
gmsh.model.geo.addPointOnGeometry(1, -5.68275, -0.267817, tag=2157)
gmsh.model.geo.addPointOnGeometry(1, -5.70155, -0.603505, tag=2158)
gmsh.model.geo.addPointOnGeometry(1, -5.6341, -0.93421, tag=2159)
gmsh.model.geo.addPointOnGeometry(1, -5.54875, -1.25854, tag=2160)
gmsh.model.geo.addPointOnGeometry(1, -5.2932, -1.45505, tag=2161)
gmsh.model.geo.addPointOnGeometry(1, -5.34455, -1.76998, tag=2162)
gmsh.model.geo.addPointOnGeometry(1, -5.32165, -2.10161, tag=2163)
gmsh.model.geo.addPointOnGeometry(1, -5.1806, -2.40754, tag=2164)
gmsh.model.geo.addPointOnGeometry(1, -4.95837, -2.65321, tag=2165)
gmsh.model.geo.addPointOnGeometry(1, -4.66805, -2.77727, tag=2166)
gmsh.model.geo.addPointOnGeometry(1, -4.41367, -2.93237, tag=2167)
gmsh.model.geo.addPointOnGeometry(1, -4.44322, -3.23219, tag=2168)
gmsh.model.geo.addPointOnGeometry(1, -4.34112, -3.53333, tag=2169)
gmsh.model.geo.addPointOnGeometry(1, -4.04476, -3.45025, tag=2170)
gmsh.model.geo.addPointOnGeometry(1, -3.7701, -3.52394, tag=2171)
gmsh.model.geo.addPointOnGeometry(1, -3.52305, -3.6364, tag=2172)
gmsh.model.geo.addPointOnGeometry(1, -3.269, -3.68985, tag=2173)
gmsh.model.geo.addPointOnGeometry(1, -3.02395, -3.68585, tag=2174)
gmsh.model.geo.addPointOnGeometry(1, -2.80229, -3.75951, tag=2175)
gmsh.model.geo.addPointOnGeometry(1, -2.65961, -3.94401, tag=2176)
gmsh.model.geo.addPointOnGeometry(1, -2.61513, -4.18479, tag=2177)
gmsh.model.geo.addPointOnGeometry(1, -2.4461, -4.3792, tag=2178)
gmsh.model.geo.addPointOnGeometry(1, -2.22139, -4.51512, tag=2179)
gmsh.model.geo.addPointOnGeometry(1, -2.00999, -4.67692, tag=2180)
gmsh.model.geo.addPointOnGeometry(1, -1.79581, -4.84664, tag=2181)
gmsh.model.geo.addPointOnGeometry(1, -1.66275, -5.1015, tag=2182)
gmsh.model.geo.addPointOnGeometry(1, -1.4265, -5.295, tag=2183)
gmsh.model.geo.addPointOnGeometry(1, -1.12277, -5.2642, tag=2184)
gmsh.model.geo.addPointOnGeometry(1, -0.9165, -5.0645, tag=2185)
gmsh.model.geo.addPointOnGeometry(1, -0.690645, -4.92354, tag=2186)
gmsh.model.geo.addPointOnGeometry(1, -0.61951, -4.68883, tag=2187)
gmsh.model.geo.addPointOnGeometry(1, -0.403588, -4.78741, tag=2188)
gmsh.model.geo.addPointOnGeometry(1, -0.183257, -4.7018, tag=2189)
gmsh.model.geo.addPointOnGeometry(1, -0.0101804, -4.86678, tag=2190)
gmsh.model.geo.addPointOnGeometry(1, 0.232504, -4.82946, tag=2191)
gmsh.model.geo.addPointOnGeometry(1, 0.449323, -4.72707, tag=2192)
gmsh.model.geo.addPointOnGeometry(1, 0.642955, -4.60157, tag=2193)
gmsh.model.geo.addPointOnGeometry(1, 0.843895, -4.50462, tag=2194)
gmsh.model.geo.addPointOnGeometry(1, 1.05514, -4.44484, tag=2195)
gmsh.model.geo.addPointOnGeometry(1, 1.24885, -4.56189, tag=2196)
gmsh.model.geo.addPointOnGeometry(1, 1.48595, -4.58054, tag=2197)
gmsh.model.geo.addPointOnGeometry(1, 1.67846, -4.43933, tag=2198)
gmsh.model.geo.addPointOnGeometry(1, 1.84718, -4.28099, tag=2199)
gmsh.model.geo.addPointOnGeometry(1, 2.07925, -4.29641, tag=2200)
gmsh.model.geo.addPointOnGeometry(1, 2.32217, -4.32332, tag=2201)
gmsh.model.geo.addPointOnGeometry(1, 2.51369, -4.16442, tag=2202)
gmsh.model.geo.addPointOnGeometry(1, 2.39842, -3.95937, tag=2203)
gmsh.model.geo.addPointOnGeometry(1, 2.62801, -3.95031, tag=2204)
gmsh.model.geo.addPointOnGeometry(1, 2.84175, -3.84749, tag=2205)
gmsh.model.geo.addPointOnGeometry(1, 3.08623, -3.82768, tag=2206)
gmsh.model.geo.addPointOnGeometry(1, 2.9912, -3.60677, tag=2207)
gmsh.model.geo.addPointOnGeometry(1, 3.19128, -3.48972, tag=2208)
gmsh.model.geo.addPointOnGeometry(1, 3.338, -3.30953, tag=2209)
gmsh.model.geo.addPointOnGeometry(1, 3.55431, -3.21682, tag=2210)
gmsh.model.geo.addPointOnGeometry(1, 3.75711, -3.08279, tag=2211)
gmsh.model.geo.addPointOnGeometry(1, 3.92776, -2.90346, tag=2212)
gmsh.model.geo.addPointOnGeometry(1, 4.06765, -2.69807, tag=2213)
gmsh.model.geo.addPointOnGeometry(1, 4.32671, -2.69912, tag=2214)
gmsh.model.geo.addPointOnGeometry(1, 4.56205, -2.55364, tag=2215)
gmsh.model.geo.addPointOnGeometry(1, 4.70283, -2.30705, tag=2216)
gmsh.model.geo.addPointOnGeometry(1, 4.95768, -2.16163, tag=2217)
gmsh.model.geo.addPointOnGeometry(1, 5.1735, -1.94055, tag=2218)
gmsh.model.geo.addPointOnGeometry(1, 5.4554, -1.77423, tag=2219)
gmsh.model.geo.addPointOnGeometry(1, 5.642, -1.48436, tag=2220)
gmsh.model.geo.addPointOnGeometry(1, 5.8942, -1.22558, tag=2221)
gmsh.model.geo.addPointOnGeometry(1, 6.2258, -1.02004, tag=2222)
gmsh.model.geo.addPointOnGeometry(1, 6.5832, -1.26452, tag=2223)
gmsh.model.geo.addPointOnGeometry(1, 6.8254, -1.6805, tag=2224)
gmsh.model.geo.addPointOnGeometry(1, 7.1989, -2.0656, tag=2225)
gmsh.model.geo.addPointOnGeometry(1, 7.72495, -2.38552, tag=2226)
gmsh.model.geo.addPointOnGeometry(1, 8.4282, -2.54953, tag=2227)
gmsh.model.geo.addPointOnGeometry(1, 8.84385, -1.85921, tag=2228)
gmsh.model.geo.addPointOnGeometry(1, 9.02735, -1.04795, tag=2229)
gmsh.model.geo.addPointOnGeometry(1, 9.5242, -0.326033, tag=2230)
gmsh.model.geo.addPointOnGeometry(1, 8.83185, 3.08457, tag=2231)
gmsh.model.geo.addPointOnGeometry(1, 8.06705, 3.40543, tag=2232)
gmsh.model.geo.addPointOnGeometry(1, 7.70995, 4.09348, tag=2233)
gmsh.model.geo.addPointOnGeometry(1, 7.018, 4.32552, tag=2234)
gmsh.model.geo.addPointOnGeometry(1, 6.45155, 4.67796, tag=2235)
gmsh.model.geo.addLine(3, 4, 2)
gmsh.model.geo.addLine(4, 5, 3)
gmsh.model.geo.addLine(5, 6, 4)
gmsh.model.geo.addLine(6, 3, 5)
gmsh.model.geo.addLine(7, 8, 7)
gmsh.model.geo.addLine(8, 9, 8)
gmsh.model.geo.addLine(9, 10, 9)
gmsh.model.geo.addLine(10, 7, 10)
gmsh.model.geo.addLine(11, 12, 12)
gmsh.model.geo.addLine(12, 13, 13)
gmsh.model.geo.addLine(13, 14, 14)
gmsh.model.geo.addLine(14, 11, 15)
gmsh.model.geo.addLine(15, 16, 17)
gmsh.model.geo.addLine(16, 17, 18)
gmsh.model.geo.addLine(17, 18, 19)
gmsh.model.geo.addLine(18, 15, 20)
gmsh.model.geo.addLine(19, 20, 22)
gmsh.model.geo.addLine(20, 21, 23)
gmsh.model.geo.addLine(21, 22, 24)
gmsh.model.geo.addLine(22, 23, 25)
gmsh.model.geo.addLine(23, 19, 26)
gmsh.model.geo.addLine(24, 25, 28)
gmsh.model.geo.addLine(25, 26, 29)
gmsh.model.geo.addLine(26, 27, 30)
gmsh.model.geo.addLine(27, 28, 31)
gmsh.model.geo.addLine(28, 24, 32)
gmsh.model.geo.addLine(29, 30, 34)
gmsh.model.geo.addLine(30, 31, 35)
gmsh.model.geo.addLine(31, 32, 36)
gmsh.model.geo.addLine(32, 29, 37)
gmsh.model.geo.addLine(33, 34, 39)
gmsh.model.geo.addLine(34, 35, 40)
gmsh.model.geo.addLine(35, 36, 41)
gmsh.model.geo.addLine(36, 37, 42)
gmsh.model.geo.addLine(37, 38, 43)
gmsh.model.geo.addLine(38, 33, 44)
gmsh.model.geo.addLine(39, 40, 46)
gmsh.model.geo.addLine(40, 41, 47)
gmsh.model.geo.addLine(41, 42, 48)
gmsh.model.geo.addLine(42, 43, 49)
gmsh.model.geo.addLine(43, 39, 50)
gmsh.model.geo.addLine(44, 45, 52)
gmsh.model.geo.addLine(45, 46, 53)
gmsh.model.geo.addLine(46, 47, 54)
gmsh.model.geo.addLine(47, 48, 55)
gmsh.model.geo.addLine(48, 49, 56)
gmsh.model.geo.addLine(49, 44, 57)
gmsh.model.geo.addLine(50, 51, 59)
gmsh.model.geo.addLine(51, 52, 60)
gmsh.model.geo.addLine(52, 53, 61)
gmsh.model.geo.addLine(53, 54, 62)
gmsh.model.geo.addLine(54, 55, 63)
gmsh.model.geo.addLine(55, 50, 64)
gmsh.model.geo.addLine(56, 57, 66)
gmsh.model.geo.addLine(57, 58, 67)
gmsh.model.geo.addLine(58, 59, 68)
gmsh.model.geo.addLine(59, 60, 69)
gmsh.model.geo.addLine(60, 61, 70)
gmsh.model.geo.addLine(61, 56, 71)
gmsh.model.geo.addLine(62, 63, 73)
gmsh.model.geo.addLine(63, 64, 74)
gmsh.model.geo.addLine(64, 65, 75)
gmsh.model.geo.addLine(65, 66, 76)
gmsh.model.geo.addLine(66, 67, 77)
gmsh.model.geo.addLine(67, 68, 78)
gmsh.model.geo.addLine(68, 62, 79)
gmsh.model.geo.addLine(69, 70, 81)
gmsh.model.geo.addLine(70, 71, 82)
gmsh.model.geo.addLine(71, 72, 83)
gmsh.model.geo.addLine(72, 73, 84)
gmsh.model.geo.addLine(73, 74, 85)
gmsh.model.geo.addLine(74, 69, 86)
gmsh.model.geo.addLine(75, 76, 88)
gmsh.model.geo.addLine(76, 77, 89)
gmsh.model.geo.addLine(77, 78, 90)
gmsh.model.geo.addLine(78, 79, 91)
gmsh.model.geo.addLine(79, 80, 92)
gmsh.model.geo.addLine(80, 81, 93)
gmsh.model.geo.addLine(81, 75, 94)
gmsh.model.geo.addLine(82, 83, 96)
gmsh.model.geo.addLine(83, 84, 97)
gmsh.model.geo.addLine(84, 85, 98)
gmsh.model.geo.addLine(85, 86, 99)
gmsh.model.geo.addLine(86, 82, 100)
gmsh.model.geo.addLine(87, 88, 102)
gmsh.model.geo.addLine(88, 89, 103)
gmsh.model.geo.addLine(89, 90, 104)
gmsh.model.geo.addLine(90, 91, 105)
gmsh.model.geo.addLine(91, 92, 106)
gmsh.model.geo.addLine(92, 87, 107)
gmsh.model.geo.addLine(93, 94, 109)
gmsh.model.geo.addLine(94, 95, 110)
gmsh.model.geo.addLine(95, 96, 111)
gmsh.model.geo.addLine(96, 97, 112)
gmsh.model.geo.addLine(97, 93, 113)
gmsh.model.geo.addLine(98, 99, 115)
gmsh.model.geo.addLine(99, 100, 116)
gmsh.model.geo.addLine(100, 101, 117)
gmsh.model.geo.addLine(101, 102, 118)
gmsh.model.geo.addLine(102, 103, 119)
gmsh.model.geo.addLine(103, 104, 120)
gmsh.model.geo.addLine(104, 105, 121)
gmsh.model.geo.addLine(105, 98, 122)
gmsh.model.geo.addLine(106, 107, 124)
gmsh.model.geo.addLine(107, 108, 125)
gmsh.model.geo.addLine(108, 109, 126)
gmsh.model.geo.addLine(109, 110, 127)
gmsh.model.geo.addLine(110, 106, 128)
gmsh.model.geo.addLine(111, 112, 130)
gmsh.model.geo.addLine(112, 113, 131)
gmsh.model.geo.addLine(113, 114, 132)
gmsh.model.geo.addLine(114, 115, 133)
gmsh.model.geo.addLine(115, 116, 134)
gmsh.model.geo.addLine(116, 117, 135)
gmsh.model.geo.addLine(117, 118, 136)
gmsh.model.geo.addLine(118, 119, 137)
gmsh.model.geo.addLine(119, 111, 138)
gmsh.model.geo.addLine(120, 121, 140)
gmsh.model.geo.addLine(121, 122, 141)
gmsh.model.geo.addLine(122, 123, 142)
gmsh.model.geo.addLine(123, 124, 143)
gmsh.model.geo.addLine(124, 125, 144)
gmsh.model.geo.addLine(125, 126, 145)
gmsh.model.geo.addLine(126, 120, 146)
gmsh.model.geo.addLine(127, 128, 148)
gmsh.model.geo.addLine(128, 129, 149)
gmsh.model.geo.addLine(129, 130, 150)
gmsh.model.geo.addLine(130, 131, 151)
gmsh.model.geo.addLine(131, 132, 152)
gmsh.model.geo.addLine(132, 133, 153)
gmsh.model.geo.addLine(133, 134, 154)
gmsh.model.geo.addLine(134, 135, 155)
gmsh.model.geo.addLine(135, 127, 156)
gmsh.model.geo.addLine(136, 137, 158)
gmsh.model.geo.addLine(137, 138, 159)
gmsh.model.geo.addLine(138, 139, 160)
gmsh.model.geo.addLine(139, 140, 161)
gmsh.model.geo.addLine(140, 136, 162)
gmsh.model.geo.addLine(141, 142, 164)
gmsh.model.geo.addLine(142, 143, 165)
gmsh.model.geo.addLine(143, 144, 166)
gmsh.model.geo.addLine(144, 145, 167)
gmsh.model.geo.addLine(145, 141, 168)
gmsh.model.geo.addLine(146, 147, 170)
gmsh.model.geo.addLine(147, 148, 171)
gmsh.model.geo.addLine(148, 149, 172)
gmsh.model.geo.addLine(149, 150, 173)
gmsh.model.geo.addLine(150, 146, 174)
gmsh.model.geo.addLine(151, 152, 176)
gmsh.model.geo.addLine(152, 153, 177)
gmsh.model.geo.addLine(153, 154, 178)
gmsh.model.geo.addLine(154, 155, 179)
gmsh.model.geo.addLine(155, 156, 180)
gmsh.model.geo.addLine(156, 157, 181)
gmsh.model.geo.addLine(157, 158, 182)
gmsh.model.geo.addLine(158, 159, 183)
gmsh.model.geo.addLine(159, 160, 184)
gmsh.model.geo.addLine(160, 151, 185)
gmsh.model.geo.addLine(161, 162, 187)
gmsh.model.geo.addLine(162, 163, 188)
gmsh.model.geo.addLine(163, 164, 189)
gmsh.model.geo.addLine(164, 165, 190)
gmsh.model.geo.addLine(165, 166, 191)
gmsh.model.geo.addLine(166, 167, 192)
gmsh.model.geo.addLine(167, 168, 193)
gmsh.model.geo.addLine(168, 169, 194)
gmsh.model.geo.addLine(169, 170, 195)
gmsh.model.geo.addLine(170, 171, 196)
gmsh.model.geo.addLine(171, 172, 197)
gmsh.model.geo.addLine(172, 161, 198)
gmsh.model.geo.addLine(173, 174, 200)
gmsh.model.geo.addLine(174, 175, 201)
gmsh.model.geo.addLine(175, 176, 202)
gmsh.model.geo.addLine(176, 177, 203)
gmsh.model.geo.addLine(177, 178, 204)
gmsh.model.geo.addLine(178, 179, 205)
gmsh.model.geo.addLine(179, 180, 206)
gmsh.model.geo.addLine(180, 173, 207)
gmsh.model.geo.addLine(181, 182, 209)
gmsh.model.geo.addLine(182, 183, 210)
gmsh.model.geo.addLine(183, 184, 211)
gmsh.model.geo.addLine(184, 185, 212)
gmsh.model.geo.addLine(185, 186, 213)
gmsh.model.geo.addLine(186, 187, 214)
gmsh.model.geo.addLine(187, 188, 215)
gmsh.model.geo.addLine(188, 189, 216)
gmsh.model.geo.addLine(189, 190, 217)
gmsh.model.geo.addLine(190, 191, 218)
gmsh.model.geo.addLine(191, 192, 219)
gmsh.model.geo.addLine(192, 181, 220)
gmsh.model.geo.addLine(193, 194, 222)
gmsh.model.geo.addLine(194, 195, 223)
gmsh.model.geo.addLine(195, 196, 224)
gmsh.model.geo.addLine(196, 197, 225)
gmsh.model.geo.addLine(197, 198, 226)
gmsh.model.geo.addLine(198, 199, 227)
gmsh.model.geo.addLine(199, 200, 228)
gmsh.model.geo.addLine(200, 201, 229)
gmsh.model.geo.addLine(201, 202, 230)
gmsh.model.geo.addLine(202, 203, 231)
gmsh.model.geo.addLine(203, 204, 232)
gmsh.model.geo.addLine(204, 205, 233)
gmsh.model.geo.addLine(205, 193, 234)
gmsh.model.geo.addLine(206, 207, 236)
gmsh.model.geo.addLine(207, 208, 237)
gmsh.model.geo.addLine(208, 209, 238)
gmsh.model.geo.addLine(209, 210, 239)
gmsh.model.geo.addLine(210, 211, 240)
gmsh.model.geo.addLine(211, 212, 241)
gmsh.model.geo.addLine(212, 206, 242)
gmsh.model.geo.addLine(213, 214, 244)
gmsh.model.geo.addLine(214, 215, 245)
gmsh.model.geo.addLine(215, 216, 246)
gmsh.model.geo.addLine(216, 217, 247)
gmsh.model.geo.addLine(217, 218, 248)
gmsh.model.geo.addLine(218, 219, 249)
gmsh.model.geo.addLine(219, 220, 250)
gmsh.model.geo.addLine(220, 221, 251)
gmsh.model.geo.addLine(221, 222, 252)
gmsh.model.geo.addLine(222, 223, 253)
gmsh.model.geo.addLine(223, 224, 254)
gmsh.model.geo.addLine(224, 213, 255)
gmsh.model.geo.addLine(225, 226, 257)
gmsh.model.geo.addLine(226, 227, 258)
gmsh.model.geo.addLine(227, 228, 259)
gmsh.model.geo.addLine(228, 229, 260)
gmsh.model.geo.addLine(229, 230, 261)
gmsh.model.geo.addLine(230, 231, 262)
gmsh.model.geo.addLine(231, 232, 263)
gmsh.model.geo.addLine(232, 233, 264)
gmsh.model.geo.addLine(233, 234, 265)
gmsh.model.geo.addLine(234, 235, 266)
gmsh.model.geo.addLine(235, 225, 267)
gmsh.model.geo.addLine(236, 237, 269)
gmsh.model.geo.addLine(237, 238, 270)
gmsh.model.geo.addLine(238, 239, 271)
gmsh.model.geo.addLine(239, 240, 272)
gmsh.model.geo.addLine(240, 241, 273)
gmsh.model.geo.addLine(241, 242, 274)
gmsh.model.geo.addLine(242, 236, 275)
gmsh.model.geo.addLine(243, 244, 277)
gmsh.model.geo.addLine(244, 245, 278)
gmsh.model.geo.addLine(245, 246, 279)
gmsh.model.geo.addLine(246, 247, 280)
gmsh.model.geo.addLine(247, 248, 281)
gmsh.model.geo.addLine(248, 249, 282)
gmsh.model.geo.addLine(249, 250, 283)
gmsh.model.geo.addLine(250, 251, 284)
gmsh.model.geo.addLine(251, 252, 285)
gmsh.model.geo.addLine(252, 253, 286)
gmsh.model.geo.addLine(253, 254, 287)
gmsh.model.geo.addLine(254, 255, 288)
gmsh.model.geo.addLine(255, 256, 289)
gmsh.model.geo.addLine(256, 257, 290)
gmsh.model.geo.addLine(257, 243, 291)
gmsh.model.geo.addLine(258, 259, 293)
gmsh.model.geo.addLine(259, 260, 294)
gmsh.model.geo.addLine(260, 261, 295)
gmsh.model.geo.addLine(261, 262, 296)
gmsh.model.geo.addLine(262, 263, 297)
gmsh.model.geo.addLine(263, 264, 298)
gmsh.model.geo.addLine(264, 265, 299)
gmsh.model.geo.addLine(265, 266, 300)
gmsh.model.geo.addLine(266, 267, 301)
gmsh.model.geo.addLine(267, 268, 302)
gmsh.model.geo.addLine(268, 269, 303)
gmsh.model.geo.addLine(269, 270, 304)
gmsh.model.geo.addLine(270, 271, 305)
gmsh.model.geo.addLine(271, 272, 306)
gmsh.model.geo.addLine(272, 273, 307)
gmsh.model.geo.addLine(273, 274, 308)
gmsh.model.geo.addLine(274, 275, 309)
gmsh.model.geo.addLine(275, 258, 310)
gmsh.model.geo.addLine(276, 277, 312)
gmsh.model.geo.addLine(277, 278, 313)
gmsh.model.geo.addLine(278, 279, 314)
gmsh.model.geo.addLine(279, 280, 315)
gmsh.model.geo.addLine(280, 281, 316)
gmsh.model.geo.addLine(281, 282, 317)
gmsh.model.geo.addLine(282, 283, 318)
gmsh.model.geo.addLine(283, 284, 319)
gmsh.model.geo.addLine(284, 285, 320)
gmsh.model.geo.addLine(285, 286, 321)
gmsh.model.geo.addLine(286, 287, 322)
gmsh.model.geo.addLine(287, 288, 323)
gmsh.model.geo.addLine(288, 289, 324)
gmsh.model.geo.addLine(289, 290, 325)
gmsh.model.geo.addLine(290, 291, 326)
gmsh.model.geo.addLine(291, 292, 327)
gmsh.model.geo.addLine(292, 293, 328)
gmsh.model.geo.addLine(293, 294, 329)
gmsh.model.geo.addLine(294, 276, 330)
gmsh.model.geo.addLine(295, 296, 332)
gmsh.model.geo.addLine(296, 297, 333)
gmsh.model.geo.addLine(297, 298, 334)
gmsh.model.geo.addLine(298, 295, 335)
gmsh.model.geo.addLine(299, 300, 337)
gmsh.model.geo.addLine(300, 301, 338)
gmsh.model.geo.addLine(301, 302, 339)
gmsh.model.geo.addLine(302, 303, 340)
gmsh.model.geo.addLine(303, 304, 341)
gmsh.model.geo.addLine(304, 305, 342)
gmsh.model.geo.addLine(305, 299, 343)
gmsh.model.geo.addLine(306, 307, 345)
gmsh.model.geo.addLine(307, 308, 346)
gmsh.model.geo.addLine(308, 309, 347)
gmsh.model.geo.addLine(309, 310, 348)
gmsh.model.geo.addLine(310, 311, 349)
gmsh.model.geo.addLine(311, 312, 350)
gmsh.model.geo.addLine(312, 313, 351)
gmsh.model.geo.addLine(313, 314, 352)
gmsh.model.geo.addLine(314, 315, 353)
gmsh.model.geo.addLine(315, 316, 354)
gmsh.model.geo.addLine(316, 317, 355)
gmsh.model.geo.addLine(317, 306, 356)
gmsh.model.geo.addLine(318, 319, 358)
gmsh.model.geo.addLine(319, 320, 359)
gmsh.model.geo.addLine(320, 321, 360)
gmsh.model.geo.addLine(321, 322, 361)
gmsh.model.geo.addLine(322, 323, 362)
gmsh.model.geo.addLine(323, 324, 363)
gmsh.model.geo.addLine(324, 325, 364)
gmsh.model.geo.addLine(325, 326, 365)
gmsh.model.geo.addLine(326, 327, 366)
gmsh.model.geo.addLine(327, 328, 367)
gmsh.model.geo.addLine(328, 329, 368)
gmsh.model.geo.addLine(329, 330, 369)
gmsh.model.geo.addLine(330, 331, 370)
gmsh.model.geo.addLine(331, 332, 371)
gmsh.model.geo.addLine(332, 318, 372)
gmsh.model.geo.addLine(333, 334, 374)
gmsh.model.geo.addLine(334, 335, 375)
gmsh.model.geo.addLine(335, 336, 376)
gmsh.model.geo.addLine(336, 337, 377)
gmsh.model.geo.addLine(337, 338, 378)
gmsh.model.geo.addLine(338, 339, 379)
gmsh.model.geo.addLine(339, 340, 380)
gmsh.model.geo.addLine(340, 341, 381)
gmsh.model.geo.addLine(341, 342, 382)
gmsh.model.geo.addLine(342, 343, 383)
gmsh.model.geo.addLine(343, 344, 384)
gmsh.model.geo.addLine(344, 345, 385)
gmsh.model.geo.addLine(345, 346, 386)
gmsh.model.geo.addLine(346, 347, 387)
gmsh.model.geo.addLine(347, 348, 388)
gmsh.model.geo.addLine(348, 349, 389)
gmsh.model.geo.addLine(349, 350, 390)
gmsh.model.geo.addLine(350, 351, 391)
gmsh.model.geo.addLine(351, 352, 392)
gmsh.model.geo.addLine(352, 353, 393)
gmsh.model.geo.addLine(353, 333, 394)
gmsh.model.geo.addLine(354, 355, 396)
gmsh.model.geo.addLine(355, 356, 397)
gmsh.model.geo.addLine(356, 357, 398)
gmsh.model.geo.addLine(357, 358, 399)
gmsh.model.geo.addLine(358, 359, 400)
gmsh.model.geo.addLine(359, 360, 401)
gmsh.model.geo.addLine(360, 361, 402)
gmsh.model.geo.addLine(361, 362, 403)
gmsh.model.geo.addLine(362, 363, 404)
gmsh.model.geo.addLine(363, 364, 405)
gmsh.model.geo.addLine(364, 365, 406)
gmsh.model.geo.addLine(365, 366, 407)
gmsh.model.geo.addLine(366, 367, 408)
gmsh.model.geo.addLine(367, 368, 409)
gmsh.model.geo.addLine(368, 369, 410)
gmsh.model.geo.addLine(369, 370, 411)
gmsh.model.geo.addLine(370, 371, 412)
gmsh.model.geo.addLine(371, 354, 413)
gmsh.model.geo.addLine(372, 373, 415)
gmsh.model.geo.addLine(373, 374, 416)
gmsh.model.geo.addLine(374, 375, 417)
gmsh.model.geo.addLine(375, 376, 418)
gmsh.model.geo.addLine(376, 377, 419)
gmsh.model.geo.addLine(377, 378, 420)
gmsh.model.geo.addLine(378, 379, 421)
gmsh.model.geo.addLine(379, 380, 422)
gmsh.model.geo.addLine(380, 381, 423)
gmsh.model.geo.addLine(381, 382, 424)
gmsh.model.geo.addLine(382, 383, 425)
gmsh.model.geo.addLine(383, 384, 426)
gmsh.model.geo.addLine(384, 385, 427)
gmsh.model.geo.addLine(385, 386, 428)
gmsh.model.geo.addLine(386, 387, 429)
gmsh.model.geo.addLine(387, 388, 430)
gmsh.model.geo.addLine(388, 389, 431)
gmsh.model.geo.addLine(389, 390, 432)
gmsh.model.geo.addLine(390, 391, 433)
gmsh.model.geo.addLine(391, 392, 434)
gmsh.model.geo.addLine(392, 393, 435)
gmsh.model.geo.addLine(393, 394, 436)
gmsh.model.geo.addLine(394, 395, 437)
gmsh.model.geo.addLine(395, 372, 438)
gmsh.model.geo.addLine(396, 397, 440)
gmsh.model.geo.addLine(397, 398, 441)
gmsh.model.geo.addLine(398, 399, 442)
gmsh.model.geo.addLine(399, 400, 443)
gmsh.model.geo.addLine(400, 401, 444)
gmsh.model.geo.addLine(401, 402, 445)
gmsh.model.geo.addLine(402, 403, 446)
gmsh.model.geo.addLine(403, 404, 447)
gmsh.model.geo.addLine(404, 405, 448)
gmsh.model.geo.addLine(405, 406, 449)
gmsh.model.geo.addLine(406, 407, 450)
gmsh.model.geo.addLine(407, 408, 451)
gmsh.model.geo.addLine(408, 409, 452)
gmsh.model.geo.addLine(409, 410, 453)
gmsh.model.geo.addLine(410, 411, 454)
gmsh.model.geo.addLine(411, 396, 455)
gmsh.model.geo.addLine(412, 413, 457)
gmsh.model.geo.addLine(413, 414, 458)
gmsh.model.geo.addLine(414, 415, 459)
gmsh.model.geo.addLine(415, 416, 460)
gmsh.model.geo.addLine(416, 417, 461)
gmsh.model.geo.addLine(417, 418, 462)
gmsh.model.geo.addLine(418, 419, 463)
gmsh.model.geo.addLine(419, 420, 464)
gmsh.model.geo.addLine(420, 421, 465)
gmsh.model.geo.addLine(421, 422, 466)
gmsh.model.geo.addLine(422, 423, 467)
gmsh.model.geo.addLine(423, 424, 468)
gmsh.model.geo.addLine(424, 425, 469)
gmsh.model.geo.addLine(425, 426, 470)
gmsh.model.geo.addLine(426, 427, 471)
gmsh.model.geo.addLine(427, 428, 472)
gmsh.model.geo.addLine(428, 429, 473)
gmsh.model.geo.addLine(429, 430, 474)
gmsh.model.geo.addLine(430, 431, 475)
gmsh.model.geo.addLine(431, 432, 476)
gmsh.model.geo.addLine(432, 433, 477)
gmsh.model.geo.addLine(433, 434, 478)
gmsh.model.geo.addLine(434, 435, 479)
gmsh.model.geo.addLine(435, 436, 480)
gmsh.model.geo.addLine(436, 437, 481)
gmsh.model.geo.addLine(437, 438, 482)
gmsh.model.geo.addLine(438, 439, 483)
gmsh.model.geo.addLine(439, 440, 484)
gmsh.model.geo.addLine(440, 441, 485)
gmsh.model.geo.addLine(441, 442, 486)
gmsh.model.geo.addLine(442, 443, 487)
gmsh.model.geo.addLine(443, 444, 488)
gmsh.model.geo.addLine(444, 445, 489)
gmsh.model.geo.addLine(445, 446, 490)
gmsh.model.geo.addLine(446, 447, 491)
gmsh.model.geo.addLine(447, 448, 492)
gmsh.model.geo.addLine(448, 449, 493)
gmsh.model.geo.addLine(449, 450, 494)
gmsh.model.geo.addLine(450, 412, 495)
gmsh.model.geo.addLine(451, 452, 497)
gmsh.model.geo.addLine(452, 453, 498)
gmsh.model.geo.addLine(453, 454, 499)
gmsh.model.geo.addLine(454, 455, 500)
gmsh.model.geo.addLine(455, 456, 501)
gmsh.model.geo.addLine(456, 457, 502)
gmsh.model.geo.addLine(457, 458, 503)
gmsh.model.geo.addLine(458, 451, 504)
gmsh.model.geo.addLine(459, 460, 506)
gmsh.model.geo.addLine(460, 461, 507)
gmsh.model.geo.addLine(461, 462, 508)
gmsh.model.geo.addLine(462, 463, 509)
gmsh.model.geo.addLine(463, 464, 510)
gmsh.model.geo.addLine(464, 465, 511)
gmsh.model.geo.addLine(465, 466, 512)
gmsh.model.geo.addLine(466, 467, 513)
gmsh.model.geo.addLine(467, 468, 514)
gmsh.model.geo.addLine(468, 469, 515)
gmsh.model.geo.addLine(469, 470, 516)
gmsh.model.geo.addLine(470, 471, 517)
gmsh.model.geo.addLine(471, 472, 518)
gmsh.model.geo.addLine(472, 473, 519)
gmsh.model.geo.addLine(473, 474, 520)
gmsh.model.geo.addLine(474, 475, 521)
gmsh.model.geo.addLine(475, 476, 522)
gmsh.model.geo.addLine(476, 477, 523)
gmsh.model.geo.addLine(477, 478, 524)
gmsh.model.geo.addLine(478, 479, 525)
gmsh.model.geo.addLine(479, 480, 526)
gmsh.model.geo.addLine(480, 481, 527)
gmsh.model.geo.addLine(481, 482, 528)
gmsh.model.geo.addLine(482, 483, 529)
gmsh.model.geo.addLine(483, 484, 530)
gmsh.model.geo.addLine(484, 485, 531)
gmsh.model.geo.addLine(485, 459, 532)
gmsh.model.geo.addLine(486, 487, 534)
gmsh.model.geo.addLine(487, 488, 535)
gmsh.model.geo.addLine(488, 489, 536)
gmsh.model.geo.addLine(489, 490, 537)
gmsh.model.geo.addLine(490, 491, 538)
gmsh.model.geo.addLine(491, 492, 539)
gmsh.model.geo.addLine(492, 493, 540)
gmsh.model.geo.addLine(493, 494, 541)
gmsh.model.geo.addLine(494, 495, 542)
gmsh.model.geo.addLine(495, 496, 543)
gmsh.model.geo.addLine(496, 497, 544)
gmsh.model.geo.addLine(497, 498, 545)
gmsh.model.geo.addLine(498, 499, 546)
gmsh.model.geo.addLine(499, 500, 547)
gmsh.model.geo.addLine(500, 501, 548)
gmsh.model.geo.addLine(501, 502, 549)
gmsh.model.geo.addLine(502, 503, 550)
gmsh.model.geo.addLine(503, 504, 551)
gmsh.model.geo.addLine(504, 505, 552)
gmsh.model.geo.addLine(505, 506, 553)
gmsh.model.geo.addLine(506, 507, 554)
gmsh.model.geo.addLine(507, 508, 555)
gmsh.model.geo.addLine(508, 509, 556)
gmsh.model.geo.addLine(509, 510, 557)
gmsh.model.geo.addLine(510, 511, 558)
gmsh.model.geo.addLine(511, 512, 559)
gmsh.model.geo.addLine(512, 513, 560)
gmsh.model.geo.addLine(513, 514, 561)
gmsh.model.geo.addLine(514, 486, 562)
gmsh.model.geo.addLine(515, 516, 564)
gmsh.model.geo.addLine(516, 517, 565)
gmsh.model.geo.addLine(517, 518, 566)
gmsh.model.geo.addLine(518, 519, 567)
gmsh.model.geo.addLine(519, 520, 568)
gmsh.model.geo.addLine(520, 521, 569)
gmsh.model.geo.addLine(521, 522, 570)
gmsh.model.geo.addLine(522, 523, 571)
gmsh.model.geo.addLine(523, 524, 572)
gmsh.model.geo.addLine(524, 525, 573)
gmsh.model.geo.addLine(525, 526, 574)
gmsh.model.geo.addLine(526, 527, 575)
gmsh.model.geo.addLine(527, 528, 576)
gmsh.model.geo.addLine(528, 529, 577)
gmsh.model.geo.addLine(529, 530, 578)
gmsh.model.geo.addLine(530, 531, 579)
gmsh.model.geo.addLine(531, 532, 580)
gmsh.model.geo.addLine(532, 533, 581)
gmsh.model.geo.addLine(533, 534, 582)
gmsh.model.geo.addLine(534, 535, 583)
gmsh.model.geo.addLine(535, 536, 584)
gmsh.model.geo.addLine(536, 537, 585)
gmsh.model.geo.addLine(537, 538, 586)
gmsh.model.geo.addLine(538, 539, 587)
gmsh.model.geo.addLine(539, 540, 588)
gmsh.model.geo.addLine(540, 541, 589)
gmsh.model.geo.addLine(541, 542, 590)
gmsh.model.geo.addLine(542, 543, 591)
gmsh.model.geo.addLine(543, 544, 592)
gmsh.model.geo.addLine(544, 545, 593)
gmsh.model.geo.addLine(545, 515, 594)
gmsh.model.geo.addLine(546, 547, 596)
gmsh.model.geo.addLine(547, 548, 597)
gmsh.model.geo.addLine(548, 549, 598)
gmsh.model.geo.addLine(549, 550, 599)
gmsh.model.geo.addLine(550, 551, 600)
gmsh.model.geo.addLine(551, 552, 601)
gmsh.model.geo.addLine(552, 553, 602)
gmsh.model.geo.addLine(553, 554, 603)
gmsh.model.geo.addLine(554, 555, 604)
gmsh.model.geo.addLine(555, 556, 605)
gmsh.model.geo.addLine(556, 557, 606)
gmsh.model.geo.addLine(557, 546, 607)
gmsh.model.geo.addLine(558, 559, 609)
gmsh.model.geo.addLine(559, 560, 610)
gmsh.model.geo.addLine(560, 561, 611)
gmsh.model.geo.addLine(561, 562, 612)
gmsh.model.geo.addLine(562, 563, 613)
gmsh.model.geo.addLine(563, 564, 614)
gmsh.model.geo.addLine(564, 565, 615)
gmsh.model.geo.addLine(565, 566, 616)
gmsh.model.geo.addLine(566, 567, 617)
gmsh.model.geo.addLine(567, 568, 618)
gmsh.model.geo.addLine(568, 569, 619)
gmsh.model.geo.addLine(569, 570, 620)
gmsh.model.geo.addLine(570, 571, 621)
gmsh.model.geo.addLine(571, 572, 622)
gmsh.model.geo.addLine(572, 573, 623)
gmsh.model.geo.addLine(573, 574, 624)
gmsh.model.geo.addLine(574, 575, 625)
gmsh.model.geo.addLine(575, 576, 626)
gmsh.model.geo.addLine(576, 577, 627)
gmsh.model.geo.addLine(577, 578, 628)
gmsh.model.geo.addLine(578, 579, 629)
gmsh.model.geo.addLine(579, 580, 630)
gmsh.model.geo.addLine(580, 581, 631)
gmsh.model.geo.addLine(581, 582, 632)
gmsh.model.geo.addLine(582, 583, 633)
gmsh.model.geo.addLine(583, 584, 634)
gmsh.model.geo.addLine(584, 585, 635)
gmsh.model.geo.addLine(585, 586, 636)
gmsh.model.geo.addLine(586, 587, 637)
gmsh.model.geo.addLine(587, 588, 638)
gmsh.model.geo.addLine(588, 589, 639)
gmsh.model.geo.addLine(589, 590, 640)
gmsh.model.geo.addLine(590, 591, 641)
gmsh.model.geo.addLine(591, 592, 642)
gmsh.model.geo.addLine(592, 593, 643)
gmsh.model.geo.addLine(593, 594, 644)
gmsh.model.geo.addLine(594, 595, 645)
gmsh.model.geo.addLine(595, 596, 646)
gmsh.model.geo.addLine(596, 597, 647)
gmsh.model.geo.addLine(597, 598, 648)
gmsh.model.geo.addLine(598, 599, 649)
gmsh.model.geo.addLine(599, 600, 650)
gmsh.model.geo.addLine(600, 601, 651)
gmsh.model.geo.addLine(601, 602, 652)
gmsh.model.geo.addLine(602, 603, 653)
gmsh.model.geo.addLine(603, 604, 654)
gmsh.model.geo.addLine(604, 605, 655)
gmsh.model.geo.addLine(605, 606, 656)
gmsh.model.geo.addLine(606, 607, 657)
gmsh.model.geo.addLine(607, 608, 658)
gmsh.model.geo.addLine(608, 609, 659)
gmsh.model.geo.addLine(609, 610, 660)
gmsh.model.geo.addLine(610, 611, 661)
gmsh.model.geo.addLine(611, 612, 662)
gmsh.model.geo.addLine(612, 613, 663)
gmsh.model.geo.addLine(613, 614, 664)
gmsh.model.geo.addLine(614, 615, 665)
gmsh.model.geo.addLine(615, 616, 666)
gmsh.model.geo.addLine(616, 617, 667)
gmsh.model.geo.addLine(617, 618, 668)
gmsh.model.geo.addLine(618, 619, 669)
gmsh.model.geo.addLine(619, 620, 670)
gmsh.model.geo.addLine(620, 621, 671)
gmsh.model.geo.addLine(621, 622, 672)
gmsh.model.geo.addLine(622, 623, 673)
gmsh.model.geo.addLine(623, 624, 674)
gmsh.model.geo.addLine(624, 625, 675)
gmsh.model.geo.addLine(625, 626, 676)
gmsh.model.geo.addLine(626, 627, 677)
gmsh.model.geo.addLine(627, 558, 678)
gmsh.model.geo.addLine(628, 629, 680)
gmsh.model.geo.addLine(629, 630, 681)
gmsh.model.geo.addLine(630, 631, 682)
gmsh.model.geo.addLine(631, 632, 683)
gmsh.model.geo.addLine(632, 633, 684)
gmsh.model.geo.addLine(633, 634, 685)
gmsh.model.geo.addLine(634, 635, 686)
gmsh.model.geo.addLine(635, 628, 687)
gmsh.model.geo.addLine(636, 637, 689)
gmsh.model.geo.addLine(637, 638, 690)
gmsh.model.geo.addLine(638, 639, 691)
gmsh.model.geo.addLine(639, 640, 692)
gmsh.model.geo.addLine(640, 641, 693)
gmsh.model.geo.addLine(641, 642, 694)
gmsh.model.geo.addLine(642, 643, 695)
gmsh.model.geo.addLine(643, 644, 696)
gmsh.model.geo.addLine(644, 645, 697)
gmsh.model.geo.addLine(645, 646, 698)
gmsh.model.geo.addLine(646, 647, 699)
gmsh.model.geo.addLine(647, 648, 700)
gmsh.model.geo.addLine(648, 649, 701)
gmsh.model.geo.addLine(649, 650, 702)
gmsh.model.geo.addLine(650, 651, 703)
gmsh.model.geo.addLine(651, 652, 704)
gmsh.model.geo.addLine(652, 653, 705)
gmsh.model.geo.addLine(653, 654, 706)
gmsh.model.geo.addLine(654, 655, 707)
gmsh.model.geo.addLine(655, 656, 708)
gmsh.model.geo.addLine(656, 657, 709)
gmsh.model.geo.addLine(657, 658, 710)
gmsh.model.geo.addLine(658, 659, 711)
gmsh.model.geo.addLine(659, 660, 712)
gmsh.model.geo.addLine(660, 661, 713)
gmsh.model.geo.addLine(661, 662, 714)
gmsh.model.geo.addLine(662, 663, 715)
gmsh.model.geo.addLine(663, 636, 716)
gmsh.model.geo.addLine(664, 665, 718)
gmsh.model.geo.addLine(665, 666, 719)
gmsh.model.geo.addLine(666, 667, 720)
gmsh.model.geo.addLine(667, 668, 721)
gmsh.model.geo.addLine(668, 669, 722)
gmsh.model.geo.addLine(669, 670, 723)
gmsh.model.geo.addLine(670, 671, 724)
gmsh.model.geo.addLine(671, 672, 725)
gmsh.model.geo.addLine(672, 673, 726)
gmsh.model.geo.addLine(673, 674, 727)
gmsh.model.geo.addLine(674, 675, 728)
gmsh.model.geo.addLine(675, 676, 729)
gmsh.model.geo.addLine(676, 677, 730)
gmsh.model.geo.addLine(677, 678, 731)
gmsh.model.geo.addLine(678, 664, 732)
gmsh.model.geo.addLine(679, 680, 734)
gmsh.model.geo.addLine(680, 681, 735)
gmsh.model.geo.addLine(681, 682, 736)
gmsh.model.geo.addLine(682, 683, 737)
gmsh.model.geo.addLine(683, 684, 738)
gmsh.model.geo.addLine(684, 685, 739)
gmsh.model.geo.addLine(685, 686, 740)
gmsh.model.geo.addLine(686, 687, 741)
gmsh.model.geo.addLine(687, 688, 742)
gmsh.model.geo.addLine(688, 689, 743)
gmsh.model.geo.addLine(689, 690, 744)
gmsh.model.geo.addLine(690, 691, 745)
gmsh.model.geo.addLine(691, 692, 746)
gmsh.model.geo.addLine(692, 693, 747)
gmsh.model.geo.addLine(693, 694, 748)
gmsh.model.geo.addLine(694, 695, 749)
gmsh.model.geo.addLine(695, 696, 750)
gmsh.model.geo.addLine(696, 697, 751)
gmsh.model.geo.addLine(697, 698, 752)
gmsh.model.geo.addLine(698, 699, 753)
gmsh.model.geo.addLine(699, 700, 754)
gmsh.model.geo.addLine(700, 701, 755)
gmsh.model.geo.addLine(701, 702, 756)
gmsh.model.geo.addLine(702, 703, 757)
gmsh.model.geo.addLine(703, 704, 758)
gmsh.model.geo.addLine(704, 705, 759)
gmsh.model.geo.addLine(705, 706, 760)
gmsh.model.geo.addLine(706, 707, 761)
gmsh.model.geo.addLine(707, 708, 762)
gmsh.model.geo.addLine(708, 709, 763)
gmsh.model.geo.addLine(709, 710, 764)
gmsh.model.geo.addLine(710, 711, 765)
gmsh.model.geo.addLine(711, 712, 766)
gmsh.model.geo.addLine(712, 713, 767)
gmsh.model.geo.addLine(713, 714, 768)
gmsh.model.geo.addLine(714, 715, 769)
gmsh.model.geo.addLine(715, 716, 770)
gmsh.model.geo.addLine(716, 717, 771)
gmsh.model.geo.addLine(717, 718, 772)
gmsh.model.geo.addLine(718, 719, 773)
gmsh.model.geo.addLine(719, 720, 774)
gmsh.model.geo.addLine(720, 721, 775)
gmsh.model.geo.addLine(721, 722, 776)
gmsh.model.geo.addLine(722, 723, 777)
gmsh.model.geo.addLine(723, 724, 778)
gmsh.model.geo.addLine(724, 725, 779)
gmsh.model.geo.addLine(725, 679, 780)
gmsh.model.geo.addLine(726, 727, 782)
gmsh.model.geo.addLine(727, 728, 783)
gmsh.model.geo.addLine(728, 729, 784)
gmsh.model.geo.addLine(729, 730, 785)
gmsh.model.geo.addLine(730, 731, 786)
gmsh.model.geo.addLine(731, 732, 787)
gmsh.model.geo.addLine(732, 733, 788)
gmsh.model.geo.addLine(733, 734, 789)
gmsh.model.geo.addLine(734, 735, 790)
gmsh.model.geo.addLine(735, 736, 791)
gmsh.model.geo.addLine(736, 737, 792)
gmsh.model.geo.addLine(737, 738, 793)
gmsh.model.geo.addLine(738, 739, 794)
gmsh.model.geo.addLine(739, 740, 795)
gmsh.model.geo.addLine(740, 741, 796)
gmsh.model.geo.addLine(741, 742, 797)
gmsh.model.geo.addLine(742, 743, 798)
gmsh.model.geo.addLine(743, 744, 799)
gmsh.model.geo.addLine(744, 745, 800)
gmsh.model.geo.addLine(745, 746, 801)
gmsh.model.geo.addLine(746, 747, 802)
gmsh.model.geo.addLine(747, 748, 803)
gmsh.model.geo.addLine(748, 749, 804)
gmsh.model.geo.addLine(749, 750, 805)
gmsh.model.geo.addLine(750, 751, 806)
gmsh.model.geo.addLine(751, 752, 807)
gmsh.model.geo.addLine(752, 753, 808)
gmsh.model.geo.addLine(753, 754, 809)
gmsh.model.geo.addLine(754, 755, 810)
gmsh.model.geo.addLine(755, 756, 811)
gmsh.model.geo.addLine(756, 757, 812)
gmsh.model.geo.addLine(757, 758, 813)
gmsh.model.geo.addLine(758, 759, 814)
gmsh.model.geo.addLine(759, 760, 815)
gmsh.model.geo.addLine(760, 761, 816)
gmsh.model.geo.addLine(761, 762, 817)
gmsh.model.geo.addLine(762, 763, 818)
gmsh.model.geo.addLine(763, 764, 819)
gmsh.model.geo.addLine(764, 765, 820)
gmsh.model.geo.addLine(765, 766, 821)
gmsh.model.geo.addLine(766, 767, 822)
gmsh.model.geo.addLine(767, 768, 823)
gmsh.model.geo.addLine(768, 769, 824)
gmsh.model.geo.addLine(769, 770, 825)
gmsh.model.geo.addLine(770, 771, 826)
gmsh.model.geo.addLine(771, 772, 827)
gmsh.model.geo.addLine(772, 773, 828)
gmsh.model.geo.addLine(773, 774, 829)
gmsh.model.geo.addLine(774, 775, 830)
gmsh.model.geo.addLine(775, 776, 831)
gmsh.model.geo.addLine(776, 777, 832)
gmsh.model.geo.addLine(777, 778, 833)
gmsh.model.geo.addLine(778, 779, 834)
gmsh.model.geo.addLine(779, 780, 835)
gmsh.model.geo.addLine(780, 781, 836)
gmsh.model.geo.addLine(781, 782, 837)
gmsh.model.geo.addLine(782, 783, 838)
gmsh.model.geo.addLine(783, 784, 839)
gmsh.model.geo.addLine(784, 785, 840)
gmsh.model.geo.addLine(785, 786, 841)
gmsh.model.geo.addLine(786, 787, 842)
gmsh.model.geo.addLine(787, 788, 843)
gmsh.model.geo.addLine(788, 789, 844)
gmsh.model.geo.addLine(789, 790, 845)
gmsh.model.geo.addLine(790, 791, 846)
gmsh.model.geo.addLine(791, 792, 847)
gmsh.model.geo.addLine(792, 793, 848)
gmsh.model.geo.addLine(793, 794, 849)
gmsh.model.geo.addLine(794, 795, 850)
gmsh.model.geo.addLine(795, 796, 851)
gmsh.model.geo.addLine(796, 797, 852)
gmsh.model.geo.addLine(797, 798, 853)
gmsh.model.geo.addLine(798, 799, 854)
gmsh.model.geo.addLine(799, 800, 855)
gmsh.model.geo.addLine(800, 801, 856)
gmsh.model.geo.addLine(801, 802, 857)
gmsh.model.geo.addLine(802, 803, 858)
gmsh.model.geo.addLine(803, 804, 859)
gmsh.model.geo.addLine(804, 805, 860)
gmsh.model.geo.addLine(805, 806, 861)
gmsh.model.geo.addLine(806, 807, 862)
gmsh.model.geo.addLine(807, 808, 863)
gmsh.model.geo.addLine(808, 809, 864)
gmsh.model.geo.addLine(809, 810, 865)
gmsh.model.geo.addLine(810, 811, 866)
gmsh.model.geo.addLine(811, 812, 867)
gmsh.model.geo.addLine(812, 813, 868)
gmsh.model.geo.addLine(813, 814, 869)
gmsh.model.geo.addLine(814, 815, 870)
gmsh.model.geo.addLine(815, 816, 871)
gmsh.model.geo.addLine(816, 817, 872)
gmsh.model.geo.addLine(817, 818, 873)
gmsh.model.geo.addLine(818, 819, 874)
gmsh.model.geo.addLine(819, 820, 875)
gmsh.model.geo.addLine(820, 821, 876)
gmsh.model.geo.addLine(821, 822, 877)
gmsh.model.geo.addLine(822, 823, 878)
gmsh.model.geo.addLine(823, 824, 879)
gmsh.model.geo.addLine(824, 825, 880)
gmsh.model.geo.addLine(825, 826, 881)
gmsh.model.geo.addLine(826, 827, 882)
gmsh.model.geo.addLine(827, 828, 883)
gmsh.model.geo.addLine(828, 829, 884)
gmsh.model.geo.addLine(829, 830, 885)
gmsh.model.geo.addLine(830, 831, 886)
gmsh.model.geo.addLine(831, 832, 887)
gmsh.model.geo.addLine(832, 833, 888)
gmsh.model.geo.addLine(833, 834, 889)
gmsh.model.geo.addLine(834, 835, 890)
gmsh.model.geo.addLine(835, 726, 891)
gmsh.model.geo.addLine(836, 837, 893)
gmsh.model.geo.addLine(837, 838, 894)
gmsh.model.geo.addLine(838, 839, 895)
gmsh.model.geo.addLine(839, 840, 896)
gmsh.model.geo.addLine(840, 841, 897)
gmsh.model.geo.addLine(841, 842, 898)
gmsh.model.geo.addLine(842, 843, 899)
gmsh.model.geo.addLine(843, 844, 900)
gmsh.model.geo.addLine(844, 845, 901)
gmsh.model.geo.addLine(845, 846, 902)
gmsh.model.geo.addLine(846, 847, 903)
gmsh.model.geo.addLine(847, 848, 904)
gmsh.model.geo.addLine(848, 849, 905)
gmsh.model.geo.addLine(849, 850, 906)
gmsh.model.geo.addLine(850, 851, 907)
gmsh.model.geo.addLine(851, 852, 908)
gmsh.model.geo.addLine(852, 853, 909)
gmsh.model.geo.addLine(853, 854, 910)
gmsh.model.geo.addLine(854, 855, 911)
gmsh.model.geo.addLine(855, 856, 912)
gmsh.model.geo.addLine(856, 857, 913)
gmsh.model.geo.addLine(857, 858, 914)
gmsh.model.geo.addLine(858, 859, 915)
gmsh.model.geo.addLine(859, 860, 916)
gmsh.model.geo.addLine(860, 861, 917)
gmsh.model.geo.addLine(861, 862, 918)
gmsh.model.geo.addLine(862, 863, 919)
gmsh.model.geo.addLine(863, 864, 920)
gmsh.model.geo.addLine(864, 865, 921)
gmsh.model.geo.addLine(865, 866, 922)
gmsh.model.geo.addLine(866, 867, 923)
gmsh.model.geo.addLine(867, 868, 924)
gmsh.model.geo.addLine(868, 869, 925)
gmsh.model.geo.addLine(869, 870, 926)
gmsh.model.geo.addLine(870, 871, 927)
gmsh.model.geo.addLine(871, 872, 928)
gmsh.model.geo.addLine(872, 873, 929)
gmsh.model.geo.addLine(873, 874, 930)
gmsh.model.geo.addLine(874, 875, 931)
gmsh.model.geo.addLine(875, 876, 932)
gmsh.model.geo.addLine(876, 877, 933)
gmsh.model.geo.addLine(877, 878, 934)
gmsh.model.geo.addLine(878, 879, 935)
gmsh.model.geo.addLine(879, 880, 936)
gmsh.model.geo.addLine(880, 881, 937)
gmsh.model.geo.addLine(881, 882, 938)
gmsh.model.geo.addLine(882, 883, 939)
gmsh.model.geo.addLine(883, 884, 940)
gmsh.model.geo.addLine(884, 885, 941)
gmsh.model.geo.addLine(885, 886, 942)
gmsh.model.geo.addLine(886, 887, 943)
gmsh.model.geo.addLine(887, 888, 944)
gmsh.model.geo.addLine(888, 889, 945)
gmsh.model.geo.addLine(889, 890, 946)
gmsh.model.geo.addLine(890, 891, 947)
gmsh.model.geo.addLine(891, 892, 948)
gmsh.model.geo.addLine(892, 893, 949)
gmsh.model.geo.addLine(893, 894, 950)
gmsh.model.geo.addLine(894, 895, 951)
gmsh.model.geo.addLine(895, 896, 952)
gmsh.model.geo.addLine(896, 897, 953)
gmsh.model.geo.addLine(897, 898, 954)
gmsh.model.geo.addLine(898, 899, 955)
gmsh.model.geo.addLine(899, 900, 956)
gmsh.model.geo.addLine(900, 901, 957)
gmsh.model.geo.addLine(901, 902, 958)
gmsh.model.geo.addLine(902, 903, 959)
gmsh.model.geo.addLine(903, 904, 960)
gmsh.model.geo.addLine(904, 905, 961)
gmsh.model.geo.addLine(905, 906, 962)
gmsh.model.geo.addLine(906, 907, 963)
gmsh.model.geo.addLine(907, 908, 964)
gmsh.model.geo.addLine(908, 909, 965)
gmsh.model.geo.addLine(909, 910, 966)
gmsh.model.geo.addLine(910, 911, 967)
gmsh.model.geo.addLine(911, 912, 968)
gmsh.model.geo.addLine(912, 913, 969)
gmsh.model.geo.addLine(913, 914, 970)
gmsh.model.geo.addLine(914, 915, 971)
gmsh.model.geo.addLine(915, 916, 972)
gmsh.model.geo.addLine(916, 917, 973)
gmsh.model.geo.addLine(917, 918, 974)
gmsh.model.geo.addLine(918, 919, 975)
gmsh.model.geo.addLine(919, 920, 976)
gmsh.model.geo.addLine(920, 921, 977)
gmsh.model.geo.addLine(921, 922, 978)
gmsh.model.geo.addLine(922, 923, 979)
gmsh.model.geo.addLine(923, 924, 980)
gmsh.model.geo.addLine(924, 925, 981)
gmsh.model.geo.addLine(925, 926, 982)
gmsh.model.geo.addLine(926, 927, 983)
gmsh.model.geo.addLine(927, 928, 984)
gmsh.model.geo.addLine(928, 929, 985)
gmsh.model.geo.addLine(929, 930, 986)
gmsh.model.geo.addLine(930, 931, 987)
gmsh.model.geo.addLine(931, 932, 988)
gmsh.model.geo.addLine(932, 933, 989)
gmsh.model.geo.addLine(933, 934, 990)
gmsh.model.geo.addLine(934, 935, 991)
gmsh.model.geo.addLine(935, 936, 992)
gmsh.model.geo.addLine(936, 937, 993)
gmsh.model.geo.addLine(937, 938, 994)
gmsh.model.geo.addLine(938, 939, 995)
gmsh.model.geo.addLine(939, 940, 996)
gmsh.model.geo.addLine(940, 941, 997)
gmsh.model.geo.addLine(941, 942, 998)
gmsh.model.geo.addLine(942, 943, 999)
gmsh.model.geo.addLine(943, 944, 1000)
gmsh.model.geo.addLine(944, 945, 1001)
gmsh.model.geo.addLine(945, 946, 1002)
gmsh.model.geo.addLine(946, 947, 1003)
gmsh.model.geo.addLine(947, 948, 1004)
gmsh.model.geo.addLine(948, 949, 1005)
gmsh.model.geo.addLine(949, 950, 1006)
gmsh.model.geo.addLine(950, 951, 1007)
gmsh.model.geo.addLine(951, 952, 1008)
gmsh.model.geo.addLine(952, 953, 1009)
gmsh.model.geo.addLine(953, 954, 1010)
gmsh.model.geo.addLine(954, 955, 1011)
gmsh.model.geo.addLine(955, 956, 1012)
gmsh.model.geo.addLine(956, 957, 1013)
gmsh.model.geo.addLine(957, 958, 1014)
gmsh.model.geo.addLine(958, 959, 1015)
gmsh.model.geo.addLine(959, 960, 1016)
gmsh.model.geo.addLine(960, 961, 1017)
gmsh.model.geo.addLine(961, 962, 1018)
gmsh.model.geo.addLine(962, 963, 1019)
gmsh.model.geo.addLine(963, 964, 1020)
gmsh.model.geo.addLine(964, 965, 1021)
gmsh.model.geo.addLine(965, 966, 1022)
gmsh.model.geo.addLine(966, 967, 1023)
gmsh.model.geo.addLine(967, 968, 1024)
gmsh.model.geo.addLine(968, 969, 1025)
gmsh.model.geo.addLine(969, 970, 1026)
gmsh.model.geo.addLine(970, 971, 1027)
gmsh.model.geo.addLine(971, 972, 1028)
gmsh.model.geo.addLine(972, 973, 1029)
gmsh.model.geo.addLine(973, 974, 1030)
gmsh.model.geo.addLine(974, 975, 1031)
gmsh.model.geo.addLine(975, 976, 1032)
gmsh.model.geo.addLine(976, 977, 1033)
gmsh.model.geo.addLine(977, 978, 1034)
gmsh.model.geo.addLine(978, 979, 1035)
gmsh.model.geo.addLine(979, 980, 1036)
gmsh.model.geo.addLine(980, 981, 1037)
gmsh.model.geo.addLine(981, 982, 1038)
gmsh.model.geo.addLine(982, 983, 1039)
gmsh.model.geo.addLine(983, 984, 1040)
gmsh.model.geo.addLine(984, 985, 1041)
gmsh.model.geo.addLine(985, 986, 1042)
gmsh.model.geo.addLine(986, 987, 1043)
gmsh.model.geo.addLine(987, 988, 1044)
gmsh.model.geo.addLine(988, 989, 1045)
gmsh.model.geo.addLine(989, 990, 1046)
gmsh.model.geo.addLine(990, 991, 1047)
gmsh.model.geo.addLine(991, 992, 1048)
gmsh.model.geo.addLine(992, 993, 1049)
gmsh.model.geo.addLine(993, 994, 1050)
gmsh.model.geo.addLine(994, 995, 1051)
gmsh.model.geo.addLine(995, 996, 1052)
gmsh.model.geo.addLine(996, 997, 1053)
gmsh.model.geo.addLine(997, 998, 1054)
gmsh.model.geo.addLine(998, 999, 1055)
gmsh.model.geo.addLine(999, 1000, 1056)
gmsh.model.geo.addLine(1000, 1001, 1057)
gmsh.model.geo.addLine(1001, 1002, 1058)
gmsh.model.geo.addLine(1002, 1003, 1059)
gmsh.model.geo.addLine(1003, 1004, 1060)
gmsh.model.geo.addLine(1004, 1005, 1061)
gmsh.model.geo.addLine(1005, 1006, 1062)
gmsh.model.geo.addLine(1006, 1007, 1063)
gmsh.model.geo.addLine(1007, 1008, 1064)
gmsh.model.geo.addLine(1008, 1009, 1065)
gmsh.model.geo.addLine(1009, 1010, 1066)
gmsh.model.geo.addLine(1010, 1011, 1067)
gmsh.model.geo.addLine(1011, 1012, 1068)
gmsh.model.geo.addLine(1012, 1013, 1069)
gmsh.model.geo.addLine(1013, 1014, 1070)
gmsh.model.geo.addLine(1014, 1015, 1071)
gmsh.model.geo.addLine(1015, 1016, 1072)
gmsh.model.geo.addLine(1016, 1017, 1073)
gmsh.model.geo.addLine(1017, 1018, 1074)
gmsh.model.geo.addLine(1018, 1019, 1075)
gmsh.model.geo.addLine(1019, 1020, 1076)
gmsh.model.geo.addLine(1020, 1021, 1077)
gmsh.model.geo.addLine(1021, 1022, 1078)
gmsh.model.geo.addLine(1022, 1023, 1079)
gmsh.model.geo.addLine(1023, 1024, 1080)
gmsh.model.geo.addLine(1024, 1025, 1081)
gmsh.model.geo.addLine(1025, 1026, 1082)
gmsh.model.geo.addLine(1026, 1027, 1083)
gmsh.model.geo.addLine(1027, 1028, 1084)
gmsh.model.geo.addLine(1028, 1029, 1085)
gmsh.model.geo.addLine(1029, 1030, 1086)
gmsh.model.geo.addLine(1030, 1031, 1087)
gmsh.model.geo.addLine(1031, 1032, 1088)
gmsh.model.geo.addLine(1032, 1033, 1089)
gmsh.model.geo.addLine(1033, 1034, 1090)
gmsh.model.geo.addLine(1034, 1035, 1091)
gmsh.model.geo.addLine(1035, 1036, 1092)
gmsh.model.geo.addLine(1036, 1037, 1093)
gmsh.model.geo.addLine(1037, 1038, 1094)
gmsh.model.geo.addLine(1038, 1039, 1095)
gmsh.model.geo.addLine(1039, 1040, 1096)
gmsh.model.geo.addLine(1040, 1041, 1097)
gmsh.model.geo.addLine(1041, 1042, 1098)
gmsh.model.geo.addLine(1042, 1043, 1099)
gmsh.model.geo.addLine(1043, 1044, 1100)
gmsh.model.geo.addLine(1044, 1045, 1101)
gmsh.model.geo.addLine(1045, 1046, 1102)
gmsh.model.geo.addLine(1046, 1047, 1103)
gmsh.model.geo.addLine(1047, 1048, 1104)
gmsh.model.geo.addLine(1048, 1049, 1105)
gmsh.model.geo.addLine(1049, 1050, 1106)
gmsh.model.geo.addLine(1050, 1051, 1107)
gmsh.model.geo.addLine(1051, 1052, 1108)
gmsh.model.geo.addLine(1052, 1053, 1109)
gmsh.model.geo.addLine(1053, 1054, 1110)
gmsh.model.geo.addLine(1054, 1055, 1111)
gmsh.model.geo.addLine(1055, 1056, 1112)
gmsh.model.geo.addLine(1056, 1057, 1113)
gmsh.model.geo.addLine(1057, 1058, 1114)
gmsh.model.geo.addLine(1058, 1059, 1115)
gmsh.model.geo.addLine(1059, 1060, 1116)
gmsh.model.geo.addLine(1060, 1061, 1117)
gmsh.model.geo.addLine(1061, 1062, 1118)
gmsh.model.geo.addLine(1062, 1063, 1119)
gmsh.model.geo.addLine(1063, 1064, 1120)
gmsh.model.geo.addLine(1064, 1065, 1121)
gmsh.model.geo.addLine(1065, 1066, 1122)
gmsh.model.geo.addLine(1066, 1067, 1123)
gmsh.model.geo.addLine(1067, 1068, 1124)
gmsh.model.geo.addLine(1068, 1069, 1125)
gmsh.model.geo.addLine(1069, 1070, 1126)
gmsh.model.geo.addLine(1070, 1071, 1127)
gmsh.model.geo.addLine(1071, 1072, 1128)
gmsh.model.geo.addLine(1072, 1073, 1129)
gmsh.model.geo.addLine(1073, 1074, 1130)
gmsh.model.geo.addLine(1074, 1075, 1131)
gmsh.model.geo.addLine(1075, 1076, 1132)
gmsh.model.geo.addLine(1076, 1077, 1133)
gmsh.model.geo.addLine(1077, 1078, 1134)
gmsh.model.geo.addLine(1078, 1079, 1135)
gmsh.model.geo.addLine(1079, 1080, 1136)
gmsh.model.geo.addLine(1080, 1081, 1137)
gmsh.model.geo.addLine(1081, 1082, 1138)
gmsh.model.geo.addLine(1082, 1083, 1139)
gmsh.model.geo.addLine(1083, 1084, 1140)
gmsh.model.geo.addLine(1084, 1085, 1141)
gmsh.model.geo.addLine(1085, 1086, 1142)
gmsh.model.geo.addLine(1086, 1087, 1143)
gmsh.model.geo.addLine(1087, 1088, 1144)
gmsh.model.geo.addLine(1088, 1089, 1145)
gmsh.model.geo.addLine(1089, 1090, 1146)
gmsh.model.geo.addLine(1090, 1091, 1147)
gmsh.model.geo.addLine(1091, 1092, 1148)
gmsh.model.geo.addLine(1092, 1093, 1149)
gmsh.model.geo.addLine(1093, 1094, 1150)
gmsh.model.geo.addLine(1094, 1095, 1151)
gmsh.model.geo.addLine(1095, 1096, 1152)
gmsh.model.geo.addLine(1096, 1097, 1153)
gmsh.model.geo.addLine(1097, 1098, 1154)
gmsh.model.geo.addLine(1098, 1099, 1155)
gmsh.model.geo.addLine(1099, 1100, 1156)
gmsh.model.geo.addLine(1100, 1101, 1157)
gmsh.model.geo.addLine(1101, 1102, 1158)
gmsh.model.geo.addLine(1102, 1103, 1159)
gmsh.model.geo.addLine(1103, 1104, 1160)
gmsh.model.geo.addLine(1104, 1105, 1161)
gmsh.model.geo.addLine(1105, 1106, 1162)
gmsh.model.geo.addLine(1106, 1107, 1163)
gmsh.model.geo.addLine(1107, 1108, 1164)
gmsh.model.geo.addLine(1108, 1109, 1165)
gmsh.model.geo.addLine(1109, 1110, 1166)
gmsh.model.geo.addLine(1110, 1111, 1167)
gmsh.model.geo.addLine(1111, 1112, 1168)
gmsh.model.geo.addLine(1112, 1113, 1169)
gmsh.model.geo.addLine(1113, 1114, 1170)
gmsh.model.geo.addLine(1114, 1115, 1171)
gmsh.model.geo.addLine(1115, 1116, 1172)
gmsh.model.geo.addLine(1116, 1117, 1173)
gmsh.model.geo.addLine(1117, 1118, 1174)
gmsh.model.geo.addLine(1118, 1119, 1175)
gmsh.model.geo.addLine(1119, 1120, 1176)
gmsh.model.geo.addLine(1120, 1121, 1177)
gmsh.model.geo.addLine(1121, 1122, 1178)
gmsh.model.geo.addLine(1122, 1123, 1179)
gmsh.model.geo.addLine(1123, 1124, 1180)
gmsh.model.geo.addLine(1124, 1125, 1181)
gmsh.model.geo.addLine(1125, 1126, 1182)
gmsh.model.geo.addLine(1126, 1127, 1183)
gmsh.model.geo.addLine(1127, 1128, 1184)
gmsh.model.geo.addLine(1128, 1129, 1185)
gmsh.model.geo.addLine(1129, 1130, 1186)
gmsh.model.geo.addLine(1130, 1131, 1187)
gmsh.model.geo.addLine(1131, 1132, 1188)
gmsh.model.geo.addLine(1132, 1133, 1189)
gmsh.model.geo.addLine(1133, 1134, 1190)
gmsh.model.geo.addLine(1134, 1135, 1191)
gmsh.model.geo.addLine(1135, 1136, 1192)
gmsh.model.geo.addLine(1136, 1137, 1193)
gmsh.model.geo.addLine(1137, 1138, 1194)
gmsh.model.geo.addLine(1138, 1139, 1195)
gmsh.model.geo.addLine(1139, 1140, 1196)
gmsh.model.geo.addLine(1140, 1141, 1197)
gmsh.model.geo.addLine(1141, 1142, 1198)
gmsh.model.geo.addLine(1142, 1143, 1199)
gmsh.model.geo.addLine(1143, 1144, 1200)
gmsh.model.geo.addLine(1144, 1145, 1201)
gmsh.model.geo.addLine(1145, 1146, 1202)
gmsh.model.geo.addLine(1146, 1147, 1203)
gmsh.model.geo.addLine(1147, 1148, 1204)
gmsh.model.geo.addLine(1148, 1149, 1205)
gmsh.model.geo.addLine(1149, 1150, 1206)
gmsh.model.geo.addLine(1150, 1151, 1207)
gmsh.model.geo.addLine(1151, 1152, 1208)
gmsh.model.geo.addLine(1152, 1153, 1209)
gmsh.model.geo.addLine(1153, 1154, 1210)
gmsh.model.geo.addLine(1154, 1155, 1211)
gmsh.model.geo.addLine(1155, 1156, 1212)
gmsh.model.geo.addLine(1156, 1157, 1213)
gmsh.model.geo.addLine(1157, 1158, 1214)
gmsh.model.geo.addLine(1158, 1159, 1215)
gmsh.model.geo.addLine(1159, 1160, 1216)
gmsh.model.geo.addLine(1160, 1161, 1217)
gmsh.model.geo.addLine(1161, 1162, 1218)
gmsh.model.geo.addLine(1162, 1163, 1219)
gmsh.model.geo.addLine(1163, 1164, 1220)
gmsh.model.geo.addLine(1164, 1165, 1221)
gmsh.model.geo.addLine(1165, 1166, 1222)
gmsh.model.geo.addLine(1166, 1167, 1223)
gmsh.model.geo.addLine(1167, 1168, 1224)
gmsh.model.geo.addLine(1168, 1169, 1225)
gmsh.model.geo.addLine(1169, 1170, 1226)
gmsh.model.geo.addLine(1170, 1171, 1227)
gmsh.model.geo.addLine(1171, 1172, 1228)
gmsh.model.geo.addLine(1172, 1173, 1229)
gmsh.model.geo.addLine(1173, 1174, 1230)
gmsh.model.geo.addLine(1174, 1175, 1231)
gmsh.model.geo.addLine(1175, 1176, 1232)
gmsh.model.geo.addLine(1176, 1177, 1233)
gmsh.model.geo.addLine(1177, 1178, 1234)
gmsh.model.geo.addLine(1178, 1179, 1235)
gmsh.model.geo.addLine(1179, 1180, 1236)
gmsh.model.geo.addLine(1180, 1181, 1237)
gmsh.model.geo.addLine(1181, 1182, 1238)
gmsh.model.geo.addLine(1182, 1183, 1239)
gmsh.model.geo.addLine(1183, 1184, 1240)
gmsh.model.geo.addLine(1184, 1185, 1241)
gmsh.model.geo.addLine(1185, 1186, 1242)
gmsh.model.geo.addLine(1186, 1187, 1243)
gmsh.model.geo.addLine(1187, 1188, 1244)
gmsh.model.geo.addLine(1188, 1189, 1245)
gmsh.model.geo.addLine(1189, 1190, 1246)
gmsh.model.geo.addLine(1190, 1191, 1247)
gmsh.model.geo.addLine(1191, 1192, 1248)
gmsh.model.geo.addLine(1192, 1193, 1249)
gmsh.model.geo.addLine(1193, 1194, 1250)
gmsh.model.geo.addLine(1194, 1195, 1251)
gmsh.model.geo.addLine(1195, 1196, 1252)
gmsh.model.geo.addLine(1196, 1197, 1253)
gmsh.model.geo.addLine(1197, 1198, 1254)
gmsh.model.geo.addLine(1198, 1199, 1255)
gmsh.model.geo.addLine(1199, 1200, 1256)
gmsh.model.geo.addLine(1200, 1201, 1257)
gmsh.model.geo.addLine(1201, 1202, 1258)
gmsh.model.geo.addLine(1202, 1203, 1259)
gmsh.model.geo.addLine(1203, 1204, 1260)
gmsh.model.geo.addLine(1204, 1205, 1261)
gmsh.model.geo.addLine(1205, 1206, 1262)
gmsh.model.geo.addLine(1206, 1207, 1263)
gmsh.model.geo.addLine(1207, 1208, 1264)
gmsh.model.geo.addLine(1208, 1209, 1265)
gmsh.model.geo.addLine(1209, 1210, 1266)
gmsh.model.geo.addLine(1210, 1211, 1267)
gmsh.model.geo.addLine(1211, 1212, 1268)
gmsh.model.geo.addLine(1212, 1213, 1269)
gmsh.model.geo.addLine(1213, 1214, 1270)
gmsh.model.geo.addLine(1214, 1215, 1271)
gmsh.model.geo.addLine(1215, 1216, 1272)
gmsh.model.geo.addLine(1216, 1217, 1273)
gmsh.model.geo.addLine(1217, 1218, 1274)
gmsh.model.geo.addLine(1218, 1219, 1275)
gmsh.model.geo.addLine(1219, 1220, 1276)
gmsh.model.geo.addLine(1220, 1221, 1277)
gmsh.model.geo.addLine(1221, 1222, 1278)
gmsh.model.geo.addLine(1222, 1223, 1279)
gmsh.model.geo.addLine(1223, 1224, 1280)
gmsh.model.geo.addLine(1224, 1225, 1281)
gmsh.model.geo.addLine(1225, 1226, 1282)
gmsh.model.geo.addLine(1226, 1227, 1283)
gmsh.model.geo.addLine(1227, 1228, 1284)
gmsh.model.geo.addLine(1228, 1229, 1285)
gmsh.model.geo.addLine(1229, 1230, 1286)
gmsh.model.geo.addLine(1230, 1231, 1287)
gmsh.model.geo.addLine(1231, 1232, 1288)
gmsh.model.geo.addLine(1232, 1233, 1289)
gmsh.model.geo.addLine(1233, 1234, 1290)
gmsh.model.geo.addLine(1234, 1235, 1291)
gmsh.model.geo.addLine(1235, 1236, 1292)
gmsh.model.geo.addLine(1236, 1237, 1293)
gmsh.model.geo.addLine(1237, 1238, 1294)
gmsh.model.geo.addLine(1238, 1239, 1295)
gmsh.model.geo.addLine(1239, 1240, 1296)
gmsh.model.geo.addLine(1240, 1241, 1297)
gmsh.model.geo.addLine(1241, 1242, 1298)
gmsh.model.geo.addLine(1242, 1243, 1299)
gmsh.model.geo.addLine(1243, 1244, 1300)
gmsh.model.geo.addLine(1244, 1245, 1301)
gmsh.model.geo.addLine(1245, 1246, 1302)
gmsh.model.geo.addLine(1246, 1247, 1303)
gmsh.model.geo.addLine(1247, 1248, 1304)
gmsh.model.geo.addLine(1248, 1249, 1305)
gmsh.model.geo.addLine(1249, 1250, 1306)
gmsh.model.geo.addLine(1250, 1251, 1307)
gmsh.model.geo.addLine(1251, 1252, 1308)
gmsh.model.geo.addLine(1252, 1253, 1309)
gmsh.model.geo.addLine(1253, 1254, 1310)
gmsh.model.geo.addLine(1254, 1255, 1311)
gmsh.model.geo.addLine(1255, 1256, 1312)
gmsh.model.geo.addLine(1256, 1257, 1313)
gmsh.model.geo.addLine(1257, 1258, 1314)
gmsh.model.geo.addLine(1258, 1259, 1315)
gmsh.model.geo.addLine(1259, 1260, 1316)
gmsh.model.geo.addLine(1260, 1261, 1317)
gmsh.model.geo.addLine(1261, 1262, 1318)
gmsh.model.geo.addLine(1262, 1263, 1319)
gmsh.model.geo.addLine(1263, 1264, 1320)
gmsh.model.geo.addLine(1264, 1265, 1321)
gmsh.model.geo.addLine(1265, 1266, 1322)
gmsh.model.geo.addLine(1266, 1267, 1323)
gmsh.model.geo.addLine(1267, 1268, 1324)
gmsh.model.geo.addLine(1268, 1269, 1325)
gmsh.model.geo.addLine(1269, 1270, 1326)
gmsh.model.geo.addLine(1270, 1271, 1327)
gmsh.model.geo.addLine(1271, 1272, 1328)
gmsh.model.geo.addLine(1272, 1273, 1329)
gmsh.model.geo.addLine(1273, 1274, 1330)
gmsh.model.geo.addLine(1274, 1275, 1331)
gmsh.model.geo.addLine(1275, 1276, 1332)
gmsh.model.geo.addLine(1276, 1277, 1333)
gmsh.model.geo.addLine(1277, 1278, 1334)
gmsh.model.geo.addLine(1278, 1279, 1335)
gmsh.model.geo.addLine(1279, 1280, 1336)
gmsh.model.geo.addLine(1280, 1281, 1337)
gmsh.model.geo.addLine(1281, 1282, 1338)
gmsh.model.geo.addLine(1282, 1283, 1339)
gmsh.model.geo.addLine(1283, 1284, 1340)
gmsh.model.geo.addLine(1284, 1285, 1341)
gmsh.model.geo.addLine(1285, 1286, 1342)
gmsh.model.geo.addLine(1286, 1287, 1343)
gmsh.model.geo.addLine(1287, 1288, 1344)
gmsh.model.geo.addLine(1288, 1289, 1345)
gmsh.model.geo.addLine(1289, 1290, 1346)
gmsh.model.geo.addLine(1290, 1291, 1347)
gmsh.model.geo.addLine(1291, 1292, 1348)
gmsh.model.geo.addLine(1292, 1293, 1349)
gmsh.model.geo.addLine(1293, 1294, 1350)
gmsh.model.geo.addLine(1294, 1295, 1351)
gmsh.model.geo.addLine(1295, 1296, 1352)
gmsh.model.geo.addLine(1296, 1297, 1353)
gmsh.model.geo.addLine(1297, 1298, 1354)
gmsh.model.geo.addLine(1298, 1299, 1355)
gmsh.model.geo.addLine(1299, 1300, 1356)
gmsh.model.geo.addLine(1300, 1301, 1357)
gmsh.model.geo.addLine(1301, 1302, 1358)
gmsh.model.geo.addLine(1302, 1303, 1359)
gmsh.model.geo.addLine(1303, 1304, 1360)
gmsh.model.geo.addLine(1304, 1305, 1361)
gmsh.model.geo.addLine(1305, 1306, 1362)
gmsh.model.geo.addLine(1306, 1307, 1363)
gmsh.model.geo.addLine(1307, 1308, 1364)
gmsh.model.geo.addLine(1308, 1309, 1365)
gmsh.model.geo.addLine(1309, 1310, 1366)
gmsh.model.geo.addLine(1310, 1311, 1367)
gmsh.model.geo.addLine(1311, 1312, 1368)
gmsh.model.geo.addLine(1312, 1313, 1369)
gmsh.model.geo.addLine(1313, 1314, 1370)
gmsh.model.geo.addLine(1314, 1315, 1371)
gmsh.model.geo.addLine(1315, 1316, 1372)
gmsh.model.geo.addLine(1316, 1317, 1373)
gmsh.model.geo.addLine(1317, 1318, 1374)
gmsh.model.geo.addLine(1318, 1319, 1375)
gmsh.model.geo.addLine(1319, 1320, 1376)
gmsh.model.geo.addLine(1320, 1321, 1377)
gmsh.model.geo.addLine(1321, 1322, 1378)
gmsh.model.geo.addLine(1322, 1323, 1379)
gmsh.model.geo.addLine(1323, 1324, 1380)
gmsh.model.geo.addLine(1324, 1325, 1381)
gmsh.model.geo.addLine(1325, 1326, 1382)
gmsh.model.geo.addLine(1326, 1327, 1383)
gmsh.model.geo.addLine(1327, 1328, 1384)
gmsh.model.geo.addLine(1328, 1329, 1385)
gmsh.model.geo.addLine(1329, 1330, 1386)
gmsh.model.geo.addLine(1330, 1331, 1387)
gmsh.model.geo.addLine(1331, 1332, 1388)
gmsh.model.geo.addLine(1332, 1333, 1389)
gmsh.model.geo.addLine(1333, 1334, 1390)
gmsh.model.geo.addLine(1334, 1335, 1391)
gmsh.model.geo.addLine(1335, 1336, 1392)
gmsh.model.geo.addLine(1336, 1337, 1393)
gmsh.model.geo.addLine(1337, 1338, 1394)
gmsh.model.geo.addLine(1338, 1339, 1395)
gmsh.model.geo.addLine(1339, 1340, 1396)
gmsh.model.geo.addLine(1340, 1341, 1397)
gmsh.model.geo.addLine(1341, 1342, 1398)
gmsh.model.geo.addLine(1342, 836, 1399)
gmsh.model.geo.addLine(1343, 1344, 1401)
gmsh.model.geo.addLine(1344, 1345, 1402)
gmsh.model.geo.addLine(1345, 1346, 1403)
gmsh.model.geo.addLine(1346, 1347, 1404)
gmsh.model.geo.addLine(1347, 1348, 1405)
gmsh.model.geo.addLine(1348, 1349, 1406)
gmsh.model.geo.addLine(1349, 1350, 1407)
gmsh.model.geo.addLine(1350, 1351, 1408)
gmsh.model.geo.addLine(1351, 1352, 1409)
gmsh.model.geo.addLine(1352, 1353, 1410)
gmsh.model.geo.addLine(1353, 1354, 1411)
gmsh.model.geo.addLine(1354, 1355, 1412)
gmsh.model.geo.addLine(1355, 1356, 1413)
gmsh.model.geo.addLine(1356, 1357, 1414)
gmsh.model.geo.addLine(1357, 1358, 1415)
gmsh.model.geo.addLine(1358, 1359, 1416)
gmsh.model.geo.addLine(1359, 1360, 1417)
gmsh.model.geo.addLine(1360, 1361, 1418)
gmsh.model.geo.addLine(1361, 1362, 1419)
gmsh.model.geo.addLine(1362, 1363, 1420)
gmsh.model.geo.addLine(1363, 1364, 1421)
gmsh.model.geo.addLine(1364, 1365, 1422)
gmsh.model.geo.addLine(1365, 1366, 1423)
gmsh.model.geo.addLine(1366, 1367, 1424)
gmsh.model.geo.addLine(1367, 1368, 1425)
gmsh.model.geo.addLine(1368, 1369, 1426)
gmsh.model.geo.addLine(1369, 1370, 1427)
gmsh.model.geo.addLine(1370, 1371, 1428)
gmsh.model.geo.addLine(1371, 1372, 1429)
gmsh.model.geo.addLine(1372, 1373, 1430)
gmsh.model.geo.addLine(1373, 1374, 1431)
gmsh.model.geo.addLine(1374, 1375, 1432)
gmsh.model.geo.addLine(1375, 1376, 1433)
gmsh.model.geo.addLine(1376, 1377, 1434)
gmsh.model.geo.addLine(1377, 1378, 1435)
gmsh.model.geo.addLine(1378, 1379, 1436)
gmsh.model.geo.addLine(1379, 1380, 1437)
gmsh.model.geo.addLine(1380, 1381, 1438)
gmsh.model.geo.addLine(1381, 1382, 1439)
gmsh.model.geo.addLine(1382, 1383, 1440)
gmsh.model.geo.addLine(1383, 1384, 1441)
gmsh.model.geo.addLine(1384, 1385, 1442)
gmsh.model.geo.addLine(1385, 1386, 1443)
gmsh.model.geo.addLine(1386, 1387, 1444)
gmsh.model.geo.addLine(1387, 1388, 1445)
gmsh.model.geo.addLine(1388, 1389, 1446)
gmsh.model.geo.addLine(1389, 1390, 1447)
gmsh.model.geo.addLine(1390, 1391, 1448)
gmsh.model.geo.addLine(1391, 1392, 1449)
gmsh.model.geo.addLine(1392, 1393, 1450)
gmsh.model.geo.addLine(1393, 1394, 1451)
gmsh.model.geo.addLine(1394, 1395, 1452)
gmsh.model.geo.addLine(1395, 1396, 1453)
gmsh.model.geo.addLine(1396, 1397, 1454)
gmsh.model.geo.addLine(1397, 1398, 1455)
gmsh.model.geo.addLine(1398, 1399, 1456)
gmsh.model.geo.addLine(1399, 1400, 1457)
gmsh.model.geo.addLine(1400, 1401, 1458)
gmsh.model.geo.addLine(1401, 1402, 1459)
gmsh.model.geo.addLine(1402, 1403, 1460)
gmsh.model.geo.addLine(1403, 1404, 1461)
gmsh.model.geo.addLine(1404, 1405, 1462)
gmsh.model.geo.addLine(1405, 1406, 1463)
gmsh.model.geo.addLine(1406, 1407, 1464)
gmsh.model.geo.addLine(1407, 1408, 1465)
gmsh.model.geo.addLine(1408, 1409, 1466)
gmsh.model.geo.addLine(1409, 1410, 1467)
gmsh.model.geo.addLine(1410, 1411, 1468)
gmsh.model.geo.addLine(1411, 1412, 1469)
gmsh.model.geo.addLine(1412, 1413, 1470)
gmsh.model.geo.addLine(1413, 1414, 1471)
gmsh.model.geo.addLine(1414, 1415, 1472)
gmsh.model.geo.addLine(1415, 1416, 1473)
gmsh.model.geo.addLine(1416, 1417, 1474)
gmsh.model.geo.addLine(1417, 1418, 1475)
gmsh.model.geo.addLine(1418, 1419, 1476)
gmsh.model.geo.addLine(1419, 1420, 1477)
gmsh.model.geo.addLine(1420, 1421, 1478)
gmsh.model.geo.addLine(1421, 1422, 1479)
gmsh.model.geo.addLine(1422, 1423, 1480)
gmsh.model.geo.addLine(1423, 1424, 1481)
gmsh.model.geo.addLine(1424, 1425, 1482)
gmsh.model.geo.addLine(1425, 1426, 1483)
gmsh.model.geo.addLine(1426, 1427, 1484)
gmsh.model.geo.addLine(1427, 1428, 1485)
gmsh.model.geo.addLine(1428, 1429, 1486)
gmsh.model.geo.addLine(1429, 1430, 1487)
gmsh.model.geo.addLine(1430, 1431, 1488)
gmsh.model.geo.addLine(1431, 1432, 1489)
gmsh.model.geo.addLine(1432, 1433, 1490)
gmsh.model.geo.addLine(1433, 1434, 1491)
gmsh.model.geo.addLine(1434, 1435, 1492)
gmsh.model.geo.addLine(1435, 1436, 1493)
gmsh.model.geo.addLine(1436, 1437, 1494)
gmsh.model.geo.addLine(1437, 1438, 1495)
gmsh.model.geo.addLine(1438, 1439, 1496)
gmsh.model.geo.addLine(1439, 1440, 1497)
gmsh.model.geo.addLine(1440, 1441, 1498)
gmsh.model.geo.addLine(1441, 1442, 1499)
gmsh.model.geo.addLine(1442, 1443, 1500)
gmsh.model.geo.addLine(1443, 1444, 1501)
gmsh.model.geo.addLine(1444, 1445, 1502)
gmsh.model.geo.addLine(1445, 1446, 1503)
gmsh.model.geo.addLine(1446, 1447, 1504)
gmsh.model.geo.addLine(1447, 1448, 1505)
gmsh.model.geo.addLine(1448, 1449, 1506)
gmsh.model.geo.addLine(1449, 1450, 1507)
gmsh.model.geo.addLine(1450, 1451, 1508)
gmsh.model.geo.addLine(1451, 1452, 1509)
gmsh.model.geo.addLine(1452, 1453, 1510)
gmsh.model.geo.addLine(1453, 1454, 1511)
gmsh.model.geo.addLine(1454, 1455, 1512)
gmsh.model.geo.addLine(1455, 1456, 1513)
gmsh.model.geo.addLine(1456, 1457, 1514)
gmsh.model.geo.addLine(1457, 1458, 1515)
gmsh.model.geo.addLine(1458, 1459, 1516)
gmsh.model.geo.addLine(1459, 1460, 1517)
gmsh.model.geo.addLine(1460, 1461, 1518)
gmsh.model.geo.addLine(1461, 1462, 1519)
gmsh.model.geo.addLine(1462, 1463, 1520)
gmsh.model.geo.addLine(1463, 1464, 1521)
gmsh.model.geo.addLine(1464, 1465, 1522)
gmsh.model.geo.addLine(1465, 1466, 1523)
gmsh.model.geo.addLine(1466, 1467, 1524)
gmsh.model.geo.addLine(1467, 1468, 1525)
gmsh.model.geo.addLine(1468, 1469, 1526)
gmsh.model.geo.addLine(1469, 1470, 1527)
gmsh.model.geo.addLine(1470, 1471, 1528)
gmsh.model.geo.addLine(1471, 1472, 1529)
gmsh.model.geo.addLine(1472, 1473, 1530)
gmsh.model.geo.addLine(1473, 1474, 1531)
gmsh.model.geo.addLine(1474, 1475, 1532)
gmsh.model.geo.addLine(1475, 1476, 1533)
gmsh.model.geo.addLine(1476, 1477, 1534)
gmsh.model.geo.addLine(1477, 1478, 1535)
gmsh.model.geo.addLine(1478, 1479, 1536)
gmsh.model.geo.addLine(1479, 1480, 1537)
gmsh.model.geo.addLine(1480, 1481, 1538)
gmsh.model.geo.addLine(1481, 1482, 1539)
gmsh.model.geo.addLine(1482, 1483, 1540)
gmsh.model.geo.addLine(1483, 1484, 1541)
gmsh.model.geo.addLine(1484, 1485, 1542)
gmsh.model.geo.addLine(1485, 1486, 1543)
gmsh.model.geo.addLine(1486, 1487, 1544)
gmsh.model.geo.addLine(1487, 1488, 1545)
gmsh.model.geo.addLine(1488, 1489, 1546)
gmsh.model.geo.addLine(1489, 1490, 1547)
gmsh.model.geo.addLine(1490, 1491, 1548)
gmsh.model.geo.addLine(1491, 1492, 1549)
gmsh.model.geo.addLine(1492, 1493, 1550)
gmsh.model.geo.addLine(1493, 1494, 1551)
gmsh.model.geo.addLine(1494, 1495, 1552)
gmsh.model.geo.addLine(1495, 1496, 1553)
gmsh.model.geo.addLine(1496, 1497, 1554)
gmsh.model.geo.addLine(1497, 1498, 1555)
gmsh.model.geo.addLine(1498, 1499, 1556)
gmsh.model.geo.addLine(1499, 1500, 1557)
gmsh.model.geo.addLine(1500, 1501, 1558)
gmsh.model.geo.addLine(1501, 1502, 1559)
gmsh.model.geo.addLine(1502, 1503, 1560)
gmsh.model.geo.addLine(1503, 1504, 1561)
gmsh.model.geo.addLine(1504, 1505, 1562)
gmsh.model.geo.addLine(1505, 1506, 1563)
gmsh.model.geo.addLine(1506, 1507, 1564)
gmsh.model.geo.addLine(1507, 1508, 1565)
gmsh.model.geo.addLine(1508, 1509, 1566)
gmsh.model.geo.addLine(1509, 1510, 1567)
gmsh.model.geo.addLine(1510, 1511, 1568)
gmsh.model.geo.addLine(1511, 1512, 1569)
gmsh.model.geo.addLine(1512, 1513, 1570)
gmsh.model.geo.addLine(1513, 1514, 1571)
gmsh.model.geo.addLine(1514, 1515, 1572)
gmsh.model.geo.addLine(1515, 1516, 1573)
gmsh.model.geo.addLine(1516, 1517, 1574)
gmsh.model.geo.addLine(1517, 1518, 1575)
gmsh.model.geo.addLine(1518, 1519, 1576)
gmsh.model.geo.addLine(1519, 1520, 1577)
gmsh.model.geo.addLine(1520, 1521, 1578)
gmsh.model.geo.addLine(1521, 1522, 1579)
gmsh.model.geo.addLine(1522, 1523, 1580)
gmsh.model.geo.addLine(1523, 1524, 1581)
gmsh.model.geo.addLine(1524, 1525, 1582)
gmsh.model.geo.addLine(1525, 1526, 1583)
gmsh.model.geo.addLine(1526, 1527, 1584)
gmsh.model.geo.addLine(1527, 1528, 1585)
gmsh.model.geo.addLine(1528, 1529, 1586)
gmsh.model.geo.addLine(1529, 1530, 1587)
gmsh.model.geo.addLine(1530, 1531, 1588)
gmsh.model.geo.addLine(1531, 1532, 1589)
gmsh.model.geo.addLine(1532, 1533, 1590)
gmsh.model.geo.addLine(1533, 1534, 1591)
gmsh.model.geo.addLine(1534, 1535, 1592)
gmsh.model.geo.addLine(1535, 1536, 1593)
gmsh.model.geo.addLine(1536, 1537, 1594)
gmsh.model.geo.addLine(1537, 1538, 1595)
gmsh.model.geo.addLine(1538, 1539, 1596)
gmsh.model.geo.addLine(1539, 1540, 1597)
gmsh.model.geo.addLine(1540, 1541, 1598)
gmsh.model.geo.addLine(1541, 1542, 1599)
gmsh.model.geo.addLine(1542, 1543, 1600)
gmsh.model.geo.addLine(1543, 1544, 1601)
gmsh.model.geo.addLine(1544, 1545, 1602)
gmsh.model.geo.addLine(1545, 1546, 1603)
gmsh.model.geo.addLine(1546, 1547, 1604)
gmsh.model.geo.addLine(1547, 1548, 1605)
gmsh.model.geo.addLine(1548, 1549, 1606)
gmsh.model.geo.addLine(1549, 1550, 1607)
gmsh.model.geo.addLine(1550, 1551, 1608)
gmsh.model.geo.addLine(1551, 1552, 1609)
gmsh.model.geo.addLine(1552, 1553, 1610)
gmsh.model.geo.addLine(1553, 1554, 1611)
gmsh.model.geo.addLine(1554, 1555, 1612)
gmsh.model.geo.addLine(1555, 1556, 1613)
gmsh.model.geo.addLine(1556, 1557, 1614)
gmsh.model.geo.addLine(1557, 1558, 1615)
gmsh.model.geo.addLine(1558, 1559, 1616)
gmsh.model.geo.addLine(1559, 1560, 1617)
gmsh.model.geo.addLine(1560, 1561, 1618)
gmsh.model.geo.addLine(1561, 1562, 1619)
gmsh.model.geo.addLine(1562, 1563, 1620)
gmsh.model.geo.addLine(1563, 1564, 1621)
gmsh.model.geo.addLine(1564, 1565, 1622)
gmsh.model.geo.addLine(1565, 1566, 1623)
gmsh.model.geo.addLine(1566, 1567, 1624)
gmsh.model.geo.addLine(1567, 1568, 1625)
gmsh.model.geo.addLine(1568, 1569, 1626)
gmsh.model.geo.addLine(1569, 1570, 1627)
gmsh.model.geo.addLine(1570, 1571, 1628)
gmsh.model.geo.addLine(1571, 1572, 1629)
gmsh.model.geo.addLine(1572, 1573, 1630)
gmsh.model.geo.addLine(1573, 1574, 1631)
gmsh.model.geo.addLine(1574, 1575, 1632)
gmsh.model.geo.addLine(1575, 1576, 1633)
gmsh.model.geo.addLine(1576, 1577, 1634)
gmsh.model.geo.addLine(1577, 1578, 1635)
gmsh.model.geo.addLine(1578, 1579, 1636)
gmsh.model.geo.addLine(1579, 1580, 1637)
gmsh.model.geo.addLine(1580, 1581, 1638)
gmsh.model.geo.addLine(1581, 1582, 1639)
gmsh.model.geo.addLine(1582, 1583, 1640)
gmsh.model.geo.addLine(1583, 1584, 1641)
gmsh.model.geo.addLine(1584, 1585, 1642)
gmsh.model.geo.addLine(1585, 1586, 1643)
gmsh.model.geo.addLine(1586, 1587, 1644)
gmsh.model.geo.addLine(1587, 1588, 1645)
gmsh.model.geo.addLine(1588, 1589, 1646)
gmsh.model.geo.addLine(1589, 1590, 1647)
gmsh.model.geo.addLine(1590, 1591, 1648)
gmsh.model.geo.addLine(1591, 1592, 1649)
gmsh.model.geo.addLine(1592, 1593, 1650)
gmsh.model.geo.addLine(1593, 1594, 1651)
gmsh.model.geo.addLine(1594, 1595, 1652)
gmsh.model.geo.addLine(1595, 1596, 1653)
gmsh.model.geo.addLine(1596, 1597, 1654)
gmsh.model.geo.addLine(1597, 1598, 1655)
gmsh.model.geo.addLine(1598, 1599, 1656)
gmsh.model.geo.addLine(1599, 1600, 1657)
gmsh.model.geo.addLine(1600, 1601, 1658)
gmsh.model.geo.addLine(1601, 1602, 1659)
gmsh.model.geo.addLine(1602, 1603, 1660)
gmsh.model.geo.addLine(1603, 1604, 1661)
gmsh.model.geo.addLine(1604, 1605, 1662)
gmsh.model.geo.addLine(1605, 1606, 1663)
gmsh.model.geo.addLine(1606, 1607, 1664)
gmsh.model.geo.addLine(1607, 1608, 1665)
gmsh.model.geo.addLine(1608, 1609, 1666)
gmsh.model.geo.addLine(1609, 1610, 1667)
gmsh.model.geo.addLine(1610, 1611, 1668)
gmsh.model.geo.addLine(1611, 1612, 1669)
gmsh.model.geo.addLine(1612, 1613, 1670)
gmsh.model.geo.addLine(1613, 1614, 1671)
gmsh.model.geo.addLine(1614, 1615, 1672)
gmsh.model.geo.addLine(1615, 1616, 1673)
gmsh.model.geo.addLine(1616, 1617, 1674)
gmsh.model.geo.addLine(1617, 1618, 1675)
gmsh.model.geo.addLine(1618, 1619, 1676)
gmsh.model.geo.addLine(1619, 1620, 1677)
gmsh.model.geo.addLine(1620, 1621, 1678)
gmsh.model.geo.addLine(1621, 1622, 1679)
gmsh.model.geo.addLine(1622, 1623, 1680)
gmsh.model.geo.addLine(1623, 1624, 1681)
gmsh.model.geo.addLine(1624, 1625, 1682)
gmsh.model.geo.addLine(1625, 1626, 1683)
gmsh.model.geo.addLine(1626, 1627, 1684)
gmsh.model.geo.addLine(1627, 1628, 1685)
gmsh.model.geo.addLine(1628, 1629, 1686)
gmsh.model.geo.addLine(1629, 1630, 1687)
gmsh.model.geo.addLine(1630, 1631, 1688)
gmsh.model.geo.addLine(1631, 1632, 1689)
gmsh.model.geo.addLine(1632, 1633, 1690)
gmsh.model.geo.addLine(1633, 1634, 1691)
gmsh.model.geo.addLine(1634, 1635, 1692)
gmsh.model.geo.addLine(1635, 1636, 1693)
gmsh.model.geo.addLine(1636, 1637, 1694)
gmsh.model.geo.addLine(1637, 1638, 1695)
gmsh.model.geo.addLine(1638, 1639, 1696)
gmsh.model.geo.addLine(1639, 1640, 1697)
gmsh.model.geo.addLine(1640, 1641, 1698)
gmsh.model.geo.addLine(1641, 1642, 1699)
gmsh.model.geo.addLine(1642, 1643, 1700)
gmsh.model.geo.addLine(1643, 1644, 1701)
gmsh.model.geo.addLine(1644, 1645, 1702)
gmsh.model.geo.addLine(1645, 1646, 1703)
gmsh.model.geo.addLine(1646, 1647, 1704)
gmsh.model.geo.addLine(1647, 1648, 1705)
gmsh.model.geo.addLine(1648, 1649, 1706)
gmsh.model.geo.addLine(1649, 1650, 1707)
gmsh.model.geo.addLine(1650, 1651, 1708)
gmsh.model.geo.addLine(1651, 1652, 1709)
gmsh.model.geo.addLine(1652, 1653, 1710)
gmsh.model.geo.addLine(1653, 1654, 1711)
gmsh.model.geo.addLine(1654, 1655, 1712)
gmsh.model.geo.addLine(1655, 1656, 1713)
gmsh.model.geo.addLine(1656, 1657, 1714)
gmsh.model.geo.addLine(1657, 1658, 1715)
gmsh.model.geo.addLine(1658, 1659, 1716)
gmsh.model.geo.addLine(1659, 1660, 1717)
gmsh.model.geo.addLine(1660, 1661, 1718)
gmsh.model.geo.addLine(1661, 1662, 1719)
gmsh.model.geo.addLine(1662, 1663, 1720)
gmsh.model.geo.addLine(1663, 1664, 1721)
gmsh.model.geo.addLine(1664, 1665, 1722)
gmsh.model.geo.addLine(1665, 1666, 1723)
gmsh.model.geo.addLine(1666, 1667, 1724)
gmsh.model.geo.addLine(1667, 1668, 1725)
gmsh.model.geo.addLine(1668, 1669, 1726)
gmsh.model.geo.addLine(1669, 1670, 1727)
gmsh.model.geo.addLine(1670, 1671, 1728)
gmsh.model.geo.addLine(1671, 1672, 1729)
gmsh.model.geo.addLine(1672, 1673, 1730)
gmsh.model.geo.addLine(1673, 1674, 1731)
gmsh.model.geo.addLine(1674, 1675, 1732)
gmsh.model.geo.addLine(1675, 1676, 1733)
gmsh.model.geo.addLine(1676, 1677, 1734)
gmsh.model.geo.addLine(1677, 1678, 1735)
gmsh.model.geo.addLine(1678, 1679, 1736)
gmsh.model.geo.addLine(1679, 1680, 1737)
gmsh.model.geo.addLine(1680, 1681, 1738)
gmsh.model.geo.addLine(1681, 1682, 1739)
gmsh.model.geo.addLine(1682, 1683, 1740)
gmsh.model.geo.addLine(1683, 1684, 1741)
gmsh.model.geo.addLine(1684, 1685, 1742)
gmsh.model.geo.addLine(1685, 1686, 1743)
gmsh.model.geo.addLine(1686, 1687, 1744)
gmsh.model.geo.addLine(1687, 1688, 1745)
gmsh.model.geo.addLine(1688, 1689, 1746)
gmsh.model.geo.addLine(1689, 1690, 1747)
gmsh.model.geo.addLine(1690, 1691, 1748)
gmsh.model.geo.addLine(1691, 1692, 1749)
gmsh.model.geo.addLine(1692, 1693, 1750)
gmsh.model.geo.addLine(1693, 1694, 1751)
gmsh.model.geo.addLine(1694, 1695, 1752)
gmsh.model.geo.addLine(1695, 1696, 1753)
gmsh.model.geo.addLine(1696, 1697, 1754)
gmsh.model.geo.addLine(1697, 1698, 1755)
gmsh.model.geo.addLine(1698, 1699, 1756)
gmsh.model.geo.addLine(1699, 1700, 1757)
gmsh.model.geo.addLine(1700, 1701, 1758)
gmsh.model.geo.addLine(1701, 1702, 1759)
gmsh.model.geo.addLine(1702, 1703, 1760)
gmsh.model.geo.addLine(1703, 1704, 1761)
gmsh.model.geo.addLine(1704, 1705, 1762)
gmsh.model.geo.addLine(1705, 1706, 1763)
gmsh.model.geo.addLine(1706, 1707, 1764)
gmsh.model.geo.addLine(1707, 1708, 1765)
gmsh.model.geo.addLine(1708, 1709, 1766)
gmsh.model.geo.addLine(1709, 1710, 1767)
gmsh.model.geo.addLine(1710, 1711, 1768)
gmsh.model.geo.addLine(1711, 1712, 1769)
gmsh.model.geo.addLine(1712, 1713, 1770)
gmsh.model.geo.addLine(1713, 1714, 1771)
gmsh.model.geo.addLine(1714, 1715, 1772)
gmsh.model.geo.addLine(1715, 1716, 1773)
gmsh.model.geo.addLine(1716, 1717, 1774)
gmsh.model.geo.addLine(1717, 1718, 1775)
gmsh.model.geo.addLine(1718, 1719, 1776)
gmsh.model.geo.addLine(1719, 1720, 1777)
gmsh.model.geo.addLine(1720, 1721, 1778)
gmsh.model.geo.addLine(1721, 1722, 1779)
gmsh.model.geo.addLine(1722, 1723, 1780)
gmsh.model.geo.addLine(1723, 1724, 1781)
gmsh.model.geo.addLine(1724, 1725, 1782)
gmsh.model.geo.addLine(1725, 1726, 1783)
gmsh.model.geo.addLine(1726, 1727, 1784)
gmsh.model.geo.addLine(1727, 1728, 1785)
gmsh.model.geo.addLine(1728, 1729, 1786)
gmsh.model.geo.addLine(1729, 1730, 1787)
gmsh.model.geo.addLine(1730, 1731, 1788)
gmsh.model.geo.addLine(1731, 1732, 1789)
gmsh.model.geo.addLine(1732, 1733, 1790)
gmsh.model.geo.addLine(1733, 1734, 1791)
gmsh.model.geo.addLine(1734, 1735, 1792)
gmsh.model.geo.addLine(1735, 1736, 1793)
gmsh.model.geo.addLine(1736, 1737, 1794)
gmsh.model.geo.addLine(1737, 1738, 1795)
gmsh.model.geo.addLine(1738, 1739, 1796)
gmsh.model.geo.addLine(1739, 1740, 1797)
gmsh.model.geo.addLine(1740, 1741, 1798)
gmsh.model.geo.addLine(1741, 1742, 1799)
gmsh.model.geo.addLine(1742, 1743, 1800)
gmsh.model.geo.addLine(1743, 1744, 1801)
gmsh.model.geo.addLine(1744, 1745, 1802)
gmsh.model.geo.addLine(1745, 1746, 1803)
gmsh.model.geo.addLine(1746, 1747, 1804)
gmsh.model.geo.addLine(1747, 1748, 1805)
gmsh.model.geo.addLine(1748, 1749, 1806)
gmsh.model.geo.addLine(1749, 1750, 1807)
gmsh.model.geo.addLine(1750, 1751, 1808)
gmsh.model.geo.addLine(1751, 1752, 1809)
gmsh.model.geo.addLine(1752, 1753, 1810)
gmsh.model.geo.addLine(1753, 1754, 1811)
gmsh.model.geo.addLine(1754, 1755, 1812)
gmsh.model.geo.addLine(1755, 1756, 1813)
gmsh.model.geo.addLine(1756, 1757, 1814)
gmsh.model.geo.addLine(1757, 1758, 1815)
gmsh.model.geo.addLine(1758, 1759, 1816)
gmsh.model.geo.addLine(1759, 1760, 1817)
gmsh.model.geo.addLine(1760, 1761, 1818)
gmsh.model.geo.addLine(1761, 1762, 1819)
gmsh.model.geo.addLine(1762, 1763, 1820)
gmsh.model.geo.addLine(1763, 1764, 1821)
gmsh.model.geo.addLine(1764, 1765, 1822)
gmsh.model.geo.addLine(1765, 1766, 1823)
gmsh.model.geo.addLine(1766, 1767, 1824)
gmsh.model.geo.addLine(1767, 1768, 1825)
gmsh.model.geo.addLine(1768, 1769, 1826)
gmsh.model.geo.addLine(1769, 1770, 1827)
gmsh.model.geo.addLine(1770, 1771, 1828)
gmsh.model.geo.addLine(1771, 1772, 1829)
gmsh.model.geo.addLine(1772, 1773, 1830)
gmsh.model.geo.addLine(1773, 1774, 1831)
gmsh.model.geo.addLine(1774, 1775, 1832)
gmsh.model.geo.addLine(1775, 1776, 1833)
gmsh.model.geo.addLine(1776, 1777, 1834)
gmsh.model.geo.addLine(1777, 1778, 1835)
gmsh.model.geo.addLine(1778, 1779, 1836)
gmsh.model.geo.addLine(1779, 1780, 1837)
gmsh.model.geo.addLine(1780, 1781, 1838)
gmsh.model.geo.addLine(1781, 1782, 1839)
gmsh.model.geo.addLine(1782, 1783, 1840)
gmsh.model.geo.addLine(1783, 1784, 1841)
gmsh.model.geo.addLine(1784, 1785, 1842)
gmsh.model.geo.addLine(1785, 1786, 1843)
gmsh.model.geo.addLine(1786, 1787, 1844)
gmsh.model.geo.addLine(1787, 1788, 1845)
gmsh.model.geo.addLine(1788, 1789, 1846)
gmsh.model.geo.addLine(1789, 1790, 1847)
gmsh.model.geo.addLine(1790, 1791, 1848)
gmsh.model.geo.addLine(1791, 1792, 1849)
gmsh.model.geo.addLine(1792, 1793, 1850)
gmsh.model.geo.addLine(1793, 1794, 1851)
gmsh.model.geo.addLine(1794, 1795, 1852)
gmsh.model.geo.addLine(1795, 1796, 1853)
gmsh.model.geo.addLine(1796, 1797, 1854)
gmsh.model.geo.addLine(1797, 1798, 1855)
gmsh.model.geo.addLine(1798, 1799, 1856)
gmsh.model.geo.addLine(1799, 1800, 1857)
gmsh.model.geo.addLine(1800, 1801, 1858)
gmsh.model.geo.addLine(1801, 1802, 1859)
gmsh.model.geo.addLine(1802, 1803, 1860)
gmsh.model.geo.addLine(1803, 1804, 1861)
gmsh.model.geo.addLine(1804, 1805, 1862)
gmsh.model.geo.addLine(1805, 1806, 1863)
gmsh.model.geo.addLine(1806, 1807, 1864)
gmsh.model.geo.addLine(1807, 1808, 1865)
gmsh.model.geo.addLine(1808, 1809, 1866)
gmsh.model.geo.addLine(1809, 1810, 1867)
gmsh.model.geo.addLine(1810, 1811, 1868)
gmsh.model.geo.addLine(1811, 1812, 1869)
gmsh.model.geo.addLine(1812, 1813, 1870)
gmsh.model.geo.addLine(1813, 1814, 1871)
gmsh.model.geo.addLine(1814, 1815, 1872)
gmsh.model.geo.addLine(1815, 1816, 1873)
gmsh.model.geo.addLine(1816, 1817, 1874)
gmsh.model.geo.addLine(1817, 1818, 1875)
gmsh.model.geo.addLine(1818, 1819, 1876)
gmsh.model.geo.addLine(1819, 1820, 1877)
gmsh.model.geo.addLine(1820, 1821, 1878)
gmsh.model.geo.addLine(1821, 1822, 1879)
gmsh.model.geo.addLine(1822, 1823, 1880)
gmsh.model.geo.addLine(1823, 1824, 1881)
gmsh.model.geo.addLine(1824, 1825, 1882)
gmsh.model.geo.addLine(1825, 1826, 1883)
gmsh.model.geo.addLine(1826, 1827, 1884)
gmsh.model.geo.addLine(1827, 1828, 1885)
gmsh.model.geo.addLine(1828, 1829, 1886)
gmsh.model.geo.addLine(1829, 1830, 1887)
gmsh.model.geo.addLine(1830, 1831, 1888)
gmsh.model.geo.addLine(1831, 1832, 1889)
gmsh.model.geo.addLine(1832, 1833, 1890)
gmsh.model.geo.addLine(1833, 1834, 1891)
gmsh.model.geo.addLine(1834, 1835, 1892)
gmsh.model.geo.addLine(1835, 1836, 1893)
gmsh.model.geo.addLine(1836, 1837, 1894)
gmsh.model.geo.addLine(1837, 1838, 1895)
gmsh.model.geo.addLine(1838, 1839, 1896)
gmsh.model.geo.addLine(1839, 1840, 1897)
gmsh.model.geo.addLine(1840, 1841, 1898)
gmsh.model.geo.addLine(1841, 1842, 1899)
gmsh.model.geo.addLine(1842, 1843, 1900)
gmsh.model.geo.addLine(1843, 1844, 1901)
gmsh.model.geo.addLine(1844, 1845, 1902)
gmsh.model.geo.addLine(1845, 1846, 1903)
gmsh.model.geo.addLine(1846, 1847, 1904)
gmsh.model.geo.addLine(1847, 1848, 1905)
gmsh.model.geo.addLine(1848, 1849, 1906)
gmsh.model.geo.addLine(1849, 1850, 1907)
gmsh.model.geo.addLine(1850, 1851, 1908)
gmsh.model.geo.addLine(1851, 1852, 1909)
gmsh.model.geo.addLine(1852, 1853, 1910)
gmsh.model.geo.addLine(1853, 1854, 1911)
gmsh.model.geo.addLine(1854, 1855, 1912)
gmsh.model.geo.addLine(1855, 1856, 1913)
gmsh.model.geo.addLine(1856, 1857, 1914)
gmsh.model.geo.addLine(1857, 1858, 1915)
gmsh.model.geo.addLine(1858, 1859, 1916)
gmsh.model.geo.addLine(1859, 1860, 1917)
gmsh.model.geo.addLine(1860, 1861, 1918)
gmsh.model.geo.addLine(1861, 1862, 1919)
gmsh.model.geo.addLine(1862, 1863, 1920)
gmsh.model.geo.addLine(1863, 1864, 1921)
gmsh.model.geo.addLine(1864, 1865, 1922)
gmsh.model.geo.addLine(1865, 1866, 1923)
gmsh.model.geo.addLine(1866, 1867, 1924)
gmsh.model.geo.addLine(1867, 1868, 1925)
gmsh.model.geo.addLine(1868, 1869, 1926)
gmsh.model.geo.addLine(1869, 1870, 1927)
gmsh.model.geo.addLine(1870, 1871, 1928)
gmsh.model.geo.addLine(1871, 1872, 1929)
gmsh.model.geo.addLine(1872, 1873, 1930)
gmsh.model.geo.addLine(1873, 1874, 1931)
gmsh.model.geo.addLine(1874, 1875, 1932)
gmsh.model.geo.addLine(1875, 1876, 1933)
gmsh.model.geo.addLine(1876, 1877, 1934)
gmsh.model.geo.addLine(1877, 1878, 1935)
gmsh.model.geo.addLine(1878, 1879, 1936)
gmsh.model.geo.addLine(1879, 1880, 1937)
gmsh.model.geo.addLine(1880, 1881, 1938)
gmsh.model.geo.addLine(1881, 1882, 1939)
gmsh.model.geo.addLine(1882, 1883, 1940)
gmsh.model.geo.addLine(1883, 1884, 1941)
gmsh.model.geo.addLine(1884, 1885, 1942)
gmsh.model.geo.addLine(1885, 1886, 1943)
gmsh.model.geo.addLine(1886, 1887, 1944)
gmsh.model.geo.addLine(1887, 1888, 1945)
gmsh.model.geo.addLine(1888, 1889, 1946)
gmsh.model.geo.addLine(1889, 1890, 1947)
gmsh.model.geo.addLine(1890, 1891, 1948)
gmsh.model.geo.addLine(1891, 1892, 1949)
gmsh.model.geo.addLine(1892, 1893, 1950)
gmsh.model.geo.addLine(1893, 1894, 1951)
gmsh.model.geo.addLine(1894, 1895, 1952)
gmsh.model.geo.addLine(1895, 1896, 1953)
gmsh.model.geo.addLine(1896, 1897, 1954)
gmsh.model.geo.addLine(1897, 1898, 1955)
gmsh.model.geo.addLine(1898, 1899, 1956)
gmsh.model.geo.addLine(1899, 1900, 1957)
gmsh.model.geo.addLine(1900, 1901, 1958)
gmsh.model.geo.addLine(1901, 1902, 1959)
gmsh.model.geo.addLine(1902, 1903, 1960)
gmsh.model.geo.addLine(1903, 1904, 1961)
gmsh.model.geo.addLine(1904, 1905, 1962)
gmsh.model.geo.addLine(1905, 1906, 1963)
gmsh.model.geo.addLine(1906, 1907, 1964)
gmsh.model.geo.addLine(1907, 1908, 1965)
gmsh.model.geo.addLine(1908, 1909, 1966)
gmsh.model.geo.addLine(1909, 1910, 1967)
gmsh.model.geo.addLine(1910, 1911, 1968)
gmsh.model.geo.addLine(1911, 1912, 1969)
gmsh.model.geo.addLine(1912, 1913, 1970)
gmsh.model.geo.addLine(1913, 1914, 1971)
gmsh.model.geo.addLine(1914, 1915, 1972)
gmsh.model.geo.addLine(1915, 1916, 1973)
gmsh.model.geo.addLine(1916, 1917, 1974)
gmsh.model.geo.addLine(1917, 1918, 1975)
gmsh.model.geo.addLine(1918, 1919, 1976)
gmsh.model.geo.addLine(1919, 1920, 1977)
gmsh.model.geo.addLine(1920, 1921, 1978)
gmsh.model.geo.addLine(1921, 1922, 1979)
gmsh.model.geo.addLine(1922, 1923, 1980)
gmsh.model.geo.addLine(1923, 1924, 1981)
gmsh.model.geo.addLine(1924, 1925, 1982)
gmsh.model.geo.addLine(1925, 1926, 1983)
gmsh.model.geo.addLine(1926, 1927, 1984)
gmsh.model.geo.addLine(1927, 1928, 1985)
gmsh.model.geo.addLine(1928, 1929, 1986)
gmsh.model.geo.addLine(1929, 1930, 1987)
gmsh.model.geo.addLine(1930, 1931, 1988)
gmsh.model.geo.addLine(1931, 1932, 1989)
gmsh.model.geo.addLine(1932, 1933, 1990)
gmsh.model.geo.addLine(1933, 1934, 1991)
gmsh.model.geo.addLine(1934, 1935, 1992)
gmsh.model.geo.addLine(1935, 1936, 1993)
gmsh.model.geo.addLine(1936, 1937, 1994)
gmsh.model.geo.addLine(1937, 1938, 1995)
gmsh.model.geo.addLine(1938, 1939, 1996)
gmsh.model.geo.addLine(1939, 1940, 1997)
gmsh.model.geo.addLine(1940, 1941, 1998)
gmsh.model.geo.addLine(1941, 1942, 1999)
gmsh.model.geo.addLine(1942, 1943, 2000)
gmsh.model.geo.addLine(1943, 1944, 2001)
gmsh.model.geo.addLine(1944, 1945, 2002)
gmsh.model.geo.addLine(1945, 1946, 2003)
gmsh.model.geo.addLine(1946, 1947, 2004)
gmsh.model.geo.addLine(1947, 1948, 2005)
gmsh.model.geo.addLine(1948, 1949, 2006)
gmsh.model.geo.addLine(1949, 1950, 2007)
gmsh.model.geo.addLine(1950, 1951, 2008)
gmsh.model.geo.addLine(1951, 1952, 2009)
gmsh.model.geo.addLine(1952, 1953, 2010)
gmsh.model.geo.addLine(1953, 1954, 2011)
gmsh.model.geo.addLine(1954, 1955, 2012)
gmsh.model.geo.addLine(1955, 1956, 2013)
gmsh.model.geo.addLine(1956, 1957, 2014)
gmsh.model.geo.addLine(1957, 1958, 2015)
gmsh.model.geo.addLine(1958, 1959, 2016)
gmsh.model.geo.addLine(1959, 1960, 2017)
gmsh.model.geo.addLine(1960, 1961, 2018)
gmsh.model.geo.addLine(1961, 1962, 2019)
gmsh.model.geo.addLine(1962, 1963, 2020)
gmsh.model.geo.addLine(1963, 1964, 2021)
gmsh.model.geo.addLine(1964, 1965, 2022)
gmsh.model.geo.addLine(1965, 1966, 2023)
gmsh.model.geo.addLine(1966, 1967, 2024)
gmsh.model.geo.addLine(1967, 1968, 2025)
gmsh.model.geo.addLine(1968, 1969, 2026)
gmsh.model.geo.addLine(1969, 1970, 2027)
gmsh.model.geo.addLine(1970, 1971, 2028)
gmsh.model.geo.addLine(1971, 1972, 2029)
gmsh.model.geo.addLine(1972, 1973, 2030)
gmsh.model.geo.addLine(1973, 1974, 2031)
gmsh.model.geo.addLine(1974, 1975, 2032)
gmsh.model.geo.addLine(1975, 1976, 2033)
gmsh.model.geo.addLine(1976, 1977, 2034)
gmsh.model.geo.addLine(1977, 1978, 2035)
gmsh.model.geo.addLine(1978, 1979, 2036)
gmsh.model.geo.addLine(1979, 1980, 2037)
gmsh.model.geo.addLine(1980, 1981, 2038)
gmsh.model.geo.addLine(1981, 1982, 2039)
gmsh.model.geo.addLine(1982, 1983, 2040)
gmsh.model.geo.addLine(1983, 1984, 2041)
gmsh.model.geo.addLine(1984, 1985, 2042)
gmsh.model.geo.addLine(1985, 1986, 2043)
gmsh.model.geo.addLine(1986, 1987, 2044)
gmsh.model.geo.addLine(1987, 1988, 2045)
gmsh.model.geo.addLine(1988, 1989, 2046)
gmsh.model.geo.addLine(1989, 1990, 2047)
gmsh.model.geo.addLine(1990, 1991, 2048)
gmsh.model.geo.addLine(1991, 1992, 2049)
gmsh.model.geo.addLine(1992, 1993, 2050)
gmsh.model.geo.addLine(1993, 1994, 2051)
gmsh.model.geo.addLine(1994, 1995, 2052)
gmsh.model.geo.addLine(1995, 1996, 2053)
gmsh.model.geo.addLine(1996, 1997, 2054)
gmsh.model.geo.addLine(1997, 1998, 2055)
gmsh.model.geo.addLine(1998, 1999, 2056)
gmsh.model.geo.addLine(1999, 2000, 2057)
gmsh.model.geo.addLine(2000, 2001, 2058)
gmsh.model.geo.addLine(2001, 2002, 2059)
gmsh.model.geo.addLine(2002, 2003, 2060)
gmsh.model.geo.addLine(2003, 2004, 2061)
gmsh.model.geo.addLine(2004, 2005, 2062)
gmsh.model.geo.addLine(2005, 2006, 2063)
gmsh.model.geo.addLine(2006, 2007, 2064)
gmsh.model.geo.addLine(2007, 2008, 2065)
gmsh.model.geo.addLine(2008, 2009, 2066)
gmsh.model.geo.addLine(2009, 2010, 2067)
gmsh.model.geo.addLine(2010, 2011, 2068)
gmsh.model.geo.addLine(2011, 2012, 2069)
gmsh.model.geo.addLine(2012, 2013, 2070)
gmsh.model.geo.addLine(2013, 2014, 2071)
gmsh.model.geo.addLine(2014, 2015, 2072)
gmsh.model.geo.addLine(2015, 2016, 2073)
gmsh.model.geo.addLine(2016, 2017, 2074)
gmsh.model.geo.addLine(2017, 2018, 2075)
gmsh.model.geo.addLine(2018, 2019, 2076)
gmsh.model.geo.addLine(2019, 2020, 2077)
gmsh.model.geo.addLine(2020, 2021, 2078)
gmsh.model.geo.addLine(2021, 2022, 2079)
gmsh.model.geo.addLine(2022, 2023, 2080)
gmsh.model.geo.addLine(2023, 2024, 2081)
gmsh.model.geo.addLine(2024, 2025, 2082)
gmsh.model.geo.addLine(2025, 2026, 2083)
gmsh.model.geo.addLine(2026, 2027, 2084)
gmsh.model.geo.addLine(2027, 2028, 2085)
gmsh.model.geo.addLine(2028, 2029, 2086)
gmsh.model.geo.addLine(2029, 2030, 2087)
gmsh.model.geo.addLine(2030, 2031, 2088)
gmsh.model.geo.addLine(2031, 2032, 2089)
gmsh.model.geo.addLine(2032, 2033, 2090)
gmsh.model.geo.addLine(2033, 2034, 2091)
gmsh.model.geo.addLine(2034, 2035, 2092)
gmsh.model.geo.addLine(2035, 2036, 2093)
gmsh.model.geo.addLine(2036, 2037, 2094)
gmsh.model.geo.addLine(2037, 2038, 2095)
gmsh.model.geo.addLine(2038, 2039, 2096)
gmsh.model.geo.addLine(2039, 2040, 2097)
gmsh.model.geo.addLine(2040, 2041, 2098)
gmsh.model.geo.addLine(2041, 2042, 2099)
gmsh.model.geo.addLine(2042, 2043, 2100)
gmsh.model.geo.addLine(2043, 2044, 2101)
gmsh.model.geo.addLine(2044, 2045, 2102)
gmsh.model.geo.addLine(2045, 2046, 2103)
gmsh.model.geo.addLine(2046, 2047, 2104)
gmsh.model.geo.addLine(2047, 2048, 2105)
gmsh.model.geo.addLine(2048, 2049, 2106)
gmsh.model.geo.addLine(2049, 2050, 2107)
gmsh.model.geo.addLine(2050, 2051, 2108)
gmsh.model.geo.addLine(2051, 2052, 2109)
gmsh.model.geo.addLine(2052, 2053, 2110)
gmsh.model.geo.addLine(2053, 2054, 2111)
gmsh.model.geo.addLine(2054, 2055, 2112)
gmsh.model.geo.addLine(2055, 2056, 2113)
gmsh.model.geo.addLine(2056, 2057, 2114)
gmsh.model.geo.addLine(2057, 2058, 2115)
gmsh.model.geo.addLine(2058, 2059, 2116)
gmsh.model.geo.addLine(2059, 2060, 2117)
gmsh.model.geo.addLine(2060, 2061, 2118)
gmsh.model.geo.addLine(2061, 2062, 2119)
gmsh.model.geo.addLine(2062, 2063, 2120)
gmsh.model.geo.addLine(2063, 2064, 2121)
gmsh.model.geo.addLine(2064, 2065, 2122)
gmsh.model.geo.addLine(2065, 2066, 2123)
gmsh.model.geo.addLine(2066, 2067, 2124)
gmsh.model.geo.addLine(2067, 2068, 2125)
gmsh.model.geo.addLine(2068, 2069, 2126)
gmsh.model.geo.addLine(2069, 2070, 2127)
gmsh.model.geo.addLine(2070, 2071, 2128)
gmsh.model.geo.addLine(2071, 2072, 2129)
gmsh.model.geo.addLine(2072, 2073, 2130)
gmsh.model.geo.addLine(2073, 2074, 2131)
gmsh.model.geo.addLine(2074, 2075, 2132)
gmsh.model.geo.addLine(2075, 2076, 2133)
gmsh.model.geo.addLine(2076, 2077, 2134)
gmsh.model.geo.addLine(2077, 2078, 2135)
gmsh.model.geo.addLine(2078, 2079, 2136)
gmsh.model.geo.addLine(2079, 2080, 2137)
gmsh.model.geo.addLine(2080, 2081, 2138)
gmsh.model.geo.addLine(2081, 2082, 2139)
gmsh.model.geo.addLine(2082, 2083, 2140)
gmsh.model.geo.addLine(2083, 2084, 2141)
gmsh.model.geo.addLine(2084, 2085, 2142)
gmsh.model.geo.addLine(2085, 2086, 2143)
gmsh.model.geo.addLine(2086, 2087, 2144)
gmsh.model.geo.addLine(2087, 2088, 2145)
gmsh.model.geo.addLine(2088, 2089, 2146)
gmsh.model.geo.addLine(2089, 2090, 2147)
gmsh.model.geo.addLine(2090, 2091, 2148)
gmsh.model.geo.addLine(2091, 2092, 2149)
gmsh.model.geo.addLine(2092, 2093, 2150)
gmsh.model.geo.addLine(2093, 2094, 2151)
gmsh.model.geo.addLine(2094, 2095, 2152)
gmsh.model.geo.addLine(2095, 1343, 2153)
gmsh.model.geo.addLine(2096, 2097, 2155)
gmsh.model.geo.addLine(2097, 2098, 2156)
gmsh.model.geo.addLine(2098, 2099, 2157)
gmsh.model.geo.addLine(2099, 2100, 2158)
gmsh.model.geo.addLine(2100, 2101, 2159)
gmsh.model.geo.addLine(2101, 2102, 2160)
gmsh.model.geo.addLine(2102, 2103, 2161)
gmsh.model.geo.addLine(2103, 2104, 2162)
gmsh.model.geo.addLine(2104, 2105, 2163)
gmsh.model.geo.addLine(2105, 2106, 2164)
gmsh.model.geo.addLine(2106, 2107, 2165)
gmsh.model.geo.addLine(2107, 2108, 2166)
gmsh.model.geo.addLine(2108, 2109, 2167)
gmsh.model.geo.addLine(2109, 2110, 2168)
gmsh.model.geo.addLine(2110, 2111, 2169)
gmsh.model.geo.addLine(2111, 2112, 2170)
gmsh.model.geo.addLine(2112, 2113, 2171)
gmsh.model.geo.addLine(2113, 2114, 2172)
gmsh.model.geo.addLine(2114, 2115, 2173)
gmsh.model.geo.addLine(2115, 2116, 2174)
gmsh.model.geo.addLine(2116, 2117, 2175)
gmsh.model.geo.addLine(2117, 2118, 2176)
gmsh.model.geo.addLine(2118, 2119, 2177)
gmsh.model.geo.addLine(2119, 2120, 2178)
gmsh.model.geo.addLine(2120, 2121, 2179)
gmsh.model.geo.addLine(2121, 2122, 2180)
gmsh.model.geo.addLine(2122, 2123, 2181)
gmsh.model.geo.addLine(2123, 2124, 2182)
gmsh.model.geo.addLine(2124, 2125, 2183)
gmsh.model.geo.addLine(2125, 2126, 2184)
gmsh.model.geo.addLine(2126, 2127, 2185)
gmsh.model.geo.addLine(2127, 2128, 2186)
gmsh.model.geo.addLine(2128, 2129, 2187)
gmsh.model.geo.addLine(2129, 2130, 2188)
gmsh.model.geo.addLine(2130, 2131, 2189)
gmsh.model.geo.addLine(2131, 2132, 2190)
gmsh.model.geo.addLine(2132, 2133, 2191)
gmsh.model.geo.addLine(2133, 2134, 2192)
gmsh.model.geo.addLine(2134, 2135, 2193)
gmsh.model.geo.addLine(2135, 2136, 2194)
gmsh.model.geo.addLine(2136, 2137, 2195)
gmsh.model.geo.addLine(2137, 2138, 2196)
gmsh.model.geo.addLine(2138, 2139, 2197)
gmsh.model.geo.addLine(2139, 2140, 2198)
gmsh.model.geo.addLine(2140, 2141, 2199)
gmsh.model.geo.addLine(2141, 2142, 2200)
gmsh.model.geo.addLine(2142, 2143, 2201)
gmsh.model.geo.addLine(2143, 2144, 2202)
gmsh.model.geo.addLine(2144, 2145, 2203)
gmsh.model.geo.addLine(2145, 2146, 2204)
gmsh.model.geo.addLine(2146, 2147, 2205)
gmsh.model.geo.addLine(2147, 2148, 2206)
gmsh.model.geo.addLine(2148, 2149, 2207)
gmsh.model.geo.addLine(2149, 2150, 2208)
gmsh.model.geo.addLine(2150, 2151, 2209)
gmsh.model.geo.addLine(2151, 2152, 2210)
gmsh.model.geo.addLine(2152, 2153, 2211)
gmsh.model.geo.addLine(2153, 2154, 2212)
gmsh.model.geo.addLine(2154, 2155, 2213)
gmsh.model.geo.addLine(2155, 2156, 2214)
gmsh.model.geo.addLine(2156, 2157, 2215)
gmsh.model.geo.addLine(2157, 2158, 2216)
gmsh.model.geo.addLine(2158, 2159, 2217)
gmsh.model.geo.addLine(2159, 2160, 2218)
gmsh.model.geo.addLine(2160, 2161, 2219)
gmsh.model.geo.addLine(2161, 2162, 2220)
gmsh.model.geo.addLine(2162, 2163, 2221)
gmsh.model.geo.addLine(2163, 2164, 2222)
gmsh.model.geo.addLine(2164, 2165, 2223)
gmsh.model.geo.addLine(2165, 2166, 2224)
gmsh.model.geo.addLine(2166, 2167, 2225)
gmsh.model.geo.addLine(2167, 2168, 2226)
gmsh.model.geo.addLine(2168, 2169, 2227)
gmsh.model.geo.addLine(2169, 2170, 2228)
gmsh.model.geo.addLine(2170, 2171, 2229)
gmsh.model.geo.addLine(2171, 2172, 2230)
gmsh.model.geo.addLine(2172, 2173, 2231)
gmsh.model.geo.addLine(2173, 2174, 2232)
gmsh.model.geo.addLine(2174, 2175, 2233)
gmsh.model.geo.addLine(2175, 2176, 2234)
gmsh.model.geo.addLine(2176, 2177, 2235)
gmsh.model.geo.addLine(2177, 2178, 2236)
gmsh.model.geo.addLine(2178, 2179, 2237)
gmsh.model.geo.addLine(2179, 2180, 2238)
gmsh.model.geo.addLine(2180, 2181, 2239)
gmsh.model.geo.addLine(2181, 2182, 2240)
gmsh.model.geo.addLine(2182, 2183, 2241)
gmsh.model.geo.addLine(2183, 2184, 2242)
gmsh.model.geo.addLine(2184, 2185, 2243)
gmsh.model.geo.addLine(2185, 2186, 2244)
gmsh.model.geo.addLine(2186, 2187, 2245)
gmsh.model.geo.addLine(2187, 2188, 2246)
gmsh.model.geo.addLine(2188, 2189, 2247)
gmsh.model.geo.addLine(2189, 2190, 2248)
gmsh.model.geo.addLine(2190, 2191, 2249)
gmsh.model.geo.addLine(2191, 2192, 2250)
gmsh.model.geo.addLine(2192, 2193, 2251)
gmsh.model.geo.addLine(2193, 2194, 2252)
gmsh.model.geo.addLine(2194, 2195, 2253)
gmsh.model.geo.addLine(2195, 2196, 2254)
gmsh.model.geo.addLine(2196, 2197, 2255)
gmsh.model.geo.addLine(2197, 2198, 2256)
gmsh.model.geo.addLine(2198, 2199, 2257)
gmsh.model.geo.addLine(2199, 2200, 2258)
gmsh.model.geo.addLine(2200, 2201, 2259)
gmsh.model.geo.addLine(2201, 2202, 2260)
gmsh.model.geo.addLine(2202, 2203, 2261)
gmsh.model.geo.addLine(2203, 2204, 2262)
gmsh.model.geo.addLine(2204, 2205, 2263)
gmsh.model.geo.addLine(2205, 2206, 2264)
gmsh.model.geo.addLine(2206, 2207, 2265)
gmsh.model.geo.addLine(2207, 2208, 2266)
gmsh.model.geo.addLine(2208, 2209, 2267)
gmsh.model.geo.addLine(2209, 2210, 2268)
gmsh.model.geo.addLine(2210, 2211, 2269)
gmsh.model.geo.addLine(2211, 2212, 2270)
gmsh.model.geo.addLine(2212, 2213, 2271)
gmsh.model.geo.addLine(2213, 2214, 2272)
gmsh.model.geo.addLine(2214, 2215, 2273)
gmsh.model.geo.addLine(2215, 2216, 2274)
gmsh.model.geo.addLine(2216, 2217, 2275)
gmsh.model.geo.addLine(2217, 2218, 2276)
gmsh.model.geo.addLine(2218, 2219, 2277)
gmsh.model.geo.addLine(2219, 2220, 2278)
gmsh.model.geo.addLine(2220, 2221, 2279)
gmsh.model.geo.addLine(2221, 2222, 2280)
gmsh.model.geo.addLine(2222, 2223, 2281)
gmsh.model.geo.addLine(2223, 2224, 2282)
gmsh.model.geo.addLine(2224, 2225, 2283)
gmsh.model.geo.addLine(2225, 2226, 2284)
gmsh.model.geo.addLine(2226, 2227, 2285)
gmsh.model.geo.addLine(2227, 2228, 2286)
gmsh.model.geo.addLine(2228, 2229, 2287)
gmsh.model.geo.addLine(2229, 2230, 2288)
gmsh.model.geo.addLine(2230, 2231, 2289)
gmsh.model.geo.addLine(2231, 2232, 2290)
gmsh.model.geo.addLine(2232, 2233, 2291)
gmsh.model.geo.addLine(2233, 2234, 2292)
gmsh.model.geo.addLine(2234, 2235, 2293)
gmsh.model.geo.addLine(2235, 2096, 2294)

cl1 = gmsh.model.geo.addCurveLoop([2, 3, 4, 5])
cl2 = gmsh.model.geo.addCurveLoop([10, 7, 8, 9])
cl3 = gmsh.model.geo.addCurveLoop([15, 12, 13, 14])
cl4 = gmsh.model.geo.addCurveLoop([20, 17, 18, 19])
cl5 = gmsh.model.geo.addCurveLoop([26, 22, 23, 24, 25])
cl6 = gmsh.model.geo.addCurveLoop([32, 28, 29, 30, 31])
cl7 = gmsh.model.geo.addCurveLoop([37, 34, 35, 36])
cl8 = gmsh.model.geo.addCurveLoop([44, 39, 40, 41, 42, 43])
cl9 = gmsh.model.geo.addCurveLoop([50, 46, 47, 48, 49])
cl10 = gmsh.model.geo.addCurveLoop([57, 52, 53, 54, 55, 56])
cl11 = gmsh.model.geo.addCurveLoop([64, 59, 60, 61, 62, 63])
cl12 = gmsh.model.geo.addCurveLoop([71, 66, 67, 68, 69, 70])
cl13 = gmsh.model.geo.addCurveLoop([79, 73, 74, 75, 76, 77, 78])
cl14 = gmsh.model.geo.addCurveLoop([86, 81, 82, 83, 84, 85])
cl15 = gmsh.model.geo.addCurveLoop([94, 88, 89, 90, 91, 92, 93])
cl16 = gmsh.model.geo.addCurveLoop([100, 96, 97, 98, 99])
cl17 = gmsh.model.geo.addCurveLoop([107, 102, 103, 104, 105, 106])
cl18 = gmsh.model.geo.addCurveLoop([113, 109, 110, 111, 112])
cl19 = gmsh.model.geo.addCurveLoop([122, 115, 116, 117, 118, 119, 120, 121])
cl20 = gmsh.model.geo.addCurveLoop([128, 124, 125, 126, 127])
cl21 = gmsh.model.geo.addCurveLoop([138, 130, 131, 132, 133, 134, 135, 136, 137])
cl22 = gmsh.model.geo.addCurveLoop([146, 140, 141, 142, 143, 144, 145])
cl23 = gmsh.model.geo.addCurveLoop([156, 148, 149, 150, 151, 152, 153, 154, 155])
cl24 = gmsh.model.geo.addCurveLoop([162, 158, 159, 160, 161])
cl25 = gmsh.model.geo.addCurveLoop([168, 164, 165, 166, 167])
cl26 = gmsh.model.geo.addCurveLoop([174, 170, 171, 172, 173])
cl27 = gmsh.model.geo.addCurveLoop([185, 176, 177, 178, 179, 180, 181, 182, 183, 184])
cl28 = gmsh.model.geo.addCurveLoop([198, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197])
cl29 = gmsh.model.geo.addCurveLoop([207, 200, 201, 202, 203, 204, 205, 206])
cl30 = gmsh.model.geo.addCurveLoop([220, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219])
cl31 = gmsh.model.geo.addCurveLoop([234, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233])
cl32 = gmsh.model.geo.addCurveLoop([242, 236, 237, 238, 239, 240, 241])
cl33 = gmsh.model.geo.addCurveLoop([255, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254])
cl34 = gmsh.model.geo.addCurveLoop([267, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266])
cl35 = gmsh.model.geo.addCurveLoop([275, 269, 270, 271, 272, 273, 274])
cl36 = gmsh.model.geo.addCurveLoop([291, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290])
cl37 = gmsh.model.geo.addCurveLoop([310, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309])
cl38 = gmsh.model.geo.addCurveLoop([330, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329])
cl39 = gmsh.model.geo.addCurveLoop([335, 332, 333, 334])
cl40 = gmsh.model.geo.addCurveLoop([343, 337, 338, 339, 340, 341, 342])
cl41 = gmsh.model.geo.addCurveLoop([356, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355])
cl42 = gmsh.model.geo.addCurveLoop([372, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371])
cl43 = gmsh.model.geo.addCurveLoop([394, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393])
cl44 = gmsh.model.geo.addCurveLoop([413, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412])
cl45 = gmsh.model.geo.addCurveLoop([438, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437])
cl46 = gmsh.model.geo.addCurveLoop([455, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454])
cl47 = gmsh.model.geo.addCurveLoop([495, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494])
cl48 = gmsh.model.geo.addCurveLoop([504, 497, 498, 499, 500, 501, 502, 503])
cl49 = gmsh.model.geo.addCurveLoop([532, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531])
cl50 = gmsh.model.geo.addCurveLoop([562, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561])
cl51 = gmsh.model.geo.addCurveLoop([594, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593])
cl52 = gmsh.model.geo.addCurveLoop([607, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606])
cl53 = gmsh.model.geo.addCurveLoop([678, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677])
cl54 = gmsh.model.geo.addCurveLoop([687, 680, 681, 682, 683, 684, 685, 686])
cl55 = gmsh.model.geo.addCurveLoop([716, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715])
cl56 = gmsh.model.geo.addCurveLoop([732, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731])
cl57 = gmsh.model.geo.addCurveLoop([780, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779])
cl58 = gmsh.model.geo.addCurveLoop([891, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890])
cl59 = gmsh.model.geo.addCurveLoop([1399, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108, 1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1156, 1157, 1158, 1159, 1160, 1161, 1162, 1163, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1172, 1173, 1174, 1175, 1176, 1177, 1178, 1179, 1180, 1181, 1182, 1183, 1184, 1185, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1194, 1195, 1196, 1197, 1198, 1199, 1200, 1201, 1202, 1203, 1204, 1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1236, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248, 1249, 1250, 1251, 1252, 1253, 1254, 1255, 1256, 1257, 1258, 1259, 1260, 1261, 1262, 1263, 1264, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277, 1278, 1279, 1280, 1281, 1282, 1283, 1284, 1285, 1286, 1287, 1288, 1289, 1290, 1291, 1292, 1293, 1294, 1295, 1296, 1297, 1298, 1299, 1300, 1301, 1302, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1310, 1311, 1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1320, 1321, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345, 1346, 1347, 1348, 1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389, 1390, 1391, 1392, 1393, 1394, 1395, 1396, 1397, 1398])
cl60 = gmsh.model.geo.addCurveLoop([2153, 1401, 1402, 1403, 1404, 1405, 1406, 1407, 1408, 1409, 1410, 1411, 1412, 1413, 1414, 1415, 1416, 1417, 1418, 1419, 1420, 1421, 1422, 1423, 1424, 1425, 1426, 1427, 1428, 1429, 1430, 1431, 1432, 1433, 1434, 1435, 1436, 1437, 1438, 1439, 1440, 1441, 1442, 1443, 1444, 1445, 1446, 1447, 1448, 1449, 1450, 1451, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, 1460, 1461, 1462, 1463, 1464, 1465, 1466, 1467, 1468, 1469, 1470, 1471, 1472, 1473, 1474, 1475, 1476, 1477, 1478, 1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517, 1518, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1533, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 1543, 1544, 1545, 1546, 1547, 1548, 1549, 1550, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 1630, 1631, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646, 1647, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1671, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1692, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1732, 1733, 1734, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152])
cl61 = gmsh.model.geo.addCurveLoop([2294, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 2260, 2261, 2262, 2263, 2264, 2265, 2266, 2267, 2268, 2269, 2270, 2271, 2272, 2273, 2274, 2275, 2276, 2277, 2278, 2279, 2280, 2281, 2282, 2283, 2284, 2285, 2286, 2287, 2288, 2289, 2290, 2291, 2292, 2293])

gmsh.model.geo.addPlaneSurface([cl1, cl2, cl3, cl4, cl5, cl6, cl7, cl8, cl9, cl10, cl11, cl12, cl13, cl14, cl15, cl16, cl17, cl18, cl19, cl20, cl21, cl22, cl23, cl24, cl25, cl26, cl27, cl28, cl29, cl30, cl31, cl32, cl33, cl34, cl35, cl36, cl37, cl38, cl39, cl40, cl41, cl42, cl43, cl44, cl45, cl46, cl47, cl48, cl49, cl50, cl51, cl52, cl53, cl54, cl55, cl56, cl57, cl58, cl59, cl60, cl61])
gmsh.model.geo.synchronize()

gmsh.model.mesh.field.add("Distance", 1)
gmsh.model.mesh.field.setNumbers(1, "CurvesList", [d[1] for d in gmsh.model.getEntities(1)])

gmsh.model.mesh.field.add("Threshold", 2)
gmsh.model.mesh.field.setNumber(2, "InField", 1)
gmsh.model.mesh.field.setNumber(2, "SizeMin", 0.01)
gmsh.model.mesh.field.setNumber(2, "SizeMax", 0.1)
gmsh.model.mesh.field.setNumber(2, "DistMin", 0.01)
gmsh.model.mesh.field.setNumber(2, "DistMax", 0.2)

gmsh.model.mesh.field.setAsBackgroundMesh(2)

gmsh.option.setNumber("Mesh.MeshSizeExtendFromBoundary", 0)

if '-nopopup' not in sys.argv:
    gmsh.fltk.run()

gmsh.finalize()
