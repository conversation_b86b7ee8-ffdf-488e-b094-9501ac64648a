// -----------------------------------------------------------------------------
//
//  Gmsh tutorial 1 - appendix 4
//
//  2D graphs, text annotations (in parsed format)
//
// -----------------------------------------------------------------------------

View "e" {
SP(0.05,0,0){3878262,3878766.4,3880412.5,3882811.2,3885521.1};
SP(0.05,0.003,0){3881437.5,3877557.5,3870436.7,3859692,3844885.1};
SP(0.05,0.006,0){3881035,3865563.5,3835303,3789965.2,3729266};
SP(0.05,0.009,0){3877285.3,3841960.1,3772279,3668541.8,3531342.7};
SP(0.05,0.012,0){3872834.7,3811664.2,3690903.7,3512085.7,3277631.1};
SP(0.05,0.015,0){3867665,3772483.8,3585037.2,3309516.4,2952240.9};
SP(0.05,0.018,0){3864583.2,3727623.6,3459110.9,3068139.7,2568116.7};
SP(0.05,0.021,0){3858805,3672801.8,3310287,2788309.5,2131593.3};
SP(0.05,0.024,0){3852670.1,3609396.1,3139010.6,2471357.5,1648926};
SP(0.05,0.027,0){3842734.9,3537490.9,2951768.8,2131756.3,1142267.4};
SP(0.05,0.03,0){3834225,3458862.1,2745296.7,1762920.7,607458.49};
SP(0.05,0.033,0){3824633.1,3371923.9,2520730.5,1371801.1,61237.585};
SP(0.05,0.036,0){3810803.4,3275453.4,2280386.6,965079.73,-485928.87};
SP(0.05,0.039,0){3800151.3,3175017.3,2027930.5,547204.34,-1023890.6};
SP(0.05,0.042,0){3787912.5,3066455,1761589.2,122007.33,-1539738.3};
SP(0.05,0.045,0){3775680.7,2952282,1485702.3,-304002.73,-2026253.9};
SP(0.05,0.048,0){3759484.2,2829182.7,1199302.2,-726763.53,-2472258.1};
SP(0.05,0.051,0){3742542.8,2699920.4,905633.67,-1140360.6,-2867954.2};
SP(0.05,0.054,0){3727173.7,2566549.6,607179.56,-1540693.8,-3208103.2};
SP(0.05,0.057,0){3709078.7,2426207.2,304406.61,-1922592.8,-3484505.1};
SP(0.05,0.06,0){3691800.9,2281348.9,-3.5647049,-2280189.1,-3687827.6};
SP(0.05,0.063,0){3671667.6,2130576.6,-304402.31,-2611120.9,-3821274.2};
SP(0.05,0.066,0){3651083,1975304.6,-607113.82,-2911137,-3879145.4};
SP(0.05,0.069,0){3632561.9,1817042.9,-906700.48,-3177626.4,-3860572.9};
SP(0.05,0.072,0){3607111.4,1651890.1,-1199327.6,-3401740.2,-3761153.5};
SP(0.05,0.075,0){3585438.1,1484975.3,-1485018.3,-3584367,-3583333.7};
SP(0.05,0.078,0){3560908.8,1314302.8,-1761236,-3725162.8,-3338501.4};
SP(0.05,0.081,0){3537675.2,1141306.9,-2027899.1,-3822957.5,-3027978.9};
SP(0.05,0.084,0){3510509.8,964930.16,-2280600.6,-3872688.1,-2656307.5};
SP(0.05,0.087,0){3487249.2,787614.54,-2522870.3,-3881805.7,-2235414.9};
SP(0.05,0.09,0){3456031.4,606908.44,-2743575.4,-3833562.1,-1762742.7};
SP(0.05,0.093,0){3429425.5,425969.02,-2951002.1,-3743949.8,-1257540.5};
SP(0.05,0.096,0){3400371.9,243792.05,-3139424.2,-3608533,-727411.18};
SP(0.05,0.099,0){3370583.5,61029.471,-3308065.3,-3428479.9,-182751.63};
SP(0.05,0.102,0){3341014.8,-121746.78,-3457880.8,-3209597.5,364802.59};
SP(0.05,0.105,0){3308134.5,-304377.88,-3583992.7,-2949335.2,905332.03};
SP(0.05,0.108,0){3276560.2,-486276.87,-3690133.4,-2655722.1,1427699.9};
SP(0.05,0.111,0){3243913.1,-667300.57,-3774045.4,-2330325.5,1923230.8};
SP(0.05,0.114,0){3208352.9,-846274.92,-3832187.1,-1975367.7,2379188.8};
SP(0.05,0.117,0){3173648.8,-1023678.8,-3867662.5,-1596576.6,2786917.2};
SP(0.05,0.12,0){3140687.3,-1199706.4,-3881794.4,-1199087.5,3139877.1};
SP(0.05,0.123,0){3102809.3,-1371676.4,-3868635.8,-786672.55,3430645.3};
SP(0.05,0.126,0){3067420.6,-1542050.8,-3835669.6,-365295.18,3656832.2};
SP(0.05,0.129,0){3025992.4,-1706179,-3772055.1,61206.515,3802657.7};
SP(0.05,0.132,0){2992178.6,-1871219.6,-3695085.3,487367.15,3881535.5};
SP(0.05,0.135,0){2948414.3,-2026214.3,-3583412.6,905791.21,3869016.3};
SP(0.05,0.138,0){2911948.3,-2182181.8,-3459620.5,1315420.9,3790462.5};
SP(0.05,0.141,0){2869275.6,-2329568.2,-3308860.3,1707795,3632603.9};
SP(0.05,0.144,0){2828899.8,-2473908.9,-3140082.6,2080137.3,3402041.6};
SP(0.05,0.147,0){2786737.3,-2612003.8,-2951491,2427438.9,3105042.9};
SP(0.05,0.15,0){2743192.7,-2743672.3,-2744324.2,2745284.2,2746379.5};
SP(0.05,0.153,0){2700298.2,-2869946.2,-2519524,3027124,2327818.5};
SP(0.05,0.156,0){2656932.4,-2990499.1,-2280908.2,3275340.7,1868243.4};
SP(0.05,0.159,0){2612112.7,-3103860.9,-2027752.2,3484687,1371243.1};
SP(0.05,0.162,0){2566432.2,-3209591.3,-1761406.6,3649048.2,845644.4};
SP(0.05,0.165,0){2521549.2,-3310234.1,-1485636.9,3773038.2,304567.55};
SP(0.05,0.168,0){2473657.2,-3400757.2,-1199380.8,3849737.1,-243531.63};
SP(0.05,0.171,0){2425158.9,-3483580.5,-905808.11,3879638.4,-786985.62};
SP(0.05,0.174,0){2379764,-3563855,-607775.84,3867583.3,-1316303.6};
SP(0.05,0.177,0){2328758.2,-3628665.3,-304536.76,3800111.8,-1816619.1};
SP(0.05,0.18,0){2281548.9,-3692247,-37.579855,3694238.7,-2284691.4};
SP(0.05,0.183,0){2231706.2,-3743404.1,304362.22,3535006.9,-2698345.5};
SP(0.05,0.186,0){2180576.5,-3786032.4,606770.83,3338271.8,-3064347};
SP(0.05,0.189,0){2130992.9,-3823790.6,906119.74,3103634.6,-3371580.3};
SP(0.05,0.192,0){2079714.6,-3851124.1,1199662.2,2830177.3,-3611146.4};
SP(0.05,0.195,0){2026478,-3867039.1,1484667.8,2520271.2,-3775244.6};
SP(0.05,0.198,0){1974917.4,-3878156.1,1761656.1,2181398.4,-3864916.3};
SP(0.05,0.201,0){1921938.1,-3879317.9,2027665.1,1816613.9,-3880374.6};
SP(0.05,0.204,0){1869422.8,-3872918.6,2280806.8,1428524.4,-3811944.2};
SP(0.05,0.207,0){1815140.8,-3855560.1,2518762.4,1023342,-3668188.5};
SP(0.05,0.21,0){1761176.3,-3831816.2,2743306.9,607216.59,-3458314.1};
SP(0.05,0.213,0){1707402.6,-3800364.4,2950852.1,182847.68,-3174696};
SP(0.05,0.216,0){1652646.5,-3759003.1,3138599.6,-243487.97,-2824923.5};
SP(0.05,0.219,0){1597373,-3709911.5,3308879.5,-666913.76,-2425464.7};
SP(0.05,0.222,0){1540815.2,-3650832.4,3457986.3,-1083171.3,-1976994.3};
SP(0.05,0.225,0){1485930.4,-3587634.8,3587922.6,-1486354.6,-1486872.1};
SP(0.05,0.228,0){1428353.2,-3510911.7,3690267.4,-1869336.3,-964967.14};
SP(0.05,0.231,0){1371609,-3429464.2,3773373.8,-2231568.5,-425601.51};
SP(0.05,0.234,0){1313987.8,-3338946.1,3831338.8,-2565381.4,122180.73};
SP(0.05,0.237,0){1257125.4,-3243600.7,3868231.3,-2869378.7,667172.2};
SP(0.05,0.24,0){1198570.3,-3137909.8,3878509.3,-3137681.1,1198655.9};
SP(0.05,0.243,0){1141180,-3029084.4,3869695.8,-3372308.2,1708654.3};
SP(0.05,0.246,0){1082605,-2910969.3,3833347,-3562569.7,2182705};
SP(0.05,0.249,0){1023597.8,-2785745,3771963.4,-3707676.5,2611054.9};
SP(0.05,0.252,0){965124.5,-2656643,3690872.2,-3812023.8,2990406.6};
SP(0.05,0.255,0){905313.7,-2518990.9,3584419.6,-3869444.1,3311631.2};
SP(0.05,0.258,0){846892.38,-2379717.2,3460082.9,-3882476.8,3566580};
SP(0.05,0.261,0){786614.09,-2230485.2,3307449.7,-3840393.9,3741985.2};
SP(0.05,0.264,0){727293.92,-2079733.5,3139981.7,-3759117.9,3850517.8};
SP(0.05,0.267,0){667386.55,-1922925.8,2950129.8,-3627185.1,3874358.1};
SP(0.05,0.27,0){607150.38,-1762003.4,2744242,-3457593.6,3832384.2};
SP(0.05,0.273,0){547005.99,-1597635.6,2521468.1,-3245097.7,3711215.1};
SP(0.05,0.276,0){485969.26,-1427626.1,2280229.4,-2990458.2,3514003.7};
SP(0.05,0.279,0){425772.09,-1256953.4,2027946.6,-2701735.8,3246128.1};
SP(0.05,0.282,0){365048.46,-1082369.9,1761750.7,-2379291.2,2913416.4};
SP(0.05,0.285,0){304602.44,-906268.2,1485460,-2027764.3,2519849.4};
SP(0.05,0.288,0){243611.08,-726931.25,1198572.9,-1650912.8,2076772.4};
SP(0.05,0.291,0){182755.26,-546565.01,905261.36,-1255449.9,1593918.7};
SP(0.05,0.294,0){121956.56,-365403.51,607434.69,-847081.6,1083449.6};
SP(0.05,0.297,0){60934.375,-182783.17,304562.81,-426212.21,547699.19};
SP(0.05,0.3,0){0,0,0,0,0};
};

View "e" {
SP(0.05,0.2,0){1939799.2,-3879808.7,1939891.9,1939888.6,-3880387.9};
T2(10,-12,0){"File created on Fri Oct 18 23:50:20 2002"};
T2(General.GraphicsWidth/4,-28,0){"First time step", "Second time step", "Third time step",
               "Fourth time step", "Last time step!"};
T3(0.1,0,0,0){"This is a 3D string, defined in model coordinates"};
T3(0.1,0.3,0,0){"Test 1","Test 2","Test 3","Test 4","Test 5","Test 6","Test 7"};
};
