// -----------------------------------------------------------------------------
//
//  Gmsh tutorial 1 - appendix 3
//
//  Scalar 3D post-processing view (in parsed format)
//
// -----------------------------------------------------------------------------

// This view contains a scalar field defined on tetrahedra.

View "A 3D scalar field" {
SS(-1,-1,-0.25,-0.86742481,-0.86548068,-0.14483364,-1,-0.70523324,-0.21165758,-1,-0.77608598,0.00064487429){2.0422973,1.5085891,1.5222776,1.5844414};
SS(0.13402468,0.11673163,-0.1460819,0,0,-0.25,0.29173763,0,-0.20843742,0.22032809,0,-9.1119885e-05){0.039337265,0.044304329,0.1134179,0.027339551};
SS(0.88049681,0.87960137,0.13412341,1,1,0.25,1,0.70834898,0.20844998,1,0.77979347,0.00010253841){1.5518824,2.0447444,1.5291243,1.5887874};
SS(0.68966181,1,0.19790566,0.88049681,0.87960137,0.13412341,0.78186447,1,3.3673518e-05,0.82853688,1,0.32125076){1.492557,1.5518824,1.5923176,1.7703132};
SS(-0.8827276,-0.88146034,0.13123348,-1,-1,-6.9388939e-15,-1,-1,0.25,-1,-0.77608598,0.00064487429){1.5595365,1.9831286,2.0427074,1.5844414};
SS(-0.8827276,-0.88146034,0.13123348,-1,-1,0.25,-1,-0.70710233,0.21356199,-1,-0.77608598,0.00064487429){1.5595365,2.0427074,1.5280688,1.5844414};
SS(-1,-1,0.25,-0.8827276,-0.88146034,0.13123348,-0.70832062,-1,0.2082538,-0.77973152,-1,-0.0001062007){2.0427074,1.5595365,1.5291125,1.588155};
SS(0.88354722,0.11667767,-0.13069643,1,0,-0.25,1,0,-6.9388939e-15,0.77985819,0,-0.00014691753){0.79839767,1.043399,0.9846322,0.58919206};
SS(0.35689191,0.091376279,-0.36932783,0.25,0,-0.5,0.5,0,-0.5,0.50007058,0,-0.27987971){0.26145514,0.28810477,0.48471812,0.31006895};
SS(-0.1159097,-0.14329028,0.19302206,0,0,0.25,0,-0.29157012,0.20836692,0,-0.22019801,5.0496855e-05){0.055235283,0.045060365,0.11172813,0.029059683};
SS(0,-0.75,-0.5,-0.11754465,-0.65214472,-0.32749638,0,-0.70830496,-0.20826096,0,-0.49997234,-0.27965571){0.79460868,0.53347202,0.5287181,0.30906942};
SS(-0.75,-1,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.70823063,-1,-0.20843533,-0.49995867,-1,-0.27986665){1.7946951,1.296688,1.5240742,1.3082069};
SS(0.87272604,0.35900693,0.37172569,1,0.25,0.5,1,0.29178008,0.20838772,1,0.50005385,0.27984222){1.0107603,1.2942978,1.1084285,1.3085441};
SS(0.87272604,0.35900693,0.37172569,1,0.25,0.5,0.78912399,0.50423732,0.5,0.81143387,0.18901581,0.5){1.0107603,1.2942978,1.1096027,0.9265446};
SS(0.62860594,0.86645525,0.049037492,0.4450496,1,-0.00012892076,0.68966181,1,0.19790566,0.78186447,1,3.3673518e-05){1.1303867,1.179155,1.492557,1.5923176};
SS(0,-1,-0.25,-0.12233239,-0.87748906,-0.13583418,-0.29168215,-1,-0.20844865,-0.22019153,-1,-0.00010416607){1.0435946,0.78823805,1.1132023,1.0287732};
SS(-0.12233239,-0.87748906,-0.13583418,0,-1,-0.25,0,-0.70830496,-0.20826096,0,-0.77970171,0.00010845427){0.78823805,1.0435946,0.5287181,0.58842154};
SS(1,0.75,0.5,0.87881231,0.64063264,0.37220388,1,0.70834898,0.20844998,1,0.50005385,0.27984222){1.7930237,1.3069719,1.5291243,1.3085441};
SS(-0.36340067,-0.87821042,-0.37678589,-0.25,-1,-0.5,-0.50377808,-0.78884267,-0.5,-0.18848435,-0.81110947,-0.5){1.0307746,1.2929607,1.1087956,0.92571371};
SS(-0.36340067,-0.87821042,-0.37678589,-0.25,-1,-0.5,-0.29168215,-1,-0.20844865,-0.49995867,-1,-0.27986665){1.0307746,1.2929607,1.1132023,1.3082069};
SS(1,0.25,-0.5,0.87867265,0.36391919,-0.37720578,0.78906409,0.5041626,-0.5,0.81149777,0.18885984,-0.5){1.2935113,1.03034,1.1105402,0.92750237};
SS(1,0.25,-0.5,0.87867265,0.36391919,-0.37720578,1,0.2917639,-0.20827961,1,0.50010355,-0.27968748){1.2935113,1.03034,1.1127834,1.3071084};
SS(-0.63815223,-0.88141187,0.37488811,-0.75,-1,0.5,-0.70832062,-1,0.2082538,-0.4999534,-1,0.27968311){1.3088768,1.7943537,1.5291125,1.3075402};
SS(0.88049681,0.87960137,0.13412341,1,1,0.25,0.78186447,1,3.3673518e-05,0.82853688,1,0.32125076){1.5518824,2.0447444,1.5923176,1.7703132};
SS(-1,-1,-6.9388939e-15,-0.8827276,-0.88146034,0.13123348,-1,-1,0.25,-0.77973152,-1,-0.0001062007){1.9831286,1.5595365,2.0427074,1.588155};
SS(1,0,-0.25,0.88354722,0.11667767,-0.13069643,1,0,-6.9388939e-15,1,0.2203628,5.6826691e-05){1.043399,0.79839767,0.9846322,1.0268649};
SS(0.88354722,0.11667767,-0.13069643,1,0,-0.25,1,0.2917639,-0.20827961,1,0.2203628,5.6826691e-05){0.79839767,1.043399,1.1127834,1.0268649};
SS(1,0,-0.25,0.88354722,0.11667767,-0.13069643,0.70841775,0,-0.20847891,0.77985819,0,-0.00014691753){1.043399,0.79839767,0.52293439,0.58919206};
SS(-1,-0.00021427218,0.00011802244,-0.88905946,-0.098697315,-0.13184676,-1,-0.25140376,-0.1934451,-1,-0.00018427889,-0.26378916){0.98080906,0.8023886,1.0790534,1.0508045};
SS(-0.88905946,-0.098697315,-0.13184676,-1,-0.00021427218,0.00011802244,-1,-0.25140376,-0.1934451,-1,-0.20076836,0.00061221676){0.8023886,0.98080906,1.0790534,1.0172898};
SS(0.50761134,0.34933779,0.39015973,0.37492492,0.49312259,0.5,0.37501462,0.2307626,0.5,0.5725222,0.50074158,0.5){0.51484928,0.61809871,0.42590445,0.8121357};
SS(0.67112401,0.32933441,0.5,0.50761134,0.34933779,0.39015973,0.37501462,0.2307626,0.5,0.5725222,0.50074158,0.5){0.79210069,0.51484928,0.42590445,0.8121357};
SS(-0.098950987,-0.13391411,-0.14594667,0,0,-0.25,0,0,-6.9388939e-15,0,-0.22019801,5.0496855e-05){0.03512721,0.044304329,-0.017891206,0.029059683};
SS(0,0,-0.25,-0.098950987,-0.13391411,-0.14594667,0,-0.29164705,-0.20823955,0,-0.22019801,5.0496855e-05){0.044304329,0.03512721,0.11473247,0.029059683};
SS(-0.49284988,-0.37485679,0.5,-0.34549718,-0.50098866,0.4105565,-0.23048975,-0.37484721,0.5,-0.50050976,-0.57246927,0.5){0.6163523,0.5260109,0.42714666,0.81219504};
SS(-0.34549718,-0.50098866,0.4105565,-0.32897755,-0.67088709,0.5,-0.23048975,-0.37484721,0.5,-0.50050976,-0.57246927,0.5){0.5260109,0.79643001,0.42714666,0.81219504};
SS(0.37492492,0.49312259,0.5,0.35567295,0.65317229,0.39545235,0.21512427,0.73211919,0.5,0.45042372,0.78359022,0.5){0.61809871,0.69293227,0.81521474,1.0496179};
SS(0.25,0,-0.5,0.35689191,0.091376279,-0.36932783,0.29173763,0,-0.20843742,0.50007058,0,-0.27987971){0.28810477,0.26145514,0.1134179,0.31006895};
SS(-1,0.49991607,0.0031934521,-0.89426176,0.41257007,-0.12932618,-1,0.25105097,-0.19350143,-1,0.47527469,-0.27513051){1.2302733,0.974079,1.0825888,1.2834809};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.49991607,0.0031934521,-1,0.25105097,-0.19350143,-1,0.29928494,0.0012550607){0.974079,1.2302733,1.0825888,1.0718665};
SS(-1,1,-0.25,-0.89962374,0.8609561,-0.16698164,-1,1,-6.9388939e-15,-1,0.77631186,0.00053339564){2.0450698,1.5692753,1.9810426,1.5817554};
SS(-0.89962374,0.8609561,-0.16698164,-1,1,-0.25,-1,0.70529035,-0.21162945,-1,0.77631186,0.00053339564){1.5692753,2.0450698,1.520296,1.5817554};
SS(-1,1,-0.25,-0.89962374,0.8609561,-0.16698164,-1,0.70529035,-0.21162945,-1,0.83964442,-0.3309874){2.0450698,1.5692753,1.520296,1.7979585};
SS(-1,-0.11111111,0.5,-0.89646962,-0.32955067,0.34017365,-1,-0.33333333,0.5,-1,-0.24887753,0.1953112){1.2390062,1.0133061,1.3443603,1.0768014};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.33333333,0.5,-1,-0.24887753,0.1953112,-1,-0.47520831,0.27427507){1.0133061,1.3443603,1.0768014,1.2822693};
SS(-0.89804207,0.11676539,-0.10792088,-1,-0.00021427218,0.00011802244,-1,0.25105097,-0.19350143,-1,-0.00018427889,-0.26378916){0.82300022,0.98080906,1.0825888,1.0508045};
SS(-1,-0.00021427218,0.00011802244,-0.89804207,0.11676539,-0.10792088,-1,0.25105097,-0.19350143,-1,0.29928494,0.0012550607){0.98080906,0.82300022,1.0825888,1.0718665};
SS(-1,0.33333333,0.5,-0.91004595,0.15296589,0.33139812,-1,0.11111111,0.5,-1,0.24865949,0.19540364){1.3403692,0.94743142,1.246301,1.0814407};
SS(-0.91004595,0.15296589,0.33139812,-1,0.11111111,0.5,-1,0.24865949,0.19540364,-1,-0.00012222908,0.26646899){0.94743142,1.246301,1.0814407,1.0506696};
SS(0.34720309,0.90097601,-0.12745168,0.4450496,1,-0.00012892076,0.43683247,1,-0.26068681,0.24937941,1,-0.00011138016){0.93504792,1.179155,1.2435523,1.0446566};
SS(0.17426348,1,-0.18078905,0.34720309,0.90097601,-0.12745168,0.43683247,1,-0.26068681,0.24937941,1,-0.00011138016){1.045853,0.93504792,1.2435523,1.0446566};
SS(0.27123349,0.36190713,0.41476339,0.37492492,0.49312259,0.5,0.11523872,0.30161582,0.5,0.37501462,0.2307626,0.5){0.36300231,0.61809871,0.33546792,0.42590445};
SS(0.37492492,0.49312259,0.5,0.27123349,0.36190713,0.41476339,0.11523872,0.30161582,0.5,0.16321322,0.50838432,0.5){0.61809871,0.36300231,0.33546792,0.52238519};
SS(-0.49284988,-0.37485679,0.5,-0.37661764,-0.26006406,0.40868766,-0.30122568,-0.11513872,0.5,-0.23048975,-0.37484721,0.5){0.6163523,0.36234206,0.33848202,0.42714666};
SS(-0.37661764,-0.26006406,0.40868766,-0.49284988,-0.37485679,0.5,-0.30122568,-0.11513872,0.5,-0.50807239,-0.16307462,0.5){0.36234206,0.6163523,0.33848202,0.52416601};
SS(-0.16643696,-0.21791406,0.42402077,0,-0.25,0.5,-0.30122568,-0.11513872,0.5,-0.23048975,-0.37484721,0.5){0.23818505,0.28720824,0.33848202,0.42714666};
SS(0,-0.25,0.5,-0.16643696,-0.21791406,0.42402077,-0.30122568,-0.11513872,0.5,-0.17669296,0.011023676,0.5){0.28720824,0.23818505,0.33848202,0.26322593};
SS(0.37549445,0.49317282,-0.5,0.27170325,0.36204749,-0.4201745,0.11583535,0.30145324,-0.5,0.37532516,0.23078833,-0.5){0.61648995,0.36885377,0.33954703,0.42551454};
SS(0.27170325,0.36204749,-0.4201745,0.37549445,0.49317282,-0.5,0.11583535,0.30145324,-0.5,0.16368264,0.50834729,-0.5){0.36885377,0.61648995,0.33954703,0.52115901};
SS(0,0,-6.9388939e-15,0.13913358,0.10014326,0.18199659,0,0,0.25,0.22032809,0,-9.1119885e-05){-0.017891206,0.045990896,0.045060365,0.027339551};
SS(0,0,0.25,0.13913358,0.10014326,0.18199659,0.29175541,0,0.20824909,0.22032809,0,-9.1119885e-05){0.045060365,0.045990896,0.1093371,0.027339551};
SS(-0.92571354,0.17249619,-0.34283108,-1,0.33333333,-0.5,-1,0.11111111,-0.5,-1,0.25105097,-0.19350143){0.99158484,1.3393331,1.2464205,1.0825888};
SS(-1,0.11111111,-0.5,-0.92571354,0.17249619,-0.34283108,-1,0.25105097,-0.19350143,-1,-0.00018427889,-0.26378916){1.2464205,0.99158484,1.0825888,1.0508045};
SS(-1,-0.55555556,0.5,-0.89663862,-0.69397302,0.37275403,-1,-0.77777778,0.5,-1,-0.70710233,0.21356199){1.5359657,1.4119512,1.8434331,1.5280688};
SS(-1,-0.55555556,0.5,-0.89663862,-0.69397302,0.37275403,-0.67513028,-0.66529728,0.5,-0.80635543,-0.81164184,0.5){1.5359657,1.4119512,1.1284607,1.5410993};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.55555556,-0.5,-1,-0.77777778,-0.5,-1,-0.70523324,-0.21165758){1.4249306,1.5366945,1.8436809,1.5222776};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.55555556,-0.5,-0.67495489,-0.6652659,-0.5,-0.80632325,-0.81147186,-0.5){1.4249306,1.5366945,1.1276355,1.5409894};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.55555556,-0.5,-1,-0.70523324,-0.21165758,-1,-0.47540235,-0.27521785){1.4249306,1.5366945,1.5222776,1.2841965};
SS(-1,-0.77777778,-0.5,-0.91414606,-0.68082467,-0.37109558,-1,-0.70523324,-0.21165758,-1,-0.83959635,-0.33115777){1.8436809,1.4249306,1.5222776,1.7998257};
SS(-1,1,-6.9388939e-15,-0.93582873,0.86427167,0.14668289,-1,1,0.25,-1,0.77631186,0.00053339564){1.9810426,1.6320629,2.0473025,1.5817554};
SS(-1,1,0.25,-0.93582873,0.86427167,0.14668289,-1,0.70725984,0.21334539,-1,0.77631186,0.00053339564){2.0473025,1.6320629,1.5286486,1.5817554};
SS(-0.93582873,0.86427167,0.14668289,-1,1,0.25,-1,0.70725984,0.21334539,-1,0.84108515,0.33242406){1.6320629,2.0473025,1.5286486,1.8031397};
SS(-0.073421274,-0.375,-0.38984354,0,-0.5,-0.5,0,-0.25,-0.5,0,-0.49997234,-0.27965571){0.28201081,0.4845449,0.29677328,0.30906942};
SS(-0.073421274,-0.375,-0.38984354,0,-0.25,-0.5,0,-0.29164705,-0.20823955,0,-0.49997234,-0.27965571){0.28201081,0.29677328,0.11473247,0.30906942};
SS(0,-0.25,-0.5,-0.073421274,-0.375,-0.38984354,0,-0.29164705,-0.20823955,0,-0.16143077,-0.33843101){0.29677328,0.28201081,0.11473247,0.12966739};
SS(-0.41648151,0.41684878,0.5,-0.29261734,0.53193925,0.43339885,-0.3132159,0.69976014,0.5,-0.18136176,0.40461939,0.5){0.58097186,0.53993003,0.82050522,0.42386795};
SS(-0.29261734,0.53193925,0.43339885,-0.41648151,0.41684878,0.5,-0.3132159,0.69976014,0.5,-0.48141868,0.60085372,0.5){0.53993003,0.58097186,0.82050522,0.82306978};
SS(-0.29261734,0.53193925,0.43339885,-0.033588837,0.5879061,0.5,-0.3132159,0.69976014,0.5,-0.18136176,0.40461939,0.5){0.53993003,0.57806214,0.82050522,0.42386795};
SS(-0.58754442,0.033885734,-0.5,-0.41767704,0.010770256,-0.44072823,-0.30131805,-0.11512588,-0.5,-0.40408872,0.18166381,-0.5){0.58180393,0.35514259,0.3368451,0.42526168};
SS(-0.41767704,0.010770256,-0.44072823,-0.58754442,0.033885734,-0.5,-0.30131805,-0.11512588,-0.5,-0.50815189,-0.16301678,-0.5){0.35514259,0.58180393,0.3368451,0.52110597};
SS(-0.41767704,0.010770256,-0.44072823,-0.30131805,-0.11512588,-0.5,-0.40408872,0.18166381,-0.5,-0.19007896,0.04567822,-0.5){0.35514259,0.3368451,0.42526168,0.27736807};
SS(-1,-0.00021427218,0.00011802244,-0.91347537,0.15552497,0.067511395,-1,0.24865949,0.19540364,-1,-0.00012222908,0.26646899){0.98080906,0.85045394,1.0814407,1.0506696};
SS(-0.91347537,0.15552497,0.067511395,-1,-0.00021427218,0.00011802244,-1,0.24865949,0.19540364,-1,0.29928494,0.0012550607){0.85045394,0.98080906,1.0814407,1.0718665};
SS(0.0011150345,0.93517443,-0.37389303,0.11111111,1,-0.5,-0.11111111,1,-0.5,-0.088882135,1,-0.23281641){1.0026385,1.2422682,1.2528065,1.0431215};
SS(0.0011150345,0.93517443,-0.37389303,0.11111111,1,-0.5,-0.088882135,1,-0.23281641,0.2222976,1,-0.35617554){1.0026385,1.2422682,1.0431215,1.1585843};
SS(0.17426348,1,-0.18078905,0.0011150345,0.93517443,-0.37389303,-0.088882135,1,-0.23281641,0.2222976,1,-0.35617554){1.045853,1.0026385,1.0431215,1.1585843};
SS(0.37549445,0.49317282,-0.5,0.50136923,0.34587735,-0.44862257,0.37532516,0.23078833,-0.5,0.57309542,0.50075776,-0.5){0.61648995,0.56260896,0.42551454,0.81773274};
SS(0.50136923,0.34587735,-0.44862257,0.671223,0.32907594,-0.5,0.37532516,0.23078833,-0.5,0.57309542,0.50075776,-0.5){0.56260896,0.79435762,0.42551454,0.81773274};
SS(0.671223,0.32907594,-0.5,0.50136923,0.34587735,-0.44862257,0.37532516,0.23078833,-0.5,0.6251418,0.1440922,-0.5){0.79435762,0.56260896,0.42551454,0.63751638};
SS(0.64232771,0.84838332,0.46476191,0.77777778,1,0.5,0.55555556,1,0.5,0.81191124,0.80644944,0.5){1.3339184,1.8450917,1.5357742,1.5425973};
SS(0.55555556,1,0.5,0.64232771,0.84838332,0.46476191,0.66554141,0.67524133,0.5,0.45042372,0.78359022,0.5){1.5357742,1.3339184,1.1271263,1.0496179};
SS(0.64232771,0.84838332,0.46476191,0.55555556,1,0.5,0.66554141,0.67524133,0.5,0.81191124,0.80644944,0.5){1.3339184,1.5357742,1.1271263,1.5425973};
SS(0.25,0,0.5,0.26083053,0.15082484,0.37728795,0.1615172,0,0.33845519,0.12517622,0.12515553,0.5){0.29281005,0.21918499,0.13068911,0.27156885};
SS(-1,0.70529035,-0.21162945,-0.83248216,0.76782327,-0.31292259,-1,0.83964442,-0.3309874,-0.89962374,0.8609561,-0.16698164){1.520296,1.366757,1.7979585,1.5692753};
SS(0.51674933,0.64481281,-0.39755292,0.49866453,0.63973666,-0.21510859,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.82858869,0.68344633,0.59958408,0.55795418};
SS(0.37549445,0.49317282,-0.5,0.50136923,0.34587735,-0.44862257,0.57309542,0.50075776,-0.5,0.48047723,0.47791267,-0.33071402){0.61648995,0.56260896,0.81773274,0.55795418};
SS(0.098704003,0.67249079,0.1943501,0.11458044,0.70010244,0.010073529,0.24404834,0.79519787,0.082231238,0.26064395,0.61953306,0.12890567){0.47957633,0.49378055,0.68472542,0.45328252};
SS(-0.65355936,0.25468043,-0.1897796,-0.49391083,0.27907498,-0.27264436,-0.62938155,0.17932964,-0.37445272,-0.63048479,0.37587985,-0.34368186){0.51379882,0.37398026,0.55109073,0.64388066};
SS(-0.32897755,-0.67088709,0.5,-0.40125956,-0.65699374,0.33213173,-0.50050976,-0.57246927,0.5,-0.34549718,-0.50098866,0.4105565){0.79643001,0.69449311,0.81219504,0.5260109};
SS(0,0,-0.25,-0.16707278,-0.087678023,-0.31121894,0,-0.16143077,-0.33843101,-0.098950987,-0.13391411,-0.14594667){0.044304329,0.11599041,0.12966739,0.03512721};
SS(0,-1,0.25,-0.12988976,-0.86995226,0.20452896,-0.16134158,-1,0.33850563,0,-0.83845667,0.33864852){1.0438639,0.79894991,1.129042,0.80178572};
SS(-0.52470763,0.46530444,0.33754711,-0.41648151,0.41684878,0.5,-0.48141868,0.60085372,0.5,-0.61509744,0.47589965,0.5){0.59371518,0.58097186,0.82306978,0.84259202};
SS(0.27170325,0.36204749,-0.4201745,0.11583535,0.30145324,-0.5,0.20129651,0.21389912,-0.31902192,0.09693172,0.3918681,-0.3370861){0.36885377,0.33954703,0.16839385,0.26256104};
SS(0.8781758,0.86708556,-0.1989731,1,1,-0.25,1,0.83864447,-0.33847614,0.82865019,1,-0.3214153){1.5462283,2.0438315,1.8065101,1.7714679};
SS(0.11583535,0.30145324,-0.5,0.27170325,0.36204749,-0.4201745,0.16368264,0.50834729,-0.5,0.09693172,0.3918681,-0.3370861){0.33954703,0.36885377,0.52115901,0.26256104};
SS(-0.65956212,-0.52273243,-0.19262862,-0.7055892,-0.50616462,-0.017961589,-0.52487586,-0.5117405,-0.017639258,-0.61549046,-0.35581383,-0.12962263){0.7287475,0.74484897,0.51812974,0.50877487};
SS(-0.36145429,0.13293621,0.35430528,-0.24000819,0.17660305,0.5,-0.4543958,0.20406131,0.5,-0.40752783,0.030201366,0.5){0.26360063,0.3210912,0.48353653,0.40526498};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65631386,-0.59724887,0.13822882,-0.52487586,-0.5117405,-0.017639258,-0.59094649,-0.40495207,0.12834587){0.74484897,0.7890621,0.51812974,0.51475101};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.24163432,0.33561251,-0.055881164,-0.096302334,0.43534175,-0.056072844,-0.19461387,0.3919517,0.10437587){0.32892635,0.16437697,0.18078295,0.19075448};
SS(0.51674933,0.64481281,-0.39755292,0.37549445,0.49317282,-0.5,0.57309542,0.50075776,-0.5,0.48047723,0.47791267,-0.33071402){0.82858869,0.61648995,0.81773274,0.55795418};
SS(-0.16015893,0.67694077,0.39025863,-0.3132159,0.69976014,0.5,-0.1827732,0.83017807,0.5,-0.30949447,0.8262402,0.33528492){0.6265216,0.82050522,0.95598938,0.87388961};
SS(0.11458044,0.70010244,0.010073529,0.098704003,0.67249079,0.1943501,0.086744979,0.52712982,0.027891324,0.26064395,0.61953306,0.12890567){0.49378055,0.47957633,0.26660844,0.45328252};
SS(-0.4581749,-0.5263483,-0.32801665,-0.49292178,-0.37477565,-0.5,-0.50036547,-0.57239096,-0.5,-0.62341011,-0.46880832,-0.38153973){0.57811658,0.6115465,0.81333009,0.73807879};
SS(0.37549445,0.49317282,-0.5,0.51674933,0.64481281,-0.39755292,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.61648995,0.82858869,0.59958408,0.55795418};
SS(-0.10743676,0.85847111,-0.11136175,-0.012406168,1,-0.034358602,-0.035654771,0.78507762,0.045007896,0.094968532,0.84539386,-0.087484586){0.7462212,0.99121748,0.60161266,0.71839764};
SS(-0.58934795,0.84141567,-0.18062024,-0.74249217,0.75399014,-0.15399718,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){1.0736489,1.1267767,1.1958888,0.87296464};
SS(-0.30122568,-0.11513872,0.5,-0.37661764,-0.26006406,0.40868766,-0.50807239,-0.16307462,0.5,-0.40506391,-0.079541407,0.3303193){0.33848202,0.36234206,0.52416601,0.26156128};
SS(-0.39806707,0.15776443,0.15870839,-0.26986228,0.26051837,0.22418657,-0.41843781,0.30742585,0.3397996,-0.36145429,0.13293621,0.35430528){0.19317292,0.1749353,0.37011438,0.26360063};
SS(-0.52470763,0.46530444,0.33754711,-0.54640726,0.34339216,0.19847863,-0.41843781,0.30742585,0.3397996,-0.61674646,0.25215289,0.3447871){0.59371518,0.43575493,0.37011438,0.54607287};
SS(-0.10037172,0.18891947,0.20844359,0,0,0.25,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.074828316,0.045060365,0.10955402,0.025420414};
SS(0.57309542,0.50075776,-0.5,0.50136923,0.34587735,-0.44862257,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.81773274,0.56260896,0.72773009,0.55795418};
SS(-0.35582611,-0.64426575,-0.070000747,-0.3533559,-0.49437708,0.037576204,-0.52487586,-0.5117405,-0.017639258,-0.50537844,-0.68762812,0.023695348){0.52757348,0.35575629,0.51812974,0.71483247};
SS(0.0011150345,0.93517443,-0.37389303,-0.11111111,1,-0.5,0.00024312215,0.80750011,-0.5,-0.18268367,0.83021756,-0.5){1.0026385,1.2528065,0.88610119,0.9573479};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65956212,-0.52273243,-0.19262862,-0.82285362,-0.63420593,-0.0683896,-0.85520613,-0.46088631,-0.14784569){0.74484897,0.7287475,1.0691297,0.95161001};
SS(-0.0073778212,0.36022468,0.15230712,-0.10037172,0.18891947,0.20844359,0.050277172,0.20853018,0.30186362,-0.11614487,0.30919383,0.33918095){0.13675819,0.074828316,0.12181545,0.20820823};
SS(-0.58258855,0.14037208,-0.067351147,-0.65355936,0.25468043,-0.1897796,-0.78848723,0.26584533,-0.068869999,-0.63246299,0.29145388,0.035195127){0.34532741,0.51379882,0.68151298,0.47226275};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.4433427,0.53576375,-0.12560501,-0.54631436,0.45612147,-0.00074796238,-0.45563594,0.60375179,0.095527884){0.32892635,0.48429505,0.48593017,0.56263538};
SS(0.11583535,0.30145324,-0.5,0.09693172,0.3918681,-0.3370861,0.16368264,0.50834729,-0.5,-0.029932551,0.40748663,-0.5){0.33954703,0.26256104,0.52115901,0.4038008};
SS(-0.7489605,0.18190923,0.13647301,-0.91347537,0.15552497,0.067511395,-0.76752638,0.004448061,-0.013214377,-0.84289574,0.018333867,0.1608607){0.59564173,0.85045394,0.5734925,0.72430843};
SS(0.27123349,0.36190713,0.41476339,0.11523872,0.30161582,0.5,0.16321322,0.50838432,0.5,0.085954007,0.41736025,0.32943097){0.36300231,0.33546792,0.52238519,0.27115576};
SS(-0.79227163,-0.79754897,0.0021844777,-1,-0.77608598,0.00064487429,-0.8827276,-0.88146034,0.13123348,-0.83996275,-0.66999882,0.11765553){1.2530106,1.5844414,1.5595365,1.1553131};
SS(0.25248643,0.73785598,-0.13082591,0.11458044,0.70010244,0.010073529,0.24404834,0.79519787,0.082231238,0.094968532,0.84539386,-0.087484586){0.60350215,0.49378055,0.68472542,0.71839764};
SS(-0.48255002,0.69900846,-0.19155417,-0.39032311,0.63241857,-0.34621958,-0.50782983,0.50249565,-0.29902586,-0.6293812,0.63993291,-0.28812602){0.74365966,0.65630059,0.58612549,0.87296464};
SS(-0.48255002,0.69900846,-0.19155417,-0.4433427,0.53576375,-0.12560501,-0.50782983,0.50249565,-0.29902586,-0.39032311,0.63241857,-0.34621958){0.74365966,0.48429505,0.58612549,0.65630059};
SS(-0.74249217,0.75399014,-0.15399718,-0.83248216,0.76782327,-0.31292259,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){1.1267767,1.366757,1.1958888,0.87296464};
SS(0.59416595,0.14141347,0.32656529,0.50011436,0,0.27961788,0.42621669,0.19017509,0.30505062,0.46476684,0.14382827,0.12247557){0.46498444,0.30940041,0.29714896,0.23450402};
SS(0.13913358,0.10014326,0.18199659,0,0,0.25,0.1615172,0,0.33845519,0.050277172,0.20853018,0.30186362){0.045990896,0.045060365,0.13068911,0.12181545};
SS(0.61535375,0.70719289,-0.095218388,0.45788353,0.76094781,-0.0096633567,0.62860594,0.86645525,0.049037492,0.54700908,0.85955032,-0.16345766){0.87858083,0.76853994,1.1303867,1.0528061};
SS(-0.30122568,-0.11513872,0.5,-0.40506391,-0.079541407,0.3303193,-0.50807239,-0.16307462,0.5,-0.40752783,0.030201366,0.5){0.33848202,0.26156128,0.52416601,0.40526498};
SS(0.50136923,0.34587735,-0.44862257,0.671223,0.32907594,-0.5,0.57309542,0.50075776,-0.5,0.67125235,0.44297685,-0.31879306){0.56260896,0.79435762,0.81773274,0.72773009};
SS(-1,-0.00021427218,0.00011802244,-0.89804207,0.11676539,-0.10792088,-0.76752638,0.004448061,-0.013214377,-0.88905946,-0.098697315,-0.13184676){0.98080906,0.82300022,0.5734925,0.8023886};
SS(-0.16707278,-0.087678023,-0.31121894,-0.29237157,-0.11865629,-0.17606411,-0.1971424,-0.26981885,-0.30750196,-0.098950987,-0.13391411,-0.14594667){0.11599041,0.11404163,0.19280289,0.03512721};
SS(-0.14850787,-0.69358405,-0.087583548,0,-0.70830496,-0.20826096,0,-0.77970171,0.00010845427,-0.12233239,-0.87748906,-0.13583418){0.49763432,0.5287181,0.58842154,0.78823805};
SS(-0.83248216,0.76782327,-0.31292259,-0.74249217,0.75399014,-0.15399718,-0.80558396,0.5878127,-0.29244037,-0.6293812,0.63993291,-0.28812602){1.366757,1.1267767,1.0616703,0.87296464};
SS(-0.34310942,-0.010167032,0.1509038,-0.15128303,0.02253305,0.11422928,-0.20656092,-0.13938028,0.029547229,-0.28278924,0.041190137,-0.04219563){0.12661586,0.025420414,0.048278496,0.063480395};
SS(0.29175541,0,0.20824909,0.13913358,0.10014326,0.18199659,0.1615172,0,0.33845519,0.26083053,0.15082484,0.37728795){0.1093371,0.045990896,0.13068911,0.21918499};
SS(-1,-0.00021427218,0.00011802244,-0.91347537,0.15552497,0.067511395,-0.76752638,0.004448061,-0.013214377,-0.89804207,0.11676539,-0.10792088){0.98080906,0.85045394,0.5734925,0.82300022};
SS(-0.62450053,-0.31310845,0.38575928,-0.73174745,-0.21491043,0.5,-0.50807239,-0.16307462,0.5,-0.64009684,-0.10188458,0.37412975){0.62379151,0.81377033,0.52416601,0.54631619};
SS(-0.48255002,0.69900846,-0.19155417,-0.58934795,0.84141567,-0.18062024,-0.48952189,0.78345034,0.019065462,-0.38143574,0.84373572,-0.12387887){0.74365966,1.0736489,0.83409809,0.85864479};
SS(0.35689191,0.091376279,-0.36932783,0.29173763,0,-0.20843742,0.50007058,0,-0.27987971,0.37137652,0.1767682,-0.19801193){0.26145514,0.1134179,0.31006895,0.19205628};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.29164705,-0.20823955,-0.1971424,-0.26981885,-0.30750196,-0.098950987,-0.13391411,-0.14594667){0.14783141,0.11473247,0.19280289,0.03512721};
SS(-0.91347537,0.15552497,0.067511395,-1,0.29928494,0.0012550607,-0.78848723,0.26584533,-0.068869999,-0.89804207,0.11676539,-0.10792088){0.85045394,1.0718665,0.68151298,0.82300022};
SS(-0.68637718,0.43295764,-0.18031685,-0.65355936,0.25468043,-0.1897796,-0.83127473,0.33505962,-0.32026923,-0.63048479,0.37587985,-0.34368186){0.67437813,0.51379882,0.89071695,0.64388066};
SS(-0.82285362,-0.63420593,-0.0683896,-0.7055892,-0.50616462,-0.017961589,-0.82595855,-0.48031431,0.11444494,-0.83996275,-0.66999882,0.11765553){1.0691297,0.74484897,0.90887195,1.1553131};
SS(0.11458044,0.70010244,0.010073529,-0.0089783977,0.64320989,-0.13441642,-0.035654771,0.78507762,0.045007896,0.094968532,0.84539386,-0.087484586){0.49378055,0.41358858,0.60161266,0.71839764};
SS(-0.52427834,0.10778268,0.27208728,-0.39806707,0.15776443,0.15870839,-0.41843781,0.30742585,0.3397996,-0.36145429,0.13293621,0.35430528){0.34448415,0.19317292,0.37011438,0.26360063};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65631386,-0.59724887,0.13822882,-0.82595855,-0.48031431,0.11444494,-0.83996275,-0.66999882,0.11765553){0.74484897,0.7890621,0.90887195,1.1553131};
SS(-1,-0.77608598,0.00064487429,-0.79227163,-0.79754897,0.0021844777,-0.82285362,-0.63420593,-0.0683896,-0.83996275,-0.66999882,0.11765553){1.5844414,1.2530106,1.0691297,1.1553131};
SS(0.050277172,0.20853018,0.30186362,0.11523872,0.30161582,0.5,-0.045146113,0.19012269,0.5,0.12517622,0.12515553,0.5){0.12181545,0.33546792,0.27176836,0.27156885};
SS(-0.11111111,1,-0.5,-0.33333333,1,-0.5,-0.18268367,0.83021756,-0.5,-0.23070339,1,-0.34855306){1.2528065,1.3407278,0.9573479,1.1599423};
SS(0.11111111,1,-0.5,0.0011150345,0.93517443,-0.37389303,-0.11111111,1,-0.5,0.00024312215,0.80750011,-0.5){1.2422682,1.0026385,1.2528065,0.88610119};
SS(-0.65631386,-0.59724887,0.13822882,-0.83996275,-0.66999882,0.11765553,-0.79575191,-0.55547687,0.30538166,-0.82595855,-0.48031431,0.11444494){0.7890621,1.1553131,1.0192798,0.90887195};
SS(-0.16707278,-0.087678023,-0.31121894,-0.30131805,-0.11512588,-0.5,-0.19007896,0.04567822,-0.5,-0.12484866,-0.12486094,-0.5){0.11599041,0.3368451,0.27736807,0.26766045};
SS(-0.30131805,-0.11512588,-0.5,-0.41767704,0.010770256,-0.44072823,-0.50815189,-0.16301678,-0.5,-0.38492375,-0.20017574,-0.33650716){0.3368451,0.35514259,0.52110597,0.28705324};
SS(-0.31289368,0.69974287,-0.5,-0.39032311,0.63241857,-0.34621958,-0.50014045,0.79673357,-0.5,-0.4813337,0.60105459,-0.5){0.82323564,0.65630059,1.1145783,0.83133251};
SS(-0.48255002,0.69900846,-0.19155417,-0.6293812,0.63993291,-0.28812602,-0.50782983,0.50249565,-0.29902586,-0.62332411,0.59900263,-0.10904345){0.74365966,0.87296464,0.58612549,0.74800561};
SS(-0.29237157,-0.11865629,-0.17606411,-0.16707278,-0.087678023,-0.31121894,-0.1971424,-0.26981885,-0.30750196,-0.38492375,-0.20017574,-0.33650716){0.11404163,0.11599041,0.19280289,0.28705324};
SS(-0.4433427,0.53576375,-0.12560501,-0.48255002,0.69900846,-0.19155417,-0.50782983,0.50249565,-0.29902586,-0.62332411,0.59900263,-0.10904345){0.48429505,0.74365966,0.58612549,0.74800561};
SS(0.50761134,0.34933779,0.39015973,0.37492492,0.49312259,0.5,0.5725222,0.50074158,0.5,0.47723835,0.52605258,0.30619083){0.51484928,0.61809871,0.8121357,0.58228229};
SS(-0.91347537,0.15552497,0.067511395,-1,-0.00021427218,0.00011802244,-0.76752638,0.004448061,-0.013214377,-0.84289574,0.018333867,0.1608607){0.85045394,0.98080906,0.5734925,0.72430843};
SS(-0.80727304,0.00024662976,0.5,-0.91004595,0.15296589,0.33139812,-0.83006559,0.18329805,0.5,-0.72768327,0.10310141,0.33233484){0.88515177,0.94743142,0.96159482,0.63492881};
SS(-0.48255002,0.69900846,-0.19155417,-0.58934795,0.84141567,-0.18062024,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){0.74365966,1.0736489,1.1958888,0.87296464};
SS(0,-0.70830496,-0.20826096,-0.11754465,-0.65214472,-0.32749638,-0.2399131,-0.76005145,-0.25989531,-0.14850787,-0.69358405,-0.087583548){0.5287181,0.53347202,0.6848256,0.49763432};
SS(0.39612945,0.70614162,0.21524614,0.36841015,0.87909734,0.37310922,0.22886345,0.79287946,0.30210005,0.35567295,0.65317229,0.39545235){0.68453461,1.0362544,0.75332396,0.69293227};
SS(0.61535375,0.70719289,-0.095218388,0.65062064,0.64268786,0.069510863,0.77861211,0.77861193,-0.067175459,0.62860594,0.86645525,0.049037492){0.87858083,0.82620698,1.1981052,1.1303867};
SS(-0.17603462,0.24070348,-0.5,-0.1182182,0.15955837,-0.3159857,-0.19007896,0.04567822,-0.5,-0.010543702,0.17712261,-0.5){0.32537509,0.11990198,0.27736807,0.25750364};
SS(-0.65956212,-0.52273243,-0.19262862,-0.85520613,-0.46088631,-0.14784569,-0.7907607,-0.33838097,-0.28342271,-0.81387526,-0.53653555,-0.3209601){0.7287475,0.95161001,0.80149819,1.0406635};
SS(-0.073421274,-0.375,-0.38984354,0,-0.29164705,-0.20823955,0,-0.16143077,-0.33843101,-0.1971424,-0.26981885,-0.30750196){0.28201081,0.11473247,0.12966739,0.19280289};
SS(-1,0.33333333,-0.5,-0.92571354,0.17249619,-0.34283108,-1,0.11111111,-0.5,-0.82994199,0.18319278,-0.5){1.3393331,0.99158484,1.2464205,0.95993957};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.77777778,0.5,-1,-0.84092895,0.33252059,-0.80635543,-0.81164184,0.5){1.4119512,1.8434331,1.8030746,1.5410993};
SS(0,-0.29164705,-0.20823955,-0.15923414,-0.34171533,-0.15079999,-0.1971424,-0.26981885,-0.30750196,-0.073421274,-0.375,-0.38984354){0.11473247,0.14783141,0.19280289,0.28201081};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.47540235,-0.27521785,-0.7907607,-0.33838097,-0.28342271,-0.81387526,-0.53653555,-0.3209601){0.95161001,1.2841965,0.80149819,1.0406635};
SS(0,0,0.25,-0.1159097,-0.14329028,0.19302206,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.045060365,0.055235283,0.10955402,0.025420414};
SS(-0.65631386,-0.59724887,0.13822882,-0.7055892,-0.50616462,-0.017961589,-0.82595855,-0.48031431,0.11444494,-0.59094649,-0.40495207,0.12834587){0.7890621,0.74484897,0.90887195,0.51475101};
SS(0.35689191,0.091376279,-0.36932783,0.50007058,0,-0.27987971,0.51910919,0.22553632,-0.31417891,0.37137652,0.1767682,-0.19801193){0.26145514,0.31006895,0.40112301,0.19205628};
SS(-0.19461387,0.3919517,0.10437587,-0.0073778212,0.36022468,0.15230712,-0.096302334,0.43534175,-0.056072844,-0.098708274,0.55956225,0.10505678){0.19075448,0.13675819,0.18078295,0.31633913};
SS(-0.39806707,0.15776443,0.15870839,-0.34310942,-0.010167032,0.1509038,-0.52427834,0.10778268,0.27208728,-0.5555987,0.045150158,0.095162244){0.19317292,0.12661586,0.34448415,0.29993682};
SS(-0.41767704,0.010770256,-0.44072823,-0.30131805,-0.11512588,-0.5,-0.19007896,0.04567822,-0.5,-0.29413589,0.046284299,-0.31274881){0.35514259,0.3368451,0.27736807,0.1681493};
SS(-0.52470763,0.46530444,0.33754711,-0.54640726,0.34339216,0.19847863,-0.35521568,0.4957142,0.26668635,-0.41843781,0.30742585,0.3397996){0.59371518,0.43575493,0.42001946,0.37011438};
SS(-0.26986228,0.26051837,0.22418657,-0.10037172,0.18891947,0.20844359,-0.13709741,0.19518884,0.034033465,-0.19461387,0.3919517,0.10437587){0.1749353,0.074828316,0.040184006,0.19075448};
SS(0.60662231,0.34516964,-0.13972301,0.74440038,0.22095066,-0.087839409,0.77491511,0.22516452,-0.26425516,0.82562789,0.37565656,-0.12707714){0.48782847,0.59875958,0.70313431,0.82387041};
SS(0.25126435,0.28098512,0.24657435,0.18202227,0.38279251,0.10350409,0.26138985,0.51848551,0.281015,0.36016656,0.41044152,0.1594367){0.18575023,0.17617817,0.40200156,0.3073722};
SS(0.00029730467,0.80760978,0.5,-0.16015893,0.67694077,0.39025863,-0.1827732,0.83017807,0.5,-0.043441254,0.79173928,0.29440137){0.88423684,0.6265216,0.95598938,0.69563564};
SS(-0.32897755,-0.67088709,0.5,-0.40125956,-0.65699374,0.33213173,-0.50400314,-0.78879927,0.5,-0.50050976,-0.57246927,0.5){0.79643001,0.69449311,1.1086821,0.81219504};
SS(0,-0.25,-0.5,0,0,-0.5,-0.12484866,-0.12486094,-0.5,0,-0.16143077,-0.33843101){0.29677328,0.23465449,0.26766045,0.12966739};
SS(0,0,0.5,0.25,0,0.5,0.1615172,0,0.33845519,0.12517622,0.12515553,0.5){0.23153294,0.29281005,0.13068911,0.27156885};
SS(-0.26986228,0.26051837,0.22418657,-0.39806707,0.15776443,0.15870839,-0.20045203,0.067929244,0.29301468,-0.36145429,0.13293621,0.35430528){0.1749353,0.19317292,0.10955402,0.26360063};
SS(-0.91004595,0.15296589,0.33139812,-1,0.11111111,0.5,-0.80727304,0.00024662976,0.5,-0.83006559,0.18329805,0.5){0.94743142,1.246301,0.88515177,0.96159482};
SS(0.1615172,0,0.33845519,0.13913358,0.10014326,0.18199659,0.050277172,0.20853018,0.30186362,0.26083053,0.15082484,0.37728795){0.13068911,0.045990896,0.12181545,0.21918499};
SS(0.098704003,0.67249079,0.1943501,0.26064395,0.61953306,0.12890567,0.24404834,0.79519787,0.082231238,0.22886345,0.79287946,0.30210005){0.47957633,0.45328252,0.68472542,0.75332396};
SS(0.26064395,0.61953306,0.12890567,0.39612945,0.70614162,0.21524614,0.24404834,0.79519787,0.082231238,0.22886345,0.79287946,0.30210005){0.45328252,0.68453461,0.68472542,0.75332396};
SS(-0.41648151,0.41684878,0.5,-0.52470763,0.46530444,0.33754711,-0.35521568,0.4957142,0.26668635,-0.41843781,0.30742585,0.3397996){0.58097186,0.59371518,0.42001946,0.37011438};
SS(0,-0.16143077,-0.33843101,-0.16707278,-0.087678023,-0.31121894,-0.1971424,-0.26981885,-0.30750196,-0.098950987,-0.13391411,-0.14594667){0.12966739,0.11599041,0.19280289,0.03512721};
SS(0.49866453,0.63973666,-0.21510859,0.42864323,0.48543211,-0.13804456,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.68344633,0.42022283,0.59958408,0.55795418};
SS(0.671223,0.32907594,-0.5,0.67125235,0.44297685,-0.31879306,0.78906409,0.5041626,-0.5,0.57309542,0.50075776,-0.5){0.79435762,0.72773009,1.1105402,0.81773274};
SS(0.65062064,0.64268786,0.069510863,0.61535375,0.70719289,-0.095218388,0.45788353,0.76094781,-0.0096633567,0.62860594,0.86645525,0.049037492){0.82620698,0.87858083,0.76853994,1.1303867};
SS(-0.50807239,-0.16307462,0.5,-0.37661764,-0.26006406,0.40868766,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.52416601,0.36234206,0.36443271,0.26156128};
SS(0,0,0.25,-0.10037172,0.18891947,0.20844359,0.050277172,0.20853018,0.30186362,0.13913358,0.10014326,0.18199659){0.045060365,0.074828316,0.12181545,0.045990896};
SS(-0.6293812,0.63993291,-0.28812602,-0.68637718,0.43295764,-0.18031685,-0.50782983,0.50249565,-0.29902586,-0.62332411,0.59900263,-0.10904345){0.87296464,0.67437813,0.58612549,0.74800561};
SS(-0.41648151,0.41684878,0.5,-0.29261734,0.53193925,0.43339885,-0.35521568,0.4957142,0.26668635,-0.52470763,0.46530444,0.33754711){0.58097186,0.53993003,0.42001946,0.59371518};
SS(0.25126435,0.28098512,0.24657435,0.26083053,0.15082484,0.37728795,0.42621669,0.19017509,0.30505062,0.27123349,0.36190713,0.41476339){0.18575023,0.21918499,0.29714896,0.36300231};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.77777778,-0.5,-0.80632325,-0.81147186,-0.5,-1,-0.83959635,-0.33115777){1.4249306,1.8436809,1.5409894,1.7998257};
SS(0.27123349,0.36190713,0.41476339,0.16321322,0.50838432,0.5,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.36300231,0.52238519,0.40200156,0.27115576};
SS(0.26083053,0.15082484,0.37728795,0.37501462,0.2307626,0.5,0.42621669,0.19017509,0.30505062,0.27123349,0.36190713,0.41476339){0.21918499,0.42590445,0.29714896,0.36300231};
SS(0.30434906,0.49798107,-4.0114635e-05,0.26064395,0.61953306,0.12890567,0.086744979,0.52712982,0.027891324,0.18202227,0.38279251,0.10350409){0.32377482,0.45328252,0.26660844,0.17617817};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.75,-0.5,-0.18848435,-0.81110947,-0.5,-0.14376826,-0.62489354,-0.5){0.53347202,0.79460868,0.92571371,0.6489606};
SS(-0.63048479,0.37587985,-0.34368186,-0.69937066,0.31351533,-0.5,-0.79644003,0.50064951,-0.5,-0.61503712,0.4760032,-0.5){0.64388066,0.81965428,1.115532,0.83978547};
SS(-1,1,0.25,-0.93582873,0.86427167,0.14668289,-0.84394966,1,0.33504415,-1,0.84108515,0.33242406){2.0473025,1.6320629,1.8084725,1.8031397};
SS(0.61535375,0.70719289,-0.095218388,0.49866453,0.63973666,-0.21510859,0.45788353,0.76094781,-0.0096633567,0.54700908,0.85955032,-0.16345766){0.87858083,0.68344633,0.76853994,1.0528061};
SS(-1,0.77777778,-0.5,-0.83248216,0.76782327,-0.31292259,-0.80479144,0.80504612,-0.5,-1,0.83964442,-0.3309874){1.8398372,1.366757,1.5255891,1.7979585};
SS(0.49866453,0.63973666,-0.21510859,0.61535375,0.70719289,-0.095218388,0.68900489,0.77311276,-0.28043733,0.54700908,0.85955032,-0.16345766){0.68344633,0.87858083,1.1326816,1.0528061};
SS(-0.41767704,0.010770256,-0.44072823,-0.58754442,0.033885734,-0.5,-0.50815189,-0.16301678,-0.5,-0.64012388,-0.10177177,-0.37237302){0.35514259,0.58180393,0.52110597,0.54269073};
SS(-0.49284988,-0.37485679,0.5,-0.62450053,-0.31310845,0.38575928,-0.50807239,-0.16307462,0.5,-0.37661764,-0.26006406,0.40868766){0.6163523,0.62379151,0.52416601,0.36234206};
SS(0.59416595,0.14141347,0.32656529,0.52843461,0.32737897,0.19102935,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.46498444,0.40790135,0.29714896,0.51484928};
SS(-0.84289574,0.018333867,0.1608607,-0.67616985,-0.069078192,0.18801024,-0.84084014,-0.14895162,0.31636914,-0.82279039,-0.18997945,0.10657137){0.72430843,0.47948004,0.81273381,0.70945047};
SS(-0.40125956,-0.65699374,0.33213173,-0.42889738,-0.75253072,0.17523232,-0.22656331,-0.68065623,0.28194433,-0.349759,-0.84853211,0.35590634){0.69449311,0.75958282,0.57683818,0.94981364};
SS(0.50136923,0.34587735,-0.44862257,0.37549445,0.49317282,-0.5,0.37532516,0.23078833,-0.5,0.27170325,0.36204749,-0.4201745){0.56260896,0.61648995,0.42551454,0.36885377};
SS(0.13913358,0.10014326,0.18199659,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362,0.26083053,0.15082484,0.37728795){0.045990896,0.18575023,0.12181545,0.21918499};
SS(0.36841015,0.87909734,0.37310922,0.21512427,0.73211919,0.5,0.22886345,0.79287946,0.30210005,0.35567295,0.65317229,0.39545235){1.0362544,0.81521474,0.75332396,0.69293227};
SS(0.39612945,0.70614162,0.21524614,0.35567295,0.65317229,0.39545235,0.26138985,0.51848551,0.281015,0.47723835,0.52605258,0.30619083){0.68453461,0.69293227,0.40200156,0.58228229};
SS(0,-0.29164705,-0.20823955,-0.15923414,-0.34171533,-0.15079999,0,-0.22019801,5.0496855e-05,-0.098950987,-0.13391411,-0.14594667){0.11473247,0.14783141,0.029059683,0.03512721};
SS(-1,0.25105097,-0.19350143,-0.92571354,0.17249619,-0.34283108,-0.77267892,0.13105707,-0.24874664,-0.89804207,0.11676539,-0.10792088){1.0825888,0.99158484,0.65386325,0.82300022};
SS(-0.83996275,-0.66999882,0.11765553,-1,-0.70710233,0.21356199,-1,-0.77608598,0.00064487429,-0.8827276,-0.88146034,0.13123348){1.1553131,1.5280688,1.5844414,1.5595365};
SS(-1,-0.70710233,0.21356199,-0.89663862,-0.69397302,0.37275403,-0.77091496,-0.77159441,0.2629049,-0.83996275,-0.66999882,0.11765553){1.5280688,1.4119512,1.2433034,1.1553131};
SS(-0.40125956,-0.65699374,0.33213173,-0.32897755,-0.67088709,0.5,-0.50400314,-0.78879927,0.5,-0.349759,-0.84853211,0.35590634){0.69449311,0.79643001,1.1086821,0.94981364};
SS(-1,-0.70710233,0.21356199,-0.83996275,-0.66999882,0.11765553,-0.77091496,-0.77159441,0.2629049,-0.8827276,-0.88146034,0.13123348){1.5280688,1.1553131,1.2433034,1.5595365};
SS(-0.77091496,-0.77159441,0.2629049,-0.79227163,-0.79754897,0.0021844777,-0.8827276,-0.88146034,0.13123348,-0.83996275,-0.66999882,0.11765553){1.2433034,1.2530106,1.5595365,1.1553131};
SS(-0.67616985,-0.069078192,0.18801024,-0.84289574,0.018333867,0.1608607,-0.76752638,0.004448061,-0.013214377,-0.82279039,-0.18997945,0.10657137){0.47948004,0.72430843,0.5734925,0.70945047};
SS(-0.31289368,0.69974287,-0.5,-0.17097214,0.64900986,-0.39927747,-0.35455825,0.80859576,-0.32177549,-0.39032311,0.63241857,-0.34621958){0.82323564,0.59741335,0.86460259,0.65630059};
SS(-1,0.25105097,-0.19350143,-0.89426176,0.41257007,-0.12932618,-1,0.29928494,0.0012550607,-0.78848723,0.26584533,-0.068869999){1.0825888,0.974079,1.0718665,0.68151298};
SS(-0.10037172,0.18891947,0.20844359,-0.0073778212,0.36022468,0.15230712,-0.13709741,0.19518884,0.034033465,-0.19461387,0.3919517,0.10437587){0.074828316,0.13675819,0.040184006,0.19075448};
SS(0.37137652,0.1767682,-0.19801193,0.50007058,0,-0.27987971,0.51910919,0.22553632,-0.31417891,0.57129187,0.13526053,-0.13726946){0.19205628,0.31006895,0.40112301,0.35115136};
SS(0.26064395,0.61953306,0.12890567,0.30434906,0.49798107,-4.0114635e-05,0.36016656,0.41044152,0.1594367,0.18202227,0.38279251,0.10350409){0.45328252,0.32377482,0.3073722,0.17617817};
SS(-0.45843014,-0.20445062,-0.15988901,-0.36174,-0.40052234,-0.23665811,-0.56113743,-0.28920115,-0.29204918,-0.38492375,-0.20017574,-0.33650716){0.26094507,0.32480953,0.46850822,0.28705324};
SS(-0.65355936,0.25468043,-0.1897796,-0.68637718,0.43295764,-0.18031685,-0.49391083,0.27907498,-0.27264436,-0.63048479,0.37587985,-0.34368186){0.51379882,0.67437813,0.37398026,0.64388066};
SS(0.37549445,0.49317282,-0.5,0.50136923,0.34587735,-0.44862257,0.48047723,0.47791267,-0.33071402,0.27170325,0.36204749,-0.4201745){0.61648995,0.56260896,0.55795418,0.36885377};
SS(-0.55555556,1,0.5,-0.60421932,0.82298164,0.34468578,-0.66659408,1,0.32529585,-0.44431425,1,0.36245944){1.5418081,1.1449713,1.5364848,1.3152029};
SS(-0.0073778212,0.36022468,0.15230712,-0.11614487,0.30919383,0.33918095,0.050277172,0.20853018,0.30186362,0.085954007,0.41736025,0.32943097){0.13675819,0.20820823,0.12181545,0.27115576};
SS(0.60662231,0.34516964,-0.13972301,0.74440038,0.22095066,-0.087839409,0.63998586,0.17856447,0.051345521,0.57129187,0.13526053,-0.13726946){0.48782847,0.59875958,0.42570365,0.35115136};
SS(0,-0.29157012,0.20836692,-0.10133362,-0.40777162,0.1162396,0,-0.49989758,0.27983937,-0.1853821,-0.42358473,0.30866054){0.11172813,0.17697987,0.30650831,0.29143101};
SS(-1,-0.70710233,0.21356199,-0.89663862,-0.69397302,0.37275403,-1,-0.84092895,0.33252059,-0.77091496,-0.77159441,0.2629049){1.5280688,1.4119512,1.8030746,1.2433034};
SS(-0.45843014,-0.20445062,-0.15988901,-0.50159539,-0.29258506,7.2987381e-06,-0.4720473,-0.063494476,-0.036829327,-0.65367362,-0.16081953,0.0014934597){0.26094507,0.32068114,0.21285629,0.4344691};
SS(-0.58934795,0.84141567,-0.18062024,-0.48255002,0.69900846,-0.19155417,-0.62332411,0.59900263,-0.10904345,-0.6293812,0.63993291,-0.28812602){1.0736489,0.74365966,0.74800561,0.87296464};
SS(-0.39806707,0.15776443,0.15870839,-0.34310942,-0.010167032,0.1509038,-0.20045203,0.067929244,0.29301468,-0.36145429,0.13293621,0.35430528){0.19317292,0.12661586,0.10955402,0.26360063};
SS(-0.54640726,0.34339216,0.19847863,-0.61674646,0.25215289,0.3447871,-0.52427834,0.10778268,0.27208728,-0.41843781,0.30742585,0.3397996){0.43575493,0.54607287,0.34448415,0.37011438};
SS(0.29173763,0,-0.20843742,0.35689191,0.091376279,-0.36932783,0.20129651,0.21389912,-0.31902192,0.37137652,0.1767682,-0.19801193){0.1134179,0.26145514,0.16839385,0.19205628};
SS(-0.39032311,0.63241857,-0.34621958,-0.31289368,0.69974287,-0.5,-0.50014045,0.79673357,-0.5,-0.35455825,0.80859576,-0.32177549){0.65630059,0.82323564,1.1145783,0.86460259};
SS(-0.37661764,-0.26006406,0.40868766,-0.25897908,-0.24013326,0.26450313,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.36234206,0.17775565,0.36443271,0.26156128};
SS(-0.74249217,0.75399014,-0.15399718,-0.58934795,0.84141567,-0.18062024,-0.62332411,0.59900263,-0.10904345,-0.6293812,0.63993291,-0.28812602){1.1267767,1.0736489,0.74800561,0.87296464};
SS(-0.41651431,0.41690828,-0.5,-0.50782983,0.50249565,-0.29902586,-0.4813337,0.60105459,-0.5,-0.61503712,0.4760032,-0.5){0.57523437,0.58612549,0.83133251,0.83978547};
SS(0,-0.29164705,-0.20823955,-0.098950987,-0.13391411,-0.14594667,0,-0.16143077,-0.33843101,-0.1971424,-0.26981885,-0.30750196){0.11473247,0.03512721,0.12966739,0.19280289};
SS(-0.65367362,-0.16081953,0.0014934597,-0.67616985,-0.069078192,0.18801024,-0.76752638,0.004448061,-0.013214377,-0.82279039,-0.18997945,0.10657137){0.4344691,0.47948004,0.5734925,0.70945047};
SS(-0.62450053,-0.31310845,0.38575928,-0.50807239,-0.16307462,0.5,-0.50874333,-0.23900991,0.2620444,-0.64009684,-0.10188458,0.37412975){0.62379151,0.52416601,0.36443271,0.54631619};
SS(-0.24163432,0.33561251,-0.055881164,-0.13709741,0.19518884,0.034033465,-0.096302334,0.43534175,-0.056072844,-0.19461387,0.3919517,0.10437587){0.16437697,0.040184006,0.18078295,0.19075448};
SS(0.25126435,0.28098512,0.24657435,0.27123349,0.36190713,0.41476339,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.18575023,0.36300231,0.40200156,0.27115576};
SS(-0.0073778212,0.36022468,0.15230712,-0.19461387,0.3919517,0.10437587,-0.11618574,0.50328545,0.29980467,-0.098708274,0.55956225,0.10505678){0.13675819,0.19075448,0.33969293,0.31633913};
SS(-0.89804207,0.11676539,-0.10792088,-1,0.25105097,-0.19350143,-1,0.29928494,0.0012550607,-0.78848723,0.26584533,-0.068869999){0.82300022,1.0825888,1.0718665,0.68151298};
SS(0.26083053,0.15082484,0.37728795,0.1615172,0,0.33845519,0.12517622,0.12515553,0.5,0.050277172,0.20853018,0.30186362){0.21918499,0.13068911,0.27156885,0.12181545};
SS(-0.66659408,1,0.32529585,-0.60421932,0.82298164,0.34468578,-0.76389013,0.77728265,0.25513738,-0.61311838,0.85766427,0.15491279){1.5364848,1.1449713,1.2358334,1.1216468};
SS(0.21512427,0.73211919,0.5,0.10211023,0.6404511,0.38011645,0.22886345,0.79287946,0.30210005,0.35567295,0.65317229,0.39545235){0.81521474,0.55160362,0.75332396,0.69293227};
SS(0,-0.70830496,-0.20826096,-0.14850787,-0.69358405,-0.087583548,-0.2399131,-0.76005145,-0.25989531,-0.12233239,-0.87748906,-0.13583418){0.5287181,0.49763432,0.6848256,0.78823805};
SS(0.10162062,0.65400865,-0.37913628,0.17777709,0.54047543,-0.2567554,-0.01813809,0.53618118,-0.30537166,0.09693172,0.3918681,-0.3370861){0.5665506,0.36840304,0.36567785,0.26256104};
SS(0.29173763,0,-0.20843742,0.35689191,0.091376279,-0.36932783,0.16149165,0,-0.33864688,0.20129651,0.21389912,-0.31902192){0.1134179,0.26145514,0.12746835,0.16839385};
SS(0.52843461,0.32737897,0.19102935,0.59416595,0.14141347,0.32656529,0.42621669,0.19017509,0.30505062,0.46476684,0.14382827,0.12247557){0.40790135,0.46498444,0.29714896,0.23450402};
SS(-0.58754442,0.033885734,-0.5,-0.41767704,0.010770256,-0.44072823,-0.49808619,0.0026201378,-0.26387206,-0.64012388,-0.10177177,-0.37237302){0.58180393,0.35514259,0.29810596,0.54269073};
SS(-1,0.11111111,-0.5,-0.92571354,0.17249619,-0.34283108,-0.80728146,0.00010990719,-0.5,-0.82994199,0.18319278,-0.5){1.2464205,0.99158484,0.88195685,0.95993957};
SS(0.16321322,0.50838432,0.5,0.10211023,0.6404511,0.38011645,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.52238519,0.55160362,0.40200156,0.27115576};
SS(0.21512427,0.73211919,0.5,0.36841015,0.87909734,0.37310922,0.45042372,0.78359022,0.5,0.35567295,0.65317229,0.39545235){0.81521474,1.0362544,1.0496179,0.69293227};
SS(-0.16707278,-0.087678023,-0.31121894,-0.12484866,-0.12486094,-0.5,0,-0.16143077,-0.33843101,-0.1971424,-0.26981885,-0.30750196){0.11599041,0.26766045,0.12966739,0.19280289};
SS(-0.32897755,-0.67088709,0.5,-0.40125956,-0.65699374,0.33213173,-0.22656331,-0.68065623,0.28194433,-0.349759,-0.84853211,0.35590634){0.79643001,0.69449311,0.57683818,0.94981364};
SS(0.39612945,0.70614162,0.21524614,0.40637652,0.87094343,0.13060843,0.24404834,0.79519787,0.082231238,0.22886345,0.79287946,0.30210005){0.68453461,0.92399337,0.68472542,0.75332396};
SS(0.35567295,0.65317229,0.39545235,0.37492492,0.49312259,0.5,0.26138985,0.51848551,0.281015,0.47723835,0.52605258,0.30619083){0.69293227,0.61809871,0.40200156,0.58228229};
SS(-0.57994589,-0.69256437,0.31204703,-0.67513028,-0.66529728,0.5,-0.50400314,-0.78879927,0.5,-0.50050976,-0.57246927,0.5){0.89957508,1.1284607,1.1086821,0.81219504};
SS(-0.76760867,-0.33664988,-0.028298027,-0.7055892,-0.50616462,-0.017961589,-0.85520613,-0.46088631,-0.14784569,-0.61549046,-0.35581383,-0.12962263){0.68479998,0.74484897,0.95161001,0.50877487};
SS(-0.0089783977,0.64320989,-0.13441642,-0.10743676,0.85847111,-0.11136175,-0.035654771,0.78507762,0.045007896,0.094968532,0.84539386,-0.087484586){0.41358858,0.7462212,0.60161266,0.71839764};
SS(0.34720309,0.90097601,-0.12745168,0.4450496,1,-0.00012892076,0.45788353,0.76094781,-0.0096633567,0.54700908,0.85955032,-0.16345766){0.93504792,1.179155,0.76853994,1.0528061};
SS(-0.3548152,-0.48825703,0.21848985,-0.40125956,-0.65699374,0.33213173,-0.22656331,-0.68065623,0.28194433,-0.34549718,-0.50098866,0.4105565){0.38862106,0.69449311,0.57683818,0.5260109};
SS(0.4450496,1,-0.00012892076,0.40637652,0.87094343,0.13060843,0.45788353,0.76094781,-0.0096633567,0.62860594,0.86645525,0.049037492){1.179155,0.92399337,0.76853994,1.1303867};
SS(-0.62341011,-0.46880832,-0.38153973,-0.65956212,-0.52273243,-0.19262862,-0.7907607,-0.33838097,-0.28342271,-0.81387526,-0.53653555,-0.3209601){0.73807879,0.7287475,0.80149819,1.0406635};
SS(-0.78315651,-0.45008839,-0.5,-0.62341011,-0.46880832,-0.38153973,-0.7907607,-0.33838097,-0.28342271,-0.81387526,-0.53653555,-0.3209601){1.0467962,0.73807879,0.80149819,1.0406635};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.61549046,-0.35581383,-0.12962263,-0.52487586,-0.5117405,-0.017639258,-0.59094649,-0.40495207,0.12834587){0.32068114,0.50877487,0.51812974,0.51475101};
SS(-0.10037172,0.18891947,0.20844359,-0.26986228,0.26051837,0.22418657,-0.20045203,0.067929244,0.29301468,-0.11614487,0.30919383,0.33918095){0.074828316,0.1749353,0.10955402,0.20820823};
SS(-0.77091496,-0.77159441,0.2629049,-0.89663862,-0.69397302,0.37275403,-0.79575191,-0.55547687,0.30538166,-0.83996275,-0.66999882,0.11765553){1.2433034,1.4119512,1.0192798,1.1553131};
SS(-0.50807239,-0.16307462,0.5,-0.62450053,-0.31310845,0.38575928,-0.50874333,-0.23900991,0.2620444,-0.37661764,-0.26006406,0.40868766){0.52416601,0.62379151,0.36443271,0.36234206};
SS(-0.68637718,0.43295764,-0.18031685,-0.6293812,0.63993291,-0.28812602,-0.80558396,0.5878127,-0.29244037,-0.62332411,0.59900263,-0.10904345){0.67437813,0.87296464,1.0616703,0.74800561};
SS(-0.76760867,-0.33664988,-0.028298027,-0.7055892,-0.50616462,-0.017961589,-0.82595855,-0.48031431,0.11444494,-0.85520613,-0.46088631,-0.14784569){0.68479998,0.74484897,0.90887195,0.95161001};
SS(-0.36145429,0.13293621,0.35430528,-0.24000819,0.17660305,0.5,-0.17669296,0.011023676,0.5,-0.20045203,0.067929244,0.29301468){0.26360063,0.3210912,0.26322593,0.10955402};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65956212,-0.52273243,-0.19262862,-0.85520613,-0.46088631,-0.14784569,-0.61549046,-0.35581383,-0.12962263){0.74484897,0.7287475,0.95161001,0.50877487};
SS(0,0,-0.25,-0.056808231,0.14323286,-0.13367928,0,0,-6.9388939e-15,0.13402468,0.11673163,-0.1460819){0.044304329,0.022140076,-0.017891206,0.039337265};
SS(-0.14850787,-0.69358405,-0.087583548,0,-0.77970171,0.00010845427,-0.22302806,-0.77703925,0.068353305,-0.12233239,-0.87748906,-0.13583418){0.49763432,0.58842154,0.64063544,0.78823805};
SS(-0.61115597,1,-0.10200355,-0.58934795,0.84141567,-0.18062024,-0.76988954,1,-0.26944904,-0.56041637,1,-0.29784853){1.3611038,1.0736489,1.6463902,1.3856141};
SS(0.36841015,0.87909734,0.37310922,0.43654676,1,0.2604635,0.55555177,0.82262944,0.31125158,0.40637652,0.87094343,0.13060843){1.0362544,1.2403655,1.0671623,0.92399337};
SS(0.51674933,0.64481281,-0.39755292,0.57309542,0.50075776,-0.5,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.82858869,0.81773274,0.72773009,0.55795418};
SS(-0.34310942,-0.010167032,0.1509038,-0.39806707,0.15776443,0.15870839,-0.52427834,0.10778268,0.27208728,-0.36145429,0.13293621,0.35430528){0.12661586,0.19317292,0.34448415,0.26360063};
SS(-0.74954172,1,0.13574231,-0.61311838,0.85766427,0.15491279,-0.76389013,0.77728265,0.25513738,-0.79370724,0.81084643,0.045877226){1.562759,1.1216468,1.2358334,1.270911};
SS(0.26138985,0.51848551,0.281015,0.26064395,0.61953306,0.12890567,0.36016656,0.41044152,0.1594367,0.18202227,0.38279251,0.10350409){0.40200156,0.45328252,0.3073722,0.17617817};
SS(-0.40125956,-0.65699374,0.33213173,-0.32897755,-0.67088709,0.5,-0.22656331,-0.68065623,0.28194433,-0.34549718,-0.50098866,0.4105565){0.69449311,0.79643001,0.57683818,0.5260109};
SS(-0.86742481,-0.86548068,-0.14483364,-1,-1,-0.25,-1,-0.83959635,-0.33115777,-0.83846289,-1,-0.33858677){1.5085891,2.0422973,1.7998257,1.8019179};
SS(-0.65631386,-0.59724887,0.13822882,-0.83996275,-0.66999882,0.11765553,-0.77091496,-0.77159441,0.2629049,-0.79575191,-0.55547687,0.30538166){0.7890621,1.1553131,1.2433034,1.0192798};
SS(-0.11614487,0.30919383,0.33918095,-0.0073778212,0.36022468,0.15230712,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.20820823,0.13675819,0.33969293,0.27115576};
SS(-0.91004595,0.15296589,0.33139812,-0.7489605,0.18190923,0.13647301,-0.72768327,0.10310141,0.33233484,-0.84289574,0.018333867,0.1608607){0.94743142,0.59564173,0.63492881,0.72430843};
SS(0.50136923,0.34587735,-0.44862257,0.34662081,0.36199915,-0.25068724,0.51910919,0.22553632,-0.31417891,0.48047723,0.47791267,-0.33071402){0.56260896,0.29696992,0.40112301,0.55795418};
SS(-0.65631386,-0.59724887,0.13822882,-0.7055892,-0.50616462,-0.017961589,-0.82285362,-0.63420593,-0.0683896,-0.83996275,-0.66999882,0.11765553){0.7890621,0.74484897,1.0691297,1.1553131};
SS(-0.62332411,0.59900263,-0.10904345,-0.65776896,0.64141588,0.074371921,-0.54631436,0.45612147,-0.00074796238,-0.45563594,0.60375179,0.095527884){0.74800561,0.83514199,0.48593017,0.56263538};
SS(-0.84394966,1,0.33504415,-1,1,0.5,-1,1,0.25,-1,0.84108515,0.33242406){1.8084725,2.2338249,2.0473025,1.8031397};
SS(-0.6293812,0.63993291,-0.28812602,-0.74249217,0.75399014,-0.15399718,-0.80558396,0.5878127,-0.29244037,-0.62332411,0.59900263,-0.10904345){0.87296464,1.1267767,1.0616703,0.74800561};
SS(0.39612945,0.70614162,0.21524614,0.40637652,0.87094343,0.13060843,0.45788353,0.76094781,-0.0096633567,0.24404834,0.79519787,0.082231238){0.68453461,0.92399337,0.76853994,0.68472542};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.25105097,-0.19350143,-1,0.47527469,-0.27513051,-0.83127473,0.33505962,-0.32026923){0.974079,1.0825888,1.2834809,0.89071695};
SS(0.50136923,0.34587735,-0.44862257,0.34662081,0.36199915,-0.25068724,0.48047723,0.47791267,-0.33071402,0.27170325,0.36204749,-0.4201745){0.56260896,0.29696992,0.55795418,0.36885377};
SS(0.40637652,0.87094343,0.13060843,0.4450496,1,-0.00012892076,0.45788353,0.76094781,-0.0096633567,0.34720309,0.90097601,-0.12745168){0.92399337,1.179155,0.76853994,0.93504792};
SS(-0.83248216,0.76782327,-0.31292259,-1,0.70529035,-0.21162945,-0.74249217,0.75399014,-0.15399718,-0.89962374,0.8609561,-0.16698164){1.366757,1.520296,1.1267767,1.5692753};
SS(-0.58934795,0.84141567,-0.18062024,-0.76988954,1,-0.26944904,-0.56041637,1,-0.29784853,-0.65756371,0.81308934,-0.3429452){1.0736489,1.6463902,1.3856141,1.1958888};
SS(0.09693172,0.3918681,-0.3370861,-0.029932551,0.40748663,-0.5,-0.12449617,0.36606215,-0.28273955,-0.01813809,0.53618118,-0.30537166){0.26256104,0.4038008,0.21185338,0.36567785};
SS(-0.83006559,0.18329805,0.5,-0.91004595,0.15296589,0.33139812,-0.83851866,0.33014205,0.32623765,-0.72768327,0.10310141,0.33233484){0.96159482,0.94743142,0.89937894,0.63492881};
SS(-0.30131805,-0.11512588,-0.5,-0.16707278,-0.087678023,-0.31121894,-0.19007896,0.04567822,-0.5,-0.29413589,0.046284299,-0.31274881){0.3368451,0.11599041,0.27736807,0.1681493};
SS(-0.73174678,-0.21478859,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.80728146,0.00010990719,-0.5,-0.85707128,-0.1416783,-0.34083416){0.81151292,0.54269073,0.88195685,0.85441326};
SS(-0.40125956,-0.65699374,0.33213173,-0.50400314,-0.78879927,0.5,-0.50050976,-0.57246927,0.5,-0.57994589,-0.69256437,0.31204703){0.69449311,1.1086821,0.81219504,0.89957508};
SS(0.671223,0.32907594,-0.5,0.50136923,0.34587735,-0.44862257,0.6251418,0.1440922,-0.5,0.51910919,0.22553632,-0.31417891){0.79435762,0.56260896,0.63751638,0.40112301};
SS(-0.24163432,0.33561251,-0.055881164,-0.32064519,0.49448821,1.4739833e-06,-0.39654734,0.26661646,0.019312696,-0.19461387,0.3919517,0.10437587){0.16437697,0.32892635,0.20710489,0.19075448};
SS(-0.29261734,0.53193925,0.43339885,-0.41648151,0.41684878,0.5,-0.48141868,0.60085372,0.5,-0.52470763,0.46530444,0.33754711){0.53993003,0.58097186,0.82306978,0.59371518};
SS(0.68985253,1,-0.19792707,0.54326203,0.87223293,-0.356993,0.68900489,0.77311276,-0.28043733,0.54700908,0.85955032,-0.16345766){1.495304,1.1662147,1.1326816,1.0528061};
SS(-0.58755791,0.033814853,0.5,-0.64009684,-0.10188458,0.37412975,-0.80727304,0.00024662976,0.5,-0.72768327,0.10310141,0.33233484){0.57778723,0.54631619,0.88515177,0.63492881};
SS(0.13402468,0.11673163,-0.1460819,0.29173763,0,-0.20843742,0.16149165,0,-0.33864688,0.20129651,0.21389912,-0.31902192){0.039337265,0.1134179,0.12746835,0.16839385};
SS(-0.69937066,0.31351533,-0.5,-0.63048479,0.37587985,-0.34368186,-0.83127473,0.33505962,-0.32026923,-0.62938155,0.17932964,-0.37445272){0.81965428,0.64388066,0.89071695,0.55109073};
SS(-0.92571354,0.17249619,-0.34283108,-1,0.25105097,-0.19350143,-1,-0.00018427889,-0.26378916,-0.89804207,0.11676539,-0.10792088){0.99158484,1.0825888,1.0508045,0.82300022};
SS(0.10162062,0.65400865,-0.37913628,0.16368264,0.50834729,-0.5,0.17777709,0.54047543,-0.2567554,0.09693172,0.3918681,-0.3370861){0.5665506,0.52115901,0.36840304,0.26256104};
SS(-0.4543958,0.20406131,0.5,-0.36145429,0.13293621,0.35430528,-0.52427834,0.10778268,0.27208728,-0.41843781,0.30742585,0.3397996){0.48353653,0.26360063,0.34448415,0.37011438};
SS(-0.89804207,0.11676539,-0.10792088,-1,-0.00021427218,0.00011802244,-1,-0.00018427889,-0.26378916,-0.88905946,-0.098697315,-0.13184676){0.82300022,0.98080906,1.0508045,0.8023886};
SS(0.74440038,0.22095066,-0.087839409,0.70841775,0,-0.20847891,0.77491511,0.22516452,-0.26425516,0.88354722,0.11667767,-0.13069643){0.59875958,0.52293439,0.70313431,0.79839767};
SS(-0.30122568,-0.11513872,0.5,-0.16643696,-0.21791406,0.42402077,-0.23048975,-0.37484721,0.5,-0.37661764,-0.26006406,0.40868766){0.33848202,0.23818505,0.42714666,0.36234206};
SS(-0.49284988,-0.37485679,0.5,-0.37661764,-0.26006406,0.40868766,-0.23048975,-0.37484721,0.5,-0.34549718,-0.50098866,0.4105565){0.6163523,0.36234206,0.42714666,0.5260109};
SS(0.45062041,0.7833899,-0.5,0.51674933,0.64481281,-0.39755292,0.33386283,0.81592026,-0.31808704,0.34412919,0.6158316,-0.3427703){1.0506853,0.82858869,0.86115027,0.59958408};
SS(-0.63815223,-0.88141187,0.37488811,-0.67513028,-0.66529728,0.5,-0.50400314,-0.78879927,0.5,-0.57994589,-0.69256437,0.31204703){1.3088768,1.1284607,1.1086821,0.89957508};
SS(-0.49284988,-0.37485679,0.5,-0.34549718,-0.50098866,0.4105565,-0.50050976,-0.57246927,0.5,-0.56348952,-0.47594309,0.3052276){0.6163523,0.5260109,0.81219504,0.61776713};
SS(-1,-1,0.5,-0.8385203,-1,0.33846229,-1,-1,0.25,-1,-0.84092895,0.33252059){2.2322143,1.8024192,2.0427074,1.8030746};
SS(-0.92571354,0.17249619,-0.34283108,-1,-0.00018427889,-0.26378916,-0.77267892,0.13105707,-0.24874664,-0.89804207,0.11676539,-0.10792088){0.99158484,1.0508045,0.65386325,0.82300022};
SS(0.36841015,0.87909734,0.37310922,0.33333333,1,0.5,0.43654676,1,0.2604635,0.23106485,1,0.31398279){1.0362544,1.3466764,1.2403655,1.1340577};
SS(0,-1,0.5,0,-0.83845667,0.33864852,0,-1,0.25,-0.16134158,-1,0.33850563){1.232491,0.80178572,1.0438639,1.129042};
SS(1,0.16156328,-0.33847781,1,0,-0.5,1,0,-0.25,0.83867599,0,-0.33865964){1.1261583,1.2327879,1.043399,0.80182539};
SS(0,-0.83851883,-0.33849865,0,-1,-0.5,0,-1,-0.25,-0.16144976,-1,-0.33863959){0.80235204,1.2333742,1.0435946,1.1250711};
SS(1,0,0.5,1,0.16158711,0.33859063,1,0,0.25,0.83866368,0,0.33843958){1.2336156,1.1259698,1.0436257,0.80106313};
SS(-0.34310942,-0.010167032,0.1509038,-0.1159097,-0.14329028,0.19302206,-0.20656092,-0.13938028,0.029547229,-0.15128303,0.02253305,0.11422928){0.12661586,0.055235283,0.048278496,0.025420414};
SS(-0.55555556,1,0.5,-0.60421932,0.82298164,0.34468578,-0.44431425,1,0.36245944,-0.50037,0.79662088,0.5){1.5418081,1.1449713,1.3152029,1.1183194};
SS(-0.29237157,-0.11865629,-0.17606411,-0.15923414,-0.34171533,-0.15079999,-0.1971424,-0.26981885,-0.30750196,-0.098950987,-0.13391411,-0.14594667){0.11404163,0.14783141,0.19280289,0.03512721};
SS(-0.40125956,-0.65699374,0.33213173,-0.3548152,-0.48825703,0.21848985,-0.56348952,-0.47594309,0.3052276,-0.34549718,-0.50098866,0.4105565){0.69449311,0.38862106,0.61776713,0.5260109};
SS(-0.52487586,-0.5117405,-0.017639258,-0.7055892,-0.50616462,-0.017961589,-0.59094649,-0.40495207,0.12834587,-0.61549046,-0.35581383,-0.12962263){0.51812974,0.74484897,0.51475101,0.50877487};
SS(-0.36145429,0.13293621,0.35430528,-0.24000819,0.17660305,0.5,-0.40752783,0.030201366,0.5,-0.17669296,0.011023676,0.5){0.26360063,0.3210912,0.40526498,0.26322593};
SS(0.86971177,0.13024645,0.1427188,1,0,0.25,1,0.16158711,0.33859063,0.83866368,0,0.33843958){0.77797836,1.0436257,1.1259698,0.80106313};
SS(-0.65355936,0.25468043,-0.1897796,-0.68637718,0.43295764,-0.18031685,-0.78848723,0.26584533,-0.068869999,-0.63246299,0.29145388,0.035195127){0.51379882,0.67437813,0.68151298,0.47226275};
SS(-1,0.70725984,0.21334539,-0.8480722,0.62150313,0.12164012,-0.76389013,0.77728265,0.25513738,-0.87046532,0.63071146,0.35630423){1.5286486,1.1084494,1.2358334,1.2666006};
SS(-0.65776896,0.64141588,0.074371921,-0.8480722,0.62150313,0.12164012,-0.76389013,0.77728265,0.25513738,-0.79370724,0.81084643,0.045877226){0.83514199,1.1084494,1.2358334,1.270911};
SS(-0.64009684,-0.10188458,0.37412975,-0.50807239,-0.16307462,0.5,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.54631619,0.52416601,0.36443271,0.26156128};
SS(0.11523872,0.30161582,0.5,0.26083053,0.15082484,0.37728795,0.12517622,0.12515553,0.5,0.050277172,0.20853018,0.30186362){0.33546792,0.21918499,0.27156885,0.12181545};
SS(-0.16643696,-0.21791406,0.42402077,-0.30122568,-0.11513872,0.5,-0.25897908,-0.24013326,0.26450313,-0.37661764,-0.26006406,0.40868766){0.23818505,0.33848202,0.17775565,0.36234206};
SS(-0.83846289,-1,-0.33858677,-1,-1,-0.5,-1,-1,-0.25,-1,-0.83959635,-0.33115777){1.8019179,2.2321573,2.0422973,1.7998257};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.19461387,0.3919517,0.10437587,-0.096302334,0.43534175,-0.056072844,-0.098708274,0.55956225,0.10505678){0.32892635,0.19075448,0.18078295,0.31633913};
SS(-0.79227163,-0.79754897,0.0021844777,-1,-0.77608598,0.00064487429,-0.82285362,-0.63420593,-0.0683896,-0.86742481,-0.86548068,-0.14483364){1.2530106,1.5844414,1.0691297,1.5085891};
SS(-0.50050976,-0.57246927,0.5,-0.40125956,-0.65699374,0.33213173,-0.56348952,-0.47594309,0.3052276,-0.34549718,-0.50098866,0.4105565){0.81219504,0.69449311,0.61776713,0.5260109};
SS(0.27123349,0.36190713,0.41476339,0.37501462,0.2307626,0.5,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.36300231,0.42590445,0.29714896,0.51484928};
SS(0,0,-0.5,-0.16707278,-0.087678023,-0.31121894,-0.12484866,-0.12486094,-0.5,0,-0.16143077,-0.33843101){0.23465449,0.11599041,0.26766045,0.12966739};
SS(0.36021608,0.23247759,-0.012351094,0.24635331,0.35131343,-0.096025322,0.13261259,0.21336316,0.036566127,0.18202227,0.38279251,0.10350409){0.16110593,0.18045455,0.046199082,0.17617817};
SS(0.17426348,1,-0.18078905,0.34720309,0.90097601,-0.12745168,0.24937941,1,-0.00011138016,0.094968532,0.84539386,-0.087484586){1.045853,0.93504792,1.0446566,0.71839764};
SS(-0.67801153,0.56076489,0.29217382,-0.66546973,0.66566005,0.5,-0.79641575,0.50054117,0.5,-0.61509744,0.47589965,0.5){0.83617727,1.1224691,1.1180299,0.84259202};
SS(0.24635331,0.35131343,-0.096025322,0.30434906,0.49798107,-4.0114635e-05,0.086744979,0.52712982,0.027891324,0.18202227,0.38279251,0.10350409){0.18045455,0.32377482,0.26660844,0.17617817};
SS(0.10162062,0.65400865,-0.37913628,-0.033284914,0.58770906,-0.5,0.16368264,0.50834729,-0.5,-0.01813809,0.53618118,-0.30537166){0.5665506,0.58301644,0.52115901,0.36567785};
SS(-1,1,-0.25,-0.89962374,0.8609561,-0.16698164,-0.76988954,1,-0.26944904,-0.81095336,1,-0.07156149){2.0450698,1.5692753,1.6463902,1.6471359};
SS(-0.098708274,0.55956225,0.10505678,-0.0073778212,0.36022468,0.15230712,-0.096302334,0.43534175,-0.056072844,0.086744979,0.52712982,0.027891324){0.31633913,0.13675819,0.18078295,0.26660844};
SS(1,1,0.25,0.88049681,0.87960137,0.13412341,1,0.83856906,0.33864755,0.82853688,1,0.32125076){2.0447444,1.5518824,1.8033242,1.7703132};
SS(-0.75,-1,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.80632325,-0.81147186,-0.5,-0.83846289,-1,-0.33858677){1.7946951,1.296688,1.5409894,1.8019179};
SS(0.37492492,0.49312259,0.5,0.27123349,0.36190713,0.41476339,0.16321322,0.50838432,0.5,0.26138985,0.51848551,0.281015){0.61809871,0.36300231,0.52238519,0.40200156};
SS(-0.4433427,0.53576375,-0.12560501,-0.54631436,0.45612147,-0.00074796238,-0.45563594,0.60375179,0.095527884,-0.62332411,0.59900263,-0.10904345){0.48429505,0.48593017,0.56263538,0.74800561};
SS(-0.10037172,0.18891947,0.20844359,-0.26986228,0.26051837,0.22418657,-0.11614487,0.30919383,0.33918095,-0.19461387,0.3919517,0.10437587){0.074828316,0.1749353,0.20820823,0.19075448};
SS(-0.91004595,0.15296589,0.33139812,-1,0.33333333,0.5,-1,0.11111111,0.5,-0.83006559,0.18329805,0.5){0.94743142,1.3403692,1.246301,0.96159482};
SS(-0.40125956,-0.65699374,0.33213173,-0.50050976,-0.57246927,0.5,-0.56348952,-0.47594309,0.3052276,-0.57994589,-0.69256437,0.31204703){0.69449311,0.81219504,0.61776713,0.89957508};
SS(-0.83248216,0.76782327,-0.31292259,-0.66548665,0.66585508,-0.5,-0.80479144,0.80504612,-0.5,-0.65756371,0.81308934,-0.3429452){1.366757,1.1221664,1.5255891,1.1958888};
SS(0.74440038,0.22095066,-0.087839409,0.77491511,0.22516452,-0.26425516,0.82562789,0.37565656,-0.12707714,0.88354722,0.11667767,-0.13069643){0.59875958,0.70313431,0.82387041,0.79839767};
SS(-1,-1,0.25,-0.8827276,-0.88146034,0.13123348,-1,-0.84092895,0.33252059,-0.8385203,-1,0.33846229){2.0427074,1.5595365,1.8030746,1.8024192};
SS(0.16368264,0.50834729,-0.5,0.10162062,0.65400865,-0.37913628,-0.01813809,0.53618118,-0.30537166,0.09693172,0.3918681,-0.3370861){0.52115901,0.5665506,0.36567785,0.26256104};
SS(0.87881231,0.64063264,0.37220388,1,0.75,0.5,1,0.83856906,0.33864755,0.81191124,0.80644944,0.5){1.3069719,1.7930237,1.8033242,1.5425973};
SS(-0.36145429,0.13293621,0.35430528,-0.34310942,-0.010167032,0.1509038,-0.20045203,0.067929244,0.29301468,-0.40506391,-0.079541407,0.3303193){0.26360063,0.12661586,0.10955402,0.26156128};
SS(-0.41651431,0.41690828,-0.5,-0.39032311,0.63241857,-0.34621958,-0.4813337,0.60105459,-0.5,-0.50782983,0.50249565,-0.29902586){0.57523437,0.65630059,0.83133251,0.58612549};
SS(-0.4433427,0.53576375,-0.12560501,-0.68637718,0.43295764,-0.18031685,-0.54631436,0.45612147,-0.00074796238,-0.62332411,0.59900263,-0.10904345){0.48429505,0.67437813,0.48593017,0.74800561};
SS(0.34662081,0.36199915,-0.25068724,0.27170325,0.36204749,-0.4201745,0.20129651,0.21389912,-0.31902192,0.09693172,0.3918681,-0.3370861){0.29696992,0.36885377,0.16839385,0.26256104};
SS(-0.5555987,0.045150158,0.095162244,-0.67616985,-0.069078192,0.18801024,-0.76752638,0.004448061,-0.013214377,-0.65367362,-0.16081953,0.0014934597){0.29993682,0.47948004,0.5734925,0.4344691};
SS(-0.7055892,-0.50616462,-0.017961589,-0.76760867,-0.33664988,-0.028298027,-0.82595855,-0.48031431,0.11444494,-0.59094649,-0.40495207,0.12834587){0.74484897,0.68479998,0.90887195,0.51475101};
SS(-0.61674646,0.25215289,0.3447871,-0.4543958,0.20406131,0.5,-0.52427834,0.10778268,0.27208728,-0.41843781,0.30742585,0.3397996){0.54607287,0.48353653,0.34448415,0.37011438};
SS(0.24635331,0.35131343,-0.096025322,0.34662081,0.36199915,-0.25068724,0.36021608,0.23247759,-0.012351094,0.37137652,0.1767682,-0.19801193){0.18045455,0.29696992,0.16110593,0.19205628};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.24163432,0.33561251,-0.055881164,-0.39654734,0.26661646,0.019312696,-0.34372617,0.39779568,-0.18541051){0.32892635,0.16437697,0.20710489,0.29650146};
SS(0.671223,0.32907594,-0.5,0.87867265,0.36391919,-0.37720578,0.78906409,0.5041626,-0.5,0.67125235,0.44297685,-0.31879306){0.79435762,1.03034,1.1105402,0.72773009};
SS(-0.23583358,-0.36008743,0.0071767184,-0.3533559,-0.49437708,0.037576204,-0.18618058,-0.5161726,-0.15035515,-0.19247216,-0.56000521,0.088357129){0.16465457,0.35575629,0.30914003,0.34206231};
SS(-0.12233239,-0.87748906,-0.13583418,0,-1,-0.25,-0.16144976,-1,-0.33863959,0,-0.83851883,-0.33849865){0.78823805,1.0435946,1.1250711,0.80235204};
SS(-0.39032311,0.63241857,-0.34621958,-0.4813337,0.60105459,-0.5,-0.50782983,0.50249565,-0.29902586,-0.6293812,0.63993291,-0.28812602){0.65630059,0.83133251,0.58612549,0.87296464};
SS(-1,0.70725984,0.21334539,-0.93582873,0.86427167,0.14668289,-1,0.84108515,0.33242406,-0.76389013,0.77728265,0.25513738){1.5286486,1.6320629,1.8031397,1.2358334};
SS(0.29173763,0,-0.20843742,0.37137652,0.1767682,-0.19801193,0.20129651,0.21389912,-0.31902192,0.13402468,0.11673163,-0.1460819){0.1134179,0.19205628,0.16839385,0.039337265};
SS(-0.93582873,0.86427167,0.14668289,-1,0.70725984,0.21334539,-1,0.77631186,0.00053339564,-0.8480722,0.62150313,0.12164012){1.6320629,1.5286486,1.5817554,1.1084494};
SS(0.27123349,0.36190713,0.41476339,0.37492492,0.49312259,0.5,0.37501462,0.2307626,0.5,0.50761134,0.34933779,0.39015973){0.36300231,0.61809871,0.42590445,0.51484928};
SS(-0.75,-1,0.5,-0.63815223,-0.88141187,0.37488811,-0.8385203,-1,0.33846229,-0.80635543,-0.81164184,0.5){1.7943537,1.3088768,1.8024192,1.5410993};
SS(0.36841015,0.87909734,0.37310922,0.39612945,0.70614162,0.21524614,0.22886345,0.79287946,0.30210005,0.40637652,0.87094343,0.13060843){1.0362544,0.68453461,0.75332396,0.92399337};
SS(-0.68637718,0.43295764,-0.18031685,-0.62332411,0.59900263,-0.10904345,-0.80558396,0.5878127,-0.29244037,-0.8068077,0.56885008,-0.063754108){0.67437813,0.74800561,1.0616703,0.96112076};
SS(0.87881231,0.64063264,0.37220388,1,0.70834898,0.20844998,1,0.50005385,0.27984222,0.84582719,0.572243,0.1361951){1.3069719,1.5291243,1.3085441,1.0417018};
SS(0,0,-6.9388939e-15,-0.098950987,-0.13391411,-0.14594667,0,-0.22019801,5.0496855e-05,-0.20656092,-0.13938028,0.029547229){-0.017891206,0.03512721,0.029059683,0.048278496};
SS(0.61535375,0.70719289,-0.095218388,0.75922048,0.56990614,-0.17060419,0.68900489,0.77311276,-0.28043733,0.77861211,0.77861193,-0.067175459){0.87858083,0.91133836,1.1326816,1.1981052};
SS(-0.38143574,0.84373572,-0.12387887,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){0.85864479,1.169501,0.83409809,0.84829643};
SS(-0.25897908,-0.24013326,0.26450313,-0.3727858,-0.19869367,0.11195566,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.17775565,0.16948569,0.36443271,0.26156128};
SS(-0.47972312,1,0.18932995,-0.60421932,0.82298164,0.34468578,-0.66659408,1,0.32529585,-0.61311838,0.85766427,0.15491279){1.2473472,1.1449713,1.5364848,1.1216468};
SS(-0.83127473,0.33505962,-0.32026923,-0.65355936,0.25468043,-0.1897796,-0.62938155,0.17932964,-0.37445272,-0.63048479,0.37587985,-0.34368186){0.89071695,0.51379882,0.55109073,0.64388066};
SS(-0.63815223,-0.88141187,0.37488811,-0.77091496,-0.77159441,0.2629049,-0.57994589,-0.69256437,0.31204703,-0.61978497,-0.82706917,0.12738472){1.3088768,1.2433034,0.89957508,1.0681409};
SS(-1,-0.5000565,0.0033661208,-0.83996275,-0.66999882,0.11765553,-0.82285362,-0.63420593,-0.0683896,-0.82595855,-0.48031431,0.11444494){1.2263361,1.1553131,1.0691297,0.90887195};
SS(-0.68637718,0.43295764,-0.18031685,-0.4433427,0.53576375,-0.12560501,-0.50782983,0.50249565,-0.29902586,-0.62332411,0.59900263,-0.10904345){0.67437813,0.48429505,0.58612549,0.74800561};
SS(-0.23048975,-0.37484721,0.5,-0.16643696,-0.21791406,0.42402077,-0.25897908,-0.24013326,0.26450313,-0.37661764,-0.26006406,0.40868766){0.42714666,0.23818505,0.17775565,0.36234206};
SS(-0.93582873,0.86427167,0.14668289,-1,0.77631186,0.00053339564,-0.79370724,0.81084643,0.045877226,-0.8480722,0.62150313,0.12164012){1.6320629,1.5817554,1.270911,1.1084494};
SS(1,0.25,0.5,0.87272604,0.35900693,0.37172569,1,0.16158711,0.33859063,0.81143387,0.18901581,0.5){1.2942978,1.0107603,1.1259698,0.9265446};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.61549046,-0.35581383,-0.12962263,-0.76760867,-0.33664988,-0.028298027,-0.65367362,-0.16081953,0.0014934597){0.32068114,0.50877487,0.68479998,0.4344691};
SS(-0.24163432,0.33561251,-0.055881164,-0.26297351,0.20404986,-0.17122089,-0.39654734,0.26661646,0.019312696,-0.34372617,0.39779568,-0.18541051){0.16437697,0.12773981,0.20710489,0.29650146};
SS(-0.14850787,-0.69358405,-0.087583548,-0.35582611,-0.64426575,-0.070000747,-0.22302806,-0.77703925,0.068353305,-0.19247216,-0.56000521,0.088357129){0.49763432,0.52757348,0.64063544,0.34206231};
SS(0.39612945,0.70614162,0.21524614,0.36841015,0.87909734,0.37310922,0.55555177,0.82262944,0.31125158,0.40637652,0.87094343,0.13060843){0.68453461,1.0362544,1.0671623,0.92399337};
SS(-0.64009684,-0.10188458,0.37412975,-0.73174745,-0.21491043,0.5,-0.80727304,0.00024662976,0.5,-0.84084014,-0.14895162,0.31636914){0.54631619,0.81377033,0.88515177,0.81273381};
SS(-0.39806707,0.15776443,0.15870839,-0.54640726,0.34339216,0.19847863,-0.52427834,0.10778268,0.27208728,-0.41843781,0.30742585,0.3397996){0.19317292,0.43575493,0.34448415,0.37011438};
SS(-0.76389013,0.77728265,0.25513738,-0.93582873,0.86427167,0.14668289,-0.79370724,0.81084643,0.045877226,-0.8480722,0.62150313,0.12164012){1.2358334,1.6320629,1.270911,1.1084494};
SS(0.85153485,0.65148612,-0.35468846,1,0.75,-0.5,0.81205362,0.80656044,-0.5,1,0.83864447,-0.33847614){1.2568282,1.7924126,1.5391707,1.8065101};
SS(-0.16643696,-0.21791406,0.42402077,-0.23048975,-0.37484721,0.5,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.23818505,0.42714666,0.17775565,0.29143101};
SS(-0.033284914,0.58770906,-0.5,-0.17097214,0.64900986,-0.39927747,-0.01813809,0.53618118,-0.30537166,0.10162062,0.65400865,-0.37913628){0.58301644,0.59741335,0.36567785,0.5665506};
SS(0.4450496,1,-0.00012892076,0.34720309,0.90097601,-0.12745168,0.43683247,1,-0.26068681,0.54700908,0.85955032,-0.16345766){1.179155,0.93504792,1.2435523,1.0528061};
SS(-0.222315,1,-0.00011890035,-0.10743676,0.85847111,-0.11136175,-0.088882135,1,-0.23281641,-0.012406168,1,-0.034358602){1.0307381,0.7462212,1.0431215,0.99121748};
SS(0.08017426,0.31429474,-0.16745504,0.24635331,0.35131343,-0.096025322,0.13261259,0.21336316,0.036566127,0.13402468,0.11673163,-0.1460819){0.11103103,0.18045455,0.046199082,0.039337265};
SS(-0.0073778212,0.36022468,0.15230712,-0.10037172,0.18891947,0.20844359,-0.11614487,0.30919383,0.33918095,-0.19461387,0.3919517,0.10437587){0.13675819,0.074828316,0.20820823,0.19075448};
SS(0.43683247,1,-0.26068681,0.34720309,0.90097601,-0.12745168,0.33386283,0.81592026,-0.31808704,0.54700908,0.85955032,-0.16345766){1.2435523,0.93504792,0.86115027,1.0528061};
SS(-0.29261734,0.53193925,0.43339885,-0.41648151,0.41684878,0.5,-0.35521568,0.4957142,0.26668635,-0.41843781,0.30742585,0.3397996){0.53993003,0.58097186,0.42001946,0.37011438};
SS(0.68900489,0.77311276,-0.28043733,0.61535375,0.70719289,-0.095218388,0.77861211,0.77861193,-0.067175459,0.54700908,0.85955032,-0.16345766){1.1326816,0.87858083,1.1981052,1.0528061};
SS(-0.61549046,-0.35581383,-0.12962263,-0.76760867,-0.33664988,-0.028298027,-0.7907607,-0.33838097,-0.28342271,-0.85520613,-0.46088631,-0.14784569){0.50877487,0.68479998,0.80149819,0.95161001};
SS(0.54326203,0.87223293,-0.356993,0.43683247,1,-0.26068681,0.33386283,0.81592026,-0.31808704,0.54700908,0.85955032,-0.16345766){1.1662147,1.2435523,0.86115027,1.0528061};
SS(-0.61311838,0.85766427,0.15491279,-0.65776896,0.64141588,0.074371921,-0.76389013,0.77728265,0.25513738,-0.79370724,0.81084643,0.045877226){1.1216468,0.83514199,1.2358334,1.270911};
SS(-0.62332411,0.59900263,-0.10904345,-0.74249217,0.75399014,-0.15399718,-0.80558396,0.5878127,-0.29244037,-0.8068077,0.56885008,-0.063754108){0.74800561,1.1267767,1.0616703,0.96112076};
SS(-0.65355936,0.25468043,-0.1897796,-0.77267892,0.13105707,-0.24874664,-0.83127473,0.33505962,-0.32026923,-0.62938155,0.17932964,-0.37445272){0.51379882,0.65386325,0.89071695,0.55109073};
SS(0.77861211,0.77861193,-0.067175459,0.61535375,0.70719289,-0.095218388,0.62860594,0.86645525,0.049037492,0.54700908,0.85955032,-0.16345766){1.1981052,0.87858083,1.1303867,1.0528061};
SS(-0.63815223,-0.88141187,0.37488811,-0.70832062,-1,0.2082538,-0.77091496,-0.77159441,0.2629049,-0.61978497,-0.82706917,0.12738472){1.3088768,1.5291125,1.2433034,1.0681409};
SS(0.46476684,0.14382827,0.12247557,0.29175541,0,0.20824909,0.50011436,0,0.27961788,0.42621669,0.19017509,0.30505062){0.23450402,0.1093371,0.30940041,0.29714896};
SS(-0.31377045,0.30492781,-0.36427962,-0.26297351,0.20404986,-0.17122089,-0.12449617,0.36606215,-0.28273955,-0.34372617,0.39779568,-0.18541051){0.30770932,0.12773981,0.21185338,0.29650146};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.70710233,0.21356199,-0.79575191,-0.55547687,0.30538166,-0.83996275,-0.66999882,0.11765553){1.4119512,1.5280688,1.0192798,1.1553131};
SS(-0.26297351,0.20404986,-0.17122089,-0.24163432,0.33561251,-0.055881164,-0.12449617,0.36606215,-0.28273955,-0.34372617,0.39779568,-0.18541051){0.12773981,0.16437697,0.21185338,0.29650146};
SS(-0.7055892,-0.50616462,-0.017961589,-0.82285362,-0.63420593,-0.0683896,-0.82595855,-0.48031431,0.11444494,-0.85520613,-0.46088631,-0.14784569){0.74484897,1.0691297,0.90887195,0.95161001};
SS(-0.34310942,-0.010167032,0.1509038,-0.36145429,0.13293621,0.35430528,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.12661586,0.26360063,0.34448415,0.26156128};
SS(-0.65956212,-0.52273243,-0.19262862,-0.61549046,-0.35581383,-0.12962263,-0.7907607,-0.33838097,-0.28342271,-0.85520613,-0.46088631,-0.14784569){0.7287475,0.50877487,0.80149819,0.95161001};
SS(-0.26297351,0.20404986,-0.17122089,-0.31377045,0.30492781,-0.36427962,-0.49391083,0.27907498,-0.27264436,-0.34372617,0.39779568,-0.18541051){0.12773981,0.30770932,0.37398026,0.29650146};
SS(-0.11111111,1,-0.5,0.0011150345,0.93517443,-0.37389303,-0.088882135,1,-0.23281641,-0.23070339,1,-0.34855306){1.2528065,1.0026385,1.0431215,1.1599423};
SS(-0.93582873,0.86427167,0.14668289,-0.74954172,1,0.13574231,-0.76389013,0.77728265,0.25513738,-0.79370724,0.81084643,0.045877226){1.6320629,1.562759,1.2358334,1.270911};
SS(1,0,-0.25,0.88354722,0.11667767,-0.13069643,1,0.16156328,-0.33847781,0.83867599,0,-0.33865964){1.043399,0.79839767,1.1261583,0.80182539};
SS(-0.32879066,-0.67072359,-0.5,-0.11754465,-0.65214472,-0.32749638,-0.14376826,-0.62489354,-0.5,-0.26056819,-0.54975154,-0.34323516){0.79007105,0.53347202,0.6489606,0.46884495};
SS(-0.63048479,0.37587985,-0.34368186,-0.41651431,0.41690828,-0.5,-0.61503712,0.4760032,-0.5,-0.50782983,0.50249565,-0.29902586){0.64388066,0.57523437,0.83978547,0.58612549};
SS(0.27123349,0.36190713,0.41476339,0.25126435,0.28098512,0.24657435,0.26138985,0.51848551,0.281015,0.36016656,0.41044152,0.1594367){0.36300231,0.18575023,0.40200156,0.3073722};
SS(-0.61978497,-0.82706917,0.12738472,-0.65631386,-0.59724887,0.13822882,-0.77091496,-0.77159441,0.2629049,-0.57994589,-0.69256437,0.31204703){1.0681409,0.7890621,1.2433034,0.89957508};
SS(0,0,-0.5,-0.16707278,-0.087678023,-0.31121894,-0.19007896,0.04567822,-0.5,-0.12484866,-0.12486094,-0.5){0.23465449,0.11599041,0.27736807,0.26766045};
SS(-0.24000819,0.17660305,0.5,-0.11614487,0.30919383,0.33918095,-0.18136176,0.40461939,0.5,-0.045146113,0.19012269,0.5){0.3210912,0.20820823,0.42386795,0.27176836};
SS(1,0.2917639,-0.20827961,0.87867265,0.36391919,-0.37720578,1,0.16156328,-0.33847781,0.77491511,0.22516452,-0.26425516){1.1127834,1.03034,1.1261583,0.70313431};
SS(-0.31377045,0.30492781,-0.36427962,-0.17603462,0.24070348,-0.5,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.30770932,0.32537509,0.21185338,0.11990198};
SS(1,0.25,-0.5,0.87867265,0.36391919,-0.37720578,0.81149777,0.18885984,-0.5,1,0.16156328,-0.33847781){1.2935113,1.03034,0.92750237,1.1261583};
SS(-0.36340067,-0.87821042,-0.37678589,-0.25,-1,-0.5,-0.18848435,-0.81110947,-0.5,-0.16144976,-1,-0.33863959){1.0307746,1.2929607,0.92571371,1.1250711};
SS(0.16368264,0.50834729,-0.5,0.27170325,0.36204749,-0.4201745,0.17777709,0.54047543,-0.2567554,0.09693172,0.3918681,-0.3370861){0.52115901,0.36885377,0.36840304,0.26256104};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.55555556,-0.5,-1,-0.47540235,-0.27521785,-0.81387526,-0.53653555,-0.3209601){1.4249306,1.5366945,1.2841965,1.0406635};
SS(-0.79227163,-0.79754897,0.0021844777,-0.77091496,-0.77159441,0.2629049,-0.61978497,-0.82706917,0.12738472,-0.83996275,-0.66999882,0.11765553){1.2530106,1.2433034,1.0681409,1.1553131};
SS(-1,0.33333333,-0.5,-0.92571354,0.17249619,-0.34283108,-0.82994199,0.18319278,-0.5,-0.83127473,0.33505962,-0.32026923){1.3393331,0.99158484,0.95993957,0.89071695};
SS(-0.63815223,-0.88141187,0.37488811,-0.70832062,-1,0.2082538,-0.8385203,-1,0.33846229,-0.77091496,-0.77159441,0.2629049){1.3088768,1.5291125,1.8024192,1.2433034};
SS(-0.7055892,-0.50616462,-0.017961589,-0.76760867,-0.33664988,-0.028298027,-0.59094649,-0.40495207,0.12834587,-0.61549046,-0.35581383,-0.12962263){0.74484897,0.68479998,0.51475101,0.50877487};
SS(-0.8480722,0.62150313,0.12164012,-0.65776896,0.64141588,0.074371921,-0.79172217,0.43302343,0.13373134,-0.8068077,0.56885008,-0.063754108){1.1084494,0.83514199,0.80968993,0.96112076};
SS(-0.24000819,0.17660305,0.5,-0.36145429,0.13293621,0.35430528,-0.4543958,0.20406131,0.5,-0.41843781,0.30742585,0.3397996){0.3210912,0.26360063,0.48353653,0.37011438};
SS(-0.62332411,0.59900263,-0.10904345,-0.65776896,0.64141588,0.074371921,-0.74249217,0.75399014,-0.15399718,-0.8068077,0.56885008,-0.063754108){0.74800561,0.83514199,1.1267767,0.96112076};
SS(-0.89426176,0.41257007,-0.12932618,-0.68637718,0.43295764,-0.18031685,-0.80558396,0.5878127,-0.29244037,-0.8068077,0.56885008,-0.063754108){0.974079,0.67437813,1.0616703,0.96112076};
SS(-0.36145429,0.13293621,0.35430528,-0.40752783,0.030201366,0.5,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.26360063,0.40526498,0.34448415,0.26156128};
SS(-0.16707278,-0.087678023,-0.31121894,-0.29237157,-0.11865629,-0.17606411,-0.29413589,0.046284299,-0.31274881,-0.38492375,-0.20017574,-0.33650716){0.11599041,0.11404163,0.1681493,0.28705324};
SS(-0.61674646,0.25215289,0.3447871,-0.69937107,0.31347586,0.5,-0.83006559,0.18329805,0.5,-0.72768327,0.10310141,0.33233484){0.54607287,0.8165723,0.96159482,0.63492881};
SS(-0.26297351,0.20404986,-0.17122089,-0.31377045,0.30492781,-0.36427962,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.12773981,0.30770932,0.21185338,0.11990198};
SS(-1,0.70725984,0.21334539,-0.93582873,0.86427167,0.14668289,-0.76389013,0.77728265,0.25513738,-0.8480722,0.62150313,0.12164012){1.5286486,1.6320629,1.2358334,1.1084494};
SS(0.4450496,1,-0.00012892076,0.40637652,0.87094343,0.13060843,0.24937941,1,-0.00011138016,0.34720309,0.90097601,-0.12745168){1.179155,0.92399337,1.0446566,0.93504792};
SS(-0.26297351,0.20404986,-0.17122089,-0.4182056,0.11248126,-0.14182463,-0.29413589,0.046284299,-0.31274881,-0.28278924,0.041190137,-0.04219563){0.12773981,0.19428145,0.1681493,0.063480395};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.29164705,-0.20823955,0,-0.49997234,-0.27965571,-0.073421274,-0.375,-0.38984354){0.14783141,0.11473247,0.30906942,0.28201081};
SS(-0.8480722,0.62150313,0.12164012,-0.65776896,0.64141588,0.074371921,-0.8068077,0.56885008,-0.063754108,-0.79370724,0.81084643,0.045877226){1.1084494,0.83514199,0.96112076,1.270911};
SS(1,0.2917639,-0.20827961,0.87867265,0.36391919,-0.37720578,0.77491511,0.22516452,-0.26425516,0.82562789,0.37565656,-0.12707714){1.1127834,1.03034,0.70313431,0.82387041};
SS(-0.20984637,0.69532212,0.20809493,-0.16015893,0.67694077,0.39025863,-0.043441254,0.79173928,0.29440137,-0.30949447,0.8262402,0.33528492){0.55022745,0.6265216,0.69563564,0.87388961};
SS(-0.8827276,-0.88146034,0.13123348,-1,-0.70710233,0.21356199,-1,-0.84092895,0.33252059,-0.77091496,-0.77159441,0.2629049){1.5595365,1.5280688,1.8030746,1.2433034};
SS(0.88354722,0.11667767,-0.13069643,1,0.2917639,-0.20827961,1,0.16156328,-0.33847781,0.77491511,0.22516452,-0.26425516){0.79839767,1.1127834,1.1261583,0.70313431};
SS(-0.16707278,-0.087678023,-0.31121894,-0.19007896,0.04567822,-0.5,-0.29413589,0.046284299,-0.31274881,-0.1182182,0.15955837,-0.3159857){0.11599041,0.27736807,0.1681493,0.11990198};
SS(-0.033588837,0.5879061,0.5,0.10211023,0.6404511,0.38011645,0.16321322,0.50838432,0.5,0.085954007,0.41736025,0.32943097){0.57806214,0.55160362,0.52238519,0.27115576};
SS(-0.29237157,-0.11865629,-0.17606411,-0.45843014,-0.20445062,-0.15988901,-0.49808619,0.0026201378,-0.26387206,-0.38492375,-0.20017574,-0.33650716){0.11404163,0.26094507,0.29810596,0.28705324};
SS(-0.63048479,0.37587985,-0.34368186,-0.68637718,0.43295764,-0.18031685,-0.50782983,0.50249565,-0.29902586,-0.6293812,0.63993291,-0.28812602){0.64388066,0.67437813,0.58612549,0.87296464};
SS(0.70841775,0,-0.20847891,0.88354722,0.11667767,-0.13069643,0.83867599,0,-0.33865964,0.77491511,0.22516452,-0.26425516){0.52293439,0.79839767,0.80182539,0.70313431};
SS(-0.65631386,-0.59724887,0.13822882,-0.7055892,-0.50616462,-0.017961589,-0.52487586,-0.5117405,-0.017639258,-0.50537844,-0.68762812,0.023695348){0.7890621,0.74484897,0.51812974,0.71483247};
SS(0.74440038,0.22095066,-0.087839409,0.60662231,0.34516964,-0.13972301,0.77491511,0.22516452,-0.26425516,0.57129187,0.13526053,-0.13726946){0.59875958,0.48782847,0.70313431,0.35115136};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.70523324,-0.21165758,-1,-0.83959635,-0.33115777,-0.76546557,-0.72634686,-0.27513208){1.4249306,1.5222776,1.7998257,1.1696133};
SS(1,0.70844226,-0.20827687,0.8781758,0.86708556,-0.1989731,1,0.83864447,-0.33847614,0.85153485,0.65148612,-0.35468846){1.5310675,1.5462283,1.8065101,1.2568282};
SS(1,0.77979347,0.00010253841,0.8988736,0.63809662,-0.070284173,0.77861211,0.77861193,-0.067175459,0.8781758,0.86708556,-0.1989731){1.5887874,1.2046527,1.1981052,1.5462283};
SS(0.36841015,0.87909734,0.37310922,0.55555556,1,0.5,0.33333333,1,0.5,0.45042372,0.78359022,0.5){1.0362544,1.5357742,1.3466764,1.0496179};
SS(-0.349759,-0.84853211,0.35590634,-0.25,-1,0.5,-0.16134158,-1,0.33850563,-0.18863677,-0.81113033,0.5){0.94981364,1.2918821,1.129042,0.92459822};
SS(-0.1182182,0.15955837,-0.3159857,0,0,-0.5,-0.19007896,0.04567822,-0.5,-0.010543702,0.17712261,-0.5){0.11990198,0.23465449,0.27736807,0.25750364};
SS(0.67112401,0.32933441,0.5,0.59416595,0.14141347,0.32656529,0.62515059,0.14422159,0.5,0.50761134,0.34933779,0.39015973){0.79210069,0.46498444,0.64726001,0.51484928};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.70830496,-0.20826096,-0.18618058,-0.5161726,-0.15035515,-0.14850787,-0.69358405,-0.087583548){0.53347202,0.5287181,0.30914003,0.49763432};
SS(1,0.70844226,-0.20827687,0.8988736,0.63809662,-0.070284173,1,0.77979347,0.00010253841,0.8781758,0.86708556,-0.1989731){1.5310675,1.2046527,1.5887874,1.5462283};
SS(-0.73479965,-0.34302295,0.24038072,-0.62450053,-0.31310845,0.38575928,-0.56348952,-0.47594309,0.3052276,-0.59094649,-0.40495207,0.12834587){0.69668046,0.62379151,0.61776713,0.51475101};
SS(0,-0.70830496,-0.20826096,-0.11754465,-0.65214472,-0.32749638,0,-0.83851883,-0.33849865,-0.2399131,-0.76005145,-0.25989531){0.5287181,0.53347202,0.80235204,0.6848256};
SS(-0.4182056,0.11248126,-0.14182463,-0.26297351,0.20404986,-0.17122089,-0.39654734,0.26661646,0.019312696,-0.28278924,0.041190137,-0.04219563){0.19428145,0.12773981,0.20710489,0.063480395};
SS(-0.31289368,0.69974287,-0.5,-0.17097214,0.64900986,-0.39927747,-0.18268367,0.83021756,-0.5,-0.35455825,0.80859576,-0.32177549){0.82323564,0.59741335,0.9573479,0.86460259};
SS(-0.88905946,-0.098697315,-0.13184676,-1,-0.25140376,-0.1934451,-1,-0.00018427889,-0.26378916,-0.85707128,-0.1416783,-0.34083416){0.8023886,1.0790534,1.0508045,0.85441326};
SS(0.54326203,0.87223293,-0.356993,0.68985253,1,-0.19792707,0.43683247,1,-0.26068681,0.54700908,0.85955032,-0.16345766){1.1662147,1.495304,1.2435523,1.0528061};
SS(-0.37661764,-0.26006406,0.40868766,-0.30122568,-0.11513872,0.5,-0.25897908,-0.24013326,0.26450313,-0.40506391,-0.079541407,0.3303193){0.36234206,0.33848202,0.17775565,0.26156128};
SS(-0.33333333,1,0.5,-0.55555556,1,0.5,-0.44431425,1,0.36245944,-0.50037,0.79662088,0.5){1.3433112,1.5418081,1.3152029,1.1183194};
SS(-0.60421932,0.82298164,0.34468578,-0.47972312,1,0.18932995,-0.66659408,1,0.32529585,-0.44431425,1,0.36245944){1.1449713,1.2473472,1.5364848,1.3152029};
SS(0.26083053,0.15082484,0.37728795,0.11523872,0.30161582,0.5,0.37501462,0.2307626,0.5,0.27123349,0.36190713,0.41476339){0.21918499,0.33546792,0.42590445,0.36300231};
SS(-0.22656331,-0.68065623,0.28194433,-0.32897755,-0.67088709,0.5,-0.18863677,-0.81113033,0.5,-0.14394692,-0.62481063,0.5){0.57683818,0.79643001,0.92459822,0.63866347};
SS(0.70841775,0,-0.20847891,0.74440038,0.22095066,-0.087839409,0.77491511,0.22516452,-0.26425516,0.57129187,0.13526053,-0.13726946){0.52293439,0.59875958,0.70313431,0.35115136};
SS(-0.0089783977,0.64320989,-0.13441642,0.10162062,0.65400865,-0.37913628,0.17777709,0.54047543,-0.2567554,-0.01813809,0.53618118,-0.30537166){0.41358858,0.5665506,0.36840304,0.36567785};
SS(-0.77091496,-0.77159441,0.2629049,-0.79227163,-0.79754897,0.0021844777,-0.61978497,-0.82706917,0.12738472,-0.8827276,-0.88146034,0.13123348){1.2433034,1.2530106,1.0681409,1.5595365};
SS(-0.2399131,-0.76005145,-0.25989531,-0.11754465,-0.65214472,-0.32749638,-0.18618058,-0.5161726,-0.15035515,-0.14850787,-0.69358405,-0.087583548){0.6848256,0.53347202,0.30914003,0.49763432};
SS(0.70841775,0,-0.20847891,0.74440038,0.22095066,-0.087839409,0.77985819,0,-0.00014691753,0.88354722,0.11667767,-0.13069643){0.52293439,0.59875958,0.58919206,0.79839767};
SS(-0.16707278,-0.087678023,-0.31121894,-0.30131805,-0.11512588,-0.5,-0.1971424,-0.26981885,-0.30750196,-0.38492375,-0.20017574,-0.33650716){0.11599041,0.3368451,0.19280289,0.28705324};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.61549046,-0.35581383,-0.12962263,-0.65367362,-0.16081953,0.0014934597,-0.45843014,-0.20445062,-0.15988901){0.32068114,0.50877487,0.4344691,0.26094507};
SS(-0.84289574,0.018333867,0.1608607,-0.67616985,-0.069078192,0.18801024,-0.7489605,0.18190923,0.13647301,-0.72768327,0.10310141,0.33233484){0.72430843,0.47948004,0.59564173,0.63492881};
SS(0.18202227,0.38279251,0.10350409,0.25126435,0.28098512,0.24657435,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.17617817,0.18575023,0.40200156,0.27115576};
SS(-0.23048975,-0.37484721,0.5,-0.37661764,-0.26006406,0.40868766,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.42714666,0.36234206,0.17775565,0.29143101};
SS(-0.3533559,-0.49437708,0.037576204,-0.35582611,-0.64426575,-0.070000747,-0.18618058,-0.5161726,-0.15035515,-0.19247216,-0.56000521,0.088357129){0.35575629,0.52757348,0.30914003,0.34206231};
SS(0.54700908,0.85955032,-0.16345766,0.4450496,1,-0.00012892076,0.45788353,0.76094781,-0.0096633567,0.62860594,0.86645525,0.049037492){1.0528061,1.179155,0.76853994,1.1303867};
SS(0,-0.75,-0.5,-0.11754465,-0.65214472,-0.32749638,-0.18848435,-0.81110947,-0.5,0,-0.83851883,-0.33849865){0.79460868,0.53347202,0.92571371,0.80235204};
SS(-0.62450053,-0.31310845,0.38575928,-0.73479965,-0.34302295,0.24038072,-0.50874333,-0.23900991,0.2620444,-0.59094649,-0.40495207,0.12834587){0.62379151,0.69668046,0.36443271,0.51475101};
SS(-1,0.49991607,0.0031934521,-0.8480722,0.62150313,0.12164012,-0.79172217,0.43302343,0.13373134,-0.8068077,0.56885008,-0.063754108){1.2302733,1.1084494,0.80968993,0.96112076};
SS(-0.81095336,1,-0.07156149,-0.89962374,0.8609561,-0.16698164,-0.74249217,0.75399014,-0.15399718,-0.79370724,0.81084643,0.045877226){1.6471359,1.5692753,1.1267767,1.270911};
SS(-0.098950987,-0.13391411,-0.14594667,-0.29237157,-0.11865629,-0.17606411,-0.20656092,-0.13938028,0.029547229,-0.28278924,0.041190137,-0.04219563){0.03512721,0.11404163,0.048278496,0.063480395};
SS(0.30434906,0.49798107,-4.0114635e-05,0.11458044,0.70010244,0.010073529,0.086744979,0.52712982,0.027891324,0.26064395,0.61953306,0.12890567){0.32377482,0.49378055,0.26660844,0.45328252};
SS(-0.62450053,-0.31310845,0.38575928,-0.50874333,-0.23900991,0.2620444,-0.56348952,-0.47594309,0.3052276,-0.59094649,-0.40495207,0.12834587){0.62379151,0.36443271,0.61776713,0.51475101};
SS(-0.91004595,0.15296589,0.33139812,-1,0.33333333,0.5,-0.83006559,0.18329805,0.5,-0.83851866,0.33014205,0.32623765){0.94743142,1.3403692,0.96159482,0.89937894};
SS(-0.7489605,0.18190923,0.13647301,-0.91347537,0.15552497,0.067511395,-0.84289574,0.018333867,0.1608607,-0.91004595,0.15296589,0.33139812){0.59564173,0.85045394,0.72430843,0.94743142};
SS(0.094968532,0.84539386,-0.087484586,0.17426348,1,-0.18078905,-0.012406168,1,-0.034358602,0.24937941,1,-0.00011138016){0.71839764,1.045853,0.99121748,1.0446566};
SS(1,1,-0.25,1,1,-0.5,1,0.83864447,-0.33847614,0.82865019,1,-0.3214153){2.0438315,2.2331531,1.8065101,1.7714679};
SS(-0.70832062,-1,0.2082538,-0.8827276,-0.88146034,0.13123348,-0.8385203,-1,0.33846229,-0.77091496,-0.77159441,0.2629049){1.5291125,1.5595365,1.8024192,1.2433034};
SS(1,1,0.5,1,1,0.25,1,0.83856906,0.33864755,0.82853688,1,0.32125076){2.2317116,2.0447444,1.8033242,1.7703132};
SS(-0.26297351,0.20404986,-0.17122089,-0.24163432,0.33561251,-0.055881164,-0.13709741,0.19518884,0.034033465,-0.056808231,0.14323286,-0.13367928){0.12773981,0.16437697,0.040184006,0.022140076};
SS(-0.34310942,-0.010167032,0.1509038,-0.3727858,-0.19869367,0.11195566,-0.25897908,-0.24013326,0.26450313,-0.40506391,-0.079541407,0.3303193){0.12661586,0.16948569,0.17775565,0.26156128};
SS(-0.29237157,-0.11865629,-0.17606411,-0.4182056,0.11248126,-0.14182463,-0.4720473,-0.063494476,-0.036829327,-0.28278924,0.041190137,-0.04219563){0.11404163,0.19428145,0.21285629,0.063480395};
SS(-0.88905946,-0.098697315,-0.13184676,-0.70236545,-0.13062851,-0.19140485,-0.76752638,0.004448061,-0.013214377,-0.65367362,-0.16081953,0.0014934597){0.8023886,0.5265969,0.5734925,0.4344691};
SS(0.34720309,0.90097601,-0.12745168,0.17426348,1,-0.18078905,0.25248643,0.73785598,-0.13082591,0.094968532,0.84539386,-0.087484586){0.93504792,1.045853,0.60350215,0.71839764};
SS(-0.76752638,0.004448061,-0.013214377,-0.91347537,0.15552497,0.067511395,-0.78848723,0.26584533,-0.068869999,-0.89804207,0.11676539,-0.10792088){0.5734925,0.85045394,0.68151298,0.82300022};
SS(-0.35582611,-0.64426575,-0.070000747,-0.14850787,-0.69358405,-0.087583548,-0.18618058,-0.5161726,-0.15035515,-0.19247216,-0.56000521,0.088357129){0.52757348,0.49763432,0.30914003,0.34206231};
SS(0.51674933,0.64481281,-0.39755292,0.45062041,0.7833899,-0.5,0.33386283,0.81592026,-0.31808704,0.54326203,0.87223293,-0.356993){0.82858869,1.0506853,0.86115027,1.1662147};
SS(-0.19461387,0.3919517,0.10437587,-0.0073778212,0.36022468,0.15230712,-0.11618574,0.50328545,0.29980467,-0.11614487,0.30919383,0.33918095){0.19075448,0.13675819,0.33969293,0.20820823};
SS(-0.58934795,0.84141567,-0.18062024,-0.48255002,0.69900846,-0.19155417,-0.35455825,0.80859576,-0.32177549,-0.38143574,0.84373572,-0.12387887){1.0736489,0.74365966,0.86460259,0.85864479};
SS(-0.40506391,-0.079541407,0.3303193,-0.58755791,0.033814853,0.5,-0.50807239,-0.16307462,0.5,-0.40752783,0.030201366,0.5){0.26156128,0.57778723,0.52416601,0.40526498};
SS(-0.16707278,-0.087678023,-0.31121894,-0.30131805,-0.11512588,-0.5,-0.12484866,-0.12486094,-0.5,-0.1971424,-0.26981885,-0.30750196){0.11599041,0.3368451,0.26766045,0.19280289};
SS(-0.85520613,-0.46088631,-0.14784569,-0.65956212,-0.52273243,-0.19262862,-0.82285362,-0.63420593,-0.0683896,-0.81387526,-0.53653555,-0.3209601){0.95161001,0.7287475,1.0691297,1.0406635};
SS(-0.26297351,0.20404986,-0.17122089,-0.056808231,0.14323286,-0.13367928,-0.13709741,0.19518884,0.034033465,-0.28278924,0.041190137,-0.04219563){0.12773981,0.022140076,0.040184006,0.063480395};
SS(-0.30949447,0.8262402,0.33528492,-0.20984637,0.69532212,0.20809493,-0.47185361,0.73769401,0.24072705,-0.32294154,0.86180803,0.13108841){0.87388961,0.55022745,0.80384956,0.84829643};
SS(-0.65956212,-0.52273243,-0.19262862,-0.61549046,-0.35581383,-0.12962263,-0.56113743,-0.28920115,-0.29204918,-0.62341011,-0.46880832,-0.38153973){0.7287475,0.50877487,0.46850822,0.73807879};
SS(-0.68637718,0.43295764,-0.18031685,-0.65355936,0.25468043,-0.1897796,-0.78848723,0.26584533,-0.068869999,-0.83127473,0.33505962,-0.32026923){0.67437813,0.51379882,0.68151298,0.89071695};
SS(-0.65355936,0.25468043,-0.1897796,-0.77267892,0.13105707,-0.24874664,-0.78848723,0.26584533,-0.068869999,-0.83127473,0.33505962,-0.32026923){0.51379882,0.65386325,0.68151298,0.89071695};
SS(0.10211023,0.6404511,0.38011645,0.21512427,0.73211919,0.5,0.16321322,0.50838432,0.5,0.35567295,0.65317229,0.39545235){0.55160362,0.81521474,0.52238519,0.69293227};
SS(-0.12988976,-0.86995226,0.20452896,-0.29157863,-1,0.20827581,-0.16134158,-1,0.33850563,-0.349759,-0.84853211,0.35590634){0.79894991,1.1139248,1.129042,0.94981364};
SS(-0.29168215,-1,-0.20844865,-0.36340067,-0.87821042,-0.37678589,-0.49995867,-1,-0.27986665,-0.42066299,-0.84356131,-0.12906413){1.1132023,1.0307746,1.3082069,0.88525127};
SS(-0.63246299,0.29145388,0.035195127,-0.54640726,0.34339216,0.19847863,-0.39654734,0.26661646,0.019312696,-0.54631436,0.45612147,-0.00074796238){0.47226275,0.43575493,0.20710489,0.48593017};
SS(0.17426348,1,-0.18078905,0.0011150345,0.93517443,-0.37389303,0.2222976,1,-0.35617554,0.081865095,0.80626877,-0.27867109){1.045853,1.0026385,1.1585843,0.71703623};
SS(-0.58258855,0.14037208,-0.067351147,-0.63246299,0.29145388,0.035195127,-0.7489605,0.18190923,0.13647301,-0.5555987,0.045150158,0.095162244){0.34532741,0.47226275,0.59564173,0.29993682};
SS(-0.4433427,0.53576375,-0.12560501,-0.48255002,0.69900846,-0.19155417,-0.24654336,0.57133462,-0.25396354,-0.39032311,0.63241857,-0.34621958){0.48429505,0.74365966,0.42991415,0.65630059};
SS(-0.69937066,0.31351533,-0.5,-0.63048479,0.37587985,-0.34368186,-0.79644003,0.50064951,-0.5,-0.83127473,0.33505962,-0.32026923){0.81965428,0.64388066,1.115532,0.89071695};
SS(0.69383766,0.49492178,-0.021800115,0.60662231,0.34516964,-0.13972301,0.75922048,0.56990614,-0.17060419,0.82562789,0.37565656,-0.12707714){0.71284258,0.48782847,0.91133836,0.82387041};
SS(0.0011150345,0.93517443,-0.37389303,-0.088882135,1,-0.23281641,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){1.0026385,1.0431215,1.1599423,0.68882385};
SS(0.60662231,0.34516964,-0.13972301,0.69383766,0.49492178,-0.021800115,0.77315808,0.36766952,0.075951375,0.82562789,0.37565656,-0.12707714){0.48782847,0.71284258,0.71793497,0.82387041};
SS(0.27170325,0.36204749,-0.4201745,0.34662081,0.36199915,-0.25068724,0.17777709,0.54047543,-0.2567554,0.09693172,0.3918681,-0.3370861){0.36885377,0.29696992,0.36840304,0.26256104};
SS(-0.74954172,1,0.13574231,-0.61311838,0.85766427,0.15491279,-0.66659408,1,0.32529585,-0.76389013,0.77728265,0.25513738){1.562759,1.1216468,1.5364848,1.2358334};
SS(-0.89962374,0.8609561,-0.16698164,-1,1,-0.25,-1,1,-6.9388939e-15,-0.81095336,1,-0.07156149){1.5692753,2.0450698,1.9810426,1.6471359};
SS(-0.89426176,0.41257007,-0.12932618,-0.68637718,0.43295764,-0.18031685,-0.78848723,0.26584533,-0.068869999,-0.83127473,0.33505962,-0.32026923){0.974079,0.67437813,0.68151298,0.89071695};
SS(0.13402468,0.11673163,-0.1460819,0,0,-6.9388939e-15,0.22032809,0,-9.1119885e-05,0.13261259,0.21336316,0.036566127){0.039337265,-0.017891206,0.027339551,0.046199082};
SS(0.69383766,0.49492178,-0.021800115,0.65062064,0.64268786,0.069510863,0.75922048,0.56990614,-0.17060419,0.61535375,0.70719289,-0.095218388){0.71284258,0.82620698,0.91133836,0.87858083};
SS(-0.18268367,0.83021756,-0.5,-0.17097214,0.64900986,-0.39927747,-0.14847812,0.78021305,-0.27623142,-0.35455825,0.80859576,-0.32177549){0.9573479,0.59741335,0.68882385,0.86460259};
SS(0.34662081,0.36199915,-0.25068724,0.24635331,0.35131343,-0.096025322,0.20129651,0.21389912,-0.31902192,0.37137652,0.1767682,-0.19801193){0.29696992,0.18045455,0.16839385,0.19205628};
SS(0.88354722,0.11667767,-0.13069643,1,0.2917639,-0.20827961,0.77491511,0.22516452,-0.26425516,0.82562789,0.37565656,-0.12707714){0.79839767,1.1127834,0.70313431,0.82387041};
SS(-0.4433427,0.53576375,-0.12560501,-0.24654336,0.57133462,-0.25396354,-0.50782983,0.50249565,-0.29902586,-0.39032311,0.63241857,-0.34621958){0.48429505,0.42991415,0.58612549,0.65630059};
SS(-0.1159097,-0.14329028,0.19302206,0,0,-6.9388939e-15,-0.20656092,-0.13938028,0.029547229,-0.15128303,0.02253305,0.11422928){0.055235283,-0.017891206,0.048278496,0.025420414};
SS(-0.41767704,0.010770256,-0.44072823,-0.30131805,-0.11512588,-0.5,-0.29413589,0.046284299,-0.31274881,-0.38492375,-0.20017574,-0.33650716){0.35514259,0.3368451,0.1681493,0.28705324};
SS(0.68966181,1,0.19790566,0.88049681,0.87960137,0.13412341,0.82853688,1,0.32125076,0.76099919,0.76690574,0.25750996){1.492557,1.5518824,1.7703132,1.2143065};
SS(0.74440038,0.22095066,-0.087839409,0.60662231,0.34516964,-0.13972301,0.77315808,0.36766952,0.075951375,0.82562789,0.37565656,-0.12707714){0.59875958,0.48782847,0.71793497,0.82387041};
SS(-0.76988954,1,-0.26944904,-0.83248216,0.76782327,-0.31292259,-0.74249217,0.75399014,-0.15399718,-0.89962374,0.8609561,-0.16698164){1.6463902,1.366757,1.1267767,1.5692753};
SS(-0.92571354,0.17249619,-0.34283108,-1,0.25105097,-0.19350143,-0.77267892,0.13105707,-0.24874664,-0.83127473,0.33505962,-0.32026923){0.99158484,1.0825888,0.65386325,0.89071695};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.5000565,0.0033661208,-0.82285362,-0.63420593,-0.0683896,-0.82595855,-0.48031431,0.11444494){0.95161001,1.2263361,1.0691297,0.90887195};
SS(0.098704003,0.67249079,0.1943501,0.11458044,0.70010244,0.010073529,0.086744979,0.52712982,0.027891324,-0.098708274,0.55956225,0.10505678){0.47957633,0.49378055,0.26660844,0.31633913};
SS(0.34720309,0.90097601,-0.12745168,0.25248643,0.73785598,-0.13082591,0.45788353,0.76094781,-0.0096633567,0.24404834,0.79519787,0.082231238){0.93504792,0.60350215,0.76853994,0.68472542};
SS(0.0011150345,0.93517443,-0.37389303,-0.11111111,1,-0.5,-0.18268367,0.83021756,-0.5,-0.23070339,1,-0.34855306){1.0026385,1.2528065,0.9573479,1.1599423};
SS(0,0,-6.9388939e-15,-0.1159097,-0.14329028,0.19302206,0,0,0.25,-0.15128303,0.02253305,0.11422928){-0.017891206,0.055235283,0.045060365,0.025420414};
SS(-1,1,0.25,-0.93582873,0.86427167,0.14668289,-0.74954172,1,0.13574231,-0.84394966,1,0.33504415){2.0473025,1.6320629,1.562759,1.8084725};
SS(0,-0.49997234,-0.27965571,-0.11754465,-0.65214472,-0.32749638,-0.18618058,-0.5161726,-0.15035515,-0.26056819,-0.54975154,-0.34323516){0.30906942,0.53347202,0.30914003,0.46884495};
SS(0,-0.25,-0.5,-0.073421274,-0.375,-0.38984354,0,-0.16143077,-0.33843101,-0.1971424,-0.26981885,-0.30750196){0.29677328,0.28201081,0.12966739,0.19280289};
SS(0.88049681,0.87960137,0.13412341,0.68966181,1,0.19790566,0.78186447,1,3.3673518e-05,0.62860594,0.86645525,0.049037492){1.5518824,1.492557,1.5923176,1.1303867};
SS(-0.056808231,0.14323286,-0.13367928,0.08017426,0.31429474,-0.16745504,0.13261259,0.21336316,0.036566127,0.13402468,0.11673163,-0.1460819){0.022140076,0.11103103,0.046199082,0.039337265};
SS(-0.11754465,-0.65214472,-0.32749638,-0.2399131,-0.76005145,-0.25989531,-0.18618058,-0.5161726,-0.15035515,-0.26056819,-0.54975154,-0.34323516){0.53347202,0.6848256,0.30914003,0.46884495};
SS(0.6657623,0.67544754,-0.5,0.85153485,0.65148612,-0.35468846,0.81205362,0.80656044,-0.5,0.68900489,0.77311276,-0.28043733){1.1304562,1.2568282,1.5391707,1.1326816};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.4433427,0.53576375,-0.12560501,-0.24654336,0.57133462,-0.25396354,-0.34372617,0.39779568,-0.18541051){0.32892635,0.48429505,0.42991415,0.29650146};
SS(-0.83996275,-0.66999882,0.11765553,-0.65631386,-0.59724887,0.13822882,-0.77091496,-0.77159441,0.2629049,-0.61978497,-0.82706917,0.12738472){1.1553131,0.7890621,1.2433034,1.0681409};
SS(0.51674933,0.64481281,-0.39755292,0.6657623,0.67544754,-0.5,0.45062041,0.7833899,-0.5,0.54326203,0.87223293,-0.356993){0.82858869,1.1304562,1.0506853,1.1662147};
SS(-0.32294154,0.86180803,0.13108841,-0.222315,1,-0.00011890035,-0.22223836,1,0.2622369,-0.084253952,1,0.13733396){0.84829643,1.0307381,1.0984067,1.0073117};
SS(-0.57994589,-0.69256437,0.31204703,-0.65631386,-0.59724887,0.13822882,-0.77091496,-0.77159441,0.2629049,-0.79575191,-0.55547687,0.30538166){0.89957508,0.7890621,1.2433034,1.0192798};
SS(-0.87046532,0.63071146,0.35630423,-0.66546973,0.66566005,0.5,-0.79641575,0.50054117,0.5,-0.67801153,0.56076489,0.29217382){1.2666006,1.1224691,1.1180299,0.83617727};
SS(0.51674933,0.64481281,-0.39755292,0.49866453,0.63973666,-0.21510859,0.33386283,0.81592026,-0.31808704,0.34412919,0.6158316,-0.3427703){0.82858869,0.68344633,0.86115027,0.59958408};
SS(-0.2401666,0.74114092,-0.051302261,-0.38143574,0.84373572,-0.12387887,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){0.58653028,0.85864479,0.83409809,0.84829643};
SS(-0.50815189,-0.16301678,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.56113743,-0.28920115,-0.29204918,-0.38492375,-0.20017574,-0.33650716){0.52110597,0.54269073,0.46850822,0.28705324};
SS(0.35567295,0.65317229,0.39545235,0.37492492,0.49312259,0.5,0.16321322,0.50838432,0.5,0.26138985,0.51848551,0.281015){0.69293227,0.61809871,0.52238519,0.40200156};
SS(-0.87046532,0.63071146,0.35630423,-1,0.70725984,0.21334539,-1,0.84108515,0.33242406,-0.76389013,0.77728265,0.25513738){1.2666006,1.5286486,1.8031397,1.2358334};
SS(-0.76389013,0.77728265,0.25513738,-0.8480722,0.62150313,0.12164012,-0.67801153,0.56076489,0.29217382,-0.87046532,0.63071146,0.35630423){1.2358334,1.1084494,0.83617727,1.2666006};
SS(0.8781758,0.86708556,-0.1989731,0.68985253,1,-0.19792707,0.82865019,1,-0.3214153,0.68900489,0.77311276,-0.28043733){1.5462283,1.495304,1.7714679,1.1326816};
SS(0.45042372,0.78359022,0.5,0.36841015,0.87909734,0.37310922,0.55555177,0.82262944,0.31125158,0.35567295,0.65317229,0.39545235){1.0496179,1.0362544,1.0671623,0.69293227};
SS(-0.17097214,0.64900986,-0.39927747,-0.24654336,0.57133462,-0.25396354,-0.35455825,0.80859576,-0.32177549,-0.39032311,0.63241857,-0.34621958){0.59741335,0.42991415,0.86460259,0.65630059};
SS(0.09693172,0.3918681,-0.3370861,0.16368264,0.50834729,-0.5,-0.029932551,0.40748663,-0.5,-0.01813809,0.53618118,-0.30537166){0.26256104,0.52115901,0.4038008,0.36567785};
SS(-0.01813809,0.53618118,-0.30537166,-0.033284914,0.58770906,-0.5,0.16368264,0.50834729,-0.5,-0.029932551,0.40748663,-0.5){0.36567785,0.58301644,0.52115901,0.4038008};
SS(-0.40408872,0.18166381,-0.5,-0.41767704,0.010770256,-0.44072823,-0.19007896,0.04567822,-0.5,-0.29413589,0.046284299,-0.31274881){0.42526168,0.35514259,0.27736807,0.1681493};
SS(-0.4182056,0.11248126,-0.14182463,-0.58258855,0.14037208,-0.067351147,-0.49808619,0.0026201378,-0.26387206,-0.4720473,-0.063494476,-0.036829327){0.19428145,0.34532741,0.29810596,0.21285629};
SS(0.50010751,0,-0.00013054911,0.46476684,0.14382827,0.12247557,0.63998586,0.17856447,0.051345521,0.57129187,0.13526053,-0.13726946){0.22823279,0.23450402,0.42570365,0.35115136};
SS(0.10211023,0.6404511,0.38011645,0.098704003,0.67249079,0.1943501,-0.043441254,0.79173928,0.29440137,0.22886345,0.79287946,0.30210005){0.55160362,0.47957633,0.69563564,0.75332396};
SS(0.55555556,1,0.5,0.64232771,0.84838332,0.46476191,0.45042372,0.78359022,0.5,0.55555177,0.82262944,0.31125158){1.5357742,1.3339184,1.0496179,1.0671623};
SS(-0.61549046,-0.35581383,-0.12962263,-0.50159539,-0.29258506,7.2987381e-06,-0.76760867,-0.33664988,-0.028298027,-0.59094649,-0.40495207,0.12834587){0.50877487,0.32068114,0.68479998,0.51475101};
SS(-0.45843014,-0.20445062,-0.15988901,-0.29237157,-0.11865629,-0.17606411,-0.49808619,0.0026201378,-0.26387206,-0.4720473,-0.063494476,-0.036829327){0.26094507,0.11404163,0.29810596,0.21285629};
SS(-0.30949447,0.8262402,0.33528492,-0.33333333,1,0.5,-0.22223836,1,0.2622369,-0.44431425,1,0.36245944){0.87388961,1.3433112,1.0984067,1.3152029};
SS(-0.45563594,0.60375179,0.095527884,-0.65776896,0.64141588,0.074371921,-0.47185361,0.73769401,0.24072705,-0.48952189,0.78345034,0.019065462){0.56263538,0.83514199,0.80384956,0.83409809};
SS(-0.16707278,-0.087678023,-0.31121894,0,0,-0.5,0,0,-0.25,0,-0.16143077,-0.33843101){0.11599041,0.23465449,0.044304329,0.12966739};
SS(-0.70832062,-1,0.2082538,-0.8827276,-0.88146034,0.13123348,-0.77091496,-0.77159441,0.2629049,-0.61978497,-0.82706917,0.12738472){1.5291125,1.5595365,1.2433034,1.0681409};
SS(-1,0.24865949,0.19540364,-0.91347537,0.15552497,0.067511395,-0.7489605,0.18190923,0.13647301,-0.91004595,0.15296589,0.33139812){1.0814407,0.85045394,0.59564173,0.94743142};
SS(0.50136923,0.34587735,-0.44862257,0.51910919,0.22553632,-0.31417891,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.56260896,0.40112301,0.72773009,0.55795418};
SS(-0.58755791,0.033814853,0.5,-0.61674646,0.25215289,0.3447871,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.57778723,0.54607287,0.34448415,0.63492881};
SS(-0.17097214,0.64900986,-0.39927747,0.00024312215,0.80750011,-0.5,-0.18268367,0.83021756,-0.5,-0.14847812,0.78021305,-0.27623142){0.59741335,0.88610119,0.9573479,0.68882385};
SS(-0.67495489,-0.6652659,-0.5,-0.62341011,-0.46880832,-0.38153973,-0.78315651,-0.45008839,-0.5,-0.81387526,-0.53653555,-0.3209601){1.1276355,0.73807879,1.0467962,1.0406635};
SS(-0.088882135,1,-0.23281641,0.0011150345,0.93517443,-0.37389303,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){1.0431215,1.0026385,0.71703623,0.68882385};
SS(0.59416595,0.14141347,0.32656529,0.67112401,0.32933441,0.5,0.62515059,0.14422159,0.5,0.73568363,0.23203612,0.2735765){0.46498444,0.79210069,0.64726001,0.6509231};
SS(-0.349759,-0.84853211,0.35590634,-0.32897755,-0.67088709,0.5,-0.18863677,-0.81113033,0.5,-0.22656331,-0.68065623,0.28194433){0.94981364,0.79643001,0.92459822,0.57683818};
SS(-0.10743676,0.85847111,-0.11136175,-0.088882135,1,-0.23281641,-0.012406168,1,-0.034358602,0.094968532,0.84539386,-0.087484586){0.7462212,1.0431215,0.99121748,0.71839764};
SS(1,0.70834898,0.20844998,0.87881231,0.64063264,0.37220388,1,0.83856906,0.33864755,0.76099919,0.76690574,0.25750996){1.5291243,1.3069719,1.8033242,1.2143065};
SS(0.00024312215,0.80750011,-0.5,0.0011150345,0.93517443,-0.37389303,-0.18268367,0.83021756,-0.5,-0.14847812,0.78021305,-0.27623142){0.88610119,1.0026385,0.9573479,0.68882385};
SS(-0.63048479,0.37587985,-0.34368186,-0.68637718,0.43295764,-0.18031685,-0.49391083,0.27907498,-0.27264436,-0.50782983,0.50249565,-0.29902586){0.64388066,0.67437813,0.37398026,0.58612549};
SS(-0.4182056,0.11248126,-0.14182463,-0.29237157,-0.11865629,-0.17606411,-0.29413589,0.046284299,-0.31274881,-0.28278924,0.041190137,-0.04219563){0.19428145,0.11404163,0.1681493,0.063480395};
SS(0.86971177,0.13024645,0.1427188,0.70845584,0,0.20819814,0.83866368,0,0.33843958,0.73568363,0.23203612,0.2735765){0.77797836,0.52761363,0.80106313,0.6509231};
SS(-0.49292178,-0.37477565,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.56113743,-0.28920115,-0.29204918,-0.62341011,-0.46880832,-0.38153973){0.6115465,0.57811658,0.46850822,0.73807879};
SS(0.16368264,0.50834729,-0.5,0.10162062,0.65400865,-0.37913628,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.52115901,0.5665506,0.36840304,0.59958408};
SS(-0.49292178,-0.37477565,-0.5,-0.38492375,-0.20017574,-0.33650716,-0.50815189,-0.16301678,-0.5,-0.56113743,-0.28920115,-0.29204918){0.6115465,0.28705324,0.52110597,0.46850822};
SS(-0.6448883,-0.87343314,-0.36731947,-0.67495489,-0.6652659,-0.5,-0.80632325,-0.81147186,-0.5,-0.76546557,-0.72634686,-0.27513208){1.296688,1.1276355,1.5409894,1.1696133};
SS(-0.24654336,0.57133462,-0.25396354,-0.4433427,0.53576375,-0.12560501,-0.50782983,0.50249565,-0.29902586,-0.34372617,0.39779568,-0.18541051){0.42991415,0.48429505,0.58612549,0.29650146};
SS(-0.70236545,-0.13062851,-0.19140485,-0.61549046,-0.35581383,-0.12962263,-0.56113743,-0.28920115,-0.29204918,-0.45843014,-0.20445062,-0.15988901){0.5265969,0.50877487,0.46850822,0.26094507};
SS(-0.65776896,0.64141588,0.074371921,-0.8480722,0.62150313,0.12164012,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382){0.83514199,1.1084494,0.80968993,0.83617727};
SS(0,0,-6.9388939e-15,0.13913358,0.10014326,0.18199659,0.22032809,0,-9.1119885e-05,0.13261259,0.21336316,0.036566127){-0.017891206,0.045990896,0.027339551,0.046199082};
SS(0.67112401,0.32933441,0.5,0.87272604,0.35900693,0.37172569,0.81143387,0.18901581,0.5,0.73568363,0.23203612,0.2735765){0.79210069,1.0107603,0.9265446,0.6509231};
SS(-0.25897908,-0.24013326,0.26450313,-0.1159097,-0.14329028,0.19302206,-0.20656092,-0.13938028,0.029547229,-0.3727858,-0.19869367,0.11195566){0.17775565,0.055235283,0.048278496,0.16948569};
SS(-1,-0.70523324,-0.21165758,-0.91414606,-0.68082467,-0.37109558,-1,-0.47540235,-0.27521785,-0.81387526,-0.53653555,-0.3209601){1.5222776,1.4249306,1.2841965,1.0406635};
SS(-0.49808619,0.0026201378,-0.26387206,-0.41767704,0.010770256,-0.44072823,-0.29413589,0.046284299,-0.31274881,-0.38492375,-0.20017574,-0.33650716){0.29810596,0.35514259,0.1681493,0.28705324};
SS(-0.8480722,0.62150313,0.12164012,-1,0.70725984,0.21334539,-1,0.4752276,0.27420758,-0.87046532,0.63071146,0.35630423){1.1084494,1.5286486,1.2803563,1.2666006};
SS(-0.61311838,0.85766427,0.15491279,-0.47972312,1,0.18932995,-0.74954172,1,0.13574231,-0.66659408,1,0.32529585){1.1216468,1.2473472,1.562759,1.5364848};
SS(-0.0089783977,0.64320989,-0.13441642,0.11458044,0.70010244,0.010073529,0.25248643,0.73785598,-0.13082591,0.094968532,0.84539386,-0.087484586){0.41358858,0.49378055,0.60350215,0.71839764};
SS(-0.23048975,-0.37484721,0.5,-0.37661764,-0.26006406,0.40868766,-0.1853821,-0.42358473,0.30866054,-0.34549718,-0.50098866,0.4105565){0.42714666,0.36234206,0.29143101,0.5260109};
SS(-0.64009684,-0.10188458,0.37412975,-0.58755791,0.033814853,0.5,-0.50807239,-0.16307462,0.5,-0.40506391,-0.079541407,0.3303193){0.54631619,0.57778723,0.52416601,0.26156128};
SS(0.36016656,0.41044152,0.1594367,0.52843461,0.32737897,0.19102935,0.47723835,0.52605258,0.30619083,0.50761134,0.34933779,0.39015973){0.3073722,0.40790135,0.58228229,0.51484928};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.24887753,0.1953112,-0.84084014,-0.14895162,0.31636914,-0.82279039,-0.18997945,0.10657137){1.0133061,1.0768014,0.81273381,0.70945047};
SS(-0.3533559,-0.49437708,0.037576204,-0.50159539,-0.29258506,7.2987381e-06,-0.52487586,-0.5117405,-0.017639258,-0.59094649,-0.40495207,0.12834587){0.35575629,0.32068114,0.51812974,0.51475101};
SS(-1,-0.70523324,-0.21165758,-0.86742481,-0.86548068,-0.14483364,-1,-0.83959635,-0.33115777,-0.76546557,-0.72634686,-0.27513208){1.5222776,1.5085891,1.7998257,1.1696133};
SS(-0.0089783977,0.64320989,-0.13441642,-0.10743676,0.85847111,-0.11136175,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){0.41358858,0.7462212,0.58653028,0.60161266};
SS(0.40637652,0.87094343,0.13060843,0.45788353,0.76094781,-0.0096633567,0.24404834,0.79519787,0.082231238,0.34720309,0.90097601,-0.12745168){0.92399337,0.76853994,0.68472542,0.93504792};
SS(-0.61549046,-0.35581383,-0.12962263,-0.70236545,-0.13062851,-0.19140485,-0.76760867,-0.33664988,-0.028298027,-0.65367362,-0.16081953,0.0014934597){0.50877487,0.5265969,0.68479998,0.4344691};
SS(-0.4182056,0.11248126,-0.14182463,-0.29237157,-0.11865629,-0.17606411,-0.49808619,0.0026201378,-0.26387206,-0.29413589,0.046284299,-0.31274881){0.19428145,0.11404163,0.29810596,0.1681493};
SS(0.42864323,0.48543211,-0.13804456,0.34662081,0.36199915,-0.25068724,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.42022283,0.29696992,0.59958408,0.55795418};
SS(-0.64009684,-0.10188458,0.37412975,-0.58755791,0.033814853,0.5,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.54631619,0.57778723,0.34448415,0.63492881};
SS(0.10211023,0.6404511,0.38011645,-0.033588837,0.5879061,0.5,0.00029730467,0.80760978,0.5,-0.16015893,0.67694077,0.39025863){0.55160362,0.57806214,0.88423684,0.6265216};
SS(0.36841015,0.87909734,0.37310922,0.39612945,0.70614162,0.21524614,0.55555177,0.82262944,0.31125158,0.35567295,0.65317229,0.39545235){1.0362544,0.68453461,1.0671623,0.69293227};
SS(-0.4543958,0.20406131,0.5,-0.36145429,0.13293621,0.35430528,-0.40752783,0.030201366,0.5,-0.52427834,0.10778268,0.27208728){0.48353653,0.26360063,0.40526498,0.34448415};
SS(-0.67616985,-0.069078192,0.18801024,-0.64009684,-0.10188458,0.37412975,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.47948004,0.54631619,0.34448415,0.63492881};
SS(-0.0089783977,0.64320989,-0.13441642,-0.098708274,0.55956225,0.10505678,-0.096302334,0.43534175,-0.056072844,0.086744979,0.52712982,0.027891324){0.41358858,0.31633913,0.18078295,0.26660844};
SS(0.50136923,0.34587735,-0.44862257,0.37532516,0.23078833,-0.5,0.6251418,0.1440922,-0.5,0.51910919,0.22553632,-0.31417891){0.56260896,0.42551454,0.63751638,0.40112301};
SS(-0.6293812,0.63993291,-0.28812602,-0.66548665,0.66585508,-0.5,-0.4813337,0.60105459,-0.5,-0.61503712,0.4760032,-0.5){0.87296464,1.1221664,0.83133251,0.83978547};
SS(0.65062064,0.64268786,0.069510863,0.69383766,0.49492178,-0.021800115,0.52218723,0.46943947,0.022097553,0.61535375,0.70719289,-0.095218388){0.82620698,0.71284258,0.46892029,0.87858083};
SS(-0.82994199,0.18319278,-0.5,-0.92571354,0.17249619,-0.34283108,-0.77267892,0.13105707,-0.24874664,-0.83127473,0.33505962,-0.32026923){0.95993957,0.99158484,0.65386325,0.89071695};
SS(0.88049681,0.87960137,0.13412341,1,0.70834898,0.20844998,1,0.83856906,0.33864755,0.76099919,0.76690574,0.25750996){1.5518824,1.5291243,1.8033242,1.2143065};
SS(-0.033284914,0.58770906,-0.5,-0.01813809,0.53618118,-0.30537166,-0.20381263,0.45499536,-0.5,-0.029932551,0.40748663,-0.5){0.58301644,0.36567785,0.478983,0.4038008};
SS(-0.73479965,-0.34302295,0.24038072,-0.89646962,-0.32955067,0.34017365,-0.84084014,-0.14895162,0.31636914,-0.82279039,-0.18997945,0.10657137){0.69668046,1.0133061,0.81273381,0.70945047};
SS(-0.31377045,0.30492781,-0.36427962,-0.17603462,0.24070348,-0.5,-0.20381263,0.45499536,-0.5,-0.12449617,0.36606215,-0.28273955){0.30770932,0.32537509,0.478983,0.21185338};
SS(-0.17097214,0.64900986,-0.39927747,-0.24654336,0.57133462,-0.25396354,-0.14847812,0.78021305,-0.27623142,-0.35455825,0.80859576,-0.32177549){0.59741335,0.42991415,0.68882385,0.86460259};
SS(-0.67495489,-0.6652659,-0.5,-0.91414606,-0.68082467,-0.37109558,-0.80632325,-0.81147186,-0.5,-0.76546557,-0.72634686,-0.27513208){1.1276355,1.4249306,1.5409894,1.1696133};
SS(-0.73174745,-0.21491043,0.5,-0.62450053,-0.31310845,0.38575928,-0.84084014,-0.14895162,0.31636914,-0.64009684,-0.10188458,0.37412975){0.81377033,0.62379151,0.81273381,0.54631619};
SS(0.59365279,0.65503723,0.24444947,0.39612945,0.70614162,0.21524614,0.47723835,0.52605258,0.30619083,0.35567295,0.65317229,0.39545235){0.82252715,0.68453461,0.58228229,0.69293227};
SS(-0.056808231,0.14323286,-0.13367928,0,0,-0.25,0,0,-6.9388939e-15,-0.098950987,-0.13391411,-0.14594667){0.022140076,0.044304329,-0.017891206,0.03512721};
SS(-0.62938155,0.17932964,-0.37445272,-0.69937066,0.31351533,-0.5,-0.82994199,0.18319278,-0.5,-0.83127473,0.33505962,-0.32026923){0.55109073,0.81965428,0.95993957,0.89071695};
SS(0,0,-6.9388939e-15,-0.056808231,0.14323286,-0.13367928,0.13261259,0.21336316,0.036566127,0.13402468,0.11673163,-0.1460819){-0.017891206,0.022140076,0.046199082,0.039337265};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.70830496,-0.20826096,0,-0.49997234,-0.27965571,-0.18618058,-0.5161726,-0.15035515){0.53347202,0.5287181,0.30906942,0.30914003};
SS(-0.1971424,-0.26981885,-0.30750196,0,-0.25,-0.5,-0.12484866,-0.12486094,-0.5,0,-0.16143077,-0.33843101){0.19280289,0.29677328,0.26766045,0.12966739};
SS(-0.91004595,0.15296589,0.33139812,-0.7489605,0.18190923,0.13647301,-0.83851866,0.33014205,0.32623765,-0.72768327,0.10310141,0.33233484){0.94743142,0.59564173,0.89937894,0.63492881};
SS(-0.50537844,-0.68762812,0.023695348,-0.65631386,-0.59724887,0.13822882,-0.63348211,-0.7706683,-0.074889286,-0.61978497,-0.82706917,0.12738472){0.71483247,0.7890621,0.97907785,1.0681409};
SS(-0.36340067,-0.87821042,-0.37678589,-0.29168215,-1,-0.20844865,-0.16144976,-1,-0.33863959,-0.2399131,-0.76005145,-0.25989531){1.0307746,1.1132023,1.1250711,0.6848256};
SS(0.34720309,0.90097601,-0.12745168,0.17426348,1,-0.18078905,0.43683247,1,-0.26068681,0.2222976,1,-0.35617554){0.93504792,1.045853,1.2435523,1.1585843};
SS(0.24937941,1,-0.00011138016,0.40637652,0.87094343,0.13060843,0.24404834,0.79519787,0.082231238,0.34720309,0.90097601,-0.12745168){1.0446566,0.92399337,0.68472542,0.93504792};
SS(-0.86742481,-0.86548068,-0.14483364,-1,-0.70523324,-0.21165758,-1,-0.77608598,0.00064487429,-0.82285362,-0.63420593,-0.0683896){1.5085891,1.5222776,1.5844414,1.0691297};
SS(0.6657623,0.67544754,-0.5,0.51674933,0.64481281,-0.39755292,0.68900489,0.77311276,-0.28043733,0.54326203,0.87223293,-0.356993){1.1304562,0.82858869,1.1326816,1.1662147};
SS(-0.14850787,-0.69358405,-0.087583548,0,-0.49997946,0.00010199173,-0.18618058,-0.5161726,-0.15035515,-0.19247216,-0.56000521,0.088357129){0.49763432,0.22811872,0.30914003,0.34206231};
SS(-0.70832062,-1,0.2082538,-0.63815223,-0.88141187,0.37488811,-0.4999534,-1,0.27968311,-0.61978497,-0.82706917,0.12738472){1.5291125,1.3088768,1.3075402,1.0681409};
SS(-0.4813337,0.60105459,-0.5,-0.6293812,0.63993291,-0.28812602,-0.61503712,0.4760032,-0.5,-0.50782983,0.50249565,-0.29902586){0.83133251,0.87296464,0.83978547,0.58612549};
SS(0.10211023,0.6404511,0.38011645,0.00029730467,0.80760978,0.5,-0.043441254,0.79173928,0.29440137,-0.16015893,0.67694077,0.39025863){0.55160362,0.88423684,0.69563564,0.6265216};
SS(-0.10743676,0.85847111,-0.11136175,-0.0089783977,0.64320989,-0.13441642,0.081865095,0.80626877,-0.27867109,0.094968532,0.84539386,-0.087484586){0.7462212,0.41358858,0.71703623,0.71839764};
SS(-0.83248216,0.76782327,-0.31292259,-0.76988954,1,-0.26944904,-0.74249217,0.75399014,-0.15399718,-0.65756371,0.81308934,-0.3429452){1.366757,1.6463902,1.1267767,1.1958888};
SS(0.10211023,0.6404511,0.38011645,0.16321322,0.50838432,0.5,0.26138985,0.51848551,0.281015,0.35567295,0.65317229,0.39545235){0.55160362,0.52238519,0.40200156,0.69293227};
SS(-0.12233239,-0.87748906,-0.13583418,0,-0.70830496,-0.20826096,0,-0.83851883,-0.33849865,-0.2399131,-0.76005145,-0.25989531){0.78823805,0.5287181,0.80235204,0.6848256};
SS(-0.3548152,-0.48825703,0.21848985,-0.34549718,-0.50098866,0.4105565,-0.22656331,-0.68065623,0.28194433,-0.1853821,-0.42358473,0.30866054){0.38862106,0.5260109,0.57683818,0.29143101};
SS(1,0,-6.9388939e-15,0.88354722,0.11667767,-0.13069643,0.77985819,0,-0.00014691753,0.86971177,0.13024645,0.1427188){0.9846322,0.79839767,0.58919206,0.77797836};
SS(0.88354722,0.11667767,-0.13069643,1,0,-6.9388939e-15,1,0.2203628,5.6826691e-05,0.86971177,0.13024645,0.1427188){0.79839767,0.9846322,1.0268649,0.77797836};
SS(-0.29168215,-1,-0.20844865,-0.12233239,-0.87748906,-0.13583418,-0.16144976,-1,-0.33863959,-0.2399131,-0.76005145,-0.25989531){1.1132023,0.78823805,1.1250711,0.6848256};
SS(-0.61674646,0.25215289,0.3447871,-0.7489605,0.18190923,0.13647301,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.54607287,0.59564173,0.34448415,0.63492881};
SS(1,0.70834898,0.20844998,0.87881231,0.64063264,0.37220388,0.76099919,0.76690574,0.25750996,0.84582719,0.572243,0.1361951){1.5291243,1.3069719,1.2143065,1.0417018};
SS(0.54326203,0.87223293,-0.356993,0.49866453,0.63973666,-0.21510859,0.68900489,0.77311276,-0.28043733,0.54700908,0.85955032,-0.16345766){1.1662147,0.68344633,1.1326816,1.0528061};
SS(0.52843461,0.32737897,0.19102935,0.50761134,0.34933779,0.39015973,0.6902006,0.50015172,0.27072419,0.47723835,0.52605258,0.30619083){0.40790135,0.51484928,0.77938072,0.58228229};
SS(-0.65631386,-0.59724887,0.13822882,-0.57994589,-0.69256437,0.31204703,-0.56348952,-0.47594309,0.3052276,-0.79575191,-0.55547687,0.30538166){0.7890621,0.89957508,0.61776713,1.0192798};
SS(-0.65776896,0.64141588,0.074371921,-0.61311838,0.85766427,0.15491279,-0.47185361,0.73769401,0.24072705,-0.48952189,0.78345034,0.019065462){0.83514199,1.1216468,0.80384956,0.83409809};
SS(0.64232771,0.84838332,0.46476191,0.66554141,0.67524133,0.5,0.45042372,0.78359022,0.5,0.55555177,0.82262944,0.31125158){1.3339184,1.1271263,1.0496179,1.0671623};
SS(0.88049681,0.87960137,0.13412341,0.78186447,1,3.3673518e-05,0.77861211,0.77861193,-0.067175459,0.62860594,0.86645525,0.049037492){1.5518824,1.5923176,1.1981052,1.1303867};
SS(-0.91347537,0.15552497,0.067511395,-0.7489605,0.18190923,0.13647301,-0.76752638,0.004448061,-0.013214377,-0.78848723,0.26584533,-0.068869999){0.85045394,0.59564173,0.5734925,0.68151298};
SS(-0.79227163,-0.79754897,0.0021844777,-1,-0.77608598,0.00064487429,-0.86742481,-0.86548068,-0.14483364,-0.8827276,-0.88146034,0.13123348){1.2530106,1.5844414,1.5085891,1.5595365};
SS(0.50761134,0.34933779,0.39015973,0.5725222,0.50074158,0.5,0.6902006,0.50015172,0.27072419,0.47723835,0.52605258,0.30619083){0.51484928,0.8121357,0.77938072,0.58228229};
SS(-0.91347537,0.15552497,0.067511395,-1,-0.00012222908,0.26646899,-0.84289574,0.018333867,0.1608607,-0.91004595,0.15296589,0.33139812){0.85045394,1.0506696,0.72430843,0.94743142};
SS(-0.65776896,0.64141588,0.074371921,-0.62332411,0.59900263,-0.10904345,-0.48952189,0.78345034,0.019065462,-0.45563594,0.60375179,0.095527884){0.83514199,0.74800561,0.83409809,0.56263538};
SS(-0.073421274,-0.375,-0.38984354,0,-0.25,-0.5,-0.12484866,-0.12486094,-0.5,-0.1971424,-0.26981885,-0.30750196){0.28201081,0.29677328,0.26766045,0.19280289};
SS(-0.12449617,0.36606215,-0.28273955,-0.17603462,0.24070348,-0.5,-0.20381263,0.45499536,-0.5,-0.029932551,0.40748663,-0.5){0.21185338,0.32537509,0.478983,0.4038008};
SS(0.67112401,0.32933441,0.5,0.73568363,0.23203612,0.2735765,0.81143387,0.18901581,0.5,0.62515059,0.14422159,0.5){0.79210069,0.6509231,0.9265446,0.64726001};
SS(0.35689191,0.091376279,-0.36932783,0.25,0,-0.5,0.16149165,0,-0.33864688,0.20129651,0.21389912,-0.31902192){0.26145514,0.28810477,0.12746835,0.16839385};
SS(0.11458044,0.70010244,0.010073529,0.098704003,0.67249079,0.1943501,-0.035654771,0.78507762,0.045007896,-0.098708274,0.55956225,0.10505678){0.49378055,0.47957633,0.60161266,0.31633913};
SS(0.34720309,0.90097601,-0.12745168,0.17426348,1,-0.18078905,0.2222976,1,-0.35617554,0.33386283,0.81592026,-0.31808704){0.93504792,1.045853,1.1585843,0.86115027};
SS(0.43683247,1,-0.26068681,0.34720309,0.90097601,-0.12745168,0.2222976,1,-0.35617554,0.33386283,0.81592026,-0.31808704){1.2435523,0.93504792,1.1585843,0.86115027};
SS(0.11136938,1,0.13859714,0.094968532,0.84539386,-0.087484586,-0.012406168,1,-0.034358602,0.24937941,1,-0.00011138016){1.0072058,0.71839764,0.99121748,1.0446566};
SS(-0.83248216,0.76782327,-0.31292259,-0.66548665,0.66585508,-0.5,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){1.366757,1.1221664,1.1958888,0.87296464};
SS(-0.11614487,0.30919383,0.33918095,0.11523872,0.30161582,0.5,-0.045146113,0.19012269,0.5,0.050277172,0.20853018,0.30186362){0.20820823,0.33546792,0.27176836,0.12181545};
SS(0.21543771,0.73213875,-0.5,0.10162062,0.65400865,-0.37913628,0.16368264,0.50834729,-0.5,0.34412919,0.6158316,-0.3427703){0.81134051,0.5665506,0.52115901,0.59958408};
SS(0.26064395,0.61953306,0.12890567,0.39612945,0.70614162,0.21524614,0.45788353,0.76094781,-0.0096633567,0.24404834,0.79519787,0.082231238){0.45328252,0.68453461,0.76853994,0.68472542};
SS(-0.77973152,-1,-0.0001062007,-0.79227163,-0.79754897,0.0021844777,-0.86742481,-0.86548068,-0.14483364,-0.8827276,-0.88146034,0.13123348){1.588155,1.2530106,1.5085891,1.5595365};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.47527469,-0.27513051,-0.80558396,0.5878127,-0.29244037,-0.83127473,0.33505962,-0.32026923){0.974079,1.2834809,1.0616703,0.89071695};
SS(-0.32294154,0.86180803,0.13108841,-0.47972312,1,0.18932995,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462){0.84829643,1.2473472,1.169501,0.83409809};
SS(0.70845584,0,0.20819814,0.86971177,0.13024645,0.1427188,0.77985819,0,-0.00014691753,0.63998586,0.17856447,0.051345521){0.52761363,0.77797836,0.58919206,0.42570365};
SS(-0.65631386,-0.59724887,0.13822882,-0.50537844,-0.68762812,0.023695348,-0.42889738,-0.75253072,0.17523232,-0.61978497,-0.82706917,0.12738472){0.7890621,0.71483247,0.75958282,1.0681409};
SS(-0.30131805,-0.11512588,-0.5,-0.16707278,-0.087678023,-0.31121894,-0.29413589,0.046284299,-0.31274881,-0.38492375,-0.20017574,-0.33650716){0.3368451,0.11599041,0.1681493,0.28705324};
SS(0.21543771,0.73213875,-0.5,0.34412919,0.6158316,-0.3427703,0.45062041,0.7833899,-0.5,0.33386283,0.81592026,-0.31808704){0.81134051,0.59958408,1.0506853,0.86115027};
SS(-0.59094649,-0.40495207,0.12834587,-0.73479965,-0.34302295,0.24038072,-0.76760867,-0.33664988,-0.028298027,-0.82595855,-0.48031431,0.11444494){0.51475101,0.69668046,0.68479998,0.90887195};
SS(-0.033284914,0.58770906,-0.5,-0.17097214,0.64900986,-0.39927747,-0.20381263,0.45499536,-0.5,-0.01813809,0.53618118,-0.30537166){0.58301644,0.59741335,0.478983,0.36567785};
SS(-0.8480722,0.62150313,0.12164012,-0.65776896,0.64141588,0.074371921,-0.76389013,0.77728265,0.25513738,-0.67801153,0.56076489,0.29217382){1.1084494,0.83514199,1.2358334,0.83617727};
SS(-0.74954172,1,0.13574231,-0.76389013,0.77728265,0.25513738,-0.66659408,1,0.32529585,-0.84394966,1,0.33504415){1.562759,1.2358334,1.5364848,1.8084725};
SS(-0.36340067,-0.87821042,-0.37678589,-0.29168215,-1,-0.20844865,-0.2399131,-0.76005145,-0.25989531,-0.42066299,-0.84356131,-0.12906413){1.0307746,1.1132023,0.6848256,0.88525127};
SS(-0.31377045,0.30492781,-0.36427962,-0.26297351,0.20404986,-0.17122089,-0.29413589,0.046284299,-0.31274881,-0.1182182,0.15955837,-0.3159857){0.30770932,0.12773981,0.1681493,0.11990198};
SS(-0.76988954,1,-0.26944904,-0.58934795,0.84141567,-0.18062024,-0.74249217,0.75399014,-0.15399718,-0.65756371,0.81308934,-0.3429452){1.6463902,1.0736489,1.1267767,1.1958888};
SS(-0.29261734,0.53193925,0.43339885,-0.033588837,0.5879061,0.5,-0.11618574,0.50328545,0.29980467,-0.16015893,0.67694077,0.39025863){0.53993003,0.57806214,0.33969293,0.6265216};
SS(0.51674933,0.64481281,-0.39755292,0.49866453,0.63973666,-0.21510859,0.68900489,0.77311276,-0.28043733,0.54326203,0.87223293,-0.356993){0.82858869,0.68344633,1.1326816,1.1662147};
SS(-0.36608751,-0.8951802,0.074405883,-0.22019153,-1,-0.00010416607,-0.22302806,-0.77703925,0.068353305,-0.12988976,-0.86995226,0.20452896){0.92652515,1.0287732,0.64063544,0.79894991};
SS(-0.36608751,-0.8951802,0.074405883,-0.29157863,-1,0.20827581,-0.22019153,-1,-0.00010416607,-0.12988976,-0.86995226,0.20452896){0.92652515,1.1139248,1.0287732,0.79894991};
SS(0.60662231,0.34516964,-0.13972301,0.82562789,0.37565656,-0.12707714,0.77491511,0.22516452,-0.26425516,0.67125235,0.44297685,-0.31879306){0.48782847,0.82387041,0.70313431,0.72773009};
SS(0.5,0,0.5,0.59416595,0.14141347,0.32656529,0.50011436,0,0.27961788,0.42621669,0.19017509,0.30505062){0.47735984,0.46498444,0.30940041,0.29714896};
SS(-0.19461387,0.3919517,0.10437587,-0.0073778212,0.36022468,0.15230712,-0.13709741,0.19518884,0.034033465,-0.096302334,0.43534175,-0.056072844){0.19075448,0.13675819,0.040184006,0.18078295};
SS(-0.10743676,0.85847111,-0.11136175,-0.088882135,1,-0.23281641,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){0.7462212,1.0431215,0.71703623,0.68882385};
SS(0,-0.7082575,0.2084616,-0.12988976,-0.86995226,0.20452896,0,-0.83845667,0.33864852,-0.22656331,-0.68065623,0.28194433){0.52387062,0.79894991,0.80178572,0.57683818};
SS(-0.38143574,0.84373572,-0.12387887,-0.222315,1,-0.00011890035,-0.36992714,1,-0.22970445,-0.42762906,1,-0.0094860889){0.85864479,1.0307381,1.1684568,1.169501};
SS(-0.91347537,0.15552497,0.067511395,-1,0.24865949,0.19540364,-1,-0.00012222908,0.26646899,-0.91004595,0.15296589,0.33139812){0.85045394,1.0814407,1.0506696,0.94743142};
SS(-0.38143574,0.84373572,-0.12387887,-0.222315,1,-0.00011890035,-0.42762906,1,-0.0094860889,-0.32294154,0.86180803,0.13108841){0.85864479,1.0307381,1.169501,0.84829643};
SS(-0.10133362,-0.40777162,0.1162396,-0.3548152,-0.48825703,0.21848985,-0.19247216,-0.56000521,0.088357129,-0.1853821,-0.42358473,0.30866054){0.17697987,0.38862106,0.34206231,0.29143101};
SS(0.36841015,0.87909734,0.37310922,0.55555556,1,0.5,0.45042372,0.78359022,0.5,0.55555177,0.82262944,0.31125158){1.0362544,1.5357742,1.0496179,1.0671623};
SS(-0.70236545,-0.13062851,-0.19140485,-0.61549046,-0.35581383,-0.12962263,-0.7907607,-0.33838097,-0.28342271,-0.56113743,-0.28920115,-0.29204918){0.5265969,0.50877487,0.80149819,0.46850822};
SS(-0.59094649,-0.40495207,0.12834587,-0.65631386,-0.59724887,0.13822882,-0.73479965,-0.34302295,0.24038072,-0.82595855,-0.48031431,0.11444494){0.51475101,0.7890621,0.69668046,0.90887195};
SS(-1,0.25105097,-0.19350143,-0.89426176,0.41257007,-0.12932618,-0.78848723,0.26584533,-0.068869999,-0.83127473,0.33505962,-0.32026923){1.0825888,0.974079,0.68151298,0.89071695};
SS(-0.60421932,0.82298164,0.34468578,-0.47972312,1,0.18932995,-0.47185361,0.73769401,0.24072705,-0.61311838,0.85766427,0.15491279){1.1449713,1.2473472,0.80384956,1.1216468};
SS(0.35567295,0.65317229,0.39545235,0.39612945,0.70614162,0.21524614,0.26138985,0.51848551,0.281015,0.22886345,0.79287946,0.30210005){0.69293227,0.68453461,0.40200156,0.75332396};
SS(0.10211023,0.6404511,0.38011645,0.26138985,0.51848551,0.281015,0.22886345,0.79287946,0.30210005,0.35567295,0.65317229,0.39545235){0.55160362,0.40200156,0.75332396,0.69293227};
SS(-0.41767704,0.010770256,-0.44072823,-0.58754442,0.033885734,-0.5,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.35514259,0.58180393,0.29810596,0.55109073};
SS(0.11458044,0.70010244,0.010073529,0.25248643,0.73785598,-0.13082591,0.24404834,0.79519787,0.082231238,0.26064395,0.61953306,0.12890567){0.49378055,0.60350215,0.68472542,0.45328252};
SS(-0.033284914,0.58770906,-0.5,-0.17097214,0.64900986,-0.39927747,0.00024312215,0.80750011,-0.5,-0.18268367,0.83021756,-0.5){0.58301644,0.59741335,0.88610119,0.9573479};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.70710233,0.21356199,-1,-0.47520831,0.27427507,-0.79575191,-0.55547687,0.30538166){1.4119512,1.5280688,1.2822693,1.0192798};
SS(-1,-0.00021427218,0.00011802244,-0.91347537,0.15552497,0.067511395,-1,-0.00012222908,0.26646899,-0.84289574,0.018333867,0.1608607){0.98080906,0.85045394,1.0506696,0.72430843};
SS(-0.67616985,-0.069078192,0.18801024,-0.84289574,0.018333867,0.1608607,-0.7489605,0.18190923,0.13647301,-0.76752638,0.004448061,-0.013214377){0.47948004,0.72430843,0.59564173,0.5734925};
SS(0.82562789,0.37565656,-0.12707714,0.60662231,0.34516964,-0.13972301,0.75922048,0.56990614,-0.17060419,0.67125235,0.44297685,-0.31879306){0.82387041,0.48782847,0.91133836,0.72773009};
SS(-0.1159097,-0.14329028,0.19302206,-0.34310942,-0.010167032,0.1509038,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.055235283,0.12661586,0.10955402,0.025420414};
SS(-0.4182056,0.11248126,-0.14182463,-0.58258855,0.14037208,-0.067351147,-0.4720473,-0.063494476,-0.036829327,-0.5555987,0.045150158,0.095162244){0.19428145,0.34532741,0.21285629,0.29993682};
SS(-0.58755791,0.033814853,0.5,-0.52427834,0.10778268,0.27208728,-0.4543958,0.20406131,0.5,-0.40752783,0.030201366,0.5){0.57778723,0.34448415,0.48353653,0.40526498};
SS(-0.16015893,0.67694077,0.39025863,-0.1827732,0.83017807,0.5,-0.043441254,0.79173928,0.29440137,-0.30949447,0.8262402,0.33528492){0.6265216,0.95598938,0.69563564,0.87388961};
SS(0.26083053,0.15082484,0.37728795,0.25,0,0.5,0.37501462,0.2307626,0.5,0.12517622,0.12515553,0.5){0.21918499,0.29281005,0.42590445,0.27156885};
SS(-0.035654771,0.78507762,0.045007896,0.11136938,1,0.13859714,-0.012406168,1,-0.034358602,-0.084253952,1,0.13733396){0.60161266,1.0072058,0.99121748,1.0073117};
SS(0.10162062,0.65400865,-0.37913628,0.21543771,0.73213875,-0.5,0.00024312215,0.80750011,-0.5,0.081865095,0.80626877,-0.27867109){0.5665506,0.81134051,0.88610119,0.71703623};
SS(0.37492492,0.49312259,0.5,0.35567295,0.65317229,0.39545235,0.5725222,0.50074158,0.5,0.47723835,0.52605258,0.30619083){0.61809871,0.69293227,0.8121357,0.58228229};
SS(0.11111111,1,0.5,-0.11111111,1,0.5,-0.014815866,1,0.31001515,0.00029730467,0.80760978,0.5){1.2368521,1.2487078,1.0772324,0.88423684};
SS(0.87867265,0.36391919,-0.37720578,1,0.2917639,-0.20827961,1,0.50010355,-0.27968748,0.82562789,0.37565656,-0.12707714){1.03034,1.1127834,1.3071084,0.82387041};
SS(0.11523872,0.30161582,0.5,0.26083053,0.15082484,0.37728795,0.37501462,0.2307626,0.5,0.12517622,0.12515553,0.5){0.33546792,0.21918499,0.42590445,0.27156885};
SS(-0.12988976,-0.86995226,0.20452896,0,-0.7082575,0.2084616,0,-0.77970171,0.00010845427,-0.22302806,-0.77703925,0.068353305){0.79894991,0.52387062,0.58842154,0.64063544};
SS(-0.58934795,0.84141567,-0.18062024,-0.61115597,1,-0.10200355,-0.42762906,1,-0.0094860889,-0.38143574,0.84373572,-0.12387887){1.0736489,1.3611038,1.169501,0.85864479};
SS(-1,-0.25140376,-0.1934451,-0.85520613,-0.46088631,-0.14784569,-1,-0.47540235,-0.27521785,-0.7907607,-0.33838097,-0.28342271){1.0790534,0.95161001,1.2841965,0.80149819};
SS(-0.29261734,0.53193925,0.43339885,-0.48141868,0.60085372,0.5,-0.35521568,0.4957142,0.26668635,-0.52470763,0.46530444,0.33754711){0.53993003,0.82306978,0.42001946,0.59371518};
SS(-0.65956212,-0.52273243,-0.19262862,-0.81387526,-0.53653555,-0.3209601,-0.76546557,-0.72634686,-0.27513208,-0.82285362,-0.63420593,-0.0683896){0.7287475,1.0406635,1.1696133,1.0691297};
SS(-1,-0.70523324,-0.21165758,-0.86742481,-0.86548068,-0.14483364,-0.76546557,-0.72634686,-0.27513208,-0.82285362,-0.63420593,-0.0683896){1.5222776,1.5085891,1.1696133,1.0691297};
SS(-0.65631386,-0.59724887,0.13822882,-0.79227163,-0.79754897,0.0021844777,-0.61978497,-0.82706917,0.12738472,-0.83996275,-0.66999882,0.11765553){0.7890621,1.2530106,1.0681409,1.1553131};
SS(-0.62450053,-0.31310845,0.38575928,-0.49284988,-0.37485679,0.5,-0.50874333,-0.23900991,0.2620444,-0.37661764,-0.26006406,0.40868766){0.62379151,0.6163523,0.36443271,0.36234206};
SS(0.26064395,0.61953306,0.12890567,0.098704003,0.67249079,0.1943501,0.26138985,0.51848551,0.281015,0.22886345,0.79287946,0.30210005){0.45328252,0.47957633,0.40200156,0.75332396};
SS(0.39612945,0.70614162,0.21524614,0.26064395,0.61953306,0.12890567,0.26138985,0.51848551,0.281015,0.22886345,0.79287946,0.30210005){0.68453461,0.45328252,0.40200156,0.75332396};
SS(0.0011150345,0.93517443,-0.37389303,0.00024312215,0.80750011,-0.5,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){1.0026385,0.88610119,0.71703623,0.68882385};
SS(0.08017426,0.31429474,-0.16745504,0.09693172,0.3918681,-0.3370861,-0.12449617,0.36606215,-0.28273955,-0.01813809,0.53618118,-0.30537166){0.11103103,0.26256104,0.21185338,0.36567785};
SS(-0.40125956,-0.65699374,0.33213173,-0.42889738,-0.75253072,0.17523232,-0.349759,-0.84853211,0.35590634,-0.57994589,-0.69256437,0.31204703){0.69449311,0.75958282,0.94981364,0.89957508};
SS(-0.35455825,0.80859576,-0.32177549,-0.18268367,0.83021756,-0.5,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){0.86460259,0.9573479,1.1599423,0.68882385};
SS(-0.58754442,0.033885734,-0.5,-0.41767704,0.010770256,-0.44072823,-0.40408872,0.18166381,-0.5,-0.62938155,0.17932964,-0.37445272){0.58180393,0.35514259,0.42526168,0.55109073};
SS(-0.29237157,-0.11865629,-0.17606411,-0.38492375,-0.20017574,-0.33650716,-0.49808619,0.0026201378,-0.26387206,-0.29413589,0.046284299,-0.31274881){0.11404163,0.28705324,0.29810596,0.1681493};
SS(-0.63246299,0.29145388,0.035195127,-0.58258855,0.14037208,-0.067351147,-0.7489605,0.18190923,0.13647301,-0.78848723,0.26584533,-0.068869999){0.47226275,0.34532741,0.59564173,0.68151298};
SS(-0.50537844,-0.68762812,0.023695348,-0.42889738,-0.75253072,0.17523232,-0.61978497,-0.82706917,0.12738472,-0.36608751,-0.8951802,0.074405883){0.71483247,0.75958282,1.0681409,0.92652515};
SS(0.65062064,0.64268786,0.069510863,0.59365279,0.65503723,0.24444947,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){0.82620698,0.82252715,0.77938072,1.0417018};
SS(-1,0.25105097,-0.19350143,-0.89804207,0.11676539,-0.10792088,-0.77267892,0.13105707,-0.24874664,-0.78848723,0.26584533,-0.068869999){1.0825888,0.82300022,0.65386325,0.68151298};
SS(-0.32879066,-0.67072359,-0.5,-0.11754465,-0.65214472,-0.32749638,-0.18848435,-0.81110947,-0.5,-0.14376826,-0.62489354,-0.5){0.79007105,0.53347202,0.92571371,0.6489606};
SS(-1,0.70529035,-0.21162945,-0.83248216,0.76782327,-0.31292259,-0.74249217,0.75399014,-0.15399718,-0.80558396,0.5878127,-0.29244037){1.520296,1.366757,1.1267767,1.0616703};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.45843014,-0.20445062,-0.15988901,-0.4720473,-0.063494476,-0.036829327,-0.3727858,-0.19869367,0.11195566){0.32068114,0.26094507,0.21285629,0.16948569};
SS(-0.16015893,0.67694077,0.39025863,-0.033588837,0.5879061,0.5,0.00029730467,0.80760978,0.5,-0.1827732,0.83017807,0.5){0.6265216,0.57806214,0.88423684,0.95598938};
SS(-1,0.77777778,0.5,-0.87046532,0.63071146,0.35630423,-1,0.84108515,0.33242406,-0.80481649,0.80494069,0.5){1.8402752,1.2666006,1.8031397,1.5232843};
SS(0.27170325,0.36204749,-0.4201745,0.37549445,0.49317282,-0.5,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.36885377,0.61648995,0.59958408,0.55795418};
SS(0.13913358,0.10014326,0.18199659,0.29175541,0,0.20824909,0.25126435,0.28098512,0.24657435,0.26083053,0.15082484,0.37728795){0.045990896,0.1093371,0.18575023,0.21918499};
SS(-0.32897755,-0.67088709,0.5,-0.34549718,-0.50098866,0.4105565,-0.14394692,-0.62481063,0.5,-0.22656331,-0.68065623,0.28194433){0.79643001,0.5260109,0.63866347,0.57683818};
SS(0.37492492,0.49312259,0.5,0.27123349,0.36190713,0.41476339,0.26138985,0.51848551,0.281015,0.47723835,0.52605258,0.30619083){0.61809871,0.36300231,0.40200156,0.58228229};
SS(-1,-0.24887753,0.1953112,-0.89646962,-0.32955067,0.34017365,-0.73479965,-0.34302295,0.24038072,-0.82279039,-0.18997945,0.10657137){1.0768014,1.0133061,0.69668046,0.70945047};
SS(-1,-0.5000565,0.0033661208,-0.85520613,-0.46088631,-0.14784569,-0.76760867,-0.33664988,-0.028298027,-0.82595855,-0.48031431,0.11444494){1.2263361,0.95161001,0.68479998,0.90887195};
SS(0.098704003,0.67249079,0.1943501,0.10211023,0.6404511,0.38011645,0.26138985,0.51848551,0.281015,0.22886345,0.79287946,0.30210005){0.47957633,0.55160362,0.40200156,0.75332396};
SS(-0.65776896,0.64141588,0.074371921,-0.79370724,0.81084643,0.045877226,-0.74249217,0.75399014,-0.15399718,-0.8068077,0.56885008,-0.063754108){0.83514199,1.270911,1.1267767,0.96112076};
SS(-0.68637718,0.43295764,-0.18031685,-0.89426176,0.41257007,-0.12932618,-0.80558396,0.5878127,-0.29244037,-0.83127473,0.33505962,-0.32026923){0.67437813,0.974079,1.0616703,0.89071695};
SS(-1,-1,-6.9388939e-15,-0.8827276,-0.88146034,0.13123348,-0.77973152,-1,-0.0001062007,-0.86742481,-0.86548068,-0.14483364){1.9831286,1.5595365,1.588155,1.5085891};
SS(0.33333333,1,-0.5,0.33386283,0.81592026,-0.31808704,0.43683247,1,-0.26068681,0.2222976,1,-0.35617554){1.342474,0.86115027,1.2435523,1.1585843};
SS(-0.36174,-0.40052234,-0.23665811,-0.15923414,-0.34171533,-0.15079999,-0.23583358,-0.36008743,0.0071767184,-0.18618058,-0.5161726,-0.15035515){0.32480953,0.14783141,0.16465457,0.30914003};
SS(-0.50400314,-0.78879927,0.5,-0.40125956,-0.65699374,0.33213173,-0.349759,-0.84853211,0.35590634,-0.57994589,-0.69256437,0.31204703){1.1086821,0.69449311,0.94981364,0.89957508};
SS(-0.65631386,-0.59724887,0.13822882,-0.61978497,-0.82706917,0.12738472,-0.42889738,-0.75253072,0.17523232,-0.57994589,-0.69256437,0.31204703){0.7890621,1.0681409,0.75958282,0.89957508};
SS(-0.0089783977,0.64320989,-0.13441642,-0.10743676,0.85847111,-0.11136175,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){0.41358858,0.7462212,0.71703623,0.68882385};
SS(-0.89962374,0.8609561,-0.16698164,-1,1,-0.25,-0.76988954,1,-0.26944904,-1,0.83964442,-0.3309874){1.5692753,2.0450698,1.6463902,1.7979585};
SS(-0.29261734,0.53193925,0.43339885,-0.033588837,0.5879061,0.5,-0.18136176,0.40461939,0.5,-0.11618574,0.50328545,0.29980467){0.53993003,0.57806214,0.42386795,0.33969293};
SS(-0.8827276,-0.88146034,0.13123348,-1,-1,-6.9388939e-15,-1,-0.77608598,0.00064487429,-0.86742481,-0.86548068,-0.14483364){1.5595365,1.9831286,1.5844414,1.5085891};
SS(-0.61503712,0.4760032,-0.5,-0.63048479,0.37587985,-0.34368186,-0.50782983,0.50249565,-0.29902586,-0.6293812,0.63993291,-0.28812602){0.83978547,0.64388066,0.58612549,0.87296464};
SS(-0.61549046,-0.35581383,-0.12962263,-0.36174,-0.40052234,-0.23665811,-0.56113743,-0.28920115,-0.29204918,-0.45843014,-0.20445062,-0.15988901){0.50877487,0.32480953,0.46850822,0.26094507};
SS(-0.1159097,-0.14329028,0.19302206,0,0,-6.9388939e-15,0,-0.22019801,5.0496855e-05,-0.20656092,-0.13938028,0.029547229){0.055235283,-0.017891206,0.029059683,0.048278496};
SS(-0.93582873,0.86427167,0.14668289,-1,1,-6.9388939e-15,-0.81095336,1,-0.07156149,-0.79370724,0.81084643,0.045877226){1.6320629,1.9810426,1.6471359,1.270911};
SS(0.66554141,0.67524133,0.5,0.64232771,0.84838332,0.46476191,0.81191124,0.80644944,0.5,0.76099919,0.76690574,0.25750996){1.1271263,1.3339184,1.5425973,1.2143065};
SS(-0.66548665,0.66585508,-0.5,-0.83248216,0.76782327,-0.31292259,-0.80558396,0.5878127,-0.29244037,-0.6293812,0.63993291,-0.28812602){1.1221664,1.366757,1.0616703,0.87296464};
SS(-0.89663862,-0.69397302,0.37275403,-0.67513028,-0.66529728,0.5,-0.80635543,-0.81164184,0.5,-0.77091496,-0.77159441,0.2629049){1.4119512,1.1284607,1.5410993,1.2433034};
SS(-0.69937107,0.31347586,0.5,-0.61674646,0.25215289,0.3447871,-0.83006559,0.18329805,0.5,-0.83851866,0.33014205,0.32623765){0.8165723,0.54607287,0.96159482,0.89937894};
SS(-0.11614487,0.30919383,0.33918095,0.11523872,0.30161582,0.5,0.050277172,0.20853018,0.30186362,0.085954007,0.41736025,0.32943097){0.20820823,0.33546792,0.12181545,0.27115576};
SS(-0.3548152,-0.48825703,0.21848985,-0.3533559,-0.49437708,0.037576204,-0.23583358,-0.36008743,0.0071767184,-0.19247216,-0.56000521,0.088357129){0.38862106,0.35575629,0.16465457,0.34206231};
SS(-0.056808231,0.14323286,-0.13367928,-0.26297351,0.20404986,-0.17122089,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.022140076,0.12773981,0.21185338,0.11990198};
SS(-0.63246299,0.29145388,0.035195127,-0.7489605,0.18190923,0.13647301,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){0.47226275,0.59564173,0.80968993,0.68151298};
SS(-0.11754465,-0.65214472,-0.32749638,-0.32879066,-0.67072359,-0.5,-0.2399131,-0.76005145,-0.25989531,-0.26056819,-0.54975154,-0.34323516){0.53347202,0.79007105,0.6848256,0.46884495};
SS(-0.67495489,-0.6652659,-0.5,-0.91414606,-0.68082467,-0.37109558,-0.76546557,-0.72634686,-0.27513208,-0.81387526,-0.53653555,-0.3209601){1.1276355,1.4249306,1.1696133,1.0406635};
SS(0.24635331,0.35131343,-0.096025322,0.08017426,0.31429474,-0.16745504,0.34662081,0.36199915,-0.25068724,0.20129651,0.21389912,-0.31902192){0.18045455,0.11103103,0.29696992,0.16839385};
SS(-0.33333333,1,-0.5,-0.35455825,0.80859576,-0.32177549,-0.18268367,0.83021756,-0.5,-0.23070339,1,-0.34855306){1.3407278,0.86460259,0.9573479,1.1599423};
SS(-0.29237157,-0.11865629,-0.17606411,-0.4182056,0.11248126,-0.14182463,-0.49808619,0.0026201378,-0.26387206,-0.4720473,-0.063494476,-0.036829327){0.11404163,0.19428145,0.29810596,0.21285629};
SS(-0.76546557,-0.72634686,-0.27513208,-0.79227163,-0.79754897,0.0021844777,-0.82285362,-0.63420593,-0.0683896,-0.86742481,-0.86548068,-0.14483364){1.1696133,1.2530106,1.0691297,1.5085891};
SS(0.68985253,1,-0.19792707,0.8781758,0.86708556,-0.1989731,0.78186447,1,3.3673518e-05,0.77861211,0.77861193,-0.067175459){1.495304,1.5462283,1.5923176,1.1981052};
SS(-0.61674646,0.25215289,0.3447871,-0.83006559,0.18329805,0.5,-0.83851866,0.33014205,0.32623765,-0.72768327,0.10310141,0.33233484){0.54607287,0.96159482,0.89937894,0.63492881};
SS(0.88354722,0.11667767,-0.13069643,1,0.16156328,-0.33847781,0.83867599,0,-0.33865964,0.77491511,0.22516452,-0.26425516){0.79839767,1.1261583,0.80182539,0.70313431};
SS(-0.043441254,0.79173928,0.29440137,-0.22223836,1,0.2622369,-0.014815866,1,0.31001515,-0.084253952,1,0.13733396){0.69563564,1.0984067,1.0772324,1.0073117};
SS(-0.64009684,-0.10188458,0.37412975,-0.67616985,-0.069078192,0.18801024,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){0.54631619,0.47948004,0.81273381,0.63492881};
SS(-0.49391083,0.27907498,-0.27264436,-0.31377045,0.30492781,-0.36427962,-0.50782983,0.50249565,-0.29902586,-0.34372617,0.39779568,-0.18541051){0.37398026,0.30770932,0.58612549,0.29650146};
SS(-0.93582873,0.86427167,0.14668289,-0.84394966,1,0.33504415,-1,0.84108515,0.33242406,-0.76389013,0.77728265,0.25513738){1.6320629,1.8084725,1.8031397,1.2358334};
SS(0.81143387,0.18901581,0.5,0.59416595,0.14141347,0.32656529,0.62515059,0.14422159,0.5,0.73568363,0.23203612,0.2735765){0.9265446,0.46498444,0.64726001,0.6509231};
SS(-1,1,-6.9388939e-15,-0.93582873,0.86427167,0.14668289,-1,0.77631186,0.00053339564,-0.79370724,0.81084643,0.045877226){1.9810426,1.6320629,1.5817554,1.270911};
SS(-0.47972312,1,0.18932995,-0.61311838,0.85766427,0.15491279,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462){1.2473472,1.1216468,1.169501,0.83409809};
SS(0.37492492,0.49312259,0.5,0.27123349,0.36190713,0.41476339,0.47723835,0.52605258,0.30619083,0.50761134,0.34933779,0.39015973){0.61809871,0.36300231,0.58228229,0.51484928};
SS(-0.033588837,0.5879061,0.5,0.10211023,0.6404511,0.38011645,-0.11618574,0.50328545,0.29980467,-0.16015893,0.67694077,0.39025863){0.57806214,0.55160362,0.33969293,0.6265216};
SS(1,0.2203628,5.6826691e-05,0.74440038,0.22095066,-0.087839409,0.82562789,0.37565656,-0.12707714,0.88354722,0.11667767,-0.13069643){1.0268649,0.59875958,0.82387041,0.79839767};
SS(0.30434906,0.49798107,-4.0114635e-05,0.24635331,0.35131343,-0.096025322,0.36021608,0.23247759,-0.012351094,0.18202227,0.38279251,0.10350409){0.32377482,0.18045455,0.16110593,0.17617817};
SS(0.52843461,0.32737897,0.19102935,0.59416595,0.14141347,0.32656529,0.73568363,0.23203612,0.2735765,0.50761134,0.34933779,0.39015973){0.40790135,0.46498444,0.6509231,0.51484928};
SS(0.24635331,0.35131343,-0.096025322,0.08017426,0.31429474,-0.16745504,0.20129651,0.21389912,-0.31902192,0.13402468,0.11673163,-0.1460819){0.18045455,0.11103103,0.16839385,0.039337265};
SS(0.671223,0.32907594,-0.5,0.50136923,0.34587735,-0.44862257,0.51910919,0.22553632,-0.31417891,0.67125235,0.44297685,-0.31879306){0.79435762,0.56260896,0.40112301,0.72773009};
SS(-0.58258855,0.14037208,-0.067351147,-0.65355936,0.25468043,-0.1897796,-0.77267892,0.13105707,-0.24874664,-0.78848723,0.26584533,-0.068869999){0.34532741,0.51379882,0.65386325,0.68151298};
SS(-0.58934795,0.84141567,-0.18062024,-0.48255002,0.69900846,-0.19155417,-0.48952189,0.78345034,0.019065462,-0.62332411,0.59900263,-0.10904345){1.0736489,0.74365966,0.83409809,0.74800561};
SS(0,-0.49997946,0.00010199173,-0.10133362,-0.40777162,0.1162396,-0.23583358,-0.36008743,0.0071767184,-0.19247216,-0.56000521,0.088357129){0.22811872,0.17697987,0.16465457,0.34206231};
SS(0.30434906,0.49798107,-4.0114635e-05,0.18202227,0.38279251,0.10350409,0.36021608,0.23247759,-0.012351094,0.36016656,0.41044152,0.1594367){0.32377482,0.17617817,0.16110593,0.3073722};
SS(-0.8827276,-0.88146034,0.13123348,-1,-0.84092895,0.33252059,-0.8385203,-1,0.33846229,-0.77091496,-0.77159441,0.2629049){1.5595365,1.8030746,1.8024192,1.2433034};
SS(-0.58755791,0.033814853,0.5,-0.72768327,0.10310141,0.33233484,-0.80727304,0.00024662976,0.5,-0.83006559,0.18329805,0.5){0.57778723,0.63492881,0.88515177,0.96159482};
SS(-0.63348211,-0.7706683,-0.074889286,-0.79227163,-0.79754897,0.0021844777,-0.76546557,-0.72634686,-0.27513208,-0.86742481,-0.86548068,-0.14483364){0.97907785,1.2530106,1.1696133,1.5085891};
SS(0.61535375,0.70719289,-0.095218388,0.65062064,0.64268786,0.069510863,0.75922048,0.56990614,-0.17060419,0.77861211,0.77861193,-0.067175459){0.87858083,0.82620698,0.91133836,1.1981052};
SS(-0.91414606,-0.68082467,-0.37109558,-1,-0.70523324,-0.21165758,-0.76546557,-0.72634686,-0.27513208,-0.81387526,-0.53653555,-0.3209601){1.4249306,1.5222776,1.1696133,1.0406635};
SS(-0.16643696,-0.21791406,0.42402077,0,0,0.5,0,-0.25,0.5,0,-0.16137283,0.3386068){0.23818505,0.23153294,0.28720824,0.12565914};
SS(-0.83248216,0.76782327,-0.31292259,-0.76988954,1,-0.26944904,-1,0.83964442,-0.3309874,-0.89962374,0.8609561,-0.16698164){1.366757,1.6463902,1.7979585,1.5692753};
SS(0.50136923,0.34587735,-0.44862257,0.37532516,0.23078833,-0.5,0.34662081,0.36199915,-0.25068724,0.27170325,0.36204749,-0.4201745){0.56260896,0.42551454,0.29696992,0.36885377};
SS(-0.67513028,-0.66529728,0.5,-0.63815223,-0.88141187,0.37488811,-0.77091496,-0.77159441,0.2629049,-0.57994589,-0.69256437,0.31204703){1.1284607,1.3088768,1.2433034,0.89957508};
SS(-0.32879066,-0.67072359,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.18848435,-0.81110947,-0.5,-0.2399131,-0.76005145,-0.25989531){0.79007105,1.0307746,0.92571371,0.6848256};
SS(-0.48141868,0.60085372,0.5,-0.52470763,0.46530444,0.33754711,-0.61509744,0.47589965,0.5,-0.67801153,0.56076489,0.29217382){0.82306978,0.59371518,0.84259202,0.83617727};
SS(0.87867265,0.36391919,-0.37720578,0.81149777,0.18885984,-0.5,1,0.16156328,-0.33847781,0.77491511,0.22516452,-0.26425516){1.03034,0.92750237,1.1261583,0.70313431};
SS(0,-0.7082575,0.2084616,-0.12988976,-0.86995226,0.20452896,-0.22656331,-0.68065623,0.28194433,-0.22302806,-0.77703925,0.068353305){0.52387062,0.79894991,0.57683818,0.64063544};
SS(0.70845584,0,0.20819814,0.59416595,0.14141347,0.32656529,0.83866368,0,0.33843958,0.73568363,0.23203612,0.2735765){0.52761363,0.46498444,0.80106313,0.6509231};
SS(-0.67616985,-0.069078192,0.18801024,-0.84289574,0.018333867,0.1608607,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){0.47948004,0.72430843,0.81273381,0.63492881};
SS(0.34720309,0.90097601,-0.12745168,0.25248643,0.73785598,-0.13082591,0.24404834,0.79519787,0.082231238,0.094968532,0.84539386,-0.087484586){0.93504792,0.60350215,0.68472542,0.71839764};
SS(-0.61549046,-0.35581383,-0.12962263,-0.65956212,-0.52273243,-0.19262862,-0.7907607,-0.33838097,-0.28342271,-0.62341011,-0.46880832,-0.38153973){0.50877487,0.7287475,0.80149819,0.73807879};
SS(1,0.2917639,-0.20827961,0.88354722,0.11667767,-0.13069643,1,0.2203628,5.6826691e-05,0.82562789,0.37565656,-0.12707714){1.1127834,0.79839767,1.0268649,0.82387041};
SS(-0.61549046,-0.35581383,-0.12962263,-0.7907607,-0.33838097,-0.28342271,-0.56113743,-0.28920115,-0.29204918,-0.62341011,-0.46880832,-0.38153973){0.50877487,0.80149819,0.46850822,0.73807879};
SS(-0.5555987,0.045150158,0.095162244,-0.67616985,-0.069078192,0.18801024,-0.7489605,0.18190923,0.13647301,-0.76752638,0.004448061,-0.013214377){0.29993682,0.47948004,0.59564173,0.5734925};
SS(0.74440038,0.22095066,-0.087839409,0.77985819,0,-0.00014691753,0.63998586,0.17856447,0.051345521,0.57129187,0.13526053,-0.13726946){0.59875958,0.58919206,0.42570365,0.35115136};
SS(0,-0.75,-0.5,-0.11754465,-0.65214472,-0.32749638,0,-0.5,-0.5,-0.14376826,-0.62489354,-0.5){0.79460868,0.53347202,0.4845449,0.6489606};
SS(0,-1,-0.5,0,-0.75,-0.5,-0.18848435,-0.81110947,-0.5,0,-0.83851883,-0.33849865){1.2333742,0.79460868,0.92571371,0.80235204};
SS(-0.79227163,-0.79754897,0.0021844777,-0.63348211,-0.7706683,-0.074889286,-0.76546557,-0.72634686,-0.27513208,-0.82285362,-0.63420593,-0.0683896){1.2530106,0.97907785,1.1696133,1.0691297};
SS(1,0,-0.5,1,0.25,-0.5,0.81149777,0.18885984,-0.5,1,0.16156328,-0.33847781){1.2327879,1.2935113,0.92750237,1.1261583};
SS(0.75,0,-0.5,1,0,-0.5,0.81149777,0.18885984,-0.5,0.83867599,0,-0.33865964){0.79494611,1.2327879,0.92750237,0.80182539};
SS(0.87881231,0.64063264,0.37220388,1,0.5,0.5,0.78912399,0.50423732,0.5,0.87272604,0.35900693,0.37172569){1.3069719,1.484684,1.1096027,1.0107603};
SS(0,-1,0.5,0,-0.75,0.5,0,-0.83845667,0.33864852,-0.18863677,-0.81113033,0.5){1.232491,0.79557901,0.80178572,0.92459822};
SS(0.77777778,1,0.5,0.64232771,0.84838332,0.46476191,0.82853688,1,0.32125076,0.81191124,0.80644944,0.5){1.8450917,1.3339184,1.7703132,1.5425973};
SS(-0.5,-1,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.50377808,-0.78884267,-0.5,-0.6448883,-0.87343314,-0.36731947){1.4844013,1.0307746,1.1087956,1.296688};
SS(-0.25,-1,-0.5,0,-1,-0.5,-0.18848435,-0.81110947,-0.5,-0.16144976,-1,-0.33863959){1.2929607,1.2333742,0.92571371,1.1250711};
SS(1,0,0.5,1,0.25,0.5,1,0.16158711,0.33859063,0.81143387,0.18901581,0.5){1.2336156,1.2942978,1.1259698,0.9265446};
SS(-0.58755791,0.033814853,0.5,-0.61674646,0.25215289,0.3447871,-0.4543958,0.20406131,0.5,-0.52427834,0.10778268,0.27208728){0.57778723,0.54607287,0.48353653,0.34448415};
SS(-0.58754442,0.033885734,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.58180393,0.54269073,0.29810596,0.55109073};
SS(0.18202227,0.38279251,0.10350409,-0.0073778212,0.36022468,0.15230712,0.25126435,0.28098512,0.24657435,0.085954007,0.41736025,0.32943097){0.17617817,0.13675819,0.18575023,0.27115576};
SS(-0.25,-1,0.5,0,-1,0.5,-0.16134158,-1,0.33850563,-0.18863677,-0.81113033,0.5){1.2918821,1.232491,1.129042,0.92459822};
SS(-0.58934795,0.84141567,-0.18062024,-0.61115597,1,-0.10200355,-0.36992714,1,-0.22970445,-0.56041637,1,-0.29784853){1.0736489,1.3611038,1.1684568,1.3856141};
SS(0.27170325,0.36204749,-0.4201745,0.37549445,0.49317282,-0.5,0.16368264,0.50834729,-0.5,0.34412919,0.6158316,-0.3427703){0.36885377,0.61648995,0.52115901,0.59958408};
SS(0.75,0,0.5,1,0,0.5,0.83866368,0,0.33843958,0.81143387,0.18901581,0.5){0.79262349,1.2336156,0.80106313,0.9265446};
SS(-0.01813809,0.53618118,-0.30537166,-0.20381263,0.45499536,-0.5,-0.029932551,0.40748663,-0.5,-0.12449617,0.36606215,-0.28273955){0.36567785,0.478983,0.4038008,0.21185338};
SS(1,0.5,0.5,0.87881231,0.64063264,0.37220388,1,0.50005385,0.27984222,0.87272604,0.35900693,0.37172569){1.484684,1.3069719,1.3085441,1.0107603};
SS(-0.36340067,-0.87821042,-0.37678589,-0.5,-1,-0.5,-0.49995867,-1,-0.27986665,-0.6448883,-0.87343314,-0.36731947){1.0307746,1.4844013,1.3082069,1.296688};
SS(-0.63815223,-0.88141187,0.37488811,-0.8385203,-1,0.33846229,-0.80635543,-0.81164184,0.5,-0.77091496,-0.77159441,0.2629049){1.3088768,1.8024192,1.5410993,1.2433034};
SS(0.55555556,1,-0.5,0.54326203,0.87223293,-0.356993,0.33333333,1,-0.5,0.45062041,0.7833899,-0.5){1.5352494,1.1662147,1.342474,1.0506853};
SS(0.43654676,1,0.2604635,0.36841015,0.87909734,0.37310922,0.23106485,1,0.31398279,0.40637652,0.87094343,0.13060843){1.2403655,1.0362544,1.1340577,0.92399337};
SS(-0.24000819,0.17660305,0.5,-0.20045203,0.067929244,0.29301468,-0.045146113,0.19012269,0.5,-0.17669296,0.011023676,0.5){0.3210912,0.10955402,0.27176836,0.26322593};
SS(-0.10743676,0.85847111,-0.11136175,-0.0089783977,0.64320989,-0.13441642,-0.2401666,0.74114092,-0.051302261,-0.14847812,0.78021305,-0.27623142){0.7462212,0.41358858,0.58653028,0.68882385};
SS(0.77491511,0.22516452,-0.26425516,0.87867265,0.36391919,-0.37720578,0.67125235,0.44297685,-0.31879306,0.82562789,0.37565656,-0.12707714){0.70313431,1.03034,0.72773009,0.82387041};
SS(-0.26986228,0.26051837,0.22418657,-0.10037172,0.18891947,0.20844359,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.1749353,0.074828316,0.10955402,0.025420414};
SS(-0.89646962,-0.32955067,0.34017365,-0.73479965,-0.34302295,0.24038072,-0.79575191,-0.55547687,0.30538166,-0.82595855,-0.48031431,0.11444494){1.0133061,0.69668046,1.0192798,0.90887195};
SS(0.87867265,0.36391919,-0.37720578,0.671223,0.32907594,-0.5,0.81149777,0.18885984,-0.5,0.77491511,0.22516452,-0.26425516){1.03034,0.79435762,0.92750237,0.70313431};
SS(-0.79227163,-0.79754897,0.0021844777,-0.77973152,-1,-0.0001062007,-0.61978497,-0.82706917,0.12738472,-0.8827276,-0.88146034,0.13123348){1.2530106,1.588155,1.0681409,1.5595365};
SS(0,-0.29157012,0.20836692,-0.16643696,-0.21791406,0.42402077,0,-0.16137283,0.3386068,-0.1159097,-0.14329028,0.19302206){0.11172813,0.23818505,0.12565914,0.055235283};
SS(-0.58258855,0.14037208,-0.067351147,-0.5555987,0.045150158,0.095162244,-0.7489605,0.18190923,0.13647301,-0.76752638,0.004448061,-0.013214377){0.34532741,0.29993682,0.59564173,0.5734925};
SS(0.8781758,0.86708556,-0.1989731,0.68985253,1,-0.19792707,0.68900489,0.77311276,-0.28043733,0.77861211,0.77861193,-0.067175459){1.5462283,1.495304,1.1326816,1.1981052};
SS(-0.66546973,0.66566005,0.5,-0.67801153,0.56076489,0.29217382,-0.48141868,0.60085372,0.5,-0.61509744,0.47589965,0.5){1.1224691,0.83617727,0.82306978,0.84259202};
SS(-0.41767704,0.010770256,-0.44072823,-0.50815189,-0.16301678,-0.5,-0.49808619,0.0026201378,-0.26387206,-0.64012388,-0.10177177,-0.37237302){0.35514259,0.52110597,0.29810596,0.54269073};
SS(-0.80727304,0.00024662976,0.5,-0.64009684,-0.10188458,0.37412975,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){0.88515177,0.54631619,0.81273381,0.63492881};
SS(0.10211023,0.6404511,0.38011645,-0.033588837,0.5879061,0.5,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.55160362,0.57806214,0.33969293,0.27115576};
SS(0.24937941,1,-0.00011138016,0.34720309,0.90097601,-0.12745168,0.24404834,0.79519787,0.082231238,0.094968532,0.84539386,-0.087484586){1.0446566,0.93504792,0.68472542,0.71839764};
SS(-0.222315,1,-0.00011890035,-0.10743676,0.85847111,-0.11136175,-0.2401666,0.74114092,-0.051302261,-0.38143574,0.84373572,-0.12387887){1.0307381,0.7462212,0.58653028,0.85864479};
SS(-0.74954172,1,0.13574231,-0.93582873,0.86427167,0.14668289,-0.81095336,1,-0.07156149,-0.79370724,0.81084643,0.045877226){1.562759,1.6320629,1.6471359,1.270911};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.29157012,0.20836692,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.17697987,0.11172813,0.17775565,0.29143101};
SS(0.77985819,0,-0.00014691753,0.74440038,0.22095066,-0.087839409,0.63998586,0.17856447,0.051345521,0.86971177,0.13024645,0.1427188){0.58919206,0.59875958,0.42570365,0.77797836};
SS(0.57129187,0.13526053,-0.13726946,0.60662231,0.34516964,-0.13972301,0.77491511,0.22516452,-0.26425516,0.51910919,0.22553632,-0.31417891){0.35115136,0.48782847,0.70313431,0.40112301};
SS(1,0.75,-0.5,1,1,-0.5,0.81205362,0.80656044,-0.5,1,0.83864447,-0.33847614){1.7924126,2.2331531,1.5391707,1.8065101};
SS(-0.1159097,-0.14329028,0.19302206,-0.34310942,-0.010167032,0.1509038,-0.20656092,-0.13938028,0.029547229,-0.3727858,-0.19869367,0.11195566){0.055235283,0.12661586,0.048278496,0.16948569};
SS(1,0.75,0.5,1,1,0.5,1,0.83856906,0.33864755,0.81191124,0.80644944,0.5){1.7930237,2.2317116,1.8033242,1.5425973};
SS(0.35567295,0.65317229,0.39545235,0.37492492,0.49312259,0.5,0.21512427,0.73211919,0.5,0.16321322,0.50838432,0.5){0.69293227,0.61809871,0.81521474,0.52238519};
SS(-0.82595855,-0.48031431,0.11444494,-0.65631386,-0.59724887,0.13822882,-0.73479965,-0.34302295,0.24038072,-0.79575191,-0.55547687,0.30538166){0.90887195,0.7890621,0.69668046,1.0192798};
SS(0.55555556,1,0.5,0.36841015,0.87909734,0.37310922,0.33333333,1,0.5,0.43654676,1,0.2604635){1.5357742,1.0362544,1.3466764,1.2403655};
SS(0.34662081,0.36199915,-0.25068724,0.27170325,0.36204749,-0.4201745,0.34412919,0.6158316,-0.3427703,0.48047723,0.47791267,-0.33071402){0.29696992,0.36885377,0.59958408,0.55795418};
SS(-0.62938155,0.17932964,-0.37445272,-0.58754442,0.033885734,-0.5,-0.80728146,0.00010990719,-0.5,-0.82994199,0.18319278,-0.5){0.55109073,0.58180393,0.88195685,0.95993957};
SS(-1,-1,0.5,-0.75,-1,0.5,-0.8385203,-1,0.33846229,-0.80635543,-0.81164184,0.5){2.2322143,1.7943537,1.8024192,1.5410993};
SS(-0.62938155,0.17932964,-0.37445272,-0.82994199,0.18319278,-0.5,-0.77267892,0.13105707,-0.24874664,-0.83127473,0.33505962,-0.32026923){0.55109073,0.95993957,0.65386325,0.89071695};
SS(-1,-1,-0.5,-0.75,-1,-0.5,-0.80632325,-0.81147186,-0.5,-0.83846289,-1,-0.33858677){2.2321573,1.7946951,1.5409894,1.8019179};
SS(-0.47972312,1,0.18932995,-0.61311838,0.85766427,0.15491279,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){1.2473472,1.1216468,0.83409809,0.84829643};
SS(0.11458044,0.70010244,0.010073529,-0.0089783977,0.64320989,-0.13441642,0.086744979,0.52712982,0.027891324,-0.098708274,0.55956225,0.10505678){0.49378055,0.41358858,0.26660844,0.31633913};
SS(-0.67513028,-0.66529728,0.5,-0.63815223,-0.88141187,0.37488811,-0.80635543,-0.81164184,0.5,-0.77091496,-0.77159441,0.2629049){1.1284607,1.3088768,1.5410993,1.2433034};
SS(-1,-0.47520831,0.27427507,-0.89646962,-0.32955067,0.34017365,-0.79575191,-0.55547687,0.30538166,-0.82595855,-0.48031431,0.11444494){1.2822693,1.0133061,1.0192798,0.90887195};
SS(-0.35582611,-0.64426575,-0.070000747,-0.14850787,-0.69358405,-0.087583548,-0.2399131,-0.76005145,-0.25989531,-0.18618058,-0.5161726,-0.15035515){0.52757348,0.49763432,0.6848256,0.30914003};
SS(0,-0.49989758,0.27983937,-0.10133362,-0.40777162,0.1162396,-0.19247216,-0.56000521,0.088357129,-0.1853821,-0.42358473,0.30866054){0.30650831,0.17697987,0.34206231,0.29143101};
SS(-1,0.24865949,0.19540364,-0.91004595,0.15296589,0.33139812,-0.7489605,0.18190923,0.13647301,-0.83851866,0.33014205,0.32623765){1.0814407,0.94743142,0.59564173,0.89937894};
SS(-0.7489605,0.18190923,0.13647301,-0.61674646,0.25215289,0.3447871,-0.83851866,0.33014205,0.32623765,-0.72768327,0.10310141,0.33233484){0.59564173,0.54607287,0.89937894,0.63492881};
SS(-0.63048479,0.37587985,-0.34368186,-0.68637718,0.43295764,-0.18031685,-0.80558396,0.5878127,-0.29244037,-0.83127473,0.33505962,-0.32026923){0.64388066,0.67437813,1.0616703,0.89071695};
SS(0,-0.22019801,5.0496855e-05,-0.15923414,-0.34171533,-0.15079999,-0.20656092,-0.13938028,0.029547229,-0.098950987,-0.13391411,-0.14594667){0.029059683,0.14783141,0.048278496,0.03512721};
SS(-0.93582873,0.86427167,0.14668289,-0.74954172,1,0.13574231,-0.84394966,1,0.33504415,-0.76389013,0.77728265,0.25513738){1.6320629,1.562759,1.8084725,1.2358334};
SS(0.74440038,0.22095066,-0.087839409,0.70841775,0,-0.20847891,0.77985819,0,-0.00014691753,0.57129187,0.13526053,-0.13726946){0.59875958,0.52293439,0.58919206,0.35115136};
SS(-0.58934795,0.84141567,-0.18062024,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462,-0.38143574,0.84373572,-0.12387887){1.0736489,1.169501,0.83409809,0.85864479};
SS(-0.54640726,0.34339216,0.19847863,-0.39806707,0.15776443,0.15870839,-0.26986228,0.26051837,0.22418657,-0.41843781,0.30742585,0.3397996){0.43575493,0.19317292,0.1749353,0.37011438};
SS(-0.34310942,-0.010167032,0.1509038,-0.3727858,-0.19869367,0.11195566,-0.4720473,-0.063494476,-0.036829327,-0.20656092,-0.13938028,0.029547229){0.12661586,0.16948569,0.21285629,0.048278496};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.49997946,0.00010199173,-0.23583358,-0.36008743,0.0071767184,-0.18618058,-0.5161726,-0.15035515){0.14783141,0.22811872,0.16465457,0.30914003};
SS(-0.8827276,-0.88146034,0.13123348,-0.70832062,-1,0.2082538,-0.77973152,-1,-0.0001062007,-0.61978497,-0.82706917,0.12738472){1.5595365,1.5291125,1.588155,1.0681409};
SS(0.27170325,0.36204749,-0.4201745,0.11583535,0.30145324,-0.5,0.37532516,0.23078833,-0.5,0.20129651,0.21389912,-0.31902192){0.36885377,0.33954703,0.42551454,0.16839385};
SS(-0.34310942,-0.010167032,0.1509038,-0.1159097,-0.14329028,0.19302206,-0.25897908,-0.24013326,0.26450313,-0.3727858,-0.19869367,0.11195566){0.12661586,0.055235283,0.17775565,0.16948569};
SS(-0.61311838,0.85766427,0.15491279,-0.47185361,0.73769401,0.24072705,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){1.1216468,0.80384956,0.83409809,0.84829643};
SS(-0.61549046,-0.35581383,-0.12962263,-0.70236545,-0.13062851,-0.19140485,-0.65367362,-0.16081953,0.0014934597,-0.45843014,-0.20445062,-0.15988901){0.50877487,0.5265969,0.4344691,0.26094507};
SS(0.86971177,0.13024645,0.1427188,0.70845584,0,0.20819814,0.73568363,0.23203612,0.2735765,0.63998586,0.17856447,0.051345521){0.77797836,0.52761363,0.6509231,0.42570365};
SS(-0.65956212,-0.52273243,-0.19262862,-0.4581749,-0.5263483,-0.32801665,-0.49676106,-0.69523221,-0.26913048,-0.62341011,-0.46880832,-0.38153973){0.7287475,0.57811658,0.78043195,0.73807879};
SS(0.59365279,0.65503723,0.24444947,0.65062064,0.64268786,0.069510863,0.76099919,0.76690574,0.25750996,0.84582719,0.572243,0.1361951){0.82252715,0.82620698,1.2143065,1.0417018};
SS(0,-0.49997234,-0.27965571,-0.11754465,-0.65214472,-0.32749638,-0.26056819,-0.54975154,-0.34323516,-0.073421274,-0.375,-0.38984354){0.30906942,0.53347202,0.46884495,0.28201081};
SS(-0.88905946,-0.098697315,-0.13184676,-0.76752638,0.004448061,-0.013214377,-0.82279039,-0.18997945,0.10657137,-0.65367362,-0.16081953,0.0014934597){0.8023886,0.5734925,0.70945047,0.4344691};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.49991607,0.0031934521,-0.79172217,0.43302343,0.13373134,-0.8068077,0.56885008,-0.063754108){0.974079,1.2302733,0.80968993,0.96112076};
SS(0.085954007,0.41736025,0.32943097,-0.0073778212,0.36022468,0.15230712,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362){0.27115576,0.13675819,0.18575023,0.12181545};
SS(0,0,0.5,0.050277172,0.20853018,0.30186362,-0.045146113,0.19012269,0.5,0.12517622,0.12515553,0.5){0.23153294,0.12181545,0.27176836,0.27156885};
SS(0.27123349,0.36190713,0.41476339,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362,0.085954007,0.41736025,0.32943097){0.36300231,0.18575023,0.12181545,0.27115576};
SS(-0.68637718,0.43295764,-0.18031685,-0.63048479,0.37587985,-0.34368186,-0.80558396,0.5878127,-0.29244037,-0.6293812,0.63993291,-0.28812602){0.67437813,0.64388066,1.0616703,0.87296464};
SS(-0.0089783977,0.64320989,-0.13441642,0.094968532,0.84539386,-0.087484586,0.25248643,0.73785598,-0.13082591,0.081865095,0.80626877,-0.27867109){0.41358858,0.71839764,0.60350215,0.71703623};
SS(-0.28278924,0.041190137,-0.04219563,-0.34310942,-0.010167032,0.1509038,-0.4720473,-0.063494476,-0.036829327,-0.20656092,-0.13938028,0.029547229){0.063480395,0.12661586,0.21285629,0.048278496};
SS(-0.29237157,-0.11865629,-0.17606411,-0.28278924,0.041190137,-0.04219563,-0.4720473,-0.063494476,-0.036829327,-0.20656092,-0.13938028,0.029547229){0.11404163,0.063480395,0.21285629,0.048278496};
SS(-0.61115597,1,-0.10200355,-0.58934795,0.84141567,-0.18062024,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462){1.3611038,1.0736489,1.169501,0.83409809};
SS(0.11523872,0.30161582,0.5,0.27123349,0.36190713,0.41476339,0.050277172,0.20853018,0.30186362,0.085954007,0.41736025,0.32943097){0.33546792,0.36300231,0.12181545,0.27115576};
SS(0,-0.49997946,0.00010199173,-0.15923414,-0.34171533,-0.15079999,-0.23583358,-0.36008743,0.0071767184,-0.10133362,-0.40777162,0.1162396){0.22811872,0.14783141,0.16465457,0.17697987};
SS(-0.222315,1,-0.00011890035,-0.10743676,0.85847111,-0.11136175,-0.012406168,1,-0.034358602,-0.084253952,1,0.13733396){1.0307381,0.7462212,0.99121748,1.0073117};
SS(-0.10743676,0.85847111,-0.11136175,-0.012406168,1,-0.034358602,-0.084253952,1,0.13733396,-0.035654771,0.78507762,0.045007896){0.7462212,0.99121748,1.0073117,0.60161266};
SS(0.87881231,0.64063264,0.37220388,0.66554141,0.67524133,0.5,0.81191124,0.80644944,0.5,0.76099919,0.76690574,0.25750996){1.3069719,1.1271263,1.5425973,1.2143065};
SS(-0.59094649,-0.40495207,0.12834587,-0.50159539,-0.29258506,7.2987381e-06,-0.76760867,-0.33664988,-0.028298027,-0.65367362,-0.16081953,0.0014934597){0.51475101,0.32068114,0.68479998,0.4344691};
SS(-0.24163432,0.33561251,-0.055881164,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465,-0.19461387,0.3919517,0.10437587){0.16437697,0.20710489,0.040184006,0.19075448};
SS(-0.088882135,1,-0.23281641,-0.10743676,0.85847111,-0.11136175,0.081865095,0.80626877,-0.27867109,0.094968532,0.84539386,-0.087484586){1.0431215,0.7462212,0.71703623,0.71839764};
SS(-0.17097214,0.64900986,-0.39927747,-0.033284914,0.58770906,-0.5,0.00024312215,0.80750011,-0.5,0.10162062,0.65400865,-0.37913628){0.59741335,0.58301644,0.88610119,0.5665506};
SS(-0.222315,1,-0.00011890035,-0.10743676,0.85847111,-0.11136175,-0.084253952,1,0.13733396,-0.035654771,0.78507762,0.045007896){1.0307381,0.7462212,1.0073117,0.60161266};
SS(-0.40506391,-0.079541407,0.3303193,-0.58755791,0.033814853,0.5,-0.40752783,0.030201366,0.5,-0.52427834,0.10778268,0.27208728){0.26156128,0.57778723,0.40526498,0.34448415};
SS(-0.222315,1,-0.00011890035,-0.38143574,0.84373572,-0.12387887,-0.2401666,0.74114092,-0.051302261,-0.32294154,0.86180803,0.13108841){1.0307381,0.85864479,0.58653028,0.84829643};
SS(-0.65631386,-0.59724887,0.13822882,-0.59094649,-0.40495207,0.12834587,-0.73479965,-0.34302295,0.24038072,-0.56348952,-0.47594309,0.3052276){0.7890621,0.51475101,0.69668046,0.61776713};
SS(-0.47185361,0.73769401,0.24072705,-0.45563594,0.60375179,0.095527884,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){0.80384956,0.56263538,0.83409809,0.84829643};
SS(0.55555556,1,0.5,0.36841015,0.87909734,0.37310922,0.43654676,1,0.2604635,0.55555177,0.82262944,0.31125158){1.5357742,1.0362544,1.2403655,1.0671623};
SS(-0.41651431,0.41690828,-0.5,-0.63048479,0.37587985,-0.34368186,-0.49391083,0.27907498,-0.27264436,-0.50782983,0.50249565,-0.29902586){0.57523437,0.64388066,0.37398026,0.58612549};
SS(-0.61311838,0.85766427,0.15491279,-0.47972312,1,0.18932995,-0.47185361,0.73769401,0.24072705,-0.32294154,0.86180803,0.13108841){1.1216468,1.2473472,0.80384956,0.84829643};
SS(0,-0.29157012,0.20836692,-0.10133362,-0.40777162,0.1162396,-0.25897908,-0.24013326,0.26450313,-0.1159097,-0.14329028,0.19302206){0.11172813,0.17697987,0.17775565,0.055235283};
SS(-1,-0.55555556,0.5,-0.89663862,-0.69397302,0.37275403,-1,-0.47520831,0.27427507,-0.79575191,-0.55547687,0.30538166){1.5359657,1.4119512,1.2822693,1.0192798};
SS(-0.78327322,-0.45013966,0.5,-0.62450053,-0.31310845,0.38575928,-0.73479965,-0.34302295,0.24038072,-0.89646962,-0.32955067,0.34017365){1.0435491,0.62379151,0.69668046,1.0133061};
SS(-0.73479965,-0.34302295,0.24038072,-0.82279039,-0.18997945,0.10657137,-0.76760867,-0.33664988,-0.028298027,-0.82595855,-0.48031431,0.11444494){0.69668046,0.70945047,0.68479998,0.90887195};
SS(-0.70236545,-0.13062851,-0.19140485,-0.45843014,-0.20445062,-0.15988901,-0.4720473,-0.063494476,-0.036829327,-0.65367362,-0.16081953,0.0014934597){0.5265969,0.26094507,0.21285629,0.4344691};
SS(0,-0.5,-0.5,-0.073421274,-0.375,-0.38984354,-0.23055166,-0.37480907,-0.5,-0.14376826,-0.62489354,-0.5){0.4845449,0.28201081,0.41992239,0.6489606};
SS(-0.89962374,0.8609561,-0.16698164,-0.76988954,1,-0.26944904,-0.81095336,1,-0.07156149,-0.74249217,0.75399014,-0.15399718){1.5692753,1.6463902,1.6471359,1.1267767};
SS(0.69383766,0.49492178,-0.021800115,0.65062064,0.64268786,0.069510863,0.84582719,0.572243,0.1361951,0.8988736,0.63809662,-0.070284173){0.71284258,0.82620698,1.0417018,1.2046527};
SS(0.52843461,0.32737897,0.19102935,0.36016656,0.41044152,0.1594367,0.36021608,0.23247759,-0.012351094,0.46476684,0.14382827,0.12247557){0.40790135,0.3073722,0.16110593,0.23450402};
SS(-0.79227163,-0.79754897,0.0021844777,-0.65631386,-0.59724887,0.13822882,-0.82285362,-0.63420593,-0.0683896,-0.83996275,-0.66999882,0.11765553){1.2530106,0.7890621,1.0691297,1.1553131};
SS(-0.18268367,0.83021756,-0.5,0.0011150345,0.93517443,-0.37389303,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){0.9573479,1.0026385,1.1599423,0.68882385};
SS(-0.3548152,-0.48825703,0.21848985,-0.1853821,-0.42358473,0.30866054,-0.22656331,-0.68065623,0.28194433,-0.19247216,-0.56000521,0.088357129){0.38862106,0.29143101,0.57683818,0.34206231};
SS(0.37532516,0.23078833,-0.5,0.50136923,0.34587735,-0.44862257,0.34662081,0.36199915,-0.25068724,0.51910919,0.22553632,-0.31417891){0.42551454,0.56260896,0.29696992,0.40112301};
SS(-0.84289574,0.018333867,0.1608607,-1,-0.00021427218,0.00011802244,-0.76752638,0.004448061,-0.013214377,-0.82279039,-0.18997945,0.10657137){0.72430843,0.98080906,0.5734925,0.70945047};
SS(-0.67616985,-0.069078192,0.18801024,-0.82279039,-0.18997945,0.10657137,-0.73479965,-0.34302295,0.24038072,-0.84084014,-0.14895162,0.31636914){0.47948004,0.70945047,0.69668046,0.81273381};
SS(-0.78327322,-0.45013966,0.5,-0.89646962,-0.32955067,0.34017365,-0.73479965,-0.34302295,0.24038072,-0.79575191,-0.55547687,0.30538166){1.0435491,1.0133061,0.69668046,1.0192798};
SS(-0.64012388,-0.10177177,-0.37237302,-0.58754442,0.033885734,-0.5,-0.73174678,-0.21478859,-0.5,-0.80728146,0.00010990719,-0.5){0.54269073,0.58180393,0.81151292,0.88195685};
SS(0.11523872,0.30161582,0.5,0.27123349,0.36190713,0.41476339,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362){0.33546792,0.36300231,0.18575023,0.12181545};
SS(0.75922048,0.56990614,-0.17060419,0.69383766,0.49492178,-0.021800115,0.82562789,0.37565656,-0.12707714,0.8988736,0.63809662,-0.070284173){0.91133836,0.71284258,0.82387041,1.2046527};
SS(-0.88905946,-0.098697315,-0.13184676,-1,-0.00021427218,0.00011802244,-1,-0.20076836,0.00061221676,-0.82279039,-0.18997945,0.10657137){0.8023886,0.98080906,1.0172898,0.70945047};
SS(0.24635331,0.35131343,-0.096025322,0.08017426,0.31429474,-0.16745504,0.17777709,0.54047543,-0.2567554,0.09693172,0.3918681,-0.3370861){0.18045455,0.11103103,0.36840304,0.26256104};
SS(0.59416595,0.14141347,0.32656529,0.67112401,0.32933441,0.5,0.73568363,0.23203612,0.2735765,0.50761134,0.34933779,0.39015973){0.46498444,0.79210069,0.6509231,0.51484928};
SS(-0.58754442,0.033885734,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.73174678,-0.21478859,-0.5,-0.50815189,-0.16301678,-0.5){0.58180393,0.54269073,0.81151292,0.52110597};
SS(0.49866453,0.63973666,-0.21510859,0.61535375,0.70719289,-0.095218388,0.75922048,0.56990614,-0.17060419,0.68900489,0.77311276,-0.28043733){0.68344633,0.87858083,0.91133836,1.1326816};
SS(-0.58755791,0.033814853,0.5,-0.64009684,-0.10188458,0.37412975,-0.73174745,-0.21491043,0.5,-0.80727304,0.00024662976,0.5){0.57778723,0.54631619,0.81377033,0.88515177};
SS(-0.35455825,0.80859576,-0.32177549,-0.33333333,1,-0.5,-0.36992714,1,-0.22970445,-0.23070339,1,-0.34855306){0.86460259,1.3407278,1.1684568,1.1599423};
SS(-1,-0.11111111,-0.5,-0.85707128,-0.1416783,-0.34083416,-0.73174678,-0.21478859,-0.5,-0.80728146,0.00010990719,-0.5){1.2438655,0.85441326,0.81151292,0.88195685};
SS(-0.073421274,-0.375,-0.38984354,0,-0.25,-0.5,-0.23055166,-0.37480907,-0.5,-0.12484866,-0.12486094,-0.5){0.28201081,0.29677328,0.41992239,0.26766045};
SS(0.88049681,0.87960137,0.13412341,1,0.83856906,0.33864755,0.82853688,1,0.32125076,0.76099919,0.76690574,0.25750996){1.5518824,1.8033242,1.7703132,1.2143065};
SS(-0.60421932,0.82298164,0.34468578,-0.66546973,0.66566005,0.5,-0.80481649,0.80494069,0.5,-0.76389013,0.77728265,0.25513738){1.1449713,1.1224691,1.5232843,1.2358334};
SS(0,-0.49997946,0.00010199173,-0.19247216,-0.56000521,0.088357129,-0.23583358,-0.36008743,0.0071767184,-0.18618058,-0.5161726,-0.15035515){0.22811872,0.34206231,0.16465457,0.30914003};
SS(-0.16643696,-0.21791406,0.42402077,0,-0.29157012,0.20836692,-0.25897908,-0.24013326,0.26450313,-0.1159097,-0.14329028,0.19302206){0.23818505,0.11172813,0.17775565,0.055235283};
SS(-0.24654336,0.57133462,-0.25396354,-0.17097214,0.64900986,-0.39927747,-0.14847812,0.78021305,-0.27623142,-0.01813809,0.53618118,-0.30537166){0.42991415,0.59741335,0.68882385,0.36567785};
SS(0.094968532,0.84539386,-0.087484586,0.17426348,1,-0.18078905,0.25248643,0.73785598,-0.13082591,0.081865095,0.80626877,-0.27867109){0.71839764,1.045853,0.60350215,0.71703623};
SS(0.10211023,0.6404511,0.38011645,0.098704003,0.67249079,0.1943501,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.55160362,0.47957633,0.40200156,0.27115576};
SS(0.77985819,0,-0.00014691753,0.74440038,0.22095066,-0.087839409,0.86971177,0.13024645,0.1427188,0.88354722,0.11667767,-0.13069643){0.58919206,0.59875958,0.77797836,0.79839767};
SS(-0.61115597,1,-0.10200355,-0.38143574,0.84373572,-0.12387887,-0.36992714,1,-0.22970445,-0.42762906,1,-0.0094860889){1.3611038,0.85864479,1.1684568,1.169501};
SS(-1,-0.84092895,0.33252059,-0.89663862,-0.69397302,0.37275403,-0.80635543,-0.81164184,0.5,-0.77091496,-0.77159441,0.2629049){1.8030746,1.4119512,1.5410993,1.2433034};
SS(0.18202227,0.38279251,0.10350409,0.25126435,0.28098512,0.24657435,0.36021608,0.23247759,-0.012351094,0.36016656,0.41044152,0.1594367){0.17617817,0.18575023,0.16110593,0.3073722};
SS(0.42864323,0.48543211,-0.13804456,0.60662231,0.34516964,-0.13972301,0.34662081,0.36199915,-0.25068724,0.48047723,0.47791267,-0.33071402){0.42022283,0.48782847,0.29696992,0.55795418};
SS(0.08017426,0.31429474,-0.16745504,0.09693172,0.3918681,-0.3370861,0.34662081,0.36199915,-0.25068724,0.20129651,0.21389912,-0.31902192){0.11103103,0.26256104,0.29696992,0.16839385};
SS(0.49866453,0.63973666,-0.21510859,0.51674933,0.64481281,-0.39755292,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.68344633,0.82858869,0.72773009,0.55795418};
SS(-0.50815189,-0.16301678,-0.5,-0.41767704,0.010770256,-0.44072823,-0.49808619,0.0026201378,-0.26387206,-0.38492375,-0.20017574,-0.33650716){0.52110597,0.35514259,0.29810596,0.28705324};
SS(-0.0089783977,0.64320989,-0.13441642,0.11458044,0.70010244,0.010073529,-0.035654771,0.78507762,0.045007896,-0.098708274,0.55956225,0.10505678){0.41358858,0.49378055,0.60161266,0.31633913};
SS(0.59365279,0.65503723,0.24444947,0.76099919,0.76690574,0.25750996,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){0.82252715,1.2143065,0.77938072,1.0417018};
SS(-0.11754465,-0.65214472,-0.32749638,-0.32879066,-0.67072359,-0.5,-0.18848435,-0.81110947,-0.5,-0.2399131,-0.76005145,-0.25989531){0.53347202,0.79007105,0.92571371,0.6848256};
SS(-0.89804207,0.11676539,-0.10792088,-1,-0.00018427889,-0.26378916,-0.77267892,0.13105707,-0.24874664,-0.88905946,-0.098697315,-0.13184676){0.82300022,1.0508045,0.65386325,0.8023886};
SS(-0.64009684,-0.10188458,0.37412975,-0.58755791,0.033814853,0.5,-0.73174745,-0.21491043,0.5,-0.50807239,-0.16307462,0.5){0.54631619,0.57778723,0.81377033,0.52416601};
SS(-0.92571354,0.17249619,-0.34283108,-0.80728146,0.00010990719,-0.5,-0.82994199,0.18319278,-0.5,-0.77267892,0.13105707,-0.24874664){0.99158484,0.88195685,0.95993957,0.65386325};
SS(0.11523872,0.30161582,0.5,0.26083053,0.15082484,0.37728795,0.25126435,0.28098512,0.24657435,0.27123349,0.36190713,0.41476339){0.33546792,0.21918499,0.18575023,0.36300231};
SS(-0.66548665,0.66585508,-0.5,-0.6293812,0.63993291,-0.28812602,-0.50014045,0.79673357,-0.5,-0.65756371,0.81308934,-0.3429452){1.1221664,0.87296464,1.1145783,1.1958888};
SS(-0.89663862,-0.69397302,0.37275403,-0.67513028,-0.66529728,0.5,-0.77091496,-0.77159441,0.2629049,-0.79575191,-0.55547687,0.30538166){1.4119512,1.1284607,1.2433034,1.0192798};
SS(0.87867265,0.36391919,-0.37720578,0.671223,0.32907594,-0.5,0.77491511,0.22516452,-0.26425516,0.67125235,0.44297685,-0.31879306){1.03034,0.79435762,0.70313431,0.72773009};
SS(-0.36340067,-0.87821042,-0.37678589,-0.2399131,-0.76005145,-0.25989531,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){1.0307746,0.6848256,0.78043195,0.88525127};
SS(0.48047723,0.47791267,-0.33071402,0.60662231,0.34516964,-0.13972301,0.51910919,0.22553632,-0.31417891,0.67125235,0.44297685,-0.31879306){0.55795418,0.48782847,0.40112301,0.72773009};
SS(-0.10037172,0.18891947,0.20844359,-0.26986228,0.26051837,0.22418657,-0.13709741,0.19518884,0.034033465,-0.15128303,0.02253305,0.11422928){0.074828316,0.1749353,0.040184006,0.025420414};
SS(0.11136938,1,0.13859714,0.094968532,0.84539386,-0.087484586,0.24937941,1,-0.00011138016,0.24404834,0.79519787,0.082231238){1.0072058,0.71839764,1.0446566,0.68472542};
SS(0,-0.5,-0.5,-0.11754465,-0.65214472,-0.32749638,0,-0.49997234,-0.27965571,-0.073421274,-0.375,-0.38984354){0.4845449,0.53347202,0.30906942,0.28201081};
SS(0.26083053,0.15082484,0.37728795,0.29175541,0,0.20824909,0.25126435,0.28098512,0.24657435,0.42621669,0.19017509,0.30505062){0.21918499,0.1093371,0.18575023,0.29714896};
SS(0.26083053,0.15082484,0.37728795,0.11523872,0.30161582,0.5,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362){0.21918499,0.33546792,0.18575023,0.12181545};
SS(-0.30949447,0.8262402,0.33528492,-0.33333333,1,0.5,-0.44431425,1,0.36245944,-0.50037,0.79662088,0.5){0.87388961,1.3433112,1.3152029,1.1183194};
SS(-0.80635543,-0.81164184,0.5,-1,-1,0.5,-1,-0.77777778,0.5,-1,-0.84092895,0.33252059){1.5410993,2.2322143,1.8434331,1.8030746};
SS(0.08017426,0.31429474,-0.16745504,0.24635331,0.35131343,-0.096025322,0.34662081,0.36199915,-0.25068724,0.09693172,0.3918681,-0.3370861){0.11103103,0.18045455,0.29696992,0.26256104};
SS(0.46476684,0.14382827,0.12247557,0.50010751,0,-0.00013054911,0.36021608,0.23247759,-0.012351094,0.57129187,0.13526053,-0.13726946){0.23450402,0.22823279,0.16110593,0.35115136};
SS(-0.34310942,-0.010167032,0.1509038,-0.39806707,0.15776443,0.15870839,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.12661586,0.19317292,0.10955402,0.025420414};
SS(-0.1182182,0.15955837,-0.3159857,-0.17603462,0.24070348,-0.5,-0.19007896,0.04567822,-0.5,-0.29413589,0.046284299,-0.31274881){0.11990198,0.32537509,0.27736807,0.1681493};
SS(-0.4433427,0.53576375,-0.12560501,-0.32064519,0.49448821,1.4739833e-06,-0.54631436,0.45612147,-0.00074796238,-0.34372617,0.39779568,-0.18541051){0.48429505,0.32892635,0.48593017,0.29650146};
SS(-0.57994589,-0.69256437,0.31204703,-0.67513028,-0.66529728,0.5,-0.50050976,-0.57246927,0.5,-0.56348952,-0.47594309,0.3052276){0.89957508,1.1284607,0.81219504,0.61776713};
SS(-0.4581749,-0.5263483,-0.32801665,-0.32879066,-0.67072359,-0.5,-0.50036547,-0.57239096,-0.5,-0.26056819,-0.54975154,-0.34323516){0.57811658,0.79007105,0.81333009,0.46884495};
SS(0.59416595,0.14141347,0.32656529,0.52843461,0.32737897,0.19102935,0.63998586,0.17856447,0.051345521,0.46476684,0.14382827,0.12247557){0.46498444,0.40790135,0.42570365,0.23450402};
SS(0.10162062,0.65400865,-0.37913628,0.21543771,0.73213875,-0.5,0.33386283,0.81592026,-0.31808704,0.34412919,0.6158316,-0.3427703){0.5665506,0.81134051,0.86115027,0.59958408};
SS(0.10211023,0.6404511,0.38011645,-0.033588837,0.5879061,0.5,0.21512427,0.73211919,0.5,0.00029730467,0.80760978,0.5){0.55160362,0.57806214,0.81521474,0.88423684};
SS(-0.35582611,-0.64426575,-0.070000747,-0.50537844,-0.68762812,0.023695348,-0.42066299,-0.84356131,-0.12906413,-0.36608751,-0.8951802,0.074405883){0.52757348,0.71483247,0.88525127,0.92652515};
SS(-0.65631386,-0.59724887,0.13822882,-0.79227163,-0.79754897,0.0021844777,-0.63348211,-0.7706683,-0.074889286,-0.61978497,-0.82706917,0.12738472){0.7890621,1.2530106,0.97907785,1.0681409};
SS(-0.64012388,-0.10177177,-0.37237302,-0.50815189,-0.16301678,-0.5,-0.49808619,0.0026201378,-0.26387206,-0.38492375,-0.20017574,-0.33650716){0.54269073,0.52110597,0.29810596,0.28705324};
SS(0.094968532,0.84539386,-0.087484586,0.17426348,1,-0.18078905,-0.088882135,1,-0.23281641,-0.012406168,1,-0.034358602){0.71839764,1.045853,1.0431215,0.99121748};
SS(-0.19461387,0.3919517,0.10437587,-0.26986228,0.26051837,0.22418657,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465){0.19075448,0.1749353,0.20710489,0.040184006};
SS(-0.55555556,1,-0.5,-0.65756371,0.81308934,-0.3429452,-0.50014045,0.79673357,-0.5,-0.56041637,1,-0.29784853){1.5379273,1.1958888,1.1145783,1.3856141};
SS(-0.64012388,-0.10177177,-0.37237302,-0.73174678,-0.21478859,-0.5,-0.50815189,-0.16301678,-0.5,-0.56113743,-0.28920115,-0.29204918){0.54269073,0.81151292,0.52110597,0.46850822};
SS(0.094968532,0.84539386,-0.087484586,0.11136938,1,0.13859714,-0.012406168,1,-0.034358602,-0.035654771,0.78507762,0.045007896){0.71839764,1.0072058,0.99121748,0.60161266};
SS(0.13913358,0.10014326,0.18199659,0.25126435,0.28098512,0.24657435,0.13261259,0.21336316,0.036566127,0.050277172,0.20853018,0.30186362){0.045990896,0.18575023,0.046199082,0.12181545};
SS(-0.93582873,0.86427167,0.14668289,-1,1,-6.9388939e-15,-0.74954172,1,0.13574231,-0.81095336,1,-0.07156149){1.6320629,1.9810426,1.562759,1.6471359};
SS(-0.11754465,-0.65214472,-0.32749638,-0.18848435,-0.81110947,-0.5,0,-0.83851883,-0.33849865,-0.2399131,-0.76005145,-0.25989531){0.53347202,0.92571371,0.80235204,0.6848256};
SS(0.68985253,1,-0.19792707,0.54700908,0.85955032,-0.16345766,0.68900489,0.77311276,-0.28043733,0.77861211,0.77861193,-0.067175459){1.495304,1.0528061,1.1326816,1.1981052};
SS(0,-0.22019801,5.0496855e-05,-0.15923414,-0.34171533,-0.15079999,-0.23583358,-0.36008743,0.0071767184,-0.20656092,-0.13938028,0.029547229){0.029059683,0.14783141,0.16465457,0.048278496};
SS(-0.62450053,-0.31310845,0.38575928,-0.73479965,-0.34302295,0.24038072,-0.84084014,-0.14895162,0.31636914,-0.64009684,-0.10188458,0.37412975){0.62379151,0.69668046,0.81273381,0.54631619};
SS(-0.15923414,-0.34171533,-0.15079999,-0.29237157,-0.11865629,-0.17606411,-0.20656092,-0.13938028,0.029547229,-0.098950987,-0.13391411,-0.14594667){0.14783141,0.11404163,0.048278496,0.03512721};
SS(0.68966181,1,0.19790566,0.88049681,0.87960137,0.13412341,0.76099919,0.76690574,0.25750996,0.62860594,0.86645525,0.049037492){1.492557,1.5518824,1.2143065,1.1303867};
SS(0.60662231,0.34516964,-0.13972301,0.37137652,0.1767682,-0.19801193,0.51910919,0.22553632,-0.31417891,0.57129187,0.13526053,-0.13726946){0.48782847,0.19205628,0.40112301,0.35115136};
SS(-1,-1,-0.5,-0.80632325,-0.81147186,-0.5,-1,-0.77777778,-0.5,-1,-0.83959635,-0.33115777){2.2321573,1.5409894,1.8436809,1.7998257};
SS(-0.31377045,0.30492781,-0.36427962,-0.41651431,0.41690828,-0.5,-0.49391083,0.27907498,-0.27264436,-0.50782983,0.50249565,-0.29902586){0.30770932,0.57523437,0.37398026,0.58612549};
SS(-0.10133362,-0.40777162,0.1162396,-0.3548152,-0.48825703,0.21848985,-0.23583358,-0.36008743,0.0071767184,-0.19247216,-0.56000521,0.088357129){0.17697987,0.38862106,0.16465457,0.34206231};
SS(0.70845584,0,0.20819814,0.59416595,0.14141347,0.32656529,0.73568363,0.23203612,0.2735765,0.63998586,0.17856447,0.051345521){0.52761363,0.46498444,0.6509231,0.42570365};
SS(0.39612945,0.70614162,0.21524614,0.59365279,0.65503723,0.24444947,0.55555177,0.82262944,0.31125158,0.35567295,0.65317229,0.39545235){0.68453461,0.82252715,1.0671623,0.69293227};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65956212,-0.52273243,-0.19262862,-0.63348211,-0.7706683,-0.074889286,-0.82285362,-0.63420593,-0.0683896){0.74484897,0.7287475,0.97907785,1.0691297};
SS(0.10162062,0.65400865,-0.37913628,-0.033284914,0.58770906,-0.5,0.21543771,0.73213875,-0.5,0.16368264,0.50834729,-0.5){0.5665506,0.58301644,0.81134051,0.52115901};
SS(0.37549445,0.49317282,-0.5,0.34412919,0.6158316,-0.3427703,0.21543771,0.73213875,-0.5,0.16368264,0.50834729,-0.5){0.61648995,0.59958408,0.81134051,0.52115901};
SS(-0.61115597,1,-0.10200355,-0.58934795,0.84141567,-0.18062024,-0.36992714,1,-0.22970445,-0.38143574,0.84373572,-0.12387887){1.3611038,1.0736489,1.1684568,0.85864479};
SS(1,0.83856906,0.33864755,0.87881231,0.64063264,0.37220388,0.81191124,0.80644944,0.5,0.76099919,0.76690574,0.25750996){1.8033242,1.3069719,1.5425973,1.2143065};
SS(-0.49284988,-0.37485679,0.5,-0.62450053,-0.31310845,0.38575928,-0.73174745,-0.21491043,0.5,-0.50807239,-0.16307462,0.5){0.6163523,0.62379151,0.81377033,0.52416601};
SS(0.67125235,0.44297685,-0.31879306,0.6657623,0.67544754,-0.5,0.78906409,0.5041626,-0.5,0.57309542,0.50075776,-0.5){0.72773009,1.1304562,1.1105402,0.81773274};
SS(-0.73174745,-0.21491043,0.5,-0.62450053,-0.31310845,0.38575928,-0.78327322,-0.45013966,0.5,-0.89646962,-0.32955067,0.34017365){0.81377033,0.62379151,1.0435491,1.0133061};
SS(0.23106485,1,0.31398279,0.36841015,0.87909734,0.37310922,0.22886345,0.79287946,0.30210005,0.40637652,0.87094343,0.13060843){1.1340577,1.0362544,0.75332396,0.92399337};
SS(-0.32879066,-0.67072359,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.50036547,-0.57239096,-0.5,-0.49676106,-0.69523221,-0.26913048){0.79007105,0.57811658,0.81333009,0.78043195};
SS(-0.033284914,0.58770906,-0.5,0.10162062,0.65400865,-0.37913628,0.21543771,0.73213875,-0.5,0.00024312215,0.80750011,-0.5){0.58301644,0.5665506,0.81134051,0.88610119};
SS(0.66554141,0.67524133,0.5,0.64232771,0.84838332,0.46476191,0.76099919,0.76690574,0.25750996,0.55555177,0.82262944,0.31125158){1.1271263,1.3339184,1.2143065,1.0671623};
SS(0.24635331,0.35131343,-0.096025322,0.36021608,0.23247759,-0.012351094,0.13261259,0.21336316,0.036566127,0.13402468,0.11673163,-0.1460819){0.18045455,0.16110593,0.046199082,0.039337265};
SS(-0.36608751,-0.8951802,0.074405883,-0.35582611,-0.64426575,-0.070000747,-0.22302806,-0.77703925,0.068353305,-0.42066299,-0.84356131,-0.12906413){0.92652515,0.52757348,0.64063544,0.88525127};
SS(0.0011150345,0.93517443,-0.37389303,0.17426348,1,-0.18078905,-0.088882135,1,-0.23281641,0.081865095,0.80626877,-0.27867109){1.0026385,1.045853,1.0431215,0.71703623};
SS(0.59416595,0.14141347,0.32656529,0.52843461,0.32737897,0.19102935,0.73568363,0.23203612,0.2735765,0.63998586,0.17856447,0.051345521){0.46498444,0.40790135,0.6509231,0.42570365};
SS(0.5,0,-0.5,0.35689191,0.091376279,-0.36932783,0.50007058,0,-0.27987971,0.51910919,0.22553632,-0.31417891){0.48471812,0.26145514,0.31006895,0.40112301};
SS(-1,-0.33333333,0.5,-0.89646962,-0.32955067,0.34017365,-1,-0.55555556,0.5,-1,-0.47520831,0.27427507){1.3443603,1.0133061,1.5359657,1.2822693};
SS(0.60662231,0.34516964,-0.13972301,0.74440038,0.22095066,-0.087839409,0.77315808,0.36766952,0.075951375,0.63998586,0.17856447,0.051345521){0.48782847,0.59875958,0.71793497,0.42570365};
SS(-0.68637718,0.43295764,-0.18031685,-0.65355936,0.25468043,-0.1897796,-0.54631436,0.45612147,-0.00074796238,-0.63246299,0.29145388,0.035195127){0.67437813,0.51379882,0.48593017,0.47226275};
SS(-0.033588837,0.5879061,0.5,0.10211023,0.6404511,0.38011645,0.21512427,0.73211919,0.5,0.16321322,0.50838432,0.5){0.57806214,0.55160362,0.81521474,0.52238519};
SS(0.65062064,0.64268786,0.069510863,0.69383766,0.49492178,-0.021800115,0.75922048,0.56990614,-0.17060419,0.8988736,0.63809662,-0.070284173){0.82620698,0.71284258,0.91133836,1.2046527};
SS(-0.67513028,-0.66529728,0.5,-0.57994589,-0.69256437,0.31204703,-0.77091496,-0.77159441,0.2629049,-0.79575191,-0.55547687,0.30538166){1.1284607,0.89957508,1.2433034,1.0192798};
SS(0.54326203,0.87223293,-0.356993,0.55555556,1,-0.5,0.33333333,1,-0.5,0.43683247,1,-0.26068681){1.1662147,1.5352494,1.342474,1.2435523};
SS(-0.16643696,-0.21791406,0.42402077,0,0,0.5,0,-0.16137283,0.3386068,-0.17669296,0.011023676,0.5){0.23818505,0.23153294,0.12565914,0.26322593};
SS(0.10162062,0.65400865,-0.37913628,0.21543771,0.73213875,-0.5,0.081865095,0.80626877,-0.27867109,0.33386283,0.81592026,-0.31808704){0.5665506,0.81134051,0.71703623,0.86115027};
SS(-0.45843014,-0.20445062,-0.15988901,-0.29237157,-0.11865629,-0.17606411,-0.36174,-0.40052234,-0.23665811,-0.38492375,-0.20017574,-0.33650716){0.26094507,0.11404163,0.32480953,0.28705324};
SS(-0.77777778,1,0.5,-1,1,0.5,-0.84394966,1,0.33504415,-0.80481649,0.80494069,0.5){1.8407438,2.2338249,1.8084725,1.5232843};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.29157012,0.20836692,0,-0.22019801,5.0496855e-05,-0.1159097,-0.14329028,0.19302206){0.17697987,0.11172813,0.029059683,0.055235283};
SS(-0.89804207,0.11676539,-0.10792088,-0.77267892,0.13105707,-0.24874664,-0.76752638,0.004448061,-0.013214377,-0.88905946,-0.098697315,-0.13184676){0.82300022,0.65386325,0.5734925,0.8023886};
SS(-0.23055166,-0.37480907,-0.5,-0.073421274,-0.375,-0.38984354,-0.12484866,-0.12486094,-0.5,-0.1971424,-0.26981885,-0.30750196){0.41992239,0.28201081,0.26766045,0.19280289};
SS(-0.83127473,0.33505962,-0.32026923,-1,0.25105097,-0.19350143,-0.77267892,0.13105707,-0.24874664,-0.78848723,0.26584533,-0.068869999){0.89071695,1.0825888,0.65386325,0.68151298};
SS(-0.26297351,0.20404986,-0.17122089,-0.24163432,0.33561251,-0.055881164,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465){0.12773981,0.16437697,0.20710489,0.040184006};
SS(0.69383766,0.49492178,-0.021800115,0.65062064,0.64268786,0.069510863,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){0.71284258,0.82620698,0.77938072,1.0417018};
SS(-0.70236545,-0.13062851,-0.19140485,-0.64012388,-0.10177177,-0.37237302,-0.7907607,-0.33838097,-0.28342271,-0.85707128,-0.1416783,-0.34083416){0.5265969,0.54269073,0.80149819,0.85441326};
SS(-0.24000819,0.17660305,0.5,-0.36145429,0.13293621,0.35430528,-0.26986228,0.26051837,0.22418657,-0.20045203,0.067929244,0.29301468){0.3210912,0.26360063,0.1749353,0.10955402};
SS(-0.79575191,-0.55547687,0.30538166,-0.65631386,-0.59724887,0.13822882,-0.73479965,-0.34302295,0.24038072,-0.56348952,-0.47594309,0.3052276){1.0192798,0.7890621,0.69668046,0.61776713};
SS(-0.73479965,-0.34302295,0.24038072,-0.62450053,-0.31310845,0.38575928,-0.84084014,-0.14895162,0.31636914,-0.89646962,-0.32955067,0.34017365){0.69668046,0.62379151,0.81273381,1.0133061};
SS(-0.62450053,-0.31310845,0.38575928,-0.73174745,-0.21491043,0.5,-0.84084014,-0.14895162,0.31636914,-0.89646962,-0.32955067,0.34017365){0.62379151,0.81377033,0.81273381,1.0133061};
SS(-1,-0.00021427218,0.00011802244,-0.88905946,-0.098697315,-0.13184676,-0.76752638,0.004448061,-0.013214377,-0.82279039,-0.18997945,0.10657137){0.98080906,0.8023886,0.5734925,0.70945047};
SS(0.77315808,0.36766952,0.075951375,0.69383766,0.49492178,-0.021800115,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){0.71793497,0.71284258,0.77938072,1.0417018};
SS(-0.23055166,-0.37480907,-0.5,-0.073421274,-0.375,-0.38984354,-0.1971424,-0.26981885,-0.30750196,-0.26056819,-0.54975154,-0.34323516){0.41992239,0.28201081,0.19280289,0.46884495};
SS(-0.0073778212,0.36022468,0.15230712,0.18202227,0.38279251,0.10350409,0.25126435,0.28098512,0.24657435,0.050277172,0.20853018,0.30186362){0.13675819,0.17617817,0.18575023,0.12181545};
SS(-0.34549718,-0.50098866,0.4105565,-0.23048975,-0.37484721,0.5,-0.14394692,-0.62481063,0.5,-0.1853821,-0.42358473,0.30866054){0.5260109,0.42714666,0.63866347,0.29143101};
SS(0.65062064,0.64268786,0.069510863,0.8988736,0.63809662,-0.070284173,0.75922048,0.56990614,-0.17060419,0.77861211,0.77861193,-0.067175459){0.82620698,1.2046527,0.91133836,1.1981052};
SS(-0.48255002,0.69900846,-0.19155417,-0.4433427,0.53576375,-0.12560501,-0.48952189,0.78345034,0.019065462,-0.62332411,0.59900263,-0.10904345){0.74365966,0.48429505,0.83409809,0.74800561};
SS(-0.7055892,-0.50616462,-0.017961589,-0.65631386,-0.59724887,0.13822882,-0.63348211,-0.7706683,-0.074889286,-0.50537844,-0.68762812,0.023695348){0.74484897,0.7890621,0.97907785,0.71483247};
SS(-0.18848435,-0.81110947,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.16144976,-1,-0.33863959,-0.2399131,-0.76005145,-0.25989531){0.92571371,1.0307746,1.1250711,0.6848256};
SS(-0.58755791,0.033814853,0.5,-0.64009684,-0.10188458,0.37412975,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.57778723,0.54631619,0.34448415,0.26156128};
SS(-0.3548152,-0.48825703,0.21848985,-0.40125956,-0.65699374,0.33213173,-0.42889738,-0.75253072,0.17523232,-0.22656331,-0.68065623,0.28194433){0.38862106,0.69449311,0.75958282,0.57683818};
SS(-0.39806707,0.15776443,0.15870839,-0.54640726,0.34339216,0.19847863,-0.39654734,0.26661646,0.019312696,-0.63246299,0.29145388,0.035195127){0.19317292,0.43575493,0.20710489,0.47226275};
SS(-0.2401666,0.74114092,-0.051302261,-0.10743676,0.85847111,-0.11136175,-0.14847812,0.78021305,-0.27623142,-0.38143574,0.84373572,-0.12387887){0.58653028,0.7462212,0.68882385,0.85864479};
SS(-0.62332411,0.59900263,-0.10904345,-0.68637718,0.43295764,-0.18031685,-0.54631436,0.45612147,-0.00074796238,-0.8068077,0.56885008,-0.063754108){0.74800561,0.67437813,0.48593017,0.96112076};
SS(-0.65776896,0.64141588,0.074371921,-0.62332411,0.59900263,-0.10904345,-0.54631436,0.45612147,-0.00074796238,-0.8068077,0.56885008,-0.063754108){0.83514199,0.74800561,0.48593017,0.96112076};
SS(-0.35582611,-0.64426575,-0.070000747,-0.50537844,-0.68762812,0.023695348,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){0.52757348,0.71483247,0.78043195,0.88525127};
SS(-0.60421932,0.82298164,0.34468578,-0.50037,0.79662088,0.5,-0.47185361,0.73769401,0.24072705,-0.30949447,0.8262402,0.33528492){1.1449713,1.1183194,0.80384956,0.87388961};
SS(0.34662081,0.36199915,-0.25068724,0.27170325,0.36204749,-0.4201745,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.29696992,0.36885377,0.36840304,0.59958408};
SS(-0.16144976,-1,-0.33863959,-0.12233239,-0.87748906,-0.13583418,0,-0.83851883,-0.33849865,-0.2399131,-0.76005145,-0.25989531){1.1250711,0.78823805,0.80235204,0.6848256};
SS(0.87272604,0.35900693,0.37172569,1,0.29178008,0.20838772,1,0.16158711,0.33859063,0.73568363,0.23203612,0.2735765){1.0107603,1.1084285,1.1259698,0.6509231};
SS(-0.42066299,-0.84356131,-0.12906413,-0.35582611,-0.64426575,-0.070000747,-0.2399131,-0.76005145,-0.25989531,-0.49676106,-0.69523221,-0.26913048){0.88525127,0.52757348,0.6848256,0.78043195};
SS(0.20129651,0.21389912,-0.31902192,0.24635331,0.35131343,-0.096025322,0.13402468,0.11673163,-0.1460819,0.37137652,0.1767682,-0.19801193){0.16839385,0.18045455,0.039337265,0.19205628};
SS(0.77315808,0.36766952,0.075951375,0.86971177,0.13024645,0.1427188,0.73568363,0.23203612,0.2735765,0.63998586,0.17856447,0.051345521){0.71793497,0.77797836,0.6509231,0.42570365};
SS(0.08017426,0.31429474,-0.16745504,-0.056808231,0.14323286,-0.13367928,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.11103103,0.022140076,0.21185338,0.11990198};
SS(0.27170325,0.36204749,-0.4201745,0.16368264,0.50834729,-0.5,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.36885377,0.52115901,0.36840304,0.59958408};
SS(-0.50537844,-0.68762812,0.023695348,-0.63348211,-0.7706683,-0.074889286,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){0.71483247,0.97907785,0.78043195,0.88525127};
SS(1,0.29178008,0.20838772,0.86971177,0.13024645,0.1427188,1,0.16158711,0.33859063,0.73568363,0.23203612,0.2735765){1.1084285,0.77797836,1.1259698,0.6509231};
SS(0.36016656,0.41044152,0.1594367,0.52843461,0.32737897,0.19102935,0.52218723,0.46943947,0.022097553,0.47723835,0.52605258,0.30619083){0.3073722,0.40790135,0.46892029,0.58228229};
SS(0.49866453,0.63973666,-0.21510859,0.51674933,0.64481281,-0.39755292,0.33386283,0.81592026,-0.31808704,0.54326203,0.87223293,-0.356993){0.68344633,0.82858869,0.86115027,1.1662147};
SS(-0.47972312,1,0.18932995,-0.30949447,0.8262402,0.33528492,-0.22223836,1,0.2622369,-0.44431425,1,0.36245944){1.2473472,0.87388961,1.0984067,1.3152029};
SS(-0.38492375,-0.20017574,-0.33650716,-0.29237157,-0.11865629,-0.17606411,-0.36174,-0.40052234,-0.23665811,-0.1971424,-0.26981885,-0.30750196){0.28705324,0.11404163,0.32480953,0.19280289};
SS(-1,1,-6.9388939e-15,-0.89962374,0.8609561,-0.16698164,-0.81095336,1,-0.07156149,-0.79370724,0.81084643,0.045877226){1.9810426,1.5692753,1.6471359,1.270911};
SS(-0.30949447,0.8262402,0.33528492,-0.47972312,1,0.18932995,-0.22223836,1,0.2622369,-0.32294154,0.86180803,0.13108841){0.87388961,1.2473472,1.0984067,0.84829643};
SS(0.10162062,0.65400865,-0.37913628,-0.0089783977,0.64320989,-0.13441642,0.17777709,0.54047543,-0.2567554,0.081865095,0.80626877,-0.27867109){0.5665506,0.41358858,0.36840304,0.71703623};
SS(0.77491511,0.22516452,-0.26425516,0.81149777,0.18885984,-0.5,1,0.16156328,-0.33847781,0.83867599,0,-0.33865964){0.70313431,0.92750237,1.1261583,0.80182539};
SS(-0.66546973,0.66566005,0.5,-0.87046532,0.63071146,0.35630423,-0.80481649,0.80494069,0.5,-0.76389013,0.77728265,0.25513738){1.1224691,1.2666006,1.5232843,1.2358334};
SS(-0.20984637,0.69532212,0.20809493,-0.29261734,0.53193925,0.43339885,-0.11618574,0.50328545,0.29980467,-0.16015893,0.67694077,0.39025863){0.55022745,0.53993003,0.33969293,0.6265216};
SS(0.52843461,0.32737897,0.19102935,0.36016656,0.41044152,0.1594367,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.40790135,0.3073722,0.29714896,0.51484928};
SS(-0.47972312,1,0.18932995,-0.30949447,0.8262402,0.33528492,-0.47185361,0.73769401,0.24072705,-0.32294154,0.86180803,0.13108841){1.2473472,0.87388961,0.80384956,0.84829643};
SS(-1,-0.5000565,0.0033661208,-0.83996275,-0.66999882,0.11765553,-1,-0.77608598,0.00064487429,-0.82285362,-0.63420593,-0.0683896){1.2263361,1.1553131,1.5844414,1.0691297};
SS(0.34662081,0.36199915,-0.25068724,0.24635331,0.35131343,-0.096025322,0.17777709,0.54047543,-0.2567554,0.09693172,0.3918681,-0.3370861){0.29696992,0.18045455,0.36840304,0.26256104};
SS(0.42864323,0.48543211,-0.13804456,0.34662081,0.36199915,-0.25068724,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.42022283,0.29696992,0.36840304,0.59958408};
SS(-1,1,0.5,-0.80481649,0.80494069,0.5,-1,0.77777778,0.5,-1,0.84108515,0.33242406){2.2338249,1.5232843,1.8402752,1.8031397};
SS(0.49866453,0.63973666,-0.21510859,0.34412919,0.6158316,-0.3427703,0.25248643,0.73785598,-0.13082591,0.33386283,0.81592026,-0.31808704){0.68344633,0.59958408,0.60350215,0.86115027};
SS(-0.20984637,0.69532212,0.20809493,-0.29261734,0.53193925,0.43339885,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467){0.55022745,0.53993003,0.42001946,0.33969293};
SS(1,0,-0.5,1,0.16156328,-0.33847781,0.81149777,0.18885984,-0.5,0.83867599,0,-0.33865964){1.2327879,1.1261583,0.92750237,0.80182539};
SS(0.76099919,0.76690574,0.25750996,0.87881231,0.64063264,0.37220388,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){1.2143065,1.3069719,0.77938072,1.0417018};
SS(0,-1,-0.5,0,-0.83851883,-0.33849865,-0.18848435,-0.81110947,-0.5,-0.16144976,-1,-0.33863959){1.2333742,0.80235204,0.92571371,1.1250711};
SS(-0.89962374,0.8609561,-0.16698164,-1,1,-6.9388939e-15,-1,0.77631186,0.00053339564,-0.79370724,0.81084643,0.045877226){1.5692753,1.9810426,1.5817554,1.270911};
SS(0,-1,0.5,0,-0.83845667,0.33864852,-0.16134158,-1,0.33850563,-0.18863677,-0.81113033,0.5){1.232491,0.80178572,1.129042,0.92459822};
SS(0,0,-6.9388939e-15,-0.056808231,0.14323286,-0.13367928,-0.13709741,0.19518884,0.034033465,0.13261259,0.21336316,0.036566127){-0.017891206,0.022140076,0.040184006,0.046199082};
SS(1,0,0.5,1,0.16158711,0.33859063,0.83866368,0,0.33843958,0.81143387,0.18901581,0.5){1.2336156,1.1259698,0.80106313,0.9265446};
SS(-0.60421932,0.82298164,0.34468578,-0.47972312,1,0.18932995,-0.44431425,1,0.36245944,-0.47185361,0.73769401,0.24072705){1.1449713,1.2473472,1.3152029,0.80384956};
SS(-0.58934795,0.84141567,-0.18062024,-0.61115597,1,-0.10200355,-0.81095336,1,-0.07156149,-0.74249217,0.75399014,-0.15399718){1.0736489,1.3611038,1.6471359,1.1267767};
SS(-0.83996275,-0.66999882,0.11765553,-1,-0.47520831,0.27427507,-0.79575191,-0.55547687,0.30538166,-0.82595855,-0.48031431,0.11444494){1.1553131,1.2822693,1.0192798,0.90887195};
SS(-0.26986228,0.26051837,0.22418657,-0.39806707,0.15776443,0.15870839,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465){0.1749353,0.19317292,0.20710489,0.040184006};
SS(-0.15128303,0.02253305,0.11422928,0,0,-6.9388939e-15,-0.20656092,-0.13938028,0.029547229,-0.28278924,0.041190137,-0.04219563){0.025420414,-0.017891206,0.048278496,0.063480395};
SS(0.74440038,0.22095066,-0.087839409,1,0.2203628,5.6826691e-05,0.86971177,0.13024645,0.1427188,0.88354722,0.11667767,-0.13069643){0.59875958,1.0268649,0.77797836,0.79839767};
SS(-0.41651431,0.41690828,-0.5,-0.31377045,0.30492781,-0.36427962,-0.17603462,0.24070348,-0.5,-0.20381263,0.45499536,-0.5){0.57523437,0.30770932,0.32537509,0.478983};
SS(-0.64012388,-0.10177177,-0.37237302,-0.73174678,-0.21478859,-0.5,-0.7907607,-0.33838097,-0.28342271,-0.85707128,-0.1416783,-0.34083416){0.54269073,0.81151292,0.80149819,0.85441326};
SS(0.33386283,0.81592026,-0.31808704,0.17426348,1,-0.18078905,0.2222976,1,-0.35617554,0.081865095,0.80626877,-0.27867109){0.86115027,1.045853,1.1585843,0.71703623};
SS(-0.28278924,0.041190137,-0.04219563,-0.26297351,0.20404986,-0.17122089,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465){0.063480395,0.12773981,0.20710489,0.040184006};
SS(0.6902006,0.50015172,0.27072419,0.67112401,0.32933441,0.5,0.78912399,0.50423732,0.5,0.5725222,0.50074158,0.5){0.77938072,0.79210069,1.1096027,0.8121357};
SS(-0.70236545,-0.13062851,-0.19140485,-0.61549046,-0.35581383,-0.12962263,-0.76760867,-0.33664988,-0.028298027,-0.7907607,-0.33838097,-0.28342271){0.5265969,0.50877487,0.68479998,0.80149819};
SS(-0.7055892,-0.50616462,-0.017961589,-0.63348211,-0.7706683,-0.074889286,-0.52487586,-0.5117405,-0.017639258,-0.50537844,-0.68762812,0.023695348){0.74484897,0.97907785,0.51812974,0.71483247};
SS(-0.36992714,1,-0.22970445,-0.58934795,0.84141567,-0.18062024,-0.35455825,0.80859576,-0.32177549,-0.38143574,0.84373572,-0.12387887){1.1684568,1.0736489,0.86460259,0.85864479};
SS(0.59365279,0.65503723,0.24444947,0.66554141,0.67524133,0.5,0.76099919,0.76690574,0.25750996,0.55555177,0.82262944,0.31125158){0.82252715,1.1271263,1.2143065,1.0671623};
SS(-0.49284988,-0.37485679,0.5,-0.62450053,-0.31310845,0.38575928,-0.50874333,-0.23900991,0.2620444,-0.56348952,-0.47594309,0.3052276){0.6163523,0.62379151,0.36443271,0.61776713};
SS(0.11458044,0.70010244,0.010073529,-0.035654771,0.78507762,0.045007896,0.24404834,0.79519787,0.082231238,0.094968532,0.84539386,-0.087484586){0.49378055,0.60161266,0.68472542,0.71839764};
SS(-0.60421932,0.82298164,0.34468578,-0.77777778,1,0.5,-0.66659408,1,0.32529585,-0.80481649,0.80494069,0.5){1.1449713,1.8407438,1.5364848,1.5232843};
SS(-0.26986228,0.26051837,0.22418657,-0.19461387,0.3919517,0.10437587,-0.11618574,0.50328545,0.29980467,-0.11614487,0.30919383,0.33918095){0.1749353,0.19075448,0.33969293,0.20820823};
SS(-0.48952189,0.78345034,0.019065462,-0.4433427,0.53576375,-0.12560501,-0.45563594,0.60375179,0.095527884,-0.62332411,0.59900263,-0.10904345){0.83409809,0.48429505,0.56263538,0.74800561};
SS(0.24635331,0.35131343,-0.096025322,0.36021608,0.23247759,-0.012351094,0.13402468,0.11673163,-0.1460819,0.37137652,0.1767682,-0.19801193){0.18045455,0.16110593,0.039337265,0.19205628};
SS(-0.80479144,0.80504612,-0.5,-1,1,-0.5,-1,0.77777778,-0.5,-1,0.83964442,-0.3309874){1.5255891,2.2287589,1.8398372,1.7979585};
SS(-0.11614487,0.30919383,0.33918095,-0.24000819,0.17660305,0.5,-0.26986228,0.26051837,0.22418657,-0.20045203,0.067929244,0.29301468){0.20820823,0.3210912,0.1749353,0.10955402};
SS(0.24635331,0.35131343,-0.096025322,0.08017426,0.31429474,-0.16745504,0.13261259,0.21336316,0.036566127,0.18202227,0.38279251,0.10350409){0.18045455,0.11103103,0.046199082,0.17617817};
SS(0.09693172,0.3918681,-0.3370861,0.08017426,0.31429474,-0.16745504,0.17777709,0.54047543,-0.2567554,-0.01813809,0.53618118,-0.30537166){0.26256104,0.11103103,0.36840304,0.36567785};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.5,-0.5,-0.14376826,-0.62489354,-0.5,-0.073421274,-0.375,-0.38984354){0.53347202,0.4845449,0.6489606,0.28201081};
SS(-0.6293812,0.63993291,-0.28812602,-0.66548665,0.66585508,-0.5,-0.50014045,0.79673357,-0.5,-0.4813337,0.60105459,-0.5){0.87296464,1.1221664,1.1145783,0.83133251};
SS(-0.64012388,-0.10177177,-0.37237302,-0.70236545,-0.13062851,-0.19140485,-0.7907607,-0.33838097,-0.28342271,-0.56113743,-0.28920115,-0.29204918){0.54269073,0.5265969,0.80149819,0.46850822};
SS(-1,-0.55555556,-0.5,-0.91414606,-0.68082467,-0.37109558,-0.78315651,-0.45008839,-0.5,-0.81387526,-0.53653555,-0.3209601){1.5366945,1.4249306,1.0467962,1.0406635};
SS(-0.0073778212,0.36022468,0.15230712,-0.10037172,0.18891947,0.20844359,-0.13709741,0.19518884,0.034033465,0.13261259,0.21336316,0.036566127){0.13675819,0.074828316,0.040184006,0.046199082};
SS(0.24635331,0.35131343,-0.096025322,0.34662081,0.36199915,-0.25068724,0.17777709,0.54047543,-0.2567554,0.42864323,0.48543211,-0.13804456){0.18045455,0.29696992,0.36840304,0.42022283};
SS(-0.24654336,0.57133462,-0.25396354,-0.31377045,0.30492781,-0.36427962,-0.12449617,0.36606215,-0.28273955,-0.34372617,0.39779568,-0.18541051){0.42991415,0.30770932,0.21185338,0.29650146};
SS(0.67112401,0.32933441,0.5,0.50761134,0.34933779,0.39015973,0.5725222,0.50074158,0.5,0.6902006,0.50015172,0.27072419){0.79210069,0.51484928,0.8121357,0.77938072};
SS(0.54326203,0.87223293,-0.356993,0.33333333,1,-0.5,0.45062041,0.7833899,-0.5,0.33386283,0.81592026,-0.31808704){1.1662147,1.342474,1.0506853,0.86115027};
SS(0.62515059,0.14422159,0.5,0.59416595,0.14141347,0.32656529,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.64726001,0.46498444,0.29714896,0.51484928};
SS(-0.10037172,0.18891947,0.20844359,0.13261259,0.21336316,0.036566127,0.050277172,0.20853018,0.30186362,0.13913358,0.10014326,0.18199659){0.074828316,0.046199082,0.12181545,0.045990896};
SS(0.26064395,0.61953306,0.12890567,0.39612945,0.70614162,0.21524614,0.26138985,0.51848551,0.281015,0.47723835,0.52605258,0.30619083){0.45328252,0.68453461,0.40200156,0.58228229};
SS(-0.66546973,0.66566005,0.5,-0.87046532,0.63071146,0.35630423,-0.76389013,0.77728265,0.25513738,-0.67801153,0.56076489,0.29217382){1.1224691,1.2666006,1.2358334,0.83617727};
SS(0.65062064,0.64268786,0.069510863,0.8988736,0.63809662,-0.070284173,0.77861211,0.77861193,-0.067175459,0.84582719,0.572243,0.1361951){0.82620698,1.2046527,1.1981052,1.0417018};
SS(0,0,-6.9388939e-15,-0.15128303,0.02253305,0.11422928,-0.13709741,0.19518884,0.034033465,-0.28278924,0.041190137,-0.04219563){-0.017891206,0.025420414,0.040184006,0.063480395};
SS(-0.60421932,0.82298164,0.34468578,-0.66546973,0.66566005,0.5,-0.76389013,0.77728265,0.25513738,-0.67801153,0.56076489,0.29217382){1.1449713,1.1224691,1.2358334,0.83617727};
SS(-0.49676106,-0.69523221,-0.26913048,-0.32879066,-0.67072359,-0.5,-0.50377808,-0.78884267,-0.5,-0.50036547,-0.57239096,-0.5){0.78043195,0.79007105,1.1087956,0.81333009};
SS(0.050277172,0.20853018,0.30186362,0,0,0.5,0.1615172,0,0.33845519,0.12517622,0.12515553,0.5){0.12181545,0.23153294,0.13068911,0.27156885};
SS(-0.77267892,0.13105707,-0.24874664,-0.89804207,0.11676539,-0.10792088,-0.76752638,0.004448061,-0.013214377,-0.78848723,0.26584533,-0.068869999){0.65386325,0.82300022,0.5734925,0.68151298};
SS(0.59416595,0.14141347,0.32656529,0.70845584,0,0.20819814,0.50011436,0,0.27961788,0.46476684,0.14382827,0.12247557){0.46498444,0.52761363,0.30940041,0.23450402};
SS(0.35689191,0.091376279,-0.36932783,0.34662081,0.36199915,-0.25068724,0.20129651,0.21389912,-0.31902192,0.37137652,0.1767682,-0.19801193){0.26145514,0.29696992,0.16839385,0.19205628};
SS(-0.24000819,0.17660305,0.5,-0.11614487,0.30919383,0.33918095,-0.045146113,0.19012269,0.5,-0.20045203,0.067929244,0.29301468){0.3210912,0.20820823,0.27176836,0.10955402};
SS(0.25126435,0.28098512,0.24657435,0.18202227,0.38279251,0.10350409,0.36021608,0.23247759,-0.012351094,0.13261259,0.21336316,0.036566127){0.18575023,0.17617817,0.16110593,0.046199082};
SS(-0.45843014,-0.20445062,-0.15988901,-0.49808619,0.0026201378,-0.26387206,-0.56113743,-0.28920115,-0.29204918,-0.64012388,-0.10177177,-0.37237302){0.26094507,0.29810596,0.46850822,0.54269073};
SS(0.69383766,0.49492178,-0.021800115,0.60662231,0.34516964,-0.13972301,0.52218723,0.46943947,0.022097553,0.42864323,0.48543211,-0.13804456){0.71284258,0.48782847,0.46892029,0.42022283};
SS(0.74440038,0.22095066,-0.087839409,0.77315808,0.36766952,0.075951375,0.63998586,0.17856447,0.051345521,0.86971177,0.13024645,0.1427188){0.59875958,0.71793497,0.42570365,0.77797836};
SS(-0.44431425,1,0.36245944,-0.60421932,0.82298164,0.34468578,-0.47185361,0.73769401,0.24072705,-0.30949447,0.8262402,0.33528492){1.3152029,1.1449713,0.80384956,0.87388961};
SS(-0.12449617,0.36606215,-0.28273955,-0.24163432,0.33561251,-0.055881164,-0.096302334,0.43534175,-0.056072844,-0.34372617,0.39779568,-0.18541051){0.21185338,0.16437697,0.18078295,0.29650146};
SS(-0.073421274,-0.375,-0.38984354,-0.23055166,-0.37480907,-0.5,-0.14376826,-0.62489354,-0.5,-0.26056819,-0.54975154,-0.34323516){0.28201081,0.41992239,0.6489606,0.46884495};
SS(0.37501462,0.2307626,0.5,0.50761134,0.34933779,0.39015973,0.62515059,0.14422159,0.5,0.42621669,0.19017509,0.30505062){0.42590445,0.51484928,0.64726001,0.29714896};
SS(-0.66659408,1,0.32529585,-0.77777778,1,0.5,-0.84394966,1,0.33504415,-0.80481649,0.80494069,0.5){1.5364848,1.8407438,1.8084725,1.5232843};
SS(-0.23583358,-0.36008743,0.0071767184,-0.3727858,-0.19869367,0.11195566,-0.25897908,-0.24013326,0.26450313,-0.20656092,-0.13938028,0.029547229){0.16465457,0.16948569,0.17775565,0.048278496};
SS(0.65062064,0.64268786,0.069510863,0.59365279,0.65503723,0.24444947,0.76099919,0.76690574,0.25750996,0.62860594,0.86645525,0.049037492){0.82620698,0.82252715,1.2143065,1.1303867};
SS(-0.87046532,0.63071146,0.35630423,-1,0.84108515,0.33242406,-0.80481649,0.80494069,0.5,-0.76389013,0.77728265,0.25513738){1.2666006,1.8031397,1.5232843,1.2358334};
SS(-0.11111111,1,0.5,-0.043441254,0.79173928,0.29440137,-0.014815866,1,0.31001515,0.00029730467,0.80760978,0.5){1.2487078,0.69563564,1.0772324,0.88423684};
SS(-0.84084014,-0.14895162,0.31636914,-1,-0.11111111,0.5,-0.73174745,-0.21491043,0.5,-0.80727304,0.00024662976,0.5){0.81273381,1.2390062,0.81377033,0.88515177};
SS(0.25,0,-0.5,0.35689191,0.091376279,-0.36932783,0.5,0,-0.5,0.37532516,0.23078833,-0.5){0.28810477,0.26145514,0.48471812,0.42551454};
SS(-0.79227163,-0.79754897,0.0021844777,-0.77973152,-1,-0.0001062007,-0.63348211,-0.7706683,-0.074889286,-0.61978497,-0.82706917,0.12738472){1.2530106,1.588155,0.97907785,1.0681409};
SS(0.51910919,0.22553632,-0.31417891,0.5,0,-0.5,0.6251418,0.1440922,-0.5,0.50007058,0,-0.27987971){0.40112301,0.48471812,0.63751638,0.31006895};
SS(0.66554141,0.67524133,0.5,0.6902006,0.50015172,0.27072419,0.78912399,0.50423732,0.5,0.5725222,0.50074158,0.5){1.1271263,0.77938072,1.1096027,0.8121357};
SS(-1,-0.70710233,0.21356199,-0.83996275,-0.66999882,0.11765553,-1,-0.47520831,0.27427507,-0.79575191,-0.55547687,0.30538166){1.5280688,1.1553131,1.2822693,1.0192798};
SS(-0.5555987,0.045150158,0.095162244,-0.67616985,-0.069078192,0.18801024,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.29993682,0.47948004,0.34448415,0.63492881};
SS(-0.78848723,0.26584533,-0.068869999,-0.58258855,0.14037208,-0.067351147,-0.7489605,0.18190923,0.13647301,-0.76752638,0.004448061,-0.013214377){0.68151298,0.34532741,0.59564173,0.5734925};
SS(0.6657623,0.67544754,-0.5,0.51674933,0.64481281,-0.39755292,0.57309542,0.50075776,-0.5,0.67125235,0.44297685,-0.31879306){1.1304562,0.82858869,0.81773274,0.72773009};
SS(0.87867265,0.36391919,-0.37720578,1,0.5,-0.5,0.78906409,0.5041626,-0.5,0.85153485,0.65148612,-0.35468846){1.03034,1.4840091,1.1105402,1.2568282};
SS(-0.0073778212,0.36022468,0.15230712,-0.10037172,0.18891947,0.20844359,0.13261259,0.21336316,0.036566127,0.050277172,0.20853018,0.30186362){0.13675819,0.074828316,0.046199082,0.12181545};
SS(-0.39806707,0.15776443,0.15870839,-0.34310942,-0.010167032,0.1509038,-0.28278924,0.041190137,-0.04219563,-0.15128303,0.02253305,0.11422928){0.19317292,0.12661586,0.063480395,0.025420414};
SS(0.34662081,0.36199915,-0.25068724,0.24635331,0.35131343,-0.096025322,0.36021608,0.23247759,-0.012351094,0.42864323,0.48543211,-0.13804456){0.29696992,0.18045455,0.16110593,0.42022283};
SS(0.24635331,0.35131343,-0.096025322,0.30434906,0.49798107,-4.0114635e-05,0.36021608,0.23247759,-0.012351094,0.42864323,0.48543211,-0.13804456){0.18045455,0.32377482,0.16110593,0.42022283};
SS(-0.55555556,1,0.5,-0.60421932,0.82298164,0.34468578,-0.77777778,1,0.5,-0.66659408,1,0.32529585){1.5418081,1.1449713,1.8407438,1.5364848};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.49997234,-0.27965571,-0.18618058,-0.5161726,-0.15035515,-0.073421274,-0.375,-0.38984354){0.14783141,0.30906942,0.30914003,0.28201081};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.22019801,5.0496855e-05,-0.23583358,-0.36008743,0.0071767184,-0.10133362,-0.40777162,0.1162396){0.14783141,0.029059683,0.16465457,0.17697987};
SS(0.11458044,0.70010244,0.010073529,0.30434906,0.49798107,-4.0114635e-05,0.25248643,0.73785598,-0.13082591,0.26064395,0.61953306,0.12890567){0.49378055,0.32377482,0.60350215,0.45328252};
SS(0.46476684,0.14382827,0.12247557,0.36021608,0.23247759,-0.012351094,0.63998586,0.17856447,0.051345521,0.57129187,0.13526053,-0.13726946){0.23450402,0.16110593,0.42570365,0.35115136};
SS(1,0.5,-0.5,0.87867265,0.36391919,-0.37720578,1,0.50010355,-0.27968748,0.85153485,0.65148612,-0.35468846){1.4840091,1.03034,1.3071084,1.2568282};
SS(0.49866453,0.63973666,-0.21510859,0.54326203,0.87223293,-0.356993,0.33386283,0.81592026,-0.31808704,0.54700908,0.85955032,-0.16345766){0.68344633,1.1662147,0.86115027,1.0528061};
SS(0.26138985,0.51848551,0.281015,0.26064395,0.61953306,0.12890567,0.47723835,0.52605258,0.30619083,0.36016656,0.41044152,0.1594367){0.40200156,0.45328252,0.58228229,0.3073722};
SS(-0.50537844,-0.68762812,0.023695348,-0.35582611,-0.64426575,-0.070000747,-0.42889738,-0.75253072,0.17523232,-0.36608751,-0.8951802,0.074405883){0.71483247,0.52757348,0.75958282,0.92652515};
SS(-0.11111111,1,0.5,-0.043441254,0.79173928,0.29440137,0.00029730467,0.80760978,0.5,-0.1827732,0.83017807,0.5){1.2487078,0.69563564,0.88423684,0.95598938};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.55555556,0.5,-0.78327322,-0.45013966,0.5,-0.79575191,-0.55547687,0.30538166){1.4119512,1.5359657,1.0435491,1.0192798};
SS(-0.056808231,0.14323286,-0.13367928,0,0,-6.9388939e-15,-0.13709741,0.19518884,0.034033465,-0.28278924,0.041190137,-0.04219563){0.022140076,-0.017891206,0.040184006,0.063480395};
SS(-0.39806707,0.15776443,0.15870839,-0.26986228,0.26051837,0.22418657,-0.20045203,0.067929244,0.29301468,-0.15128303,0.02253305,0.11422928){0.19317292,0.1749353,0.10955402,0.025420414};
SS(0,-0.29157012,0.20836692,-0.16643696,-0.21791406,0.42402077,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.11172813,0.23818505,0.17775565,0.29143101};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.34372617,0.39779568,-0.18541051,-0.39654734,0.26661646,0.019312696,-0.54631436,0.45612147,-0.00074796238){0.32892635,0.29650146,0.20710489,0.48593017};
SS(-0.36608751,-0.8951802,0.074405883,-0.35582611,-0.64426575,-0.070000747,-0.42889738,-0.75253072,0.17523232,-0.22302806,-0.77703925,0.068353305){0.92652515,0.52757348,0.75958282,0.64063544};
SS(-0.0089783977,0.64320989,-0.13441642,-0.01813809,0.53618118,-0.30537166,-0.24654336,0.57133462,-0.25396354,-0.14847812,0.78021305,-0.27623142){0.41358858,0.36567785,0.42991415,0.68882385};
SS(0.60662231,0.34516964,-0.13972301,0.67125235,0.44297685,-0.31879306,0.77491511,0.22516452,-0.26425516,0.51910919,0.22553632,-0.31417891){0.48782847,0.72773009,0.70313431,0.40112301};
SS(-0.88905946,-0.098697315,-0.13184676,-0.70236545,-0.13062851,-0.19140485,-0.7907607,-0.33838097,-0.28342271,-0.85707128,-0.1416783,-0.34083416){0.8023886,0.5265969,0.80149819,0.85441326};
SS(-0.31377045,0.30492781,-0.36427962,-0.41651431,0.41690828,-0.5,-0.40408872,0.18166381,-0.5,-0.49391083,0.27907498,-0.27264436){0.30770932,0.57523437,0.42526168,0.37398026};
SS(-0.80632325,-0.81147186,-0.5,-0.91414606,-0.68082467,-0.37109558,-1,-0.83959635,-0.33115777,-0.76546557,-0.72634686,-0.27513208){1.5409894,1.4249306,1.7998257,1.1696133};
SS(-0.7489605,0.18190923,0.13647301,-0.5555987,0.045150158,0.095162244,-0.52427834,0.10778268,0.27208728,-0.72768327,0.10310141,0.33233484){0.59564173,0.29993682,0.34448415,0.63492881};
SS(0.87272604,0.35900693,0.37172569,1,0.16158711,0.33859063,0.81143387,0.18901581,0.5,0.73568363,0.23203612,0.2735765){1.0107603,1.1259698,0.9265446,0.6509231};
SS(-0.49808619,0.0026201378,-0.26387206,-0.45843014,-0.20445062,-0.15988901,-0.56113743,-0.28920115,-0.29204918,-0.38492375,-0.20017574,-0.33650716){0.29810596,0.26094507,0.46850822,0.28705324};
SS(-1,-0.25140376,-0.1934451,-0.88905946,-0.098697315,-0.13184676,-0.7907607,-0.33838097,-0.28342271,-0.85707128,-0.1416783,-0.34083416){1.0790534,0.8023886,0.80149819,0.85441326};
SS(-0.70236545,-0.13062851,-0.19140485,-0.45843014,-0.20445062,-0.15988901,-0.56113743,-0.28920115,-0.29204918,-0.64012388,-0.10177177,-0.37237302){0.5265969,0.26094507,0.46850822,0.54269073};
SS(0.48047723,0.47791267,-0.33071402,0.60662231,0.34516964,-0.13972301,0.34662081,0.36199915,-0.25068724,0.51910919,0.22553632,-0.31417891){0.55795418,0.48782847,0.29696992,0.40112301};
SS(-0.82285362,-0.63420593,-0.0683896,-0.65956212,-0.52273243,-0.19262862,-0.63348211,-0.7706683,-0.074889286,-0.76546557,-0.72634686,-0.27513208){1.0691297,0.7287475,0.97907785,1.1696133};
SS(-0.4182056,0.11248126,-0.14182463,-0.26297351,0.20404986,-0.17122089,-0.49391083,0.27907498,-0.27264436,-0.34372617,0.39779568,-0.18541051){0.19428145,0.12773981,0.37398026,0.29650146};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.70523324,-0.21165758,-1,-0.47540235,-0.27521785,-0.81387526,-0.53653555,-0.3209601){0.95161001,1.5222776,1.2841965,1.0406635};
SS(-0.30131805,-0.11512588,-0.5,-0.1971424,-0.26981885,-0.30750196,-0.23055166,-0.37480907,-0.5,-0.12484866,-0.12486094,-0.5){0.3368451,0.19280289,0.41992239,0.26766045};
SS(-0.67616985,-0.069078192,0.18801024,-0.5555987,0.045150158,0.095162244,-0.7489605,0.18190923,0.13647301,-0.72768327,0.10310141,0.33233484){0.47948004,0.29993682,0.59564173,0.63492881};
SS(-0.10037172,0.18891947,0.20844359,0,0,0.25,0.050277172,0.20853018,0.30186362,-0.20045203,0.067929244,0.29301468){0.074828316,0.045060365,0.12181545,0.10955402};
SS(0.8988736,0.63809662,-0.070284173,1,0.70844226,-0.20827687,1,0.50010355,-0.27968748,0.75922048,0.56990614,-0.17060419){1.2046527,1.5310675,1.3071084,0.91133836};
SS(0.52843461,0.32737897,0.19102935,0.63998586,0.17856447,0.051345521,0.77315808,0.36766952,0.075951375,0.73568363,0.23203612,0.2735765){0.40790135,0.42570365,0.71793497,0.6509231};
SS(-0.80635543,-0.81164184,0.5,-1,-1,0.5,-1,-0.84092895,0.33252059,-0.8385203,-1,0.33846229){1.5410993,2.2322143,1.8030746,1.8024192};
SS(-0.5,-1,0.5,-0.63815223,-0.88141187,0.37488811,-0.50400314,-0.78879927,0.5,-0.349759,-0.84853211,0.35590634){1.4840089,1.3088768,1.1086821,0.94981364};
SS(-0.77091496,-0.77159441,0.2629049,-1,-0.84092895,0.33252059,-0.8385203,-1,0.33846229,-0.80635543,-0.81164184,0.5){1.2433034,1.8030746,1.8024192,1.5410993};
SS(-0.47972312,1,0.18932995,-0.30949447,0.8262402,0.33528492,-0.44431425,1,0.36245944,-0.47185361,0.73769401,0.24072705){1.2473472,0.87388961,1.3152029,0.80384956};
SS(0.36016656,0.41044152,0.1594367,0.52843461,0.32737897,0.19102935,0.42621669,0.19017509,0.30505062,0.46476684,0.14382827,0.12247557){0.3073722,0.40790135,0.29714896,0.23450402};
SS(-0.11754465,-0.65214472,-0.32749638,-0.14376826,-0.62489354,-0.5,-0.26056819,-0.54975154,-0.34323516,-0.073421274,-0.375,-0.38984354){0.53347202,0.6489606,0.46884495,0.28201081};
SS(0.33333333,1,-0.5,0.54326203,0.87223293,-0.356993,0.43683247,1,-0.26068681,0.33386283,0.81592026,-0.31808704){1.342474,1.1662147,1.2435523,0.86115027};
SS(-0.65631386,-0.59724887,0.13822882,-0.7055892,-0.50616462,-0.017961589,-0.63348211,-0.7706683,-0.074889286,-0.82285362,-0.63420593,-0.0683896){0.7890621,0.74484897,0.97907785,1.0691297};
SS(-0.63815223,-0.88141187,0.37488811,-0.5,-1,0.5,-0.4999534,-1,0.27968311,-0.349759,-0.84853211,0.35590634){1.3088768,1.4840089,1.3075402,0.94981364};
SS(-0.30131805,-0.11512588,-0.5,-0.38492375,-0.20017574,-0.33650716,-0.23055166,-0.37480907,-0.5,-0.1971424,-0.26981885,-0.30750196){0.3368451,0.28705324,0.41992239,0.19280289};
SS(0.10162062,0.65400865,-0.37913628,-0.0089783977,0.64320989,-0.13441642,0.081865095,0.80626877,-0.27867109,-0.01813809,0.53618118,-0.30537166){0.5665506,0.41358858,0.71703623,0.36567785};
SS(-0.64012388,-0.10177177,-0.37237302,-0.49808619,0.0026201378,-0.26387206,-0.56113743,-0.28920115,-0.29204918,-0.38492375,-0.20017574,-0.33650716){0.54269073,0.29810596,0.46850822,0.28705324};
SS(-0.10037172,0.18891947,0.20844359,-0.045146113,0.19012269,0.5,0.050277172,0.20853018,0.30186362,-0.11614487,0.30919383,0.33918095){0.074828316,0.27176836,0.12181545,0.20820823};
SS(0.77491511,0.22516452,-0.26425516,0.671223,0.32907594,-0.5,0.81149777,0.18885984,-0.5,0.6251418,0.1440922,-0.5){0.70313431,0.79435762,0.92750237,0.63751638};
SS(-0.4581749,-0.5263483,-0.32801665,-0.65956212,-0.52273243,-0.19262862,-0.56113743,-0.28920115,-0.29204918,-0.62341011,-0.46880832,-0.38153973){0.57811658,0.7287475,0.46850822,0.73807879};
SS(0.62860594,0.86645525,0.049037492,0.68985253,1,-0.19792707,0.78186447,1,3.3673518e-05,0.77861211,0.77861193,-0.067175459){1.1303867,1.495304,1.5923176,1.1981052};
SS(0.050277172,0.20853018,0.30186362,0,0,0.5,0,0,0.25,0.1615172,0,0.33845519){0.12181545,0.23153294,0.045060365,0.13068911};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.33333333,0.5,-1,-0.55555556,0.5,-0.78327322,-0.45013966,0.5){1.0133061,1.3443603,1.5359657,1.0435491};
SS(-0.49292178,-0.37477565,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.36174,-0.40052234,-0.23665811,-0.56113743,-0.28920115,-0.29204918){0.6115465,0.57811658,0.32480953,0.46850822};
SS(1,0.70844226,-0.20827687,0.85153485,0.65148612,-0.35468846,1,0.50010355,-0.27968748,0.75922048,0.56990614,-0.17060419){1.5310675,1.2568282,1.3071084,0.91133836};
SS(0.25248643,0.73785598,-0.13082591,0.10162062,0.65400865,-0.37913628,0.17777709,0.54047543,-0.2567554,0.081865095,0.80626877,-0.27867109){0.60350215,0.5665506,0.36840304,0.71703623};
SS(0.5725222,0.50074158,0.5,0.59365279,0.65503723,0.24444947,0.6902006,0.50015172,0.27072419,0.47723835,0.52605258,0.30619083){0.8121357,0.82252715,0.77938072,0.58228229};
SS(-0.88905946,-0.098697315,-0.13184676,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027,-0.82279039,-0.18997945,0.10657137){0.8023886,1.0172898,0.68479998,0.70945047};
SS(-1,-1,-0.5,-0.80632325,-0.81147186,-0.5,-1,-0.83959635,-0.33115777,-0.83846289,-1,-0.33858677){2.2321573,1.5409894,1.7998257,1.8019179};
SS(-0.79227163,-0.79754897,0.0021844777,-0.65631386,-0.59724887,0.13822882,-0.63348211,-0.7706683,-0.074889286,-0.82285362,-0.63420593,-0.0683896){1.2530106,0.7890621,0.97907785,1.0691297};
SS(-0.29168215,-1,-0.20844865,-0.12233239,-0.87748906,-0.13583418,-0.2399131,-0.76005145,-0.25989531,-0.42066299,-0.84356131,-0.12906413){1.1132023,0.78823805,0.6848256,0.88525127};
SS(1,0.16158711,0.33859063,0.86971177,0.13024645,0.1427188,0.83866368,0,0.33843958,0.73568363,0.23203612,0.2735765){1.1259698,0.77797836,0.80106313,0.6509231};
SS(-0.17603462,0.24070348,-0.5,-0.31377045,0.30492781,-0.36427962,-0.29413589,0.046284299,-0.31274881,-0.1182182,0.15955837,-0.3159857){0.32537509,0.30770932,0.1681493,0.11990198};
SS(0.17426348,1,-0.18078905,0.094968532,0.84539386,-0.087484586,-0.088882135,1,-0.23281641,0.081865095,0.80626877,-0.27867109){1.045853,0.71839764,1.0431215,0.71703623};
SS(0.13402468,0.11673163,-0.1460819,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094,0.13261259,0.21336316,0.036566127){0.039337265,0.027339551,0.16110593,0.046199082};
SS(-0.60421932,0.82298164,0.34468578,-0.44431425,1,0.36245944,-0.50037,0.79662088,0.5,-0.30949447,0.8262402,0.33528492){1.1449713,1.3152029,1.1183194,0.87388961};
SS(-0.54640726,0.34339216,0.19847863,-0.63246299,0.29145388,0.035195127,-0.79172217,0.43302343,0.13373134,-0.54631436,0.45612147,-0.00074796238){0.43575493,0.47226275,0.80968993,0.48593017};
SS(0.68966181,1,0.19790566,0.62860594,0.86645525,0.049037492,0.76099919,0.76690574,0.25750996,0.55555177,0.82262944,0.31125158){1.492557,1.1303867,1.2143065,1.0671623};
SS(1,1,-0.5,0.77777778,1,-0.5,0.81205362,0.80656044,-0.5,0.82865019,1,-0.3214153){2.2331531,1.8341362,1.5391707,1.7714679};
SS(-0.36992714,1,-0.22970445,-0.58934795,0.84141567,-0.18062024,-0.56041637,1,-0.29784853,-0.35455825,0.80859576,-0.32177549){1.1684568,1.0736489,1.3856141,0.86460259};
SS(-0.8068077,0.56885008,-0.063754108,-1,0.70529035,-0.21162945,-0.74249217,0.75399014,-0.15399718,-0.80558396,0.5878127,-0.29244037){0.96112076,1.520296,1.1267767,1.0616703};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.49991607,0.0031934521,-1,0.29928494,0.0012550607,-0.79172217,0.43302343,0.13373134){0.974079,1.2302733,1.0718665,0.80968993};
SS(-0.67495489,-0.6652659,-0.5,-0.49676106,-0.69523221,-0.26913048,-0.50377808,-0.78884267,-0.5,-0.50036547,-0.57239096,-0.5){1.1276355,0.78043195,1.1087956,0.81333009};
SS(-0.66546973,0.66566005,0.5,-0.60421932,0.82298164,0.34468578,-0.50037,0.79662088,0.5,-0.48141868,0.60085372,0.5){1.1224691,1.1449713,1.1183194,0.82306978};
SS(-0.30949447,0.8262402,0.33528492,-0.33333333,1,0.5,-0.3132159,0.69976014,0.5,-0.1827732,0.83017807,0.5){0.87388961,1.3433112,0.82050522,0.95598938};
SS(-0.58934795,0.84141567,-0.18062024,-0.61115597,1,-0.10200355,-0.76988954,1,-0.26944904,-0.81095336,1,-0.07156149){1.0736489,1.3611038,1.6463902,1.6471359};
SS(-1,0.77631186,0.00053339564,-0.8480722,0.62150313,0.12164012,-0.8068077,0.56885008,-0.063754108,-0.79370724,0.81084643,0.045877226){1.5817554,1.1084494,0.96112076,1.270911};
SS(-0.89962374,0.8609561,-0.16698164,-1,0.77631186,0.00053339564,-0.74249217,0.75399014,-0.15399718,-0.79370724,0.81084643,0.045877226){1.5692753,1.5817554,1.1267767,1.270911};
SS(1,1,0.5,0.77777778,1,0.5,0.82853688,1,0.32125076,0.81191124,0.80644944,0.5){2.2317116,1.8450917,1.7703132,1.5425973};
SS(0.21512427,0.73211919,0.5,0.10211023,0.6404511,0.38011645,0.00029730467,0.80760978,0.5,0.22886345,0.79287946,0.30210005){0.81521474,0.55160362,0.88423684,0.75332396};
SS(0.26064395,0.61953306,0.12890567,0.39612945,0.70614162,0.21524614,0.47723835,0.52605258,0.30619083,0.36016656,0.41044152,0.1594367){0.45328252,0.68453461,0.58228229,0.3073722};
SS(0.34662081,0.36199915,-0.25068724,0.35689191,0.091376279,-0.36932783,0.51910919,0.22553632,-0.31417891,0.37137652,0.1767682,-0.19801193){0.29696992,0.26145514,0.40112301,0.19205628};
SS(-0.19247216,-0.56000521,0.088357129,0,-0.7082575,0.2084616,-0.22656331,-0.68065623,0.28194433,-0.22302806,-0.77703925,0.068353305){0.34206231,0.52387062,0.57683818,0.64063544};
SS(-0.45843014,-0.20445062,-0.15988901,-0.70236545,-0.13062851,-0.19140485,-0.49808619,0.0026201378,-0.26387206,-0.64012388,-0.10177177,-0.37237302){0.26094507,0.5265969,0.29810596,0.54269073};
SS(-0.54640726,0.34339216,0.19847863,-0.61674646,0.25215289,0.3447871,-0.7489605,0.18190923,0.13647301,-0.52427834,0.10778268,0.27208728){0.43575493,0.54607287,0.59564173,0.34448415};
SS(-0.83851866,0.33014205,0.32623765,-1,0.24865949,0.19540364,-1,0.4752276,0.27420758,-0.79172217,0.43302343,0.13373134){0.89937894,1.0814407,1.2803563,0.80968993};
SS(-0.20984637,0.69532212,0.20809493,-0.098708274,0.55956225,0.10505678,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){0.55022745,0.31633913,0.58653028,0.60161266};
SS(0,0,-6.9388939e-15,-0.10037172,0.18891947,0.20844359,-0.13709741,0.19518884,0.034033465,-0.15128303,0.02253305,0.11422928){-0.017891206,0.074828316,0.040184006,0.025420414};
SS(0.50010751,0,-0.00013054911,0.37137652,0.1767682,-0.19801193,0.36021608,0.23247759,-0.012351094,0.57129187,0.13526053,-0.13726946){0.22823279,0.19205628,0.16110593,0.35115136};
SS(-0.79644003,0.50064951,-0.5,-0.63048479,0.37587985,-0.34368186,-0.80558396,0.5878127,-0.29244037,-0.83127473,0.33505962,-0.32026923){1.115532,0.64388066,1.0616703,0.89071695};
SS(-0.50377808,-0.78884267,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.49676106,-0.69523221,-0.26913048,-0.6448883,-0.87343314,-0.36731947){1.1087956,1.0307746,0.78043195,1.296688};
SS(1,0.2203628,5.6826691e-05,0.74440038,0.22095066,-0.087839409,0.77315808,0.36766952,0.075951375,0.82562789,0.37565656,-0.12707714){1.0268649,0.59875958,0.71793497,0.82387041};
SS(-0.17097214,0.64900986,-0.39927747,-0.31289368,0.69974287,-0.5,-0.20381263,0.45499536,-0.5,-0.39032311,0.63241857,-0.34621958){0.59741335,0.82323564,0.478983,0.65630059};
SS(-0.20984637,0.69532212,0.20809493,-0.16015893,0.67694077,0.39025863,-0.11618574,0.50328545,0.29980467,-0.043441254,0.79173928,0.29440137){0.55022745,0.6265216,0.33969293,0.69563564};
SS(0.29175541,0,0.20824909,0.26083053,0.15082484,0.37728795,0.50011436,0,0.27961788,0.42621669,0.19017509,0.30505062){0.1093371,0.21918499,0.30940041,0.29714896};
SS(-0.80728146,0.00010990719,-0.5,-0.62938155,0.17932964,-0.37445272,-0.82994199,0.18319278,-0.5,-0.77267892,0.13105707,-0.24874664){0.88195685,0.55109073,0.95993957,0.65386325};
SS(0.57129187,0.13526053,-0.13726946,0.50010751,0,-0.00013054911,0.77985819,0,-0.00014691753,0.63998586,0.17856447,0.051345521){0.35115136,0.22823279,0.58919206,0.42570365};
SS(-0.61549046,-0.35581383,-0.12962263,-0.50159539,-0.29258506,7.2987381e-06,-0.36174,-0.40052234,-0.23665811,-0.45843014,-0.20445062,-0.15988901){0.50877487,0.32068114,0.32480953,0.26094507};
SS(-0.24163432,0.33561251,-0.055881164,-0.32064519,0.49448821,1.4739833e-06,-0.096302334,0.43534175,-0.056072844,-0.34372617,0.39779568,-0.18541051){0.16437697,0.32892635,0.18078295,0.29650146};
SS(-0.26056819,-0.54975154,-0.34323516,-0.23055166,-0.37480907,-0.5,-0.36174,-0.40052234,-0.23665811,-0.1971424,-0.26981885,-0.30750196){0.46884495,0.41992239,0.32480953,0.19280289};
SS(0.25126435,0.28098512,0.24657435,0.36016656,0.41044152,0.1594367,0.42621669,0.19017509,0.30505062,0.46476684,0.14382827,0.12247557){0.18575023,0.3073722,0.29714896,0.23450402};
SS(0.27170325,0.36204749,-0.4201745,0.37532516,0.23078833,-0.5,0.34662081,0.36199915,-0.25068724,0.20129651,0.21389912,-0.31902192){0.36885377,0.42551454,0.29696992,0.16839385};
SS(0.18202227,0.38279251,0.10350409,-0.0073778212,0.36022468,0.15230712,0.13261259,0.21336316,0.036566127,0.050277172,0.20853018,0.30186362){0.17617817,0.13675819,0.046199082,0.12181545};
SS(-0.60421932,0.82298164,0.34468578,-0.47185361,0.73769401,0.24072705,-0.76389013,0.77728265,0.25513738,-0.61311838,0.85766427,0.15491279){1.1449713,0.80384956,1.2358334,1.1216468};
SS(0.25126435,0.28098512,0.24657435,0.18202227,0.38279251,0.10350409,0.13261259,0.21336316,0.036566127,0.050277172,0.20853018,0.30186362){0.18575023,0.17617817,0.046199082,0.12181545};
SS(-0.16707278,-0.087678023,-0.31121894,0,0,-0.5,-0.19007896,0.04567822,-0.5,-0.1182182,0.15955837,-0.3159857){0.11599041,0.23465449,0.27736807,0.11990198};
SS(-0.81387526,-0.53653555,-0.3209601,-1,-0.70523324,-0.21165758,-0.76546557,-0.72634686,-0.27513208,-0.82285362,-0.63420593,-0.0683896){1.0406635,1.5222776,1.1696133,1.0691297};
SS(-0.29157863,-1,0.20827581,-0.36608751,-0.8951802,0.074405883,-0.4999534,-1,0.27968311,-0.349759,-0.84853211,0.35590634){1.1139248,0.92652515,1.3075402,0.94981364};
SS(0.37549445,0.49317282,-0.5,0.51674933,0.64481281,-0.39755292,0.45062041,0.7833899,-0.5,0.34412919,0.6158316,-0.3427703){0.61648995,0.82858869,1.0506853,0.59958408};
SS(0.68985253,1,-0.19792707,0.54700908,0.85955032,-0.16345766,0.77861211,0.77861193,-0.067175459,0.62860594,0.86645525,0.049037492){1.495304,1.0528061,1.1981052,1.1303867};
SS(0.25126435,0.28098512,0.24657435,0.27123349,0.36190713,0.41476339,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.18575023,0.36300231,0.29714896,0.51484928};
SS(0.87272604,0.35900693,0.37172569,0.67112401,0.32933441,0.5,0.78912399,0.50423732,0.5,0.6902006,0.50015172,0.27072419){1.0107603,0.79210069,1.1096027,0.77938072};
SS(-0.37661764,-0.26006406,0.40868766,-0.49284988,-0.37485679,0.5,-0.50874333,-0.23900991,0.2620444,-0.56348952,-0.47594309,0.3052276){0.36234206,0.6163523,0.36443271,0.61776713};
SS(-0.83248216,0.76782327,-0.31292259,-0.80479144,0.80504612,-0.5,-0.76988954,1,-0.26944904,-0.65756371,0.81308934,-0.3429452){1.366757,1.5255891,1.6463902,1.1958888};
SS(-0.73479965,-0.34302295,0.24038072,-0.62450053,-0.31310845,0.38575928,-0.50874333,-0.23900991,0.2620444,-0.64009684,-0.10188458,0.37412975){0.69668046,0.62379151,0.36443271,0.54631619};
SS(-0.39806707,0.15776443,0.15870839,-0.54640726,0.34339216,0.19847863,-0.26986228,0.26051837,0.22418657,-0.39654734,0.26661646,0.019312696){0.19317292,0.43575493,0.1749353,0.20710489};
SS(-0.70823063,-1,-0.20843533,-0.6448883,-0.87343314,-0.36731947,-0.83846289,-1,-0.33858677,-0.76546557,-0.72634686,-0.27513208){1.5240742,1.296688,1.8019179,1.1696133};
SS(-0.36340067,-0.87821042,-0.37678589,-0.32879066,-0.67072359,-0.5,-0.50377808,-0.78884267,-0.5,-0.49676106,-0.69523221,-0.26913048){1.0307746,0.79007105,1.1087956,0.78043195};
SS(-0.70236545,-0.13062851,-0.19140485,-0.45843014,-0.20445062,-0.15988901,-0.49808619,0.0026201378,-0.26387206,-0.4720473,-0.063494476,-0.036829327){0.5265969,0.26094507,0.29810596,0.21285629};
SS(0,0,-0.5,-0.16707278,-0.087678023,-0.31121894,0,0,-0.25,-0.1182182,0.15955837,-0.3159857){0.23465449,0.11599041,0.044304329,0.11990198};
SS(-0.65756371,0.81308934,-0.3429452,-0.77777778,1,-0.5,-0.80479144,0.80504612,-0.5,-0.76988954,1,-0.26944904){1.1958888,1.8319852,1.5255891,1.6463902};
SS(-0.4999534,-1,0.27968311,-0.36608751,-0.8951802,0.074405883,-0.42889738,-0.75253072,0.17523232,-0.349759,-0.84853211,0.35590634){1.3075402,0.92652515,0.75958282,0.94981364};
SS(-1,0.33333333,0.5,-0.83851866,0.33014205,0.32623765,-0.69937107,0.31347586,0.5,-0.83006559,0.18329805,0.5){1.3403692,0.89937894,0.8165723,0.96159482};
SS(0.6657623,0.67544754,-0.5,0.85153485,0.65148612,-0.35468846,0.78906409,0.5041626,-0.5,0.81205362,0.80656044,-0.5){1.1304562,1.2568282,1.1105402,1.5391707};
SS(0.70845584,0,0.20819814,0.59416595,0.14141347,0.32656529,0.63998586,0.17856447,0.051345521,0.46476684,0.14382827,0.12247557){0.52761363,0.46498444,0.42570365,0.23450402};
SS(-1,0.49991607,0.0031934521,-0.8480722,0.62150313,0.12164012,-1,0.4752276,0.27420758,-0.79172217,0.43302343,0.13373134){1.2302733,1.1084494,1.2803563,0.80968993};
SS(-0.80481649,0.80494069,0.5,-1,1,0.5,-0.84394966,1,0.33504415,-1,0.84108515,0.33242406){1.5232843,2.2338249,1.8084725,1.8031397};
SS(0.098704003,0.67249079,0.1943501,0.11458044,0.70010244,0.010073529,-0.035654771,0.78507762,0.045007896,0.24404834,0.79519787,0.082231238){0.47957633,0.49378055,0.60161266,0.68472542};
SS(0,0,-0.25,0.13402468,0.11673163,-0.1460819,0.16149165,0,-0.33864688,0.20129651,0.21389912,-0.31902192){0.044304329,0.039337265,0.12746835,0.16839385};
SS(-0.17097214,0.64900986,-0.39927747,-0.20381263,0.45499536,-0.5,-0.24654336,0.57133462,-0.25396354,-0.39032311,0.63241857,-0.34621958){0.59741335,0.478983,0.42991415,0.65630059};
SS(-0.63246299,0.29145388,0.035195127,-0.58258855,0.14037208,-0.067351147,-0.39654734,0.26661646,0.019312696,-0.5555987,0.045150158,0.095162244){0.47226275,0.34532741,0.20710489,0.29993682};
SS(-0.86742481,-0.86548068,-0.14483364,-0.70823063,-1,-0.20843533,-0.83846289,-1,-0.33858677,-0.76546557,-0.72634686,-0.27513208){1.5085891,1.5240742,1.8019179,1.1696133};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.22019801,5.0496855e-05,-0.23583358,-0.36008743,0.0071767184,-0.20656092,-0.13938028,0.029547229){0.17697987,0.029059683,0.16465457,0.048278496};
SS(0.17426348,1,-0.18078905,0.34720309,0.90097601,-0.12745168,0.25248643,0.73785598,-0.13082591,0.33386283,0.81592026,-0.31808704){1.045853,0.93504792,0.60350215,0.86115027};
SS(0.87881231,0.64063264,0.37220388,0.66554141,0.67524133,0.5,0.78912399,0.50423732,0.5,0.81191124,0.80644944,0.5){1.3069719,1.1271263,1.1096027,1.5425973};
SS(-0.4720473,-0.063494476,-0.036829327,-0.5555987,0.045150158,0.095162244,-0.76752638,0.004448061,-0.013214377,-0.65367362,-0.16081953,0.0014934597){0.21285629,0.29993682,0.5734925,0.4344691};
SS(-0.1853821,-0.42358473,0.30866054,0,-0.5,0.5,-0.23048975,-0.37484721,0.5,-0.14394692,-0.62481063,0.5){0.29143101,0.48207879,0.42714666,0.63866347};
SS(0.87881231,0.64063264,0.37220388,0.78912399,0.50423732,0.5,0.6902006,0.50015172,0.27072419,0.87272604,0.35900693,0.37172569){1.3069719,1.1096027,0.77938072,1.0107603};
SS(-0.6448883,-0.87343314,-0.36731947,-0.67495489,-0.6652659,-0.5,-0.50377808,-0.78884267,-0.5,-0.80632325,-0.81147186,-0.5){1.296688,1.1276355,1.1087956,1.5409894};
SS(-0.41648151,0.41684878,0.5,-0.52470763,0.46530444,0.33754711,-0.41843781,0.30742585,0.3397996,-0.61674646,0.25215289,0.3447871){0.58097186,0.59371518,0.37011438,0.54607287};
SS(-0.6448883,-0.87343314,-0.36731947,-0.80632325,-0.81147186,-0.5,-0.83846289,-1,-0.33858677,-0.76546557,-0.72634686,-0.27513208){1.296688,1.5409894,1.8019179,1.1696133};
SS(0.36841015,0.87909734,0.37310922,0.33333333,1,0.5,0.21512427,0.73211919,0.5,0.45042372,0.78359022,0.5){1.0362544,1.3466764,0.81521474,1.0496179};
SS(-0.3533559,-0.49437708,0.037576204,-0.36174,-0.40052234,-0.23665811,-0.23583358,-0.36008743,0.0071767184,-0.18618058,-0.5161726,-0.15035515){0.35575629,0.32480953,0.16465457,0.30914003};
SS(0.59416595,0.14141347,0.32656529,0.5,0,0.5,0.62515059,0.14422159,0.5,0.42621669,0.19017509,0.30505062){0.46498444,0.47735984,0.64726001,0.29714896};
SS(-0.70236545,-0.13062851,-0.19140485,-0.88905946,-0.098697315,-0.13184676,-0.76760867,-0.33664988,-0.028298027,-0.65367362,-0.16081953,0.0014934597){0.5265969,0.8023886,0.68479998,0.4344691};
SS(-0.54640726,0.34339216,0.19847863,-0.63246299,0.29145388,0.035195127,-0.7489605,0.18190923,0.13647301,-0.79172217,0.43302343,0.13373134){0.43575493,0.47226275,0.59564173,0.80968993};
SS(0.35689191,0.091376279,-0.36932783,0.5,0,-0.5,0.6251418,0.1440922,-0.5,0.51910919,0.22553632,-0.31417891){0.26145514,0.48471812,0.63751638,0.40112301};
SS(-0.76988954,1,-0.26944904,-0.58934795,0.84141567,-0.18062024,-0.81095336,1,-0.07156149,-0.74249217,0.75399014,-0.15399718){1.6463902,1.0736489,1.6471359,1.1267767};
SS(0.11583535,0.30145324,-0.5,0.09693172,0.3918681,-0.3370861,-0.029932551,0.40748663,-0.5,-0.010543702,0.17712261,-0.5){0.33954703,0.26256104,0.4038008,0.25750364};
SS(-0.41648151,0.41684878,0.5,-0.41843781,0.30742585,0.3397996,-0.24000819,0.17660305,0.5,-0.4543958,0.20406131,0.5){0.58097186,0.37011438,0.3210912,0.48353653};
SS(-0.67513028,-0.66529728,0.5,-0.63815223,-0.88141187,0.37488811,-0.50400314,-0.78879927,0.5,-0.80635543,-0.81164184,0.5){1.1284607,1.3088768,1.1086821,1.5410993};
SS(-0.36145429,0.13293621,0.35430528,-0.24000819,0.17660305,0.5,-0.26986228,0.26051837,0.22418657,-0.41843781,0.30742585,0.3397996){0.26360063,0.3210912,0.1749353,0.37011438};
SS(-0.073421274,-0.375,-0.38984354,0,-0.49997234,-0.27965571,-0.18618058,-0.5161726,-0.15035515,-0.26056819,-0.54975154,-0.34323516){0.28201081,0.30906942,0.30914003,0.46884495};
SS(0.27123349,0.36190713,0.41476339,0.26138985,0.51848551,0.281015,0.47723835,0.52605258,0.30619083,0.36016656,0.41044152,0.1594367){0.36300231,0.40200156,0.58228229,0.3073722};
SS(0.30434906,0.49798107,-4.0114635e-05,0.24635331,0.35131343,-0.096025322,0.17777709,0.54047543,-0.2567554,0.42864323,0.48543211,-0.13804456){0.32377482,0.18045455,0.36840304,0.42022283};
SS(-0.73174678,-0.21478859,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.7907607,-0.33838097,-0.28342271,-0.56113743,-0.28920115,-0.29204918){0.81151292,0.54269073,0.80149819,0.46850822};
SS(-0.31377045,0.30492781,-0.36427962,-0.41651431,0.41690828,-0.5,-0.17603462,0.24070348,-0.5,-0.40408872,0.18166381,-0.5){0.30770932,0.57523437,0.32537509,0.42526168};
SS(-0.3548152,-0.48825703,0.21848985,-0.3533559,-0.49437708,0.037576204,-0.52487586,-0.5117405,-0.017639258,-0.59094649,-0.40495207,0.12834587){0.38862106,0.35575629,0.51812974,0.51475101};
SS(0.76099919,0.76690574,0.25750996,0.59365279,0.65503723,0.24444947,0.55555177,0.82262944,0.31125158,0.62860594,0.86645525,0.049037492){1.2143065,0.82252715,1.0671623,1.1303867};
SS(-0.6293812,0.63993291,-0.28812602,-0.66548665,0.66585508,-0.5,-0.79644003,0.50064951,-0.5,-0.80558396,0.5878127,-0.29244037){0.87296464,1.1221664,1.115532,1.0616703};
SS(-0.37661764,-0.26006406,0.40868766,-0.3548152,-0.48825703,0.21848985,-0.1853821,-0.42358473,0.30866054,-0.34549718,-0.50098866,0.4105565){0.36234206,0.38862106,0.29143101,0.5260109};
SS(0,0,0.5,-0.20045203,0.067929244,0.29301468,0,0,0.25,-0.17669296,0.011023676,0.5){0.23153294,0.10955402,0.045060365,0.26322593};
SS(-0.2401666,0.74114092,-0.051302261,-0.38143574,0.84373572,-0.12387887,-0.14847812,0.78021305,-0.27623142,-0.35455825,0.80859576,-0.32177549){0.58653028,0.85864479,0.68882385,0.86460259};
SS(-0.5555987,0.045150158,0.095162244,-0.34310942,-0.010167032,0.1509038,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.29993682,0.12661586,0.34448415,0.26156128};
SS(-0.87046532,0.63071146,0.35630423,-1,0.55555556,0.5,-1,0.4752276,0.27420758,-0.79641575,0.50054117,0.5){1.2666006,1.5401154,1.2803563,1.1180299};
SS(-1,-0.33333333,0.5,-0.89646962,-0.32955067,0.34017365,-0.73174745,-0.21491043,0.5,-0.78327322,-0.45013966,0.5){1.3443603,1.0133061,0.81377033,1.0435491};
SS(-0.76389013,0.77728265,0.25513738,-0.84394966,1,0.33504415,-1,0.84108515,0.33242406,-0.80481649,0.80494069,0.5){1.2358334,1.8084725,1.8031397,1.5232843};
SS(-1,0.29928494,0.0012550607,-0.89426176,0.41257007,-0.12932618,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){1.0718665,0.974079,0.80968993,0.68151298};
SS(-0.66546973,0.66566005,0.5,-0.87046532,0.63071146,0.35630423,-0.79641575,0.50054117,0.5,-0.80481649,0.80494069,0.5){1.1224691,1.2666006,1.1180299,1.5232843};
SS(-0.26297351,0.20404986,-0.17122089,-0.4182056,0.11248126,-0.14182463,-0.39654734,0.26661646,0.019312696,-0.34372617,0.39779568,-0.18541051){0.12773981,0.19428145,0.20710489,0.29650146};
SS(-0.17097214,0.64900986,-0.39927747,-0.14847812,0.78021305,-0.27623142,-0.01813809,0.53618118,-0.30537166,0.10162062,0.65400865,-0.37913628){0.59741335,0.68882385,0.36567785,0.5665506};
SS(0.30434906,0.49798107,-4.0114635e-05,0.36016656,0.41044152,0.1594367,0.36021608,0.23247759,-0.012351094,0.52218723,0.46943947,0.022097553){0.32377482,0.3073722,0.16110593,0.46892029};
SS(-0.045146113,0.19012269,0.5,-0.10037172,0.18891947,0.20844359,-0.20045203,0.067929244,0.29301468,-0.11614487,0.30919383,0.33918095){0.27176836,0.074828316,0.10955402,0.20820823};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.3533559,-0.49437708,0.037576204,-0.23583358,-0.36008743,0.0071767184,-0.3727858,-0.19869367,0.11195566){0.32068114,0.35575629,0.16465457,0.16948569};
SS(-0.83127473,0.33505962,-0.32026923,-1,0.33333333,-0.5,-0.69937066,0.31351533,-0.5,-0.82994199,0.18319278,-0.5){0.89071695,1.3393331,0.81965428,0.95993957};
SS(-0.66548665,0.66585508,-0.5,-0.6293812,0.63993291,-0.28812602,-0.79644003,0.50064951,-0.5,-0.61503712,0.4760032,-0.5){1.1221664,0.87296464,1.115532,0.83978547};
SS(-0.4581749,-0.5263483,-0.32801665,-0.50036547,-0.57239096,-0.5,-0.49676106,-0.69523221,-0.26913048,-0.62341011,-0.46880832,-0.38153973){0.57811658,0.81333009,0.78043195,0.73807879};
SS(-0.61674646,0.25215289,0.3447871,-0.41648151,0.41684878,0.5,-0.4543958,0.20406131,0.5,-0.41843781,0.30742585,0.3397996){0.54607287,0.58097186,0.48353653,0.37011438};
SS(0.35689191,0.091376279,-0.36932783,0.5,0,-0.5,0.37532516,0.23078833,-0.5,0.6251418,0.1440922,-0.5){0.26145514,0.48471812,0.42551454,0.63751638};
SS(-0.64009684,-0.10188458,0.37412975,-0.67616985,-0.069078192,0.18801024,-0.73479965,-0.34302295,0.24038072,-0.84084014,-0.14895162,0.31636914){0.54631619,0.47948004,0.69668046,0.81273381};
SS(1,0.75,-0.5,0.85153485,0.65148612,-0.35468846,1,0.70844226,-0.20827687,1,0.83864447,-0.33847614){1.7924126,1.2568282,1.5310675,1.8065101};
SS(-1,-0.70523324,-0.21165758,-0.85520613,-0.46088631,-0.14784569,-0.82285362,-0.63420593,-0.0683896,-0.81387526,-0.53653555,-0.3209601){1.5222776,0.95161001,1.0691297,1.0406635};
SS(-0.66548665,0.66585508,-0.5,-0.65756371,0.81308934,-0.3429452,-0.50014045,0.79673357,-0.5,-0.80479144,0.80504612,-0.5){1.1221664,1.1958888,1.1145783,1.5255891};
SS(-0.349759,-0.84853211,0.35590634,-0.25,-1,0.5,-0.29157863,-1,0.20827581,-0.16134158,-1,0.33850563){0.94981364,1.2918821,1.1139248,1.129042};
SS(-0.34310942,-0.010167032,0.1509038,-0.5555987,0.045150158,0.095162244,-0.4720473,-0.063494476,-0.036829327,-0.3727858,-0.19869367,0.11195566){0.12661586,0.29993682,0.21285629,0.16948569};
SS(0.64232771,0.84838332,0.46476191,0.82853688,1,0.32125076,0.81191124,0.80644944,0.5,0.76099919,0.76690574,0.25750996){1.3339184,1.7703132,1.5425973,1.2143065};
SS(-0.37661764,-0.26006406,0.40868766,-0.3548152,-0.48825703,0.21848985,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.36234206,0.38862106,0.17775565,0.29143101};
SS(-0.65956212,-0.52273243,-0.19262862,-0.4581749,-0.5263483,-0.32801665,-0.56113743,-0.28920115,-0.29204918,-0.61549046,-0.35581383,-0.12962263){0.7287475,0.57811658,0.46850822,0.50877487};
SS(0,0,-6.9388939e-15,-0.098950987,-0.13391411,-0.14594667,-0.20656092,-0.13938028,0.029547229,-0.28278924,0.041190137,-0.04219563){-0.017891206,0.03512721,0.048278496,0.063480395};
SS(-0.60421932,0.82298164,0.34468578,-0.66546973,0.66566005,0.5,-0.50037,0.79662088,0.5,-0.80481649,0.80494069,0.5){1.1449713,1.1224691,1.1183194,1.5232843};
SS(0,-0.5,0.5,-0.1853821,-0.42358473,0.30866054,0,-0.49989758,0.27983937,-0.14394692,-0.62481063,0.5){0.48207879,0.29143101,0.30650831,0.63866347};
SS(0.59416595,0.14141347,0.32656529,0.5,0,0.5,0.75,0,0.5,0.62515059,0.14422159,0.5){0.46498444,0.47735984,0.79262349,0.64726001};
SS(-0.17603462,0.24070348,-0.5,-0.29413589,0.046284299,-0.31274881,-0.40408872,0.18166381,-0.5,-0.19007896,0.04567822,-0.5){0.32537509,0.1681493,0.42526168,0.27736807};
SS(0.37532516,0.23078833,-0.5,0.35689191,0.091376279,-0.36932783,0.6251418,0.1440922,-0.5,0.51910919,0.22553632,-0.31417891){0.42551454,0.26145514,0.63751638,0.40112301};
SS(-0.33333333,1,0.5,-0.30949447,0.8262402,0.33528492,-0.22223836,1,0.2622369,-0.1827732,0.83017807,0.5){1.3433112,0.87388961,1.0984067,0.95598938};
SS(0,0,-0.5,-0.1182182,0.15955837,-0.3159857,0,0,-0.25,-0.010543702,0.17712261,-0.5){0.23465449,0.11990198,0.044304329,0.25750364};
SS(-0.6293812,0.63993291,-0.28812602,-0.79644003,0.50064951,-0.5,-0.61503712,0.4760032,-0.5,-0.80558396,0.5878127,-0.29244037){0.87296464,1.115532,0.83978547,1.0616703};
SS(-0.63815223,-0.88141187,0.37488811,-0.50400314,-0.78879927,0.5,-0.349759,-0.84853211,0.35590634,-0.57994589,-0.69256437,0.31204703){1.3088768,1.1086821,0.94981364,0.89957508};
SS(-1,-0.00012222908,0.26646899,-0.84289574,0.018333867,0.1608607,-0.84084014,-0.14895162,0.31636914,-0.82279039,-0.18997945,0.10657137){1.0506696,0.72430843,0.81273381,0.70945047};
SS(-0.39806707,0.15776443,0.15870839,-0.39654734,0.26661646,0.019312696,-0.5555987,0.045150158,0.095162244,-0.63246299,0.29145388,0.035195127){0.19317292,0.20710489,0.29993682,0.47226275};
SS(0.25,0,0.5,0.26083053,0.15082484,0.37728795,0.29175541,0,0.20824909,0.1615172,0,0.33845519){0.29281005,0.21918499,0.1093371,0.13068911};
SS(-0.76760867,-0.33664988,-0.028298027,-0.88905946,-0.098697315,-0.13184676,-0.82279039,-0.18997945,0.10657137,-0.65367362,-0.16081953,0.0014934597){0.68479998,0.8023886,0.70945047,0.4344691};
SS(-0.58258855,0.14037208,-0.067351147,-0.65355936,0.25468043,-0.1897796,-0.49391083,0.27907498,-0.27264436,-0.4182056,0.11248126,-0.14182463){0.34532741,0.51379882,0.37398026,0.19428145};
SS(0.60662231,0.34516964,-0.13972301,0.37137652,0.1767682,-0.19801193,0.34662081,0.36199915,-0.25068724,0.51910919,0.22553632,-0.31417891){0.48782847,0.19205628,0.29696992,0.40112301};
SS(-0.88905946,-0.098697315,-0.13184676,-0.70236545,-0.13062851,-0.19140485,-0.77267892,0.13105707,-0.24874664,-0.76752638,0.004448061,-0.013214377){0.8023886,0.5265969,0.65386325,0.5734925};
SS(-1,-0.83959635,-0.33115777,-0.86742481,-0.86548068,-0.14483364,-0.83846289,-1,-0.33858677,-0.76546557,-0.72634686,-0.27513208){1.7998257,1.5085891,1.8019179,1.1696133};
SS(-0.41648151,0.41684878,0.5,-0.29261734,0.53193925,0.43339885,-0.18136176,0.40461939,0.5,-0.41843781,0.30742585,0.3397996){0.58097186,0.53993003,0.42386795,0.37011438};
SS(-0.12988976,-0.86995226,0.20452896,-0.16134158,-1,0.33850563,0,-0.83845667,0.33864852,-0.18863677,-0.81113033,0.5){0.79894991,1.129042,0.80178572,0.92459822};
SS(-0.4433427,0.53576375,-0.12560501,-0.49391083,0.27907498,-0.27264436,-0.50782983,0.50249565,-0.29902586,-0.34372617,0.39779568,-0.18541051){0.48429505,0.37398026,0.58612549,0.29650146};
SS(-0.36608751,-0.8951802,0.074405883,-0.4999534,-1,0.27968311,-0.42889738,-0.75253072,0.17523232,-0.61978497,-0.82706917,0.12738472){0.92652515,1.3075402,0.75958282,1.0681409};
SS(0.37532516,0.23078833,-0.5,0.35689191,0.091376279,-0.36932783,0.34662081,0.36199915,-0.25068724,0.20129651,0.21389912,-0.31902192){0.42551454,0.26145514,0.29696992,0.16839385};
SS(0.11136938,1,0.13859714,-0.043441254,0.79173928,0.29440137,-0.014815866,1,0.31001515,-0.084253952,1,0.13733396){1.0072058,0.69563564,1.0772324,1.0073117};
SS(0,-0.77970171,0.00010845427,-0.14850787,-0.69358405,-0.087583548,-0.22302806,-0.77703925,0.068353305,-0.19247216,-0.56000521,0.088357129){0.58842154,0.49763432,0.64063544,0.34206231};
SS(-0.67616985,-0.069078192,0.18801024,-0.64009684,-0.10188458,0.37412975,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.47948004,0.54631619,0.36443271,0.26156128};
SS(1,1,-0.5,0.81205362,0.80656044,-0.5,1,0.83864447,-0.33847614,0.82865019,1,-0.3214153){2.2331531,1.5391707,1.8065101,1.7714679};
SS(-0.58258855,0.14037208,-0.067351147,-0.78848723,0.26584533,-0.068869999,-0.77267892,0.13105707,-0.24874664,-0.76752638,0.004448061,-0.013214377){0.34532741,0.68151298,0.65386325,0.5734925};
SS(-0.34549718,-0.50098866,0.4105565,-0.14394692,-0.62481063,0.5,-0.22656331,-0.68065623,0.28194433,-0.1853821,-0.42358473,0.30866054){0.5260109,0.63866347,0.57683818,0.29143101};
SS(-0.65956212,-0.52273243,-0.19262862,-0.7055892,-0.50616462,-0.017961589,-0.63348211,-0.7706683,-0.074889286,-0.52487586,-0.5117405,-0.017639258){0.7287475,0.74484897,0.97907785,0.51812974};
SS(-0.39806707,0.15776443,0.15870839,-0.39654734,0.26661646,0.019312696,-0.28278924,0.041190137,-0.04219563,-0.4182056,0.11248126,-0.14182463){0.19317292,0.20710489,0.063480395,0.19428145};
SS(0.86971177,0.13024645,0.1427188,1,0.29178008,0.20838772,1,0.2203628,5.6826691e-05,0.77315808,0.36766952,0.075951375){0.77797836,1.1084285,1.0268649,0.71793497};
SS(1,0.83856906,0.33864755,1,1,0.5,0.82853688,1,0.32125076,0.81191124,0.80644944,0.5){1.8033242,2.2317116,1.7703132,1.5425973};
SS(-0.36174,-0.40052234,-0.23665811,-0.15923414,-0.34171533,-0.15079999,-0.18618058,-0.5161726,-0.15035515,-0.26056819,-0.54975154,-0.34323516){0.32480953,0.14783141,0.30914003,0.46884495};
SS(-0.5555987,0.045150158,0.095162244,-0.34310942,-0.010167032,0.1509038,-0.4720473,-0.063494476,-0.036829327,-0.28278924,0.041190137,-0.04219563){0.29993682,0.12661586,0.21285629,0.063480395};
SS(-0.33333333,1,-0.5,-0.35455825,0.80859576,-0.32177549,-0.31289368,0.69974287,-0.5,-0.18268367,0.83021756,-0.5){1.3407278,0.86460259,0.82323564,0.9573479};
SS(-0.39032311,0.63241857,-0.34621958,-0.50014045,0.79673357,-0.5,-0.4813337,0.60105459,-0.5,-0.6293812,0.63993291,-0.28812602){0.65630059,1.1145783,0.83133251,0.87296464};
SS(-0.30122568,-0.11513872,0.5,-0.40506391,-0.079541407,0.3303193,-0.17669296,0.011023676,0.5,-0.20045203,0.067929244,0.29301468){0.33848202,0.26156128,0.26322593,0.10955402};
SS(0.5,0,0.5,0.42621669,0.19017509,0.30505062,0.37501462,0.2307626,0.5,0.62515059,0.14422159,0.5){0.47735984,0.29714896,0.42590445,0.64726001};
SS(0.59365279,0.65503723,0.24444947,0.65062064,0.64268786,0.069510863,0.39612945,0.70614162,0.21524614,0.45788353,0.76094781,-0.0096633567){0.82252715,0.82620698,0.68453461,0.76853994};
SS(1,0.50009037,3.487572e-05,0.69383766,0.49492178,-0.021800115,0.84582719,0.572243,0.1361951,0.8988736,0.63809662,-0.070284173){1.2275825,0.71284258,1.0417018,1.2046527};
SS(-0.65776896,0.64141588,0.074371921,-0.61311838,0.85766427,0.15491279,-0.48952189,0.78345034,0.019065462,-0.79370724,0.81084643,0.045877226){0.83514199,1.1216468,0.83409809,1.270911};
SS(-1,-1,-0.25,-0.86742481,-0.86548068,-0.14483364,-0.70823063,-1,-0.20843533,-0.83846289,-1,-0.33858677){2.0422973,1.5085891,1.5240742,1.8019179};
SS(-0.54640726,0.34339216,0.19847863,-0.52470763,0.46530444,0.33754711,-0.35521568,0.4957142,0.26668635,-0.45563594,0.60375179,0.095527884){0.43575493,0.59371518,0.42001946,0.56263538};
SS(-0.29237157,-0.11865629,-0.17606411,-0.15923414,-0.34171533,-0.15079999,-0.36174,-0.40052234,-0.23665811,-0.1971424,-0.26981885,-0.30750196){0.11404163,0.14783141,0.32480953,0.19280289};
SS(0.61535375,0.70719289,-0.095218388,0.65062064,0.64268786,0.069510863,0.45788353,0.76094781,-0.0096633567,0.52218723,0.46943947,0.022097553){0.87858083,0.82620698,0.76853994,0.46892029};
SS(0.35689191,0.091376279,-0.36932783,0.37532516,0.23078833,-0.5,0.34662081,0.36199915,-0.25068724,0.51910919,0.22553632,-0.31417891){0.26145514,0.42551454,0.29696992,0.40112301};
SS(-0.8480722,0.62150313,0.12164012,-1,0.49991607,0.0031934521,-1,0.77631186,0.00053339564,-0.8068077,0.56885008,-0.063754108){1.1084494,1.2302733,1.5817554,0.96112076};
SS(-0.77973152,-1,-0.0001062007,-0.79227163,-0.79754897,0.0021844777,-0.63348211,-0.7706683,-0.074889286,-0.86742481,-0.86548068,-0.14483364){1.588155,1.2530106,0.97907785,1.5085891};
SS(-0.5555987,0.045150158,0.095162244,-0.58258855,0.14037208,-0.067351147,-0.4720473,-0.063494476,-0.036829327,-0.76752638,0.004448061,-0.013214377){0.29993682,0.34532741,0.21285629,0.5734925};
SS(-0.39806707,0.15776443,0.15870839,-0.39654734,0.26661646,0.019312696,-0.13709741,0.19518884,0.034033465,-0.28278924,0.041190137,-0.04219563){0.19317292,0.20710489,0.040184006,0.063480395};
SS(-0.8480722,0.62150313,0.12164012,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382,-0.87046532,0.63071146,0.35630423){1.1084494,0.80968993,0.83617727,1.2666006};
SS(-0.38492375,-0.20017574,-0.33650716,-0.49292178,-0.37477565,-0.5,-0.36174,-0.40052234,-0.23665811,-0.56113743,-0.28920115,-0.29204918){0.28705324,0.6115465,0.32480953,0.46850822};
SS(-0.11111111,1,0.5,-0.33333333,1,0.5,-0.22223836,1,0.2622369,-0.1827732,0.83017807,0.5){1.2487078,1.3433112,1.0984067,0.95598938};
SS(-0.62450053,-0.31310845,0.38575928,-0.73479965,-0.34302295,0.24038072,-0.56348952,-0.47594309,0.3052276,-0.79575191,-0.55547687,0.30538166){0.62379151,0.69668046,0.61776713,1.0192798};
SS(-0.26986228,0.26051837,0.22418657,-0.39806707,0.15776443,0.15870839,-0.13709741,0.19518884,0.034033465,-0.15128303,0.02253305,0.11422928){0.1749353,0.19317292,0.040184006,0.025420414};
SS(-0.36145429,0.13293621,0.35430528,-0.30122568,-0.11513872,0.5,-0.40752783,0.030201366,0.5,-0.40506391,-0.079541407,0.3303193){0.26360063,0.33848202,0.40526498,0.26156128};
SS(-0.30122568,-0.11513872,0.5,-0.36145429,0.13293621,0.35430528,-0.40752783,0.030201366,0.5,-0.17669296,0.011023676,0.5){0.33848202,0.26360063,0.40526498,0.26322593};
SS(-0.70823063,-1,-0.20843533,-0.86742481,-0.86548068,-0.14483364,-0.77973152,-1,-0.0001062007,-0.63348211,-0.7706683,-0.074889286){1.5240742,1.5085891,1.588155,0.97907785};
SS(-0.18848435,-0.81110947,-0.5,-0.2399131,-0.76005145,-0.25989531,-0.16144976,-1,-0.33863959,0,-0.83851883,-0.33849865){0.92571371,0.6848256,1.1250711,0.80235204};
SS(-0.49995867,-1,-0.27986665,-0.36340067,-0.87821042,-0.37678589,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){1.3082069,1.0307746,0.78043195,0.88525127};
SS(-0.32879066,-0.67072359,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.49676106,-0.69523221,-0.26913048,-0.26056819,-0.54975154,-0.34323516){0.79007105,0.57811658,0.78043195,0.46884495};
SS(-1,-0.24887753,0.1953112,-0.82279039,-0.18997945,0.10657137,-1,-0.00012222908,0.26646899,-0.84084014,-0.14895162,0.31636914){1.0768014,0.70945047,1.0506696,0.81273381};
SS(1,0,0.25,0.86971177,0.13024645,0.1427188,0.70845584,0,0.20819814,0.83866368,0,0.33843958){1.0436257,0.77797836,0.52761363,0.80106313};
SS(-0.30122568,-0.11513872,0.5,-0.36145429,0.13293621,0.35430528,-0.17669296,0.011023676,0.5,-0.40506391,-0.079541407,0.3303193){0.33848202,0.26360063,0.26322593,0.26156128};
SS(-0.13709741,0.19518884,0.034033465,-0.24163432,0.33561251,-0.055881164,-0.096302334,0.43534175,-0.056072844,-0.056808231,0.14323286,-0.13367928){0.040184006,0.16437697,0.18078295,0.022140076};
SS(-0.39032311,0.63241857,-0.34621958,-0.48255002,0.69900846,-0.19155417,-0.24654336,0.57133462,-0.25396354,-0.35455825,0.80859576,-0.32177549){0.65630059,0.74365966,0.42991415,0.86460259};
SS(-0.80558396,0.5878127,-0.29244037,-1,0.55555556,-0.5,-0.79644003,0.50064951,-0.5,-1,0.47527469,-0.27513051){1.0616703,1.5309384,1.115532,1.2834809};
SS(0.37137652,0.1767682,-0.19801193,0.29173763,0,-0.20843742,0.22032809,0,-9.1119885e-05,0.13402468,0.11673163,-0.1460819){0.19205628,0.1134179,0.027339551,0.039337265};
SS(0.86971177,0.13024645,0.1427188,1,0,0.25,1,0.29178008,0.20838772,1,0.16158711,0.33859063){0.77797836,1.0436257,1.1084285,1.1259698};
SS(-0.17669296,0.011023676,0.5,-0.36145429,0.13293621,0.35430528,-0.20045203,0.067929244,0.29301468,-0.40506391,-0.079541407,0.3303193){0.26322593,0.26360063,0.10955402,0.26156128};
SS(-0.20381263,0.45499536,-0.5,-0.17097214,0.64900986,-0.39927747,-0.24654336,0.57133462,-0.25396354,-0.01813809,0.53618118,-0.30537166){0.478983,0.59741335,0.42991415,0.36567785};
SS(-0.65756371,0.81308934,-0.3429452,-0.77777778,1,-0.5,-0.76988954,1,-0.26944904,-0.56041637,1,-0.29784853){1.1958888,1.8319852,1.6463902,1.3856141};
SS(-0.58934795,0.84141567,-0.18062024,-0.48255002,0.69900846,-0.19155417,-0.65756371,0.81308934,-0.3429452,-0.35455825,0.80859576,-0.32177549){1.0736489,0.74365966,1.1958888,0.86460259};
SS(0.36016656,0.41044152,0.1594367,0.25126435,0.28098512,0.24657435,0.36021608,0.23247759,-0.012351094,0.46476684,0.14382827,0.12247557){0.3073722,0.18575023,0.16110593,0.23450402};
SS(-0.17603462,0.24070348,-0.5,-0.31377045,0.30492781,-0.36427962,-0.40408872,0.18166381,-0.5,-0.29413589,0.046284299,-0.31274881){0.32537509,0.30770932,0.42526168,0.1681493};
SS(-0.34310942,-0.010167032,0.1509038,-0.39806707,0.15776443,0.15870839,-0.28278924,0.041190137,-0.04219563,-0.5555987,0.045150158,0.095162244){0.12661586,0.19317292,0.063480395,0.29993682};
SS(-0.62450053,-0.31310845,0.38575928,-0.78327322,-0.45013966,0.5,-0.73479965,-0.34302295,0.24038072,-0.79575191,-0.55547687,0.30538166){0.62379151,1.0435491,0.69668046,1.0192798};
SS(-0.57994589,-0.69256437,0.31204703,-0.67513028,-0.66529728,0.5,-0.56348952,-0.47594309,0.3052276,-0.79575191,-0.55547687,0.30538166){0.89957508,1.1284607,0.61776713,1.0192798};
SS(0.76099919,0.76690574,0.25750996,1,0.83856906,0.33864755,0.82853688,1,0.32125076,0.81191124,0.80644944,0.5){1.2143065,1.8033242,1.7703132,1.5425973};
SS(0.66554141,0.67524133,0.5,0.87881231,0.64063264,0.37220388,0.78912399,0.50423732,0.5,0.6902006,0.50015172,0.27072419){1.1271263,1.3069719,1.1096027,0.77938072};
SS(-0.79644003,0.50064951,-0.5,-0.63048479,0.37587985,-0.34368186,-0.61503712,0.4760032,-0.5,-0.80558396,0.5878127,-0.29244037){1.115532,0.64388066,0.83978547,1.0616703};
SS(-0.65355936,0.25468043,-0.1897796,-0.49391083,0.27907498,-0.27264436,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.51379882,0.37398026,0.29810596,0.55109073};
SS(-0.67495489,-0.6652659,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.50377808,-0.78884267,-0.5,-0.49676106,-0.69523221,-0.26913048){1.1276355,1.296688,1.1087956,0.78043195};
SS(1,0.25,0.5,0.87272604,0.35900693,0.37172569,1,0.29178008,0.20838772,1,0.16158711,0.33859063){1.2942978,1.0107603,1.1084285,1.1259698};
SS(0.36016656,0.41044152,0.1594367,0.52843461,0.32737897,0.19102935,0.36021608,0.23247759,-0.012351094,0.52218723,0.46943947,0.022097553){0.3073722,0.40790135,0.16110593,0.46892029};
SS(0.36016656,0.41044152,0.1594367,0.25126435,0.28098512,0.24657435,0.42621669,0.19017509,0.30505062,0.50761134,0.34933779,0.39015973){0.3073722,0.18575023,0.29714896,0.51484928};
SS(-0.65756371,0.81308934,-0.3429452,-0.55555556,1,-0.5,-0.77777778,1,-0.5,-0.56041637,1,-0.29784853){1.1958888,1.5379273,1.8319852,1.3856141};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.3727858,-0.19869367,0.11195566,-0.4720473,-0.063494476,-0.036829327,-0.65367362,-0.16081953,0.0014934597){0.32068114,0.16948569,0.21285629,0.4344691};
SS(-0.15923414,-0.34171533,-0.15079999,-0.29237157,-0.11865629,-0.17606411,-0.23583358,-0.36008743,0.0071767184,-0.20656092,-0.13938028,0.029547229){0.14783141,0.11404163,0.16465457,0.048278496};
SS(-0.52470763,0.46530444,0.33754711,-0.47185361,0.73769401,0.24072705,-0.35521568,0.4957142,0.26668635,-0.45563594,0.60375179,0.095527884){0.59371518,0.80384956,0.42001946,0.56263538};
SS(0.86971177,0.13024645,0.1427188,1,0.29178008,0.20838772,0.77315808,0.36766952,0.075951375,0.73568363,0.23203612,0.2735765){0.77797836,1.1084285,0.71793497,0.6509231};
SS(-0.15923414,-0.34171533,-0.15079999,-0.36174,-0.40052234,-0.23665811,-0.1971424,-0.26981885,-0.30750196,-0.26056819,-0.54975154,-0.34323516){0.14783141,0.32480953,0.19280289,0.46884495};
SS(-0.33333333,1,0.5,-0.30949447,0.8262402,0.33528492,-0.3132159,0.69976014,0.5,-0.50037,0.79662088,0.5){1.3433112,0.87388961,0.82050522,1.1183194};
SS(-0.20381263,0.45499536,-0.5,-0.01813809,0.53618118,-0.30537166,-0.24654336,0.57133462,-0.25396354,-0.12449617,0.36606215,-0.28273955){0.478983,0.36567785,0.42991415,0.21185338};
SS(-0.26986228,0.26051837,0.22418657,-0.19461387,0.3919517,0.10437587,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467){0.1749353,0.19075448,0.42001946,0.33969293};
SS(1,0.50009037,3.487572e-05,0.69383766,0.49492178,-0.021800115,0.77315808,0.36766952,0.075951375,0.84582719,0.572243,0.1361951){1.2275825,0.71284258,0.71793497,1.0417018};
SS(0.88049681,0.87960137,0.13412341,1,0.70834898,0.20844998,0.76099919,0.76690574,0.25750996,0.84582719,0.572243,0.1361951){1.5518824,1.5291243,1.2143065,1.0417018};
SS(-0.13709741,0.19518884,0.034033465,-0.39806707,0.15776443,0.15870839,-0.28278924,0.041190137,-0.04219563,-0.15128303,0.02253305,0.11422928){0.040184006,0.19317292,0.063480395,0.025420414};
SS(0.59416595,0.14141347,0.32656529,0.75,0,0.5,0.70845584,0,0.20819814,0.83866368,0,0.33843958){0.46498444,0.79262349,0.52761363,0.80106313};
SS(0.69383766,0.49492178,-0.021800115,1,0.50009037,3.487572e-05,0.82562789,0.37565656,-0.12707714,0.8988736,0.63809662,-0.070284173){0.71284258,1.2275825,0.82387041,1.2046527};
SS(0.08017426,0.31429474,-0.16745504,0.24635331,0.35131343,-0.096025322,0.086744979,0.52712982,0.027891324,0.18202227,0.38279251,0.10350409){0.11103103,0.18045455,0.26660844,0.17617817};
SS(-0.6448883,-0.87343314,-0.36731947,-0.75,-1,-0.5,-0.70823063,-1,-0.20843533,-0.83846289,-1,-0.33858677){1.296688,1.7946951,1.5240742,1.8019179};
SS(-0.91004595,0.15296589,0.33139812,-1,0.11111111,0.5,-1,-0.00012222908,0.26646899,-0.80727304,0.00024662976,0.5){0.94743142,1.246301,1.0506696,0.88515177};
SS(-0.3533559,-0.49437708,0.037576204,-0.35582611,-0.64426575,-0.070000747,-0.42889738,-0.75253072,0.17523232,-0.50537844,-0.68762812,0.023695348){0.35575629,0.52757348,0.75958282,0.71483247};
SS(0.00024312215,0.80750011,-0.5,0.10162062,0.65400865,-0.37913628,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){0.88610119,0.5665506,0.71703623,0.68882385};
SS(0.59365279,0.65503723,0.24444947,0.66554141,0.67524133,0.5,0.5725222,0.50074158,0.5,0.6902006,0.50015172,0.27072419){0.82252715,1.1271263,0.8121357,0.77938072};
SS(-0.12233239,-0.87748906,-0.13583418,0,-1,-0.25,-0.29168215,-1,-0.20844865,-0.16144976,-1,-0.33863959){0.78823805,1.0435946,1.1132023,1.1250711};
SS(-0.83127473,0.33505962,-0.32026923,-0.79644003,0.50064951,-0.5,-1,0.47527469,-0.27513051,-0.80558396,0.5878127,-0.29244037){0.89071695,1.115532,1.2834809,1.0616703};
SS(0.66554141,0.67524133,0.5,0.59365279,0.65503723,0.24444947,0.5725222,0.50074158,0.5,0.47723835,0.52605258,0.30619083){1.1271263,0.82252715,0.8121357,0.58228229};
SS(-0.59094649,-0.40495207,0.12834587,-0.3548152,-0.48825703,0.21848985,-0.50874333,-0.23900991,0.2620444,-0.56348952,-0.47594309,0.3052276){0.51475101,0.38862106,0.36443271,0.61776713};
SS(-0.19461387,0.3919517,0.10437587,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467,-0.098708274,0.55956225,0.10505678){0.19075448,0.42001946,0.33969293,0.31633913};
SS(-0.24654336,0.57133462,-0.25396354,-0.34372617,0.39779568,-0.18541051,-0.12449617,0.36606215,-0.28273955,-0.096302334,0.43534175,-0.056072844){0.42991415,0.29650146,0.21185338,0.18078295};
SS(0.17426348,1,-0.18078905,0.33386283,0.81592026,-0.31808704,0.25248643,0.73785598,-0.13082591,0.081865095,0.80626877,-0.27867109){1.045853,0.86115027,0.60350215,0.71703623};
SS(-0.17603462,0.24070348,-0.5,-0.1182182,0.15955837,-0.3159857,-0.010543702,0.17712261,-0.5,-0.12449617,0.36606215,-0.28273955){0.32537509,0.11990198,0.25750364,0.21185338};
SS(0.08017426,0.31429474,-0.16745504,-0.01813809,0.53618118,-0.30537166,-0.12449617,0.36606215,-0.28273955,-0.096302334,0.43534175,-0.056072844){0.11103103,0.36567785,0.21185338,0.18078295};
SS(0.69383766,0.49492178,-0.021800115,1,0.50009037,3.487572e-05,0.77315808,0.36766952,0.075951375,0.82562789,0.37565656,-0.12707714){0.71284258,1.2275825,0.71793497,0.82387041};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.22019801,5.0496855e-05,-0.20656092,-0.13938028,0.029547229,-0.1159097,-0.14329028,0.19302206){0.17697987,0.029059683,0.048278496,0.055235283};
SS(0,-1,0.25,-0.12988976,-0.86995226,0.20452896,-0.29157863,-1,0.20827581,-0.16134158,-1,0.33850563){1.0438639,0.79894991,1.1139248,1.129042};
SS(0,-1,-0.25,-0.12233239,-0.87748906,-0.13583418,0,-0.70830496,-0.20826096,0,-0.83851883,-0.33849865){1.0435946,0.78823805,0.5287181,0.80235204};
SS(-0.4581749,-0.5263483,-0.32801665,-0.36174,-0.40052234,-0.23665811,-0.56113743,-0.28920115,-0.29204918,-0.61549046,-0.35581383,-0.12962263){0.57811658,0.32480953,0.46850822,0.50877487};
SS(-0.25,-1,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.29168215,-1,-0.20844865,-0.16144976,-1,-0.33863959){1.2929607,1.0307746,1.1132023,1.1250711};
SS(-0.10743676,0.85847111,-0.11136175,-0.222315,1,-0.00011890035,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){0.7462212,1.0307381,0.58653028,0.60161266};
SS(-0.12988976,-0.86995226,0.20452896,0,-1,0.25,0,-0.7082575,0.2084616,0,-0.83845667,0.33864852){0.79894991,1.0438639,0.52387062,0.80178572};
SS(-0.83851866,0.33014205,0.32623765,-1,0.33333333,0.5,-0.69937107,0.31347586,0.5,-0.79641575,0.50054117,0.5){0.89937894,1.3403692,0.8165723,1.1180299};
SS(0.26083053,0.15082484,0.37728795,0.25,0,0.5,0.5,0,0.5,0.37501462,0.2307626,0.5){0.21918499,0.29281005,0.47735984,0.42590445};
SS(0.42864323,0.48543211,-0.13804456,0.30434906,0.49798107,-4.0114635e-05,0.36021608,0.23247759,-0.012351094,0.52218723,0.46943947,0.022097553){0.42022283,0.32377482,0.16110593,0.46892029};
SS(-0.045146113,0.19012269,0.5,-0.10037172,0.18891947,0.20844359,0.050277172,0.20853018,0.30186362,-0.20045203,0.067929244,0.29301468){0.27176836,0.074828316,0.12181545,0.10955402};
SS(-0.67513028,-0.66529728,0.5,-0.89663862,-0.69397302,0.37275403,-0.78327322,-0.45013966,0.5,-0.79575191,-0.55547687,0.30538166){1.1284607,1.4119512,1.0435491,1.0192798};
SS(0.78906409,0.5041626,-0.5,0.87867265,0.36391919,-0.37720578,0.85153485,0.65148612,-0.35468846,0.67125235,0.44297685,-0.31879306){1.1105402,1.03034,1.2568282,0.72773009};
SS(-0.10037172,0.18891947,0.20844359,0,0,-6.9388939e-15,0,0,0.25,-0.15128303,0.02253305,0.11422928){0.074828316,-0.017891206,0.045060365,0.025420414};
SS(1,0.16158711,0.33859063,0.73568363,0.23203612,0.2735765,0.83866368,0,0.33843958,0.81143387,0.18901581,0.5){1.1259698,0.6509231,0.80106313,0.9265446};
SS(-0.62341011,-0.46880832,-0.38153973,-0.73174678,-0.21478859,-0.5,-0.78315651,-0.45008839,-0.5,-0.7907607,-0.33838097,-0.28342271){0.73807879,0.81151292,1.0467962,0.80149819};
SS(-0.3727858,-0.19869367,0.11195566,-0.29237157,-0.11865629,-0.17606411,-0.4720473,-0.063494476,-0.036829327,-0.20656092,-0.13938028,0.029547229){0.16948569,0.11404163,0.21285629,0.048278496};
SS(-1,-0.24887753,0.1953112,-0.89646962,-0.32955067,0.34017365,-1,-0.47520831,0.27427507,-0.82595855,-0.48031431,0.11444494){1.0768014,1.0133061,1.2822693,0.90887195};
SS(0.87867265,0.36391919,-0.37720578,1,0.25,-0.5,1,0.2917639,-0.20827961,1,0.16156328,-0.33847781){1.03034,1.2935113,1.1127834,1.1261583};
SS(-0.58258855,0.14037208,-0.067351147,-0.4182056,0.11248126,-0.14182463,-0.39654734,0.26661646,0.019312696,-0.5555987,0.045150158,0.095162244){0.34532741,0.19428145,0.20710489,0.29993682};
SS(-0.8827276,-0.88146034,0.13123348,-1,-1,0.25,-0.70832062,-1,0.2082538,-0.8385203,-1,0.33846229){1.5595365,2.0427074,1.5291125,1.8024192};
SS(0.59416595,0.14141347,0.32656529,0.75,0,0.5,0.81143387,0.18901581,0.5,0.62515059,0.14422159,0.5){0.46498444,0.79262349,0.9265446,0.64726001};
SS(-1,0.47527469,-0.27513051,-0.89426176,0.41257007,-0.12932618,-0.80558396,0.5878127,-0.29244037,-0.8068077,0.56885008,-0.063754108){1.2834809,0.974079,1.0616703,0.96112076};
SS(-0.45563594,0.60375179,0.095527884,-0.20984637,0.69532212,0.20809493,-0.47185361,0.73769401,0.24072705,-0.35521568,0.4957142,0.26668635){0.56263538,0.55022745,0.80384956,0.42001946};
SS(1,1,0.25,0.88049681,0.87960137,0.13412341,1,0.70834898,0.20844998,1,0.83856906,0.33864755){2.0447444,1.5518824,1.5291243,1.8033242};
SS(0.33333333,1,-0.5,0.33386283,0.81592026,-0.31808704,0.21543771,0.73213875,-0.5,0.45062041,0.7833899,-0.5){1.342474,0.86115027,0.81134051,1.0506853};
SS(-0.65776896,0.64141588,0.074371921,-0.45563594,0.60375179,0.095527884,-0.47185361,0.73769401,0.24072705,-0.67801153,0.56076489,0.29217382){0.83514199,0.56263538,0.80384956,0.83617727};
SS(-0.52470763,0.46530444,0.33754711,-0.41648151,0.41684878,0.5,-0.61509744,0.47589965,0.5,-0.61674646,0.25215289,0.3447871){0.59371518,0.58097186,0.84259202,0.54607287};
SS(0.10162062,0.65400865,-0.37913628,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142,-0.01813809,0.53618118,-0.30537166){0.5665506,0.71703623,0.68882385,0.36567785};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.25140376,-0.1934451,-0.76760867,-0.33664988,-0.028298027,-0.7907607,-0.33838097,-0.28342271){0.95161001,1.0790534,0.68479998,0.80149819};
SS(0.61535375,0.70719289,-0.095218388,0.49866453,0.63973666,-0.21510859,0.52218723,0.46943947,0.022097553,0.42864323,0.48543211,-0.13804456){0.87858083,0.68344633,0.46892029,0.42022283};
SS(-0.3533559,-0.49437708,0.037576204,-0.35582611,-0.64426575,-0.070000747,-0.36174,-0.40052234,-0.23665811,-0.18618058,-0.5161726,-0.15035515){0.35575629,0.52757348,0.32480953,0.30914003};
SS(-0.66659408,1,0.32529585,-0.60421932,0.82298164,0.34468578,-0.80481649,0.80494069,0.5,-0.76389013,0.77728265,0.25513738){1.5364848,1.1449713,1.5232843,1.2358334};
SS(-0.65355936,0.25468043,-0.1897796,-0.58258855,0.14037208,-0.067351147,-0.49808619,0.0026201378,-0.26387206,-0.4182056,0.11248126,-0.14182463){0.51379882,0.34532741,0.29810596,0.19428145};
SS(-0.26056819,-0.54975154,-0.34323516,-0.35582611,-0.64426575,-0.070000747,-0.2399131,-0.76005145,-0.25989531,-0.18618058,-0.5161726,-0.15035515){0.46884495,0.52757348,0.6848256,0.30914003};
SS(0.88354722,0.11667767,-0.13069643,1,0,-0.25,0.70841775,0,-0.20847891,0.83867599,0,-0.33865964){0.79839767,1.043399,0.52293439,0.80182539};
SS(-0.01813809,0.53618118,-0.30537166,-0.0089783977,0.64320989,-0.13441642,0.081865095,0.80626877,-0.27867109,-0.14847812,0.78021305,-0.27623142){0.36567785,0.41358858,0.71703623,0.68882385};
SS(-0.74249217,0.75399014,-0.15399718,-0.58934795,0.84141567,-0.18062024,-0.48952189,0.78345034,0.019065462,-0.62332411,0.59900263,-0.10904345){1.1267767,1.0736489,0.83409809,0.74800561};
SS(-0.78327322,-0.45013966,0.5,-0.62450053,-0.31310845,0.38575928,-0.56348952,-0.47594309,0.3052276,-0.79575191,-0.55547687,0.30538166){1.0435491,0.62379151,0.61776713,1.0192798};
SS(0.00029730467,0.80760978,0.5,0.10211023,0.6404511,0.38011645,-0.043441254,0.79173928,0.29440137,0.22886345,0.79287946,0.30210005){0.88423684,0.55160362,0.69563564,0.75332396};
SS(0.84582719,0.572243,0.1361951,1,0.50009037,3.487572e-05,1,0.70834898,0.20844998,1,0.50005385,0.27984222){1.0417018,1.2275825,1.5291243,1.3085441};
SS(-0.098708274,0.55956225,0.10505678,-0.20984637,0.69532212,0.20809493,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467){0.31633913,0.55022745,0.42001946,0.33969293};
SS(0.87881231,0.64063264,0.37220388,1,0.75,0.5,1,0.70834898,0.20844998,1,0.83856906,0.33864755){1.3069719,1.7930237,1.5291243,1.8033242};
SS(-0.01813809,0.53618118,-0.30537166,-0.24654336,0.57133462,-0.25396354,-0.12449617,0.36606215,-0.28273955,-0.096302334,0.43534175,-0.056072844){0.36567785,0.42991415,0.21185338,0.18078295};
SS(-0.49998858,-1,-4.7037318e-05,-0.42066299,-0.84356131,-0.12906413,-0.29168215,-1,-0.20844865,-0.49995867,-1,-0.27986665){1.2276085,0.88525127,1.1132023,1.3082069};
SS(-0.8480722,0.62150313,0.12164012,-1,0.49991607,0.0031934521,-1,0.70725984,0.21334539,-1,0.77631186,0.00053339564){1.1084494,1.2302733,1.5286486,1.5817554};
SS(-0.49391083,0.27907498,-0.27264436,-0.65355936,0.25468043,-0.1897796,-0.49808619,0.0026201378,-0.26387206,-0.4182056,0.11248126,-0.14182463){0.37398026,0.51379882,0.29810596,0.19428145};
SS(0,-0.49997946,0.00010199173,-0.14850787,-0.69358405,-0.087583548,0,-0.70830496,-0.20826096,0,-0.77970171,0.00010845427){0.22811872,0.49763432,0.5287181,0.58842154};
SS(-0.58755791,0.033814853,0.5,-0.61674646,0.25215289,0.3447871,-0.69937107,0.31347586,0.5,-0.4543958,0.20406131,0.5){0.57778723,0.54607287,0.8165723,0.48353653};
SS(-0.49998858,-1,-4.7037318e-05,-0.36608751,-0.8951802,0.074405883,-0.22019153,-1,-0.00010416607,-0.42066299,-0.84356131,-0.12906413){1.2276085,0.92652515,1.0287732,0.88525127};
SS(-0.64009684,-0.10188458,0.37412975,-0.67616985,-0.069078192,0.18801024,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.54631619,0.47948004,0.34448415,0.26156128};
SS(-1,0.4752276,0.27420758,-0.87046532,0.63071146,0.35630423,-0.79641575,0.50054117,0.5,-0.83851866,0.33014205,0.32623765){1.2803563,1.2666006,1.1180299,0.89937894};
SS(0.22886345,0.79287946,0.30210005,0.11136938,1,0.13859714,0.23106485,1,0.31398279,0.24404834,0.79519787,0.082231238){0.75332396,1.0072058,1.1340577,0.68472542};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.49997946,0.00010199173,0,-0.49989758,0.27983937,-0.19247216,-0.56000521,0.088357129){0.17697987,0.22811872,0.30650831,0.34206231};
SS(0.25126435,0.28098512,0.24657435,0.27123349,0.36190713,0.41476339,0.50761134,0.34933779,0.39015973,0.36016656,0.41044152,0.1594367){0.18575023,0.36300231,0.51484928,0.3073722};
SS(0.27123349,0.36190713,0.41476339,0.47723835,0.52605258,0.30619083,0.50761134,0.34933779,0.39015973,0.36016656,0.41044152,0.1594367){0.36300231,0.58228229,0.51484928,0.3073722};
SS(-0.30949447,0.8262402,0.33528492,-0.22223836,1,0.2622369,-0.20984637,0.69532212,0.20809493,-0.32294154,0.86180803,0.13108841){0.87388961,1.0984067,0.55022745,0.84829643};
SS(0.50010751,0,-0.00013054911,0.46476684,0.14382827,0.12247557,0.29175541,0,0.20824909,0.50011436,0,0.27961788){0.22823279,0.23450402,0.1093371,0.30940041};
SS(1,0,-0.25,0.88354722,0.11667767,-0.13069643,1,0.2917639,-0.20827961,1,0.16156328,-0.33847781){1.043399,0.79839767,1.1127834,1.1261583};
SS(-0.11618574,0.50328545,0.29980467,0.10211023,0.6404511,0.38011645,-0.043441254,0.79173928,0.29440137,-0.16015893,0.67694077,0.39025863){0.33969293,0.55160362,0.69563564,0.6265216};
SS(0,-0.5,-0.5,-0.073421274,-0.375,-0.38984354,0,-0.25,-0.5,-0.23055166,-0.37480907,-0.5){0.4845449,0.28201081,0.29677328,0.41992239};
SS(-0.20984637,0.69532212,0.20809493,-0.45563594,0.60375179,0.095527884,-0.47185361,0.73769401,0.24072705,-0.32294154,0.86180803,0.13108841){0.55022745,0.56263538,0.80384956,0.84829643};
SS(0,-0.49997946,0.00010199173,-0.15923414,-0.34171533,-0.15079999,0,-0.49997234,-0.27965571,-0.18618058,-0.5161726,-0.15035515){0.22811872,0.14783141,0.30906942,0.30914003};
SS(-0.8480722,0.62150313,0.12164012,-1,0.4752276,0.27420758,-0.79172217,0.43302343,0.13373134,-0.87046532,0.63071146,0.35630423){1.1084494,1.2803563,0.80968993,1.2666006};
SS(-0.76389013,0.77728265,0.25513738,-0.66659408,1,0.32529585,-0.84394966,1,0.33504415,-0.80481649,0.80494069,0.5){1.2358334,1.5364848,1.8084725,1.5232843};
SS(-0.77777778,1,-0.5,-0.83248216,0.76782327,-0.31292259,-0.80479144,0.80504612,-0.5,-0.76988954,1,-0.26944904){1.8319852,1.366757,1.5255891,1.6463902};
SS(-0.20045203,0.067929244,0.29301468,0,0,0.5,-0.045146113,0.19012269,0.5,-0.17669296,0.011023676,0.5){0.10955402,0.23153294,0.27176836,0.26322593};
SS(-0.38143574,0.84373572,-0.12387887,-0.48255002,0.69900846,-0.19155417,-0.2401666,0.74114092,-0.051302261,-0.48952189,0.78345034,0.019065462){0.85864479,0.74365966,0.58653028,0.83409809};
SS(-1,-0.11111111,-0.5,-0.85707128,-0.1416783,-0.34083416,-0.80728146,0.00010990719,-0.5,-1,-0.00018427889,-0.26378916){1.2438655,0.85441326,0.88195685,1.0508045};
SS(-0.63048479,0.37587985,-0.34368186,-0.41651431,0.41690828,-0.5,-0.69937066,0.31351533,-0.5,-0.61503712,0.4760032,-0.5){0.64388066,0.57523437,0.81965428,0.83978547};
SS(-0.18136176,0.40461939,0.5,-0.29261734,0.53193925,0.43339885,-0.11618574,0.50328545,0.29980467,-0.11614487,0.30919383,0.33918095){0.42386795,0.53993003,0.33969293,0.20820823};
SS(0.87881231,0.64063264,0.37220388,1,0.50005385,0.27984222,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){1.3069719,1.3085441,0.77938072,1.0417018};
SS(0.69383766,0.49492178,-0.021800115,0.60662231,0.34516964,-0.13972301,0.77315808,0.36766952,0.075951375,0.52218723,0.46943947,0.022097553){0.71284258,0.48782847,0.71793497,0.46892029};
SS(-0.67616985,-0.069078192,0.18801024,-0.64009684,-0.10188458,0.37412975,-0.73479965,-0.34302295,0.24038072,-0.50874333,-0.23900991,0.2620444){0.47948004,0.54631619,0.69668046,0.36443271};
SS(0.65062064,0.64268786,0.069510863,0.69383766,0.49492178,-0.021800115,0.6902006,0.50015172,0.27072419,0.52218723,0.46943947,0.022097553){0.82620698,0.71284258,0.77938072,0.46892029};
SS(-1,0.33333333,-0.5,-0.83127473,0.33505962,-0.32026923,-0.69937066,0.31351533,-0.5,-0.79644003,0.50064951,-0.5){1.3393331,0.89071695,0.81965428,1.115532};
SS(-0.0089783977,0.64320989,-0.13441642,0.081865095,0.80626877,-0.27867109,0.25248643,0.73785598,-0.13082591,0.17777709,0.54047543,-0.2567554){0.41358858,0.71703623,0.60350215,0.36840304};
SS(-1,-0.5000565,0.0033661208,-0.83996275,-0.66999882,0.11765553,-1,-0.70710233,0.21356199,-1,-0.77608598,0.00064487429){1.2263361,1.1553131,1.5280688,1.5844414};
SS(-0.30122568,-0.11513872,0.5,-0.16643696,-0.21791406,0.42402077,-0.25897908,-0.24013326,0.26450313,-0.40506391,-0.079541407,0.3303193){0.33848202,0.23818505,0.17775565,0.26156128};
SS(-0.61311838,0.85766427,0.15491279,-0.61115597,1,-0.10200355,-0.42762906,1,-0.0094860889,-0.48952189,0.78345034,0.019065462){1.1216468,1.3611038,1.169501,0.83409809};
SS(-1,-0.11111111,0.5,-0.84084014,-0.14895162,0.31636914,-1,-0.00012222908,0.26646899,-0.80727304,0.00024662976,0.5){1.2390062,0.81273381,1.0506696,0.88515177};
SS(-0.92571354,0.17249619,-0.34283108,-1,0.11111111,-0.5,-0.80728146,0.00010990719,-0.5,-1,-0.00018427889,-0.26378916){0.99158484,1.2464205,0.88195685,1.0508045};
SS(-0.35455825,0.80859576,-0.32177549,-0.33333333,1,-0.5,-0.31289368,0.69974287,-0.5,-0.50014045,0.79673357,-0.5){0.86460259,1.3407278,0.82323564,1.1145783};
SS(-0.36608751,-0.8951802,0.074405883,-0.49998858,-1,-4.7037318e-05,-0.4999534,-1,0.27968311,-0.61978497,-0.82706917,0.12738472){0.92652515,1.2276085,1.3075402,1.0681409};
SS(-0.23583358,-0.36008743,0.0071767184,-0.10133362,-0.40777162,0.1162396,-0.20656092,-0.13938028,0.029547229,-0.1159097,-0.14329028,0.19302206){0.16465457,0.17697987,0.048278496,0.055235283};
SS(-0.60421932,0.82298164,0.34468578,-0.50037,0.79662088,0.5,-0.48141868,0.60085372,0.5,-0.47185361,0.73769401,0.24072705){1.1449713,1.1183194,0.82306978,0.80384956};
SS(0.57129187,0.13526053,-0.13726946,0.50010751,0,-0.00013054911,0.70841775,0,-0.20847891,0.77985819,0,-0.00014691753){0.35115136,0.22823279,0.52293439,0.58919206};
SS(-0.63048479,0.37587985,-0.34368186,-0.61503712,0.4760032,-0.5,-0.80558396,0.5878127,-0.29244037,-0.6293812,0.63993291,-0.28812602){0.64388066,0.83978547,1.0616703,0.87296464};
SS(-0.75,-1,0.5,-0.63815223,-0.88141187,0.37488811,-0.70832062,-1,0.2082538,-0.8385203,-1,0.33846229){1.7943537,1.3088768,1.5291125,1.8024192};
SS(-0.56113743,-0.28920115,-0.29204918,-0.49292178,-0.37477565,-0.5,-0.73174678,-0.21478859,-0.5,-0.50815189,-0.16301678,-0.5){0.46850822,0.6115465,0.81151292,0.52110597};
SS(-0.12449617,0.36606215,-0.28273955,-0.17603462,0.24070348,-0.5,-0.029932551,0.40748663,-0.5,-0.010543702,0.17712261,-0.5){0.21185338,0.32537509,0.4038008,0.25750364};
SS(0.8988736,0.63809662,-0.070284173,1,0.70834898,0.20844998,1,0.77979347,0.00010253841,0.84582719,0.572243,0.1361951){1.2046527,1.5291243,1.5887874,1.0417018};
SS(-0.30949447,0.8262402,0.33528492,-0.3132159,0.69976014,0.5,-0.50037,0.79662088,0.5,-0.47185361,0.73769401,0.24072705){0.87388961,0.82050522,1.1183194,0.80384956};
SS(-0.37661764,-0.26006406,0.40868766,-0.49284988,-0.37485679,0.5,-0.56348952,-0.47594309,0.3052276,-0.34549718,-0.50098866,0.4105565){0.36234206,0.6163523,0.61776713,0.5260109};
SS(0.52843461,0.32737897,0.19102935,0.73568363,0.23203612,0.2735765,0.77315808,0.36766952,0.075951375,0.6902006,0.50015172,0.27072419){0.40790135,0.6509231,0.71793497,0.77938072};
SS(-0.58754442,0.033885734,-0.5,-0.62938155,0.17932964,-0.37445272,-0.69937066,0.31351533,-0.5,-0.82994199,0.18319278,-0.5){0.58180393,0.55109073,0.81965428,0.95993957};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.49997946,0.00010199173,0,-0.22019801,5.0496855e-05,-0.10133362,-0.40777162,0.1162396){0.14783141,0.22811872,0.029059683,0.17697987};
SS(1,0.50010355,-0.27968748,0.8988736,0.63809662,-0.070284173,0.75922048,0.56990614,-0.17060419,0.82562789,0.37565656,-0.12707714){1.3071084,1.2046527,0.91133836,0.82387041};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.34372617,0.39779568,-0.18541051,-0.24654336,0.57133462,-0.25396354,-0.096302334,0.43534175,-0.056072844){0.32892635,0.29650146,0.42991415,0.18078295};
SS(0.88049681,0.87960137,0.13412341,1,1,-6.9388939e-15,1,0.77979347,0.00010253841,0.78186447,1,3.3673518e-05){1.5518824,1.9807485,1.5887874,1.5923176};
SS(-0.043441254,0.79173928,0.29440137,-0.11111111,1,0.5,-0.014815866,1,0.31001515,-0.1827732,0.83017807,0.5){0.69563564,1.2487078,1.0772324,0.95598938};
SS(-0.17097214,0.64900986,-0.39927747,0.00024312215,0.80750011,-0.5,-0.14847812,0.78021305,-0.27623142,0.10162062,0.65400865,-0.37913628){0.59741335,0.88610119,0.68882385,0.5665506};
SS(0.50010751,0,-0.00013054911,0.46476684,0.14382827,0.12247557,0.70845584,0,0.20819814,0.63998586,0.17856447,0.051345521){0.22823279,0.23450402,0.52761363,0.42570365};
SS(-0.26056819,-0.54975154,-0.34323516,-0.32879066,-0.67072359,-0.5,-0.23055166,-0.37480907,-0.5,-0.14376826,-0.62489354,-0.5){0.46884495,0.79007105,0.41992239,0.6489606};
SS(-0.61115597,1,-0.10200355,-0.79370724,0.81084643,0.045877226,-0.74954172,1,0.13574231,-0.81095336,1,-0.07156149){1.3611038,1.270911,1.562759,1.6471359};
SS(0,0,0.25,-0.1159097,-0.14329028,0.19302206,0,-0.16137283,0.3386068,-0.20045203,0.067929244,0.29301468){0.045060365,0.055235283,0.12565914,0.10955402};
SS(-0.76546557,-0.72634686,-0.27513208,-0.65956212,-0.52273243,-0.19262862,-0.63348211,-0.7706683,-0.074889286,-0.49676106,-0.69523221,-0.26913048){1.1696133,0.7287475,0.97907785,0.78043195};
SS(-0.36340067,-0.87821042,-0.37678589,-0.49995867,-1,-0.27986665,-0.49676106,-0.69523221,-0.26913048,-0.6448883,-0.87343314,-0.36731947){1.0307746,1.3082069,0.78043195,1.296688};
SS(-1,-0.25140376,-0.1934451,-0.88905946,-0.098697315,-0.13184676,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027){1.0790534,0.8023886,1.0172898,0.68479998};
SS(-1,0.24865949,0.19540364,-0.83851866,0.33014205,0.32623765,-0.7489605,0.18190923,0.13647301,-0.79172217,0.43302343,0.13373134){1.0814407,0.89937894,0.59564173,0.80968993};
SS(0.25248643,0.73785598,-0.13082591,0.26064395,0.61953306,0.12890567,0.45788353,0.76094781,-0.0096633567,0.24404834,0.79519787,0.082231238){0.60350215,0.45328252,0.76853994,0.68472542};
SS(-0.64012388,-0.10177177,-0.37237302,-0.58754442,0.033885734,-0.5,-0.80728146,0.00010990719,-0.5,-0.62938155,0.17932964,-0.37445272){0.54269073,0.58180393,0.88195685,0.55109073};
SS(-0.4182056,0.11248126,-0.14182463,-0.49391083,0.27907498,-0.27264436,-0.39654734,0.26661646,0.019312696,-0.34372617,0.39779568,-0.18541051){0.19428145,0.37398026,0.20710489,0.29650146};
SS(-0.66548665,0.66585508,-0.5,-0.83248216,0.76782327,-0.31292259,-0.80479144,0.80504612,-0.5,-0.80558396,0.5878127,-0.29244037){1.1221664,1.366757,1.5255891,1.0616703};
SS(-0.20984637,0.69532212,0.20809493,-0.098708274,0.55956225,0.10505678,-0.035654771,0.78507762,0.045007896,-0.043441254,0.79173928,0.29440137){0.55022745,0.31633913,0.60161266,0.69563564};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.75,-0.5,0,-0.70830496,-0.20826096,0,-0.83851883,-0.33849865){0.53347202,0.79460868,0.5287181,0.80235204};
SS(-0.11614487,0.30919383,0.33918095,-0.26986228,0.26051837,0.22418657,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467){0.20820823,0.1749353,0.42001946,0.33969293};
SS(-0.50537844,-0.68762812,0.023695348,-0.49998858,-1,-4.7037318e-05,-0.42066299,-0.84356131,-0.12906413,-0.36608751,-0.8951802,0.074405883){0.71483247,1.2276085,0.88525127,0.92652515};
SS(1,0.77979347,0.00010253841,0.88049681,0.87960137,0.13412341,0.78186447,1,3.3673518e-05,0.77861211,0.77861193,-0.067175459){1.5887874,1.5518824,1.5923176,1.1981052};
SS(-0.41651431,0.41690828,-0.5,-0.39032311,0.63241857,-0.34621958,-0.31289368,0.69974287,-0.5,-0.4813337,0.60105459,-0.5){0.57523437,0.65630059,0.82323564,0.83133251};
SS(0.5,0,0.5,0.26083053,0.15082484,0.37728795,0.37501462,0.2307626,0.5,0.42621669,0.19017509,0.30505062){0.47735984,0.21918499,0.42590445,0.29714896};
SS(-0.12233239,-0.87748906,-0.13583418,-0.29168215,-1,-0.20844865,-0.22019153,-1,-0.00010416607,-0.42066299,-0.84356131,-0.12906413){0.78823805,1.1132023,1.0287732,0.88525127};
SS(-0.098708274,0.55956225,0.10505678,0.098704003,0.67249079,0.1943501,-0.035654771,0.78507762,0.045007896,-0.043441254,0.79173928,0.29440137){0.31633913,0.47957633,0.60161266,0.69563564};
SS(0,0,-6.9388939e-15,-0.10037172,0.18891947,0.20844359,0,0,0.25,0.13913358,0.10014326,0.18199659){-0.017891206,0.074828316,0.045060365,0.045990896};
SS(-0.70823063,-1,-0.20843533,-0.86742481,-0.86548068,-0.14483364,-0.63348211,-0.7706683,-0.074889286,-0.76546557,-0.72634686,-0.27513208){1.5240742,1.5085891,0.97907785,1.1696133};
SS(0.74440038,0.22095066,-0.087839409,1,0.2203628,5.6826691e-05,0.77315808,0.36766952,0.075951375,0.86971177,0.13024645,0.1427188){0.59875958,1.0268649,0.71793497,0.77797836};
SS(-0.24163432,0.33561251,-0.055881164,-0.26297351,0.20404986,-0.17122089,-0.12449617,0.36606215,-0.28273955,-0.056808231,0.14323286,-0.13367928){0.16437697,0.12773981,0.21185338,0.022140076};
SS(-1,0.11111111,0.5,-0.80727304,0.00024662976,0.5,-1,-0.11111111,0.5,-1,-0.00012222908,0.26646899){1.246301,0.88515177,1.2390062,1.0506696};
SS(-0.49998858,-1,-4.7037318e-05,-0.50537844,-0.68762812,0.023695348,-0.61978497,-0.82706917,0.12738472,-0.36608751,-0.8951802,0.074405883){1.2276085,0.71483247,1.0681409,0.92652515};
SS(-0.14850787,-0.69358405,-0.087583548,-0.35582611,-0.64426575,-0.070000747,-0.2399131,-0.76005145,-0.25989531,-0.42066299,-0.84356131,-0.12906413){0.49763432,0.52757348,0.6848256,0.88525127};
SS(-0.61674646,0.25215289,0.3447871,-0.58755791,0.033814853,0.5,-0.69937107,0.31347586,0.5,-0.72768327,0.10310141,0.33233484){0.54607287,0.57778723,0.8165723,0.63492881};
SS(-1,-0.00012222908,0.26646899,-0.91004595,0.15296589,0.33139812,-0.72768327,0.10310141,0.33233484,-0.84289574,0.018333867,0.1608607){1.0506696,0.94743142,0.63492881,0.72430843};
SS(0,-1,-6.9388939e-15,-0.12233239,-0.87748906,-0.13583418,-0.22019153,-1,-0.00010416607,0,-0.77970171,0.00010845427){0.98008605,0.78823805,1.0287732,0.58842154};
SS(-0.0089783977,0.64320989,-0.13441642,-0.14847812,0.78021305,-0.27623142,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.41358858,0.68882385,0.42991415,0.58653028};
SS(-0.84289574,0.018333867,0.1608607,-1,-0.00012222908,0.26646899,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){0.72430843,1.0506696,0.81273381,0.63492881};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.24887753,0.1953112,-0.73479965,-0.34302295,0.24038072,-0.82595855,-0.48031431,0.11444494){1.0133061,1.0768014,0.69668046,0.90887195};
SS(-0.62341011,-0.46880832,-0.38153973,-0.67495489,-0.6652659,-0.5,-0.78315651,-0.45008839,-0.5,-0.50036547,-0.57239096,-0.5){0.73807879,1.1276355,1.0467962,0.81333009};
SS(-1,-0.00012222908,0.26646899,-0.91004595,0.15296589,0.33139812,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){1.0506696,0.94743142,0.81273381,0.63492881};
SS(0.49866453,0.63973666,-0.21510859,0.69383766,0.49492178,-0.021800115,0.75922048,0.56990614,-0.17060419,0.61535375,0.70719289,-0.095218388){0.68344633,0.71284258,0.91133836,0.87858083};
SS(-1,0.49991607,0.0031934521,-0.8480722,0.62150313,0.12164012,-1,0.70725984,0.21334539,-1,0.4752276,0.27420758){1.2302733,1.1084494,1.5286486,1.2803563};
SS(0.29175541,0,0.20824909,0.46476684,0.14382827,0.12247557,0.25126435,0.28098512,0.24657435,0.42621669,0.19017509,0.30505062){0.1093371,0.23450402,0.18575023,0.29714896};
SS(-0.39654734,0.26661646,0.019312696,-0.39806707,0.15776443,0.15870839,-0.5555987,0.045150158,0.095162244,-0.4182056,0.11248126,-0.14182463){0.20710489,0.19317292,0.29993682,0.19428145};
SS(0.67125235,0.44297685,-0.31879306,0.671223,0.32907594,-0.5,0.77491511,0.22516452,-0.26425516,0.51910919,0.22553632,-0.31417891){0.72773009,0.79435762,0.70313431,0.40112301};
SS(-0.26297351,0.20404986,-0.17122089,-0.4182056,0.11248126,-0.14182463,-0.49391083,0.27907498,-0.27264436,-0.29413589,0.046284299,-0.31274881){0.12773981,0.19428145,0.37398026,0.1681493};
SS(1,0.50009037,3.487572e-05,0.8988736,0.63809662,-0.070284173,1,0.70844226,-0.20827687,1,0.50010355,-0.27968748){1.2275825,1.2046527,1.5310675,1.3071084};
SS(0.26064395,0.61953306,0.12890567,0.26138985,0.51848551,0.281015,0.086744979,0.52712982,0.027891324,0.18202227,0.38279251,0.10350409){0.45328252,0.40200156,0.26660844,0.17617817};
SS(0.50010751,0,-0.00013054911,0.57129187,0.13526053,-0.13726946,0.70841775,0,-0.20847891,0.50007058,0,-0.27987971){0.22823279,0.35115136,0.52293439,0.31006895};
SS(-0.12988976,-0.86995226,0.20452896,-0.16134158,-1,0.33850563,-0.18863677,-0.81113033,0.5,-0.349759,-0.84853211,0.35590634){0.79894991,1.129042,0.92459822,0.94981364};
SS(0.37137652,0.1767682,-0.19801193,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094,0.13402468,0.11673163,-0.1460819){0.19205628,0.027339551,0.16110593,0.039337265};
SS(-0.098708274,0.55956225,0.10505678,-0.0089783977,0.64320989,-0.13441642,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){0.31633913,0.41358858,0.58653028,0.60161266};
SS(0.50010751,0,-0.00013054911,0.63998586,0.17856447,0.051345521,0.70845584,0,0.20819814,0.77985819,0,-0.00014691753){0.22823279,0.42570365,0.52761363,0.58919206};
SS(0.8781758,0.86708556,-0.1989731,1,1,-0.25,1,0.70844226,-0.20827687,1,0.83864447,-0.33847614){1.5462283,2.0438315,1.5310675,1.8065101};
SS(0.094968532,0.84539386,-0.087484586,0.11136938,1,0.13859714,-0.035654771,0.78507762,0.045007896,0.24404834,0.79519787,0.082231238){0.71839764,1.0072058,0.60161266,0.68472542};
SS(-0.14850787,-0.69358405,-0.087583548,0,-0.49997946,0.00010199173,0,-0.70830496,-0.20826096,-0.18618058,-0.5161726,-0.15035515){0.49763432,0.22811872,0.5287181,0.30914003};
SS(0.81205362,0.80656044,-0.5,0.8781758,0.86708556,-0.1989731,1,0.83864447,-0.33847614,0.82865019,1,-0.3214153){1.5391707,1.5462283,1.8065101,1.7714679};
SS(-0.91347537,0.15552497,0.067511395,-1,-0.00021427218,0.00011802244,-1,0.29928494,0.0012550607,-0.89804207,0.11676539,-0.10792088){0.85045394,0.98080906,1.0718665,0.82300022};
SS(-0.36608751,-0.8951802,0.074405883,-0.49998858,-1,-4.7037318e-05,-0.29157863,-1,0.20827581,-0.4999534,-1,0.27968311){0.92652515,1.2276085,1.1139248,1.3075402};
SS(-0.7489605,0.18190923,0.13647301,-0.91347537,0.15552497,0.067511395,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){0.59564173,0.85045394,0.80968993,0.68151298};
SS(-0.82285362,-0.63420593,-0.0683896,-1,-0.5000565,0.0033661208,-1,-0.70523324,-0.21165758,-1,-0.77608598,0.00064487429){1.0691297,1.2263361,1.5222776,1.5844414};
SS(-0.59094649,-0.40495207,0.12834587,-0.50159539,-0.29258506,7.2987381e-06,-0.50874333,-0.23900991,0.2620444,-0.3727858,-0.19869367,0.11195566){0.51475101,0.32068114,0.36443271,0.16948569};
SS(-0.31377045,0.30492781,-0.36427962,-0.20381263,0.45499536,-0.5,-0.24654336,0.57133462,-0.25396354,-0.12449617,0.36606215,-0.28273955){0.30770932,0.478983,0.42991415,0.21185338};
SS(0,0,-0.25,0.13402468,0.11673163,-0.1460819,0.29173763,0,-0.20843742,0.16149165,0,-0.33864688){0.044304329,0.039337265,0.1134179,0.12746835};
SS(0.671223,0.32907594,-0.5,0.51910919,0.22553632,-0.31417891,0.6251418,0.1440922,-0.5,0.77491511,0.22516452,-0.26425516){0.79435762,0.40112301,0.63751638,0.70313431};
SS(-0.12233239,-0.87748906,-0.13583418,-0.22019153,-1,-0.00010416607,0,-0.77970171,0.00010845427,-0.22302806,-0.77703925,0.068353305){0.78823805,1.0287732,0.58842154,0.64063544};
SS(-0.1853821,-0.42358473,0.30866054,0,-0.49989758,0.27983937,-0.22656331,-0.68065623,0.28194433,-0.19247216,-0.56000521,0.088357129){0.29143101,0.30650831,0.57683818,0.34206231};
SS(-0.93582873,0.86427167,0.14668289,-1,1,-6.9388939e-15,-1,1,0.25,-0.74954172,1,0.13574231){1.6320629,1.9810426,2.0473025,1.562759};
SS(0.54326203,0.87223293,-0.356993,0.6657623,0.67544754,-0.5,0.81205362,0.80656044,-0.5,0.68900489,0.77311276,-0.28043733){1.1662147,1.1304562,1.5391707,1.1326816};
SS(0.40637652,0.87094343,0.13060843,0.23106485,1,0.31398279,0.24404834,0.79519787,0.082231238,0.22886345,0.79287946,0.30210005){0.92399337,1.1340577,0.68472542,0.75332396};
SS(-0.68637718,0.43295764,-0.18031685,-0.89426176,0.41257007,-0.12932618,-0.78848723,0.26584533,-0.068869999,-0.8068077,0.56885008,-0.063754108){0.67437813,0.974079,0.68151298,0.96112076};
SS(0.49866453,0.63973666,-0.21510859,0.34720309,0.90097601,-0.12745168,0.45788353,0.76094781,-0.0096633567,0.54700908,0.85955032,-0.16345766){0.68344633,0.93504792,0.76853994,1.0528061};
SS(0.59416595,0.14141347,0.32656529,0.75,0,0.5,0.83866368,0,0.33843958,0.81143387,0.18901581,0.5){0.46498444,0.79262349,0.80106313,0.9265446};
SS(0.098704003,0.67249079,0.1943501,0.10211023,0.6404511,0.38011645,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.47957633,0.55160362,0.33969293,0.27115576};
SS(0.37137652,0.1767682,-0.19801193,0.60662231,0.34516964,-0.13972301,0.36021608,0.23247759,-0.012351094,0.57129187,0.13526053,-0.13726946){0.19205628,0.48782847,0.16110593,0.35115136};
SS(0.83866368,0,0.33843958,0.59416595,0.14141347,0.32656529,0.81143387,0.18901581,0.5,0.73568363,0.23203612,0.2735765){0.80106313,0.46498444,0.9265446,0.6509231};
SS(-0.42066299,-0.84356131,-0.12906413,-0.49998858,-1,-4.7037318e-05,-0.29168215,-1,-0.20844865,-0.22019153,-1,-0.00010416607){0.88525127,1.2276085,1.1132023,1.0287732};
SS(-0.49998858,-1,-4.7037318e-05,-0.61978497,-0.82706917,0.12738472,-0.70832062,-1,0.2082538,-0.4999534,-1,0.27968311){1.2276085,1.0681409,1.5291125,1.3075402};
SS(-0.50537844,-0.68762812,0.023695348,-0.49998858,-1,-4.7037318e-05,-0.63348211,-0.7706683,-0.074889286,-0.42066299,-0.84356131,-0.12906413){0.71483247,1.2276085,0.97907785,0.88525127};
SS(0.59365279,0.65503723,0.24444947,0.65062064,0.64268786,0.069510863,0.6902006,0.50015172,0.27072419,0.52218723,0.46943947,0.022097553){0.82252715,0.82620698,0.77938072,0.46892029};
SS(-0.72768327,0.10310141,0.33233484,-0.58755791,0.033814853,0.5,-0.69937107,0.31347586,0.5,-0.83006559,0.18329805,0.5){0.63492881,0.57778723,0.8165723,0.96159482};
SS(-0.49998858,-1,-4.7037318e-05,-0.50537844,-0.68762812,0.023695348,-0.63348211,-0.7706683,-0.074889286,-0.61978497,-0.82706917,0.12738472){1.2276085,0.71483247,0.97907785,1.0681409};
SS(-0.32294154,0.86180803,0.13108841,-0.20984637,0.69532212,0.20809493,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){0.84829643,0.55022745,0.58653028,0.60161266};
SS(-0.23055166,-0.37480907,-0.5,-0.38492375,-0.20017574,-0.33650716,-0.36174,-0.40052234,-0.23665811,-0.1971424,-0.26981885,-0.30750196){0.41992239,0.28705324,0.32480953,0.19280289};
SS(-0.4182056,0.11248126,-0.14182463,-0.4720473,-0.063494476,-0.036829327,-0.28278924,0.041190137,-0.04219563,-0.5555987,0.045150158,0.095162244){0.19428145,0.21285629,0.063480395,0.29993682};
SS(-0.61115597,1,-0.10200355,-0.79370724,0.81084643,0.045877226,-0.81095336,1,-0.07156149,-0.74249217,0.75399014,-0.15399718){1.3611038,1.270911,1.6471359,1.1267767};
SS(0.6657623,0.67544754,-0.5,0.67125235,0.44297685,-0.31879306,0.78906409,0.5041626,-0.5,0.85153485,0.65148612,-0.35468846){1.1304562,0.72773009,1.1105402,1.2568282};
SS(0,-0.83845667,0.33864852,-0.12988976,-0.86995226,0.20452896,-0.18863677,-0.81113033,0.5,-0.22656331,-0.68065623,0.28194433){0.80178572,0.79894991,0.92459822,0.57683818};
SS(-0.45843014,-0.20445062,-0.15988901,-0.29237157,-0.11865629,-0.17606411,-0.4720473,-0.063494476,-0.036829327,-0.3727858,-0.19869367,0.11195566){0.26094507,0.11404163,0.21285629,0.16948569};
SS(-0.31377045,0.30492781,-0.36427962,-0.26297351,0.20404986,-0.17122089,-0.49391083,0.27907498,-0.27264436,-0.29413589,0.046284299,-0.31274881){0.30770932,0.12773981,0.37398026,0.1681493};
SS(-0.47185361,0.73769401,0.24072705,-0.60421932,0.82298164,0.34468578,-0.76389013,0.77728265,0.25513738,-0.67801153,0.56076489,0.29217382){0.80384956,1.1449713,1.2358334,0.83617727};
SS(0.8988736,0.63809662,-0.070284173,1,0.50009037,3.487572e-05,1,0.70834898,0.20844998,0.84582719,0.572243,0.1361951){1.2046527,1.2275825,1.5291243,1.0417018};
SS(0.33386283,0.81592026,-0.31808704,0.33333333,1,-0.5,0.21543771,0.73213875,-0.5,0.2222976,1,-0.35617554){0.86115027,1.342474,0.81134051,1.1585843};
SS(-0.15923414,-0.34171533,-0.15079999,0,-0.49997946,0.00010199173,0,-0.29164705,-0.20823955,0,-0.22019801,5.0496855e-05){0.14783141,0.22811872,0.11473247,0.029059683};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.5000565,0.0033661208,-1,-0.70523324,-0.21165758,-0.82285362,-0.63420593,-0.0683896){0.95161001,1.2263361,1.5222776,1.0691297};
SS(1,0.50009037,3.487572e-05,0.8988736,0.63809662,-0.070284173,1,0.70834898,0.20844998,1,0.77979347,0.00010253841){1.2275825,1.2046527,1.5291243,1.5887874};
SS(-0.62341011,-0.46880832,-0.38153973,-0.67495489,-0.6652659,-0.5,-0.50036547,-0.57239096,-0.5,-0.49676106,-0.69523221,-0.26913048){0.73807879,1.1276355,0.81333009,0.78043195};
SS(0.46476684,0.14382827,0.12247557,0.50010751,0,-0.00013054911,0.70845584,0,0.20819814,0.50011436,0,0.27961788){0.23450402,0.22823279,0.52761363,0.30940041};
SS(-0.88905946,-0.098697315,-0.13184676,-1,-0.00018427889,-0.26378916,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){0.8023886,1.0508045,0.65386325,0.85441326};
SS(0.098704003,0.67249079,0.1943501,0.26064395,0.61953306,0.12890567,0.26138985,0.51848551,0.281015,0.086744979,0.52712982,0.027891324){0.47957633,0.45328252,0.40200156,0.26660844};
SS(-0.54640726,0.34339216,0.19847863,-0.39806707,0.15776443,0.15870839,-0.52427834,0.10778268,0.27208728,-0.5555987,0.045150158,0.095162244){0.43575493,0.19317292,0.34448415,0.29993682};
SS(-0.01813809,0.53618118,-0.30537166,-0.0089783977,0.64320989,-0.13441642,-0.24654336,0.57133462,-0.25396354,-0.096302334,0.43534175,-0.056072844){0.36567785,0.41358858,0.42991415,0.18078295};
SS(-0.80728146,0.00010990719,-0.5,-1,0.11111111,-0.5,-1,-0.11111111,-0.5,-1,-0.00018427889,-0.26378916){0.88195685,1.2464205,1.2438655,1.0508045};
SS(-0.1159097,-0.14329028,0.19302206,-0.23583358,-0.36008743,0.0071767184,-0.25897908,-0.24013326,0.26450313,-0.20656092,-0.13938028,0.029547229){0.055235283,0.16465457,0.17775565,0.048278496};
SS(-0.19247216,-0.56000521,0.088357129,0,-0.7082575,0.2084616,0,-0.49989758,0.27983937,-0.22656331,-0.68065623,0.28194433){0.34206231,0.52387062,0.30650831,0.57683818};
SS(0.82562789,0.37565656,-0.12707714,1,0.50009037,3.487572e-05,1,0.2917639,-0.20827961,1,0.50010355,-0.27968748){0.82387041,1.2275825,1.1127834,1.3071084};
SS(-0.80632325,-0.81147186,-0.5,-0.76546557,-0.72634686,-0.27513208,-1,-0.83959635,-0.33115777,-0.83846289,-1,-0.33858677){1.5409894,1.1696133,1.7998257,1.8019179};
SS(-1,-0.47520831,0.27427507,-0.89646962,-0.32955067,0.34017365,-0.78327322,-0.45013966,0.5,-0.79575191,-0.55547687,0.30538166){1.2822693,1.0133061,1.0435491,1.0192798};
SS(-0.8068077,0.56885008,-0.063754108,-0.65776896,0.64141588,0.074371921,-0.79172217,0.43302343,0.13373134,-0.54631436,0.45612147,-0.00074796238){0.96112076,0.83514199,0.80968993,0.48593017};
SS(-1,-0.00012222908,0.26646899,-0.91004595,0.15296589,0.33139812,-0.80727304,0.00024662976,0.5,-0.84084014,-0.14895162,0.31636914){1.0506696,0.94743142,0.88515177,0.81273381};
SS(-0.61978497,-0.82706917,0.12738472,-0.49998858,-1,-4.7037318e-05,-0.70832062,-1,0.2082538,-0.77973152,-1,-0.0001062007){1.0681409,1.2276085,1.5291125,1.588155};
SS(0.60662231,0.34516964,-0.13972301,0.42864323,0.48543211,-0.13804456,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.48782847,0.42022283,0.72773009,0.55795418};
SS(-0.91347537,0.15552497,0.067511395,-1,0.24865949,0.19540364,-0.7489605,0.18190923,0.13647301,-0.79172217,0.43302343,0.13373134){0.85045394,1.0814407,0.59564173,0.80968993};
SS(-0.54640726,0.34339216,0.19847863,-0.41843781,0.30742585,0.3397996,-0.26986228,0.26051837,0.22418657,-0.35521568,0.4957142,0.26668635){0.43575493,0.37011438,0.1749353,0.42001946};
SS(-0.91414606,-0.68082467,-0.37109558,-0.67495489,-0.6652659,-0.5,-0.78315651,-0.45008839,-0.5,-0.81387526,-0.53653555,-0.3209601){1.4249306,1.1276355,1.0467962,1.0406635};
SS(0.68985253,1,-0.19792707,0.54326203,0.87223293,-0.356993,0.82865019,1,-0.3214153,0.68900489,0.77311276,-0.28043733){1.495304,1.1662147,1.7714679,1.1326816};
SS(0.50761134,0.34933779,0.39015973,0.52843461,0.32737897,0.19102935,0.6902006,0.50015172,0.27072419,0.73568363,0.23203612,0.2735765){0.51484928,0.40790135,0.77938072,0.6509231};
SS(1,0.50005385,0.27984222,0.87881231,0.64063264,0.37220388,0.6902006,0.50015172,0.27072419,0.87272604,0.35900693,0.37172569){1.3085441,1.3069719,0.77938072,1.0107603};
SS(1,0.50009037,3.487572e-05,0.82562789,0.37565656,-0.12707714,1,0.2917639,-0.20827961,1,0.2203628,5.6826691e-05){1.2275825,0.82387041,1.1127834,1.0268649};
SS(0.87272604,0.35900693,0.37172569,0.67112401,0.32933441,0.5,0.6902006,0.50015172,0.27072419,0.73568363,0.23203612,0.2735765){1.0107603,0.79210069,0.77938072,0.6509231};
SS(-0.65956212,-0.52273243,-0.19262862,-0.62341011,-0.46880832,-0.38153973,-0.76546557,-0.72634686,-0.27513208,-0.81387526,-0.53653555,-0.3209601){0.7287475,0.73807879,1.1696133,1.0406635};
SS(-0.56041637,1,-0.29784853,-0.58934795,0.84141567,-0.18062024,-0.65756371,0.81308934,-0.3429452,-0.35455825,0.80859576,-0.32177549){1.3856141,1.0736489,1.1958888,0.86460259};
SS(0.87867265,0.36391919,-0.37720578,0.75922048,0.56990614,-0.17060419,0.67125235,0.44297685,-0.31879306,0.82562789,0.37565656,-0.12707714){1.03034,0.91133836,0.72773009,0.82387041};
SS(-1,0.24865949,0.19540364,-0.91347537,0.15552497,0.067511395,-1,0.29928494,0.0012550607,-0.79172217,0.43302343,0.13373134){1.0814407,0.85045394,1.0718665,0.80968993};
SS(0,-0.7082575,0.2084616,-0.19247216,-0.56000521,0.088357129,0,-0.77970171,0.00010845427,-0.22302806,-0.77703925,0.068353305){0.52387062,0.34206231,0.58842154,0.64063544};
SS(-1,0.33333333,0.5,-0.91004595,0.15296589,0.33139812,-1,0.24865949,0.19540364,-0.83851866,0.33014205,0.32623765){1.3403692,0.94743142,1.0814407,0.89937894};
SS(-0.65776896,0.64141588,0.074371921,-0.62332411,0.59900263,-0.10904345,-0.74249217,0.75399014,-0.15399718,-0.48952189,0.78345034,0.019065462){0.83514199,0.74800561,1.1267767,0.83409809};
SS(0,-0.49997946,0.00010199173,-0.15923414,-0.34171533,-0.15079999,0,-0.29164705,-0.20823955,0,-0.49997234,-0.27965571){0.22811872,0.14783141,0.11473247,0.30906942};
SS(0.34720309,0.90097601,-0.12745168,0.49866453,0.63973666,-0.21510859,0.33386283,0.81592026,-0.31808704,0.54700908,0.85955032,-0.16345766){0.93504792,0.68344633,0.86115027,1.0528061};
SS(0.50761134,0.34933779,0.39015973,0.67112401,0.32933441,0.5,0.37501462,0.2307626,0.5,0.62515059,0.14422159,0.5){0.51484928,0.79210069,0.42590445,0.64726001};
SS(1,0.50009037,3.487572e-05,0.8988736,0.63809662,-0.070284173,1,0.50010355,-0.27968748,0.82562789,0.37565656,-0.12707714){1.2275825,1.2046527,1.3071084,0.82387041};
SS(-0.12988976,-0.86995226,0.20452896,-0.18863677,-0.81113033,0.5,-0.22656331,-0.68065623,0.28194433,-0.349759,-0.84853211,0.35590634){0.79894991,0.92459822,0.57683818,0.94981364};
SS(-0.54640726,0.34339216,0.19847863,-0.39806707,0.15776443,0.15870839,-0.5555987,0.045150158,0.095162244,-0.63246299,0.29145388,0.035195127){0.43575493,0.19317292,0.29993682,0.47226275};
SS(0.57129187,0.13526053,-0.13726946,0.70841775,0,-0.20847891,0.50007058,0,-0.27987971,0.51910919,0.22553632,-0.31417891){0.35115136,0.52293439,0.31006895,0.40112301};
SS(-0.91347537,0.15552497,0.067511395,-1,0.29928494,0.0012550607,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){0.85045394,1.0718665,0.80968993,0.68151298};
SS(-0.22223836,1,0.2622369,-0.30949447,0.8262402,0.33528492,-0.20984637,0.69532212,0.20809493,-0.043441254,0.79173928,0.29440137){1.0984067,0.87388961,0.55022745,0.69563564};
SS(0.69383766,0.49492178,-0.021800115,0.49866453,0.63973666,-0.21510859,0.52218723,0.46943947,0.022097553,0.61535375,0.70719289,-0.095218388){0.71284258,0.68344633,0.46892029,0.87858083};
SS(0.11458044,0.70010244,0.010073529,-0.0089783977,0.64320989,-0.13441642,0.25248643,0.73785598,-0.13082591,0.17777709,0.54047543,-0.2567554){0.49378055,0.41358858,0.60350215,0.36840304};
SS(-0.91004595,0.15296589,0.33139812,-0.80727304,0.00024662976,0.5,-0.84084014,-0.14895162,0.31636914,-0.72768327,0.10310141,0.33233484){0.94743142,0.88515177,0.81273381,0.63492881};
SS(0,-0.49997946,0.00010199173,-0.14850787,-0.69358405,-0.087583548,0,-0.77970171,0.00010845427,-0.19247216,-0.56000521,0.088357129){0.22811872,0.49763432,0.58842154,0.34206231};
SS(-0.41648151,0.41684878,0.5,-0.61674646,0.25215289,0.3447871,-0.69937107,0.31347586,0.5,-0.61509744,0.47589965,0.5){0.58097186,0.54607287,0.8165723,0.84259202};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.55555556,0.5,-1,-0.47520831,0.27427507,-0.78327322,-0.45013966,0.5){1.0133061,1.5359657,1.2822693,1.0435491};
SS(-0.4433427,0.53576375,-0.12560501,-0.32064519,0.49448821,1.4739833e-06,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.48429505,0.32892635,0.42991415,0.58653028};
SS(-0.62341011,-0.46880832,-0.38153973,-0.73174678,-0.21478859,-0.5,-0.7907607,-0.33838097,-0.28342271,-0.56113743,-0.28920115,-0.29204918){0.73807879,0.81151292,0.80149819,0.46850822};
SS(-0.61311838,0.85766427,0.15491279,-0.65776896,0.64141588,0.074371921,-0.47185361,0.73769401,0.24072705,-0.76389013,0.77728265,0.25513738){1.1216468,0.83514199,0.80384956,1.2358334};
SS(-0.79641575,0.50054117,0.5,-0.87046532,0.63071146,0.35630423,-0.67801153,0.56076489,0.29217382,-0.83851866,0.33014205,0.32623765){1.1180299,1.2666006,0.83617727,0.89937894};
SS(0,0,0.5,0,0,0.25,0,-0.16137283,0.3386068,-0.17669296,0.011023676,0.5){0.23153294,0.045060365,0.12565914,0.26322593};
SS(0.25,0,-0.5,0.35689191,0.091376279,-0.36932783,0.37532516,0.23078833,-0.5,0.20129651,0.21389912,-0.31902192){0.28810477,0.26145514,0.42551454,0.16839385};
SS(0.49866453,0.63973666,-0.21510859,0.69383766,0.49492178,-0.021800115,0.52218723,0.46943947,0.022097553,0.42864323,0.48543211,-0.13804456){0.68344633,0.71284258,0.46892029,0.42022283};
SS(0,0,0.5,0.050277172,0.20853018,0.30186362,0,0,0.25,-0.045146113,0.19012269,0.5){0.23153294,0.12181545,0.045060365,0.27176836};
SS(-0.15923414,-0.34171533,-0.15079999,-0.18618058,-0.5161726,-0.15035515,-0.26056819,-0.54975154,-0.34323516,-0.073421274,-0.375,-0.38984354){0.14783141,0.30914003,0.46884495,0.28201081};
SS(0.49866453,0.63973666,-0.21510859,0.34720309,0.90097601,-0.12745168,0.25248643,0.73785598,-0.13082591,0.45788353,0.76094781,-0.0096633567){0.68344633,0.93504792,0.60350215,0.76853994};
SS(0.16149165,0,-0.33864688,0,0,-0.5,0,0,-0.25,-0.010543702,0.17712261,-0.5){0.12746835,0.23465449,0.044304329,0.25750364};
SS(-0.10133362,-0.40777162,0.1162396,-0.23583358,-0.36008743,0.0071767184,-0.25897908,-0.24013326,0.26450313,-0.1159097,-0.14329028,0.19302206){0.17697987,0.16465457,0.17775565,0.055235283};
SS(0.37137652,0.1767682,-0.19801193,0.50010751,0,-0.00013054911,0.50007058,0,-0.27987971,0.57129187,0.13526053,-0.13726946){0.19205628,0.22823279,0.31006895,0.35115136};
SS(-0.033588837,0.5879061,0.5,-0.29261734,0.53193925,0.43339885,-0.3132159,0.69976014,0.5,-0.16015893,0.67694077,0.39025863){0.57806214,0.53993003,0.82050522,0.6265216};
SS(-0.1971424,-0.26981885,-0.30750196,-0.15923414,-0.34171533,-0.15079999,-0.26056819,-0.54975154,-0.34323516,-0.073421274,-0.375,-0.38984354){0.19280289,0.14783141,0.46884495,0.28201081};
SS(-1,-0.55555556,0.5,-0.79575191,-0.55547687,0.30538166,-1,-0.47520831,0.27427507,-0.78327322,-0.45013966,0.5){1.5359657,1.0192798,1.2822693,1.0435491};
SS(-0.70236545,-0.13062851,-0.19140485,-0.88905946,-0.098697315,-0.13184676,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){0.5265969,0.8023886,0.65386325,0.85441326};
SS(-0.39032311,0.63241857,-0.34621958,-0.41651431,0.41690828,-0.5,-0.31289368,0.69974287,-0.5,-0.20381263,0.45499536,-0.5){0.65630059,0.57523437,0.82323564,0.478983};
SS(0.10162062,0.65400865,-0.37913628,0.25248643,0.73785598,-0.13082591,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.5665506,0.60350215,0.36840304,0.59958408};
SS(-0.58934795,0.84141567,-0.18062024,-0.61115597,1,-0.10200355,-0.74249217,0.75399014,-0.15399718,-0.79370724,0.81084643,0.045877226){1.0736489,1.3611038,1.1267767,1.270911};
SS(-0.36992714,1,-0.22970445,-0.35455825,0.80859576,-0.32177549,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){1.1684568,0.86460259,1.1599423,0.68882385};
SS(-0.79370724,0.81084643,0.045877226,-1,0.77631186,0.00053339564,-0.74249217,0.75399014,-0.15399718,-0.8068077,0.56885008,-0.063754108){1.270911,1.5817554,1.1267767,0.96112076};
SS(-1,0.77777778,0.5,-0.87046532,0.63071146,0.35630423,-1,0.70725984,0.21334539,-1,0.84108515,0.33242406){1.8402752,1.2666006,1.5286486,1.8031397};
SS(0.59365279,0.65503723,0.24444947,0.39612945,0.70614162,0.21524614,0.55555177,0.82262944,0.31125158,0.40637652,0.87094343,0.13060843){0.82252715,0.68453461,1.0671623,0.92399337};
SS(-0.3548152,-0.48825703,0.21848985,-0.3533559,-0.49437708,0.037576204,-0.42889738,-0.75253072,0.17523232,-0.50537844,-0.68762812,0.023695348){0.38862106,0.35575629,0.75958282,0.71483247};
SS(-0.89426176,0.41257007,-0.12932618,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999,-0.8068077,0.56885008,-0.063754108){0.974079,0.80968993,0.68151298,0.96112076};
SS(-0.42889738,-0.75253072,0.17523232,-0.19247216,-0.56000521,0.088357129,-0.22656331,-0.68065623,0.28194433,-0.22302806,-0.77703925,0.068353305){0.75958282,0.34206231,0.57683818,0.64063544};
SS(0.10211023,0.6404511,0.38011645,0.098704003,0.67249079,0.1943501,-0.11618574,0.50328545,0.29980467,-0.043441254,0.79173928,0.29440137){0.55160362,0.47957633,0.33969293,0.69563564};
SS(-1,-0.11111111,-0.5,-0.85707128,-0.1416783,-0.34083416,-1,-0.33333333,-0.5,-0.73174678,-0.21478859,-0.5){1.2438655,0.85441326,1.3342594,0.81151292};
SS(0.0011150345,0.93517443,-0.37389303,0.11111111,1,-0.5,0.2222976,1,-0.35617554,0.081865095,0.80626877,-0.27867109){1.0026385,1.2422682,1.1585843,0.71703623};
SS(-0.67495489,-0.6652659,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.49676106,-0.69523221,-0.26913048,-0.76546557,-0.72634686,-0.27513208){1.1276355,1.296688,0.78043195,1.1696133};
SS(-0.30949447,0.8262402,0.33528492,-0.22223836,1,0.2622369,-0.1827732,0.83017807,0.5,-0.043441254,0.79173928,0.29440137){0.87388961,1.0984067,0.95598938,0.69563564};
SS(-0.61674646,0.25215289,0.3447871,-0.41648151,0.41684878,0.5,-0.69937107,0.31347586,0.5,-0.4543958,0.20406131,0.5){0.54607287,0.58097186,0.8165723,0.48353653};
SS(-0.49391083,0.27907498,-0.27264436,-0.4182056,0.11248126,-0.14182463,-0.49808619,0.0026201378,-0.26387206,-0.29413589,0.046284299,-0.31274881){0.37398026,0.19428145,0.29810596,0.1681493};
SS(-1,-0.24887753,0.1953112,-0.82279039,-0.18997945,0.10657137,-0.73479965,-0.34302295,0.24038072,-0.82595855,-0.48031431,0.11444494){1.0768014,0.70945047,0.69668046,0.90887195};
SS(0,0,0.25,-0.1159097,-0.14329028,0.19302206,0,-0.29157012,0.20836692,0,-0.16137283,0.3386068){0.045060365,0.055235283,0.11172813,0.12565914};
SS(0.8781758,0.86708556,-0.1989731,0.81205362,0.80656044,-0.5,1,0.83864447,-0.33847614,0.85153485,0.65148612,-0.35468846){1.5462283,1.5391707,1.8065101,1.2568282};
SS(-0.32879066,-0.67072359,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.2399131,-0.76005145,-0.25989531,-0.49676106,-0.69523221,-0.26913048){0.79007105,1.0307746,0.6848256,0.78043195};
SS(-0.47185361,0.73769401,0.24072705,-0.3132159,0.69976014,0.5,-0.50037,0.79662088,0.5,-0.48141868,0.60085372,0.5){0.80384956,0.82050522,1.1183194,0.82306978};
SS(0.34720309,0.90097601,-0.12745168,0.49866453,0.63973666,-0.21510859,0.25248643,0.73785598,-0.13082591,0.33386283,0.81592026,-0.31808704){0.93504792,0.68344633,0.60350215,0.86115027};
SS(-0.41843781,0.30742585,0.3397996,-0.24000819,0.17660305,0.5,-0.26986228,0.26051837,0.22418657,-0.11614487,0.30919383,0.33918095){0.37011438,0.3210912,0.1749353,0.20820823};
SS(-0.50537844,-0.68762812,0.023695348,-0.35582611,-0.64426575,-0.070000747,-0.49676106,-0.69523221,-0.26913048,-0.52487586,-0.5117405,-0.017639258){0.71483247,0.52757348,0.78043195,0.51812974};
SS(-0.62341011,-0.46880832,-0.38153973,-0.67495489,-0.6652659,-0.5,-0.76546557,-0.72634686,-0.27513208,-0.81387526,-0.53653555,-0.3209601){0.73807879,1.1276355,1.1696133,1.0406635};
SS(-0.73479965,-0.34302295,0.24038072,-0.65367362,-0.16081953,0.0014934597,-0.76760867,-0.33664988,-0.028298027,-0.82279039,-0.18997945,0.10657137){0.69668046,0.4344691,0.68479998,0.70945047};
SS(-0.77267892,0.13105707,-0.24874664,-0.65355936,0.25468043,-0.1897796,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.65386325,0.51379882,0.29810596,0.55109073};
SS(-0.18136176,0.40461939,0.5,-0.11614487,0.30919383,0.33918095,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.42386795,0.20820823,0.33969293,0.27115576};
SS(-0.63246299,0.29145388,0.035195127,-0.54640726,0.34339216,0.19847863,-0.52427834,0.10778268,0.27208728,-0.5555987,0.045150158,0.095162244){0.47226275,0.43575493,0.34448415,0.29993682};
SS(-0.056808231,0.14323286,-0.13367928,0,0,-6.9388939e-15,-0.28278924,0.041190137,-0.04219563,-0.098950987,-0.13391411,-0.14594667){0.022140076,-0.017891206,0.063480395,0.03512721};
SS(0.81205362,0.80656044,-0.5,0.8781758,0.86708556,-0.1989731,0.82865019,1,-0.3214153,0.68900489,0.77311276,-0.28043733){1.5391707,1.5462283,1.7714679,1.1326816};
SS(0.25248643,0.73785598,-0.13082591,0.10162062,0.65400865,-0.37913628,0.081865095,0.80626877,-0.27867109,0.33386283,0.81592026,-0.31808704){0.60350215,0.5665506,0.71703623,0.86115027};
SS(0.68966181,1,0.19790566,0.40637652,0.87094343,0.13060843,0.43654676,1,0.2604635,0.62860594,0.86645525,0.049037492){1.492557,0.92399337,1.2403655,1.1303867};
SS(-0.11614487,0.30919383,0.33918095,0.11523872,0.30161582,0.5,-0.18136176,0.40461939,0.5,-0.045146113,0.19012269,0.5){0.20820823,0.33546792,0.42386795,0.27176836};
SS(0.33333333,1,-0.5,0.11111111,1,-0.5,0.21543771,0.73213875,-0.5,0.2222976,1,-0.35617554){1.342474,1.2422682,0.81134051,1.1585843};
SS(-0.35582611,-0.64426575,-0.070000747,-0.3533559,-0.49437708,0.037576204,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.52757348,0.35575629,0.32480953,0.51812974};
SS(-0.18618058,-0.5161726,-0.15035515,0,-0.49997946,0.00010199173,0,-0.70830496,-0.20826096,0,-0.49997234,-0.27965571){0.30914003,0.22811872,0.5287181,0.30906942};
SS(-1,0.70529035,-0.21162945,-0.8068077,0.56885008,-0.063754108,-1,0.47527469,-0.27513051,-0.80558396,0.5878127,-0.29244037){1.520296,0.96112076,1.2834809,1.0616703};
SS(-0.41843781,0.30742585,0.3397996,-0.41648151,0.41684878,0.5,-0.24000819,0.17660305,0.5,-0.18136176,0.40461939,0.5){0.37011438,0.58097186,0.3210912,0.42386795};
SS(0.46476684,0.14382827,0.12247557,0.50010751,0,-0.00013054911,0.29175541,0,0.20824909,0.22032809,0,-9.1119885e-05){0.23450402,0.22823279,0.1093371,0.027339551};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.61549046,-0.35581383,-0.12962263,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.32068114,0.50877487,0.32480953,0.51812974};
SS(-0.1159097,-0.14329028,0.19302206,-0.34310942,-0.010167032,0.1509038,-0.25897908,-0.24013326,0.26450313,-0.20045203,0.067929244,0.29301468){0.055235283,0.12661586,0.17775565,0.10955402};
SS(-0.58258855,0.14037208,-0.067351147,-0.4182056,0.11248126,-0.14182463,-0.49391083,0.27907498,-0.27264436,-0.39654734,0.26661646,0.019312696){0.34532741,0.19428145,0.37398026,0.20710489};
SS(-0.63246299,0.29145388,0.035195127,-0.54640726,0.34339216,0.19847863,-0.7489605,0.18190923,0.13647301,-0.52427834,0.10778268,0.27208728){0.47226275,0.43575493,0.59564173,0.34448415};
SS(-0.73479965,-0.34302295,0.24038072,-0.59094649,-0.40495207,0.12834587,-0.76760867,-0.33664988,-0.028298027,-0.65367362,-0.16081953,0.0014934597){0.69668046,0.51475101,0.68479998,0.4344691};
SS(-0.39806707,0.15776443,0.15870839,-0.28278924,0.041190137,-0.04219563,-0.5555987,0.045150158,0.095162244,-0.4182056,0.11248126,-0.14182463){0.19317292,0.063480395,0.29993682,0.19428145};
SS(-0.35582611,-0.64426575,-0.070000747,-0.14850787,-0.69358405,-0.087583548,-0.22302806,-0.77703925,0.068353305,-0.42066299,-0.84356131,-0.12906413){0.52757348,0.49763432,0.64063544,0.88525127};
SS(1,0.50005385,0.27984222,0.87272604,0.35900693,0.37172569,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){1.3085441,1.0107603,0.77938072,1.0417018};
SS(-0.7489605,0.18190923,0.13647301,-0.63246299,0.29145388,0.035195127,-0.52427834,0.10778268,0.27208728,-0.5555987,0.045150158,0.095162244){0.59564173,0.47226275,0.34448415,0.29993682};
SS(-0.45563594,0.60375179,0.095527884,-0.2401666,0.74114092,-0.051302261,-0.48952189,0.78345034,0.019065462,-0.32294154,0.86180803,0.13108841){0.56263538,0.58653028,0.83409809,0.84829643};
SS(-0.47185361,0.73769401,0.24072705,-0.52470763,0.46530444,0.33754711,-0.67801153,0.56076489,0.29217382,-0.45563594,0.60375179,0.095527884){0.80384956,0.59371518,0.83617727,0.56263538};
SS(0.81205362,0.80656044,-0.5,0.8781758,0.86708556,-0.1989731,0.68900489,0.77311276,-0.28043733,0.85153485,0.65148612,-0.35468846){1.5391707,1.5462283,1.1326816,1.2568282};
SS(-1,-0.5000565,0.0033661208,-0.85520613,-0.46088631,-0.14784569,-1,-0.70523324,-0.21165758,-1,-0.47540235,-0.27521785){1.2263361,0.95161001,1.5222776,1.2841965};
SS(-0.3548152,-0.48825703,0.21848985,-0.37661764,-0.26006406,0.40868766,-0.25897908,-0.24013326,0.26450313,-0.50874333,-0.23900991,0.2620444){0.38862106,0.36234206,0.17775565,0.36443271};
SS(-0.42889738,-0.75253072,0.17523232,-0.63815223,-0.88141187,0.37488811,-0.57994589,-0.69256437,0.31204703,-0.61978497,-0.82706917,0.12738472){0.75958282,1.3088768,0.89957508,1.0681409};
SS(-0.62341011,-0.46880832,-0.38153973,-0.49292178,-0.37477565,-0.5,-0.73174678,-0.21478859,-0.5,-0.56113743,-0.28920115,-0.29204918){0.73807879,0.6115465,0.81151292,0.46850822};
SS(0.52843461,0.32737897,0.19102935,0.47723835,0.52605258,0.30619083,0.6902006,0.50015172,0.27072419,0.52218723,0.46943947,0.022097553){0.40790135,0.58228229,0.77938072,0.46892029};
SS(-0.6448883,-0.87343314,-0.36731947,-0.63348211,-0.7706683,-0.074889286,-0.49676106,-0.69523221,-0.26913048,-0.76546557,-0.72634686,-0.27513208){1.296688,0.97907785,0.78043195,1.1696133};
SS(0.11583535,0.30145324,-0.5,0.09693172,0.3918681,-0.3370861,-0.010543702,0.17712261,-0.5,0.20129651,0.21389912,-0.31902192){0.33954703,0.26256104,0.25750364,0.16839385};
SS(-0.35582611,-0.64426575,-0.070000747,-0.26056819,-0.54975154,-0.34323516,-0.36174,-0.40052234,-0.23665811,-0.18618058,-0.5161726,-0.15035515){0.52757348,0.46884495,0.32480953,0.30914003};
SS(-0.26297351,0.20404986,-0.17122089,-0.056808231,0.14323286,-0.13367928,-0.29413589,0.046284299,-0.31274881,-0.1182182,0.15955837,-0.3159857){0.12773981,0.022140076,0.1681493,0.11990198};
SS(1,1,-6.9388939e-15,0.77861211,0.77861193,-0.067175459,1,0.77979347,0.00010253841,0.78186447,1,3.3673518e-05){1.9807485,1.1981052,1.5887874,1.5923176};
SS(0.8781758,0.86708556,-0.1989731,1,1,-6.9388939e-15,0.78186447,1,3.3673518e-05,0.77861211,0.77861193,-0.067175459){1.5462283,1.9807485,1.5923176,1.1981052};
SS(1,1,-6.9388939e-15,0.8781758,0.86708556,-0.1989731,1,0.77979347,0.00010253841,0.77861211,0.77861193,-0.067175459){1.9807485,1.5462283,1.5887874,1.1981052};
SS(-0.87046532,0.63071146,0.35630423,-1,0.77777778,0.5,-1,0.55555556,0.5,-0.80481649,0.80494069,0.5){1.2666006,1.8402752,1.5401154,1.5232843};
SS(-1,0.70529035,-0.21162945,-0.89962374,0.8609561,-0.16698164,-1,0.77631186,0.00053339564,-0.74249217,0.75399014,-0.15399718){1.520296,1.5692753,1.5817554,1.1267767};
SS(-0.55555556,1,-0.5,-0.65756371,0.81308934,-0.3429452,-0.77777778,1,-0.5,-0.80479144,0.80504612,-0.5){1.5379273,1.1958888,1.8319852,1.5255891};
SS(-0.64012388,-0.10177177,-0.37237302,-0.77267892,0.13105707,-0.24874664,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.54269073,0.65386325,0.29810596,0.55109073};
SS(-0.34310942,-0.010167032,0.1509038,-0.40506391,-0.079541407,0.3303193,-0.25897908,-0.24013326,0.26450313,-0.20045203,0.067929244,0.29301468){0.12661586,0.26156128,0.17775565,0.10955402};
SS(-0.056808231,0.14323286,-0.13367928,0.08017426,0.31429474,-0.16745504,-0.12449617,0.36606215,-0.28273955,-0.096302334,0.43534175,-0.056072844){0.022140076,0.11103103,0.21185338,0.18078295};
SS(-0.22019153,-1,-0.00010416607,-0.36608751,-0.8951802,0.074405883,-0.22302806,-0.77703925,0.068353305,-0.42066299,-0.84356131,-0.12906413){1.0287732,0.92652515,0.64063544,0.88525127};
SS(0.75,0,-0.5,0.83867599,0,-0.33865964,0.81149777,0.18885984,-0.5,0.6251418,0.1440922,-0.5){0.79494611,0.80182539,0.92750237,0.63751638};
SS(0,-0.75,0.5,0,-0.83845667,0.33864852,-0.18863677,-0.81113033,0.5,-0.14394692,-0.62481063,0.5){0.79557901,0.80178572,0.92459822,0.63866347};
SS(0.87867265,0.36391919,-0.37720578,1,0.50010355,-0.27968748,0.75922048,0.56990614,-0.17060419,0.82562789,0.37565656,-0.12707714){1.03034,1.3071084,0.91133836,0.82387041};
SS(0.46476684,0.14382827,0.12247557,0.50010751,0,-0.00013054911,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094){0.23450402,0.22823279,0.027339551,0.16110593};
SS(0.64232771,0.84838332,0.46476191,0.68966181,1,0.19790566,0.76099919,0.76690574,0.25750996,0.55555177,0.82262944,0.31125158){1.3339184,1.492557,1.2143065,1.0671623};
SS(-0.056808231,0.14323286,-0.13367928,0,0,-0.25,-0.1182182,0.15955837,-0.3159857,0.13402468,0.11673163,-0.1460819){0.022140076,0.044304329,0.11990198,0.039337265};
SS(1,0.29178008,0.20838772,0.87272604,0.35900693,0.37172569,0.77315808,0.36766952,0.075951375,0.73568363,0.23203612,0.2735765){1.1084285,1.0107603,0.71793497,0.6509231};
SS(-0.6448883,-0.87343314,-0.36731947,-0.70823063,-1,-0.20843533,-0.63348211,-0.7706683,-0.074889286,-0.76546557,-0.72634686,-0.27513208){1.296688,1.5240742,0.97907785,1.1696133};
SS(0.21543771,0.73213875,-0.5,0.33386283,0.81592026,-0.31808704,0.2222976,1,-0.35617554,0.081865095,0.80626877,-0.27867109){0.81134051,0.86115027,1.1585843,0.71703623};
SS(0.37137652,0.1767682,-0.19801193,0.50010751,0,-0.00013054911,0.29173763,0,-0.20843742,0.50007058,0,-0.27987971){0.19205628,0.22823279,0.1134179,0.31006895};
SS(-1,-0.55555556,-0.5,-0.81387526,-0.53653555,-0.3209601,-0.78315651,-0.45008839,-0.5,-1,-0.47540235,-0.27521785){1.5366945,1.0406635,1.0467962,1.2841965};
SS(-0.61115597,1,-0.10200355,-0.61311838,0.85766427,0.15491279,-0.74954172,1,0.13574231,-0.79370724,0.81084643,0.045877226){1.3611038,1.1216468,1.562759,1.270911};
SS(0.69383766,0.49492178,-0.021800115,0.77315808,0.36766952,0.075951375,0.6902006,0.50015172,0.27072419,0.52218723,0.46943947,0.022097553){0.71284258,0.71793497,0.77938072,0.46892029};
SS(-0.50014045,0.79673357,-0.5,-0.35455825,0.80859576,-0.32177549,-0.56041637,1,-0.29784853,-0.65756371,0.81308934,-0.3429452){1.1145783,0.86460259,1.3856141,1.1958888};
SS(1,0.70834898,0.20844998,0.88049681,0.87960137,0.13412341,1,0.77979347,0.00010253841,0.84582719,0.572243,0.1361951){1.5291243,1.5518824,1.5887874,1.0417018};
SS(0.42864323,0.48543211,-0.13804456,0.49866453,0.63973666,-0.21510859,0.67125235,0.44297685,-0.31879306,0.48047723,0.47791267,-0.33071402){0.42022283,0.68344633,0.72773009,0.55795418};
SS(0.77315808,0.36766952,0.075951375,0.87272604,0.35900693,0.37172569,0.6902006,0.50015172,0.27072419,0.73568363,0.23203612,0.2735765){0.71793497,1.0107603,0.77938072,0.6509231};
SS(-0.29261734,0.53193925,0.43339885,-0.35521568,0.4957142,0.26668635,-0.11618574,0.50328545,0.29980467,-0.11614487,0.30919383,0.33918095){0.53993003,0.42001946,0.33969293,0.20820823};
SS(-0.65355936,0.25468043,-0.1897796,-0.58258855,0.14037208,-0.067351147,-0.77267892,0.13105707,-0.24874664,-0.49808619,0.0026201378,-0.26387206){0.51379882,0.34532741,0.65386325,0.29810596};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.59094649,-0.40495207,0.12834587,-0.50874333,-0.23900991,0.2620444,-0.65367362,-0.16081953,0.0014934597){0.32068114,0.51475101,0.36443271,0.4344691};
SS(0,-0.49997946,0.00010199173,-0.19247216,-0.56000521,0.088357129,0,-0.7082575,0.2084616,0,-0.49989758,0.27983937){0.22811872,0.34206231,0.52387062,0.30650831};
SS(-0.26056819,-0.54975154,-0.34323516,-0.32879066,-0.67072359,-0.5,-0.2399131,-0.76005145,-0.25989531,-0.49676106,-0.69523221,-0.26913048){0.46884495,0.79007105,0.6848256,0.78043195};
SS(0.59365279,0.65503723,0.24444947,0.65062064,0.64268786,0.069510863,0.45788353,0.76094781,-0.0096633567,0.62860594,0.86645525,0.049037492){0.82252715,0.82620698,0.76853994,1.1303867};
SS(-0.48255002,0.69900846,-0.19155417,-0.4433427,0.53576375,-0.12560501,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.74365966,0.48429505,0.42991415,0.58653028};
SS(0.37492492,0.49312259,0.5,0.35567295,0.65317229,0.39545235,0.45042372,0.78359022,0.5,0.5725222,0.50074158,0.5){0.61809871,0.69293227,1.0496179,0.8121357};
SS(0.25248643,0.73785598,-0.13082591,0.10162062,0.65400865,-0.37913628,0.33386283,0.81592026,-0.31808704,0.34412919,0.6158316,-0.3427703){0.60350215,0.5665506,0.86115027,0.59958408};
SS(-0.77777778,1,-0.5,-1,1,-0.5,-0.80479144,0.80504612,-0.5,-1,0.83964442,-0.3309874){1.8319852,2.2287589,1.5255891,1.7979585};
SS(-0.67616985,-0.069078192,0.18801024,-0.5555987,0.045150158,0.095162244,-0.52427834,0.10778268,0.27208728,-0.40506391,-0.079541407,0.3303193){0.47948004,0.29993682,0.34448415,0.26156128};
SS(-0.60421932,0.82298164,0.34468578,-0.55555556,1,0.5,-0.77777778,1,0.5,-0.80481649,0.80494069,0.5){1.1449713,1.5418081,1.8407438,1.5232843};
SS(0.49866453,0.63973666,-0.21510859,0.42864323,0.48543211,-0.13804456,0.25248643,0.73785598,-0.13082591,0.34412919,0.6158316,-0.3427703){0.68344633,0.42022283,0.60350215,0.59958408};
SS(-0.4999534,-1,0.27968311,-0.63815223,-0.88141187,0.37488811,-0.42889738,-0.75253072,0.17523232,-0.61978497,-0.82706917,0.12738472){1.3075402,1.3088768,0.75958282,1.0681409};
SS(-0.098708274,0.55956225,0.10505678,-0.20984637,0.69532212,0.20809493,-0.11618574,0.50328545,0.29980467,-0.043441254,0.79173928,0.29440137){0.31633913,0.55022745,0.33969293,0.69563564};
SS(0.34412919,0.6158316,-0.3427703,0.37549445,0.49317282,-0.5,0.21543771,0.73213875,-0.5,0.45062041,0.7833899,-0.5){0.59958408,0.61648995,0.81134051,1.0506853};
SS(0.64232771,0.84838332,0.46476191,0.68966181,1,0.19790566,0.82853688,1,0.32125076,0.76099919,0.76690574,0.25750996){1.3339184,1.492557,1.7703132,1.2143065};
SS(0.40637652,0.87094343,0.13060843,0.68966181,1,0.19790566,0.43654676,1,0.2604635,0.55555177,0.82262944,0.31125158){0.92399337,1.492557,1.2403655,1.0671623};
SS(-0.0073778212,0.36022468,0.15230712,0.18202227,0.38279251,0.10350409,0.086744979,0.52712982,0.027891324,0.085954007,0.41736025,0.32943097){0.13675819,0.17617817,0.26660844,0.27115576};
SS(-0.349759,-0.84853211,0.35590634,-0.42889738,-0.75253072,0.17523232,-0.22656331,-0.68065623,0.28194433,-0.22302806,-0.77703925,0.068353305){0.94981364,0.75958282,0.57683818,0.64063544};
SS(0.40637652,0.87094343,0.13060843,0.68966181,1,0.19790566,0.55555177,0.82262944,0.31125158,0.62860594,0.86645525,0.049037492){0.92399337,1.492557,1.0671623,1.1303867};
SS(0,-0.83845667,0.33864852,-0.22656331,-0.68065623,0.28194433,-0.18863677,-0.81113033,0.5,-0.14394692,-0.62481063,0.5){0.80178572,0.57683818,0.92459822,0.63866347};
SS(0.6902006,0.50015172,0.27072419,0.59365279,0.65503723,0.24444947,0.52218723,0.46943947,0.022097553,0.47723835,0.52605258,0.30619083){0.77938072,0.82252715,0.46892029,0.58228229};
SS(0.11136938,1,0.13859714,-0.043441254,0.79173928,0.29440137,-0.084253952,1,0.13733396,-0.035654771,0.78507762,0.045007896){1.0072058,0.69563564,1.0073117,0.60161266};
SS(-0.64012388,-0.10177177,-0.37237302,-0.70236545,-0.13062851,-0.19140485,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){0.54269073,0.5265969,0.65386325,0.85441326};
SS(0.8988736,0.63809662,-0.070284173,1,0.70844226,-0.20827687,0.75922048,0.56990614,-0.17060419,0.85153485,0.65148612,-0.35468846){1.2046527,1.5310675,0.91133836,1.2568282};
SS(-0.4433427,0.53576375,-0.12560501,-0.32064519,0.49448821,1.4739833e-06,-0.2401666,0.74114092,-0.051302261,-0.45563594,0.60375179,0.095527884){0.48429505,0.32892635,0.58653028,0.56263538};
SS(-0.12988976,-0.86995226,0.20452896,-0.22656331,-0.68065623,0.28194433,-0.22302806,-0.77703925,0.068353305,-0.349759,-0.84853211,0.35590634){0.79894991,0.57683818,0.64063544,0.94981364};
SS(0.24635331,0.35131343,-0.096025322,0.30434906,0.49798107,-4.0114635e-05,0.17777709,0.54047543,-0.2567554,0.086744979,0.52712982,0.027891324){0.18045455,0.32377482,0.36840304,0.26660844};
SS(-0.49292178,-0.37477565,-0.5,-0.62341011,-0.46880832,-0.38153973,-0.73174678,-0.21478859,-0.5,-0.78315651,-0.45008839,-0.5){0.6115465,0.73807879,0.81151292,1.0467962};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.11111111,0.5,-0.73174745,-0.21491043,0.5,-0.84084014,-0.14895162,0.31636914){1.0133061,1.2390062,0.81377033,0.81273381};
SS(0.18202227,0.38279251,0.10350409,0.098704003,0.67249079,0.1943501,0.26138985,0.51848551,0.281015,0.086744979,0.52712982,0.027891324){0.17617817,0.47957633,0.40200156,0.26660844};
SS(-1,-0.24887753,0.1953112,-0.84289574,0.018333867,0.1608607,-1,-0.00012222908,0.26646899,-0.82279039,-0.18997945,0.10657137){1.0768014,0.72430843,1.0506696,0.70945047};
SS(0.67112401,0.32933441,0.5,0.50761134,0.34933779,0.39015973,0.6902006,0.50015172,0.27072419,0.73568363,0.23203612,0.2735765){0.79210069,0.51484928,0.77938072,0.6509231};
SS(-0.3533559,-0.49437708,0.037576204,-0.3548152,-0.48825703,0.21848985,-0.23583358,-0.36008743,0.0071767184,-0.3727858,-0.19869367,0.11195566){0.35575629,0.38862106,0.16465457,0.16948569};
SS(0.75922048,0.56990614,-0.17060419,0.85153485,0.65148612,-0.35468846,0.68900489,0.77311276,-0.28043733,0.77861211,0.77861193,-0.067175459){0.91133836,1.2568282,1.1326816,1.1981052};
SS(-0.65776896,0.64141588,0.074371921,-0.67801153,0.56076489,0.29217382,-0.47185361,0.73769401,0.24072705,-0.76389013,0.77728265,0.25513738){0.83514199,0.83617727,0.80384956,1.2358334};
SS(0.68900489,0.77311276,-0.28043733,0.8781758,0.86708556,-0.1989731,0.77861211,0.77861193,-0.067175459,0.85153485,0.65148612,-0.35468846){1.1326816,1.5462283,1.1981052,1.2568282};
SS(0.09693172,0.3918681,-0.3370861,0.08017426,0.31429474,-0.16745504,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.26256104,0.11103103,0.21185338,0.11990198};
SS(-0.16707278,-0.087678023,-0.31121894,0,0,-0.25,-0.1182182,0.15955837,-0.3159857,-0.056808231,0.14323286,-0.13367928){0.11599041,0.044304329,0.11990198,0.022140076};
SS(-0.92571354,0.17249619,-0.34283108,-1,0.33333333,-0.5,-1,0.25105097,-0.19350143,-0.83127473,0.33505962,-0.32026923){0.99158484,1.3393331,1.0825888,0.89071695};
SS(0.11458044,0.70010244,0.010073529,-0.0089783977,0.64320989,-0.13441642,0.17777709,0.54047543,-0.2567554,0.086744979,0.52712982,0.027891324){0.49378055,0.41358858,0.36840304,0.26660844};
SS(-0.24163432,0.33561251,-0.055881164,-0.12449617,0.36606215,-0.28273955,-0.096302334,0.43534175,-0.056072844,-0.056808231,0.14323286,-0.13367928){0.16437697,0.21185338,0.18078295,0.022140076};
SS(-0.22302806,-0.77703925,0.068353305,0,-1,-6.9388939e-15,-0.22019153,-1,-0.00010416607,0,-0.77970171,0.00010845427){0.64063544,0.98008605,1.0287732,0.58842154};
SS(-0.38143574,0.84373572,-0.12387887,-0.36992714,1,-0.22970445,-0.14847812,0.78021305,-0.27623142,-0.35455825,0.80859576,-0.32177549){0.85864479,1.1684568,0.68882385,0.86460259};
SS(-0.40408872,0.18166381,-0.5,-0.31377045,0.30492781,-0.36427962,-0.49391083,0.27907498,-0.27264436,-0.29413589,0.046284299,-0.31274881){0.42526168,0.30770932,0.37398026,0.1681493};
SS(-0.222315,1,-0.00011890035,-0.32294154,0.86180803,0.13108841,-0.2401666,0.74114092,-0.051302261,-0.035654771,0.78507762,0.045007896){1.0307381,0.84829643,0.58653028,0.60161266};
SS(0.70841775,0,-0.20847891,0.57129187,0.13526053,-0.13726946,0.77491511,0.22516452,-0.26425516,0.51910919,0.22553632,-0.31417891){0.52293439,0.35115136,0.70313431,0.40112301};
SS(0,0,0.25,-0.20045203,0.067929244,0.29301468,0,-0.16137283,0.3386068,-0.17669296,0.011023676,0.5){0.045060365,0.10955402,0.12565914,0.26322593};
SS(-0.24654336,0.57133462,-0.25396354,-0.35455825,0.80859576,-0.32177549,-0.2401666,0.74114092,-0.051302261,-0.14847812,0.78021305,-0.27623142){0.42991415,0.86460259,0.58653028,0.68882385};
SS(-0.16643696,-0.21791406,0.42402077,0,-0.25,0.5,-0.23048975,-0.37484721,0.5,-0.1853821,-0.42358473,0.30866054){0.23818505,0.28720824,0.42714666,0.29143101};
SS(-0.12988976,-0.86995226,0.20452896,0,-1,-6.9388939e-15,-0.22019153,-1,-0.00010416607,-0.22302806,-0.77703925,0.068353305){0.79894991,0.98008605,1.0287732,0.64063544};
SS(0,-1,-6.9388939e-15,-0.12988976,-0.86995226,0.20452896,0,-0.77970171,0.00010845427,-0.22302806,-0.77703925,0.068353305){0.98008605,0.79894991,0.58842154,0.64063544};
SS(-0.83996275,-0.66999882,0.11765553,-1,-0.5000565,0.0033661208,-1,-0.47520831,0.27427507,-0.82595855,-0.48031431,0.11444494){1.1553131,1.2263361,1.2822693,0.90887195};
SS(0.60662231,0.34516964,-0.13972301,0.42864323,0.48543211,-0.13804456,0.36021608,0.23247759,-0.012351094,0.52218723,0.46943947,0.022097553){0.48782847,0.42022283,0.16110593,0.46892029};
SS(-0.16643696,-0.21791406,0.42402077,-0.30122568,-0.11513872,0.5,-0.17669296,0.011023676,0.5,-0.20045203,0.067929244,0.29301468){0.23818505,0.33848202,0.26322593,0.10955402};
SS(0.49866453,0.63973666,-0.21510859,0.61535375,0.70719289,-0.095218388,0.45788353,0.76094781,-0.0096633567,0.42864323,0.48543211,-0.13804456){0.68344633,0.87858083,0.76853994,0.42022283};
SS(-0.83996275,-0.66999882,0.11765553,-1,-0.5000565,0.0033661208,-1,-0.70710233,0.21356199,-1,-0.47520831,0.27427507){1.1553131,1.2263361,1.5280688,1.2822693};
SS(-0.014815866,1,0.31001515,-0.11111111,1,0.5,-0.22223836,1,0.2622369,-0.1827732,0.83017807,0.5){1.0772324,1.2487078,1.0984067,0.95598938};
SS(-0.79644003,0.50064951,-0.5,-1,0.55555556,-0.5,-1,0.33333333,-0.5,-1,0.47527469,-0.27513051){1.115532,1.5309384,1.3393331,1.2834809};
SS(-0.67616985,-0.069078192,0.18801024,-0.65367362,-0.16081953,0.0014934597,-0.73479965,-0.34302295,0.24038072,-0.82279039,-0.18997945,0.10657137){0.47948004,0.4344691,0.69668046,0.70945047};
SS(-0.1853821,-0.42358473,0.30866054,0,-0.49989758,0.27983937,-0.14394692,-0.62481063,0.5,-0.22656331,-0.68065623,0.28194433){0.29143101,0.30650831,0.63866347,0.57683818};
SS(0.40637652,0.87094343,0.13060843,0.11136938,1,0.13859714,0.24937941,1,-0.00011138016,0.24404834,0.79519787,0.082231238){0.92399337,1.0072058,1.0446566,0.68472542};
SS(-0.89646962,-0.32955067,0.34017365,-1,-0.11111111,0.5,-1,-0.33333333,0.5,-0.73174745,-0.21491043,0.5){1.0133061,1.2390062,1.3443603,0.81377033};
SS(0.33333333,1,0.5,0.36841015,0.87909734,0.37310922,0.11111111,1,0.5,0.23106485,1,0.31398279){1.3466764,1.0362544,1.2368521,1.1340577};
SS(0,0,-0.5,0.25,0,-0.5,-0.010543702,0.17712261,-0.5,0.16149165,0,-0.33864688){0.23465449,0.28810477,0.25750364,0.12746835};
SS(-0.349759,-0.84853211,0.35590634,-0.32897755,-0.67088709,0.5,-0.50400314,-0.78879927,0.5,-0.18863677,-0.81113033,0.5){0.94981364,0.79643001,1.1086821,0.92459822};
SS(0.4450496,1,-0.00012892076,0.40637652,0.87094343,0.13060843,0.43654676,1,0.2604635,0.24937941,1,-0.00011138016){1.179155,0.92399337,1.2403655,1.0446566};
SS(1,0.50009037,3.487572e-05,0.82562789,0.37565656,-0.12707714,1,0.2203628,5.6826691e-05,0.77315808,0.36766952,0.075951375){1.2275825,0.82387041,1.0268649,0.71793497};
SS(-0.16643696,-0.21791406,0.42402077,0,-0.25,0.5,0,-0.29157012,0.20836692,0,-0.16137283,0.3386068){0.23818505,0.28720824,0.11172813,0.12565914};
SS(-0.86742481,-0.86548068,-0.14483364,-1,-1,-0.25,-1,-0.70523324,-0.21165758,-1,-0.83959635,-0.33115777){1.5085891,2.0422973,1.5222776,1.7998257};
SS(-1,0.70529035,-0.21162945,-0.89426176,0.41257007,-0.12932618,-1,0.47527469,-0.27513051,-0.8068077,0.56885008,-0.063754108){1.520296,0.974079,1.2834809,0.96112076};
SS(0.098704003,0.67249079,0.1943501,-0.098708274,0.55956225,0.10505678,-0.11618574,0.50328545,0.29980467,-0.043441254,0.79173928,0.29440137){0.47957633,0.31633913,0.33969293,0.69563564};
SS(-1,0.55555556,0.5,-0.79641575,0.50054117,0.5,-1,0.33333333,0.5,-1,0.4752276,0.27420758){1.5401154,1.1180299,1.3403692,1.2803563};
SS(-0.67513028,-0.66529728,0.5,-0.79575191,-0.55547687,0.30538166,-0.78327322,-0.45013966,0.5,-0.56348952,-0.47594309,0.3052276){1.1284607,1.0192798,1.0435491,0.61776713};
SS(0.45788353,0.76094781,-0.0096633567,0.61535375,0.70719289,-0.095218388,0.52218723,0.46943947,0.022097553,0.42864323,0.48543211,-0.13804456){0.76853994,0.87858083,0.46892029,0.42022283};
SS(1,0.5,-0.5,0.85153485,0.65148612,-0.35468846,1,0.75,-0.5,0.78906409,0.5041626,-0.5){1.4840091,1.2568282,1.7924126,1.1105402};
SS(0,0,0.5,-0.16643696,-0.21791406,0.42402077,0,-0.25,0.5,-0.17669296,0.011023676,0.5){0.23153294,0.23818505,0.28720824,0.26322593};
SS(-1,0.33333333,0.5,-0.83851866,0.33014205,0.32623765,-1,0.24865949,0.19540364,-1,0.4752276,0.27420758){1.3403692,0.89937894,1.0814407,1.2803563};
SS(-0.50014045,0.79673357,-0.5,-0.39032311,0.63241857,-0.34621958,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){1.1145783,0.65630059,1.1958888,0.87296464};
SS(-0.78315651,-0.45008839,-0.5,-1,-0.33333333,-0.5,-1,-0.55555556,-0.5,-1,-0.47540235,-0.27521785){1.0467962,1.3342594,1.5366945,1.2841965};
SS(-0.2399131,-0.76005145,-0.25989531,-0.14850787,-0.69358405,-0.087583548,-0.42066299,-0.84356131,-0.12906413,-0.12233239,-0.87748906,-0.13583418){0.6848256,0.49763432,0.88525127,0.78823805};
SS(-0.19247216,-0.56000521,0.088357129,-0.3548152,-0.48825703,0.21848985,-0.42889738,-0.75253072,0.17523232,-0.22656331,-0.68065623,0.28194433){0.34206231,0.38862106,0.75958282,0.57683818};
SS(-1,0.33333333,-0.5,-0.83127473,0.33505962,-0.32026923,-0.79644003,0.50064951,-0.5,-1,0.47527469,-0.27513051){1.3393331,0.89071695,1.115532,1.2834809};
SS(-1,0.33333333,0.5,-0.83851866,0.33014205,0.32623765,-1,0.4752276,0.27420758,-0.79641575,0.50054117,0.5){1.3403692,0.89937894,1.2803563,1.1180299};
SS(-0.39032311,0.63241857,-0.34621958,-0.48255002,0.69900846,-0.19155417,-0.65756371,0.81308934,-0.3429452,-0.6293812,0.63993291,-0.28812602){0.65630059,0.74365966,1.1958888,0.87296464};
SS(-0.3548152,-0.48825703,0.21848985,-0.37661764,-0.26006406,0.40868766,-0.56348952,-0.47594309,0.3052276,-0.34549718,-0.50098866,0.4105565){0.38862106,0.36234206,0.61776713,0.5260109};
SS(-0.3132159,0.69976014,0.5,-0.29261734,0.53193925,0.43339885,-0.30949447,0.8262402,0.33528492,-0.16015893,0.67694077,0.39025863){0.82050522,0.53993003,0.87388961,0.6265216};
SS(-1,-1,0.25,-0.8827276,-0.88146034,0.13123348,-1,-0.70710233,0.21356199,-1,-0.84092895,0.33252059){2.0427074,1.5595365,1.5280688,1.8030746};
SS(-0.61978497,-0.82706917,0.12738472,-0.49998858,-1,-4.7037318e-05,-0.77973152,-1,-0.0001062007,-0.63348211,-0.7706683,-0.074889286){1.0681409,1.2276085,1.588155,0.97907785};
SS(-0.033588837,0.5879061,0.5,0.085954007,0.41736025,0.32943097,-0.18136176,0.40461939,0.5,-0.11618574,0.50328545,0.29980467){0.57806214,0.27115576,0.42386795,0.33969293};
SS(-0.64012388,-0.10177177,-0.37237302,-0.80728146,0.00010990719,-0.5,-0.77267892,0.13105707,-0.24874664,-0.62938155,0.17932964,-0.37445272){0.54269073,0.88195685,0.65386325,0.55109073};
SS(0.52218723,0.46943947,0.022097553,0.52843461,0.32737897,0.19102935,0.77315808,0.36766952,0.075951375,0.6902006,0.50015172,0.27072419){0.46892029,0.40790135,0.71793497,0.77938072};
SS(-0.39032311,0.63241857,-0.34621958,-0.50014045,0.79673357,-0.5,-0.48255002,0.69900846,-0.19155417,-0.35455825,0.80859576,-0.32177549){0.65630059,1.1145783,0.74365966,0.86460259};
SS(-0.088882135,1,-0.23281641,-0.10743676,0.85847111,-0.11136175,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){1.0431215,0.7462212,1.1599423,0.68882385};
SS(0,-0.49997946,0.00010199173,-0.10133362,-0.40777162,0.1162396,0,-0.29157012,0.20836692,0,-0.22019801,5.0496855e-05){0.22811872,0.17697987,0.11172813,0.029059683};
SS(-0.83127473,0.33505962,-0.32026923,-1,0.33333333,-0.5,-1,0.25105097,-0.19350143,-1,0.47527469,-0.27513051){0.89071695,1.3393331,1.0825888,1.2834809};
SS(-0.32294154,0.86180803,0.13108841,-0.222315,1,-0.00011890035,-0.084253952,1,0.13733396,-0.035654771,0.78507762,0.045007896){0.84829643,1.0307381,1.0073117,0.60161266};
SS(-0.10743676,0.85847111,-0.11136175,-0.222315,1,-0.00011890035,-0.36992714,1,-0.22970445,-0.38143574,0.84373572,-0.12387887){0.7462212,1.0307381,1.1684568,0.85864479};
SS(-0.29157863,-1,0.20827581,-0.36608751,-0.8951802,0.074405883,-0.349759,-0.84853211,0.35590634,-0.12988976,-0.86995226,0.20452896){1.1139248,0.92652515,0.94981364,0.79894991};
SS(-0.3727858,-0.19869367,0.11195566,-0.3548152,-0.48825703,0.21848985,-0.25897908,-0.24013326,0.26450313,-0.50874333,-0.23900991,0.2620444){0.16948569,0.38862106,0.17775565,0.36443271};
SS(-0.35582611,-0.64426575,-0.070000747,-0.4581749,-0.5263483,-0.32801665,-0.36174,-0.40052234,-0.23665811,-0.26056819,-0.54975154,-0.34323516){0.52757348,0.57811658,0.32480953,0.46884495};
SS(0.52843461,0.32737897,0.19102935,0.52218723,0.46943947,0.022097553,0.77315808,0.36766952,0.075951375,0.63998586,0.17856447,0.051345521){0.40790135,0.46892029,0.71793497,0.42570365};
SS(-0.42889738,-0.75253072,0.17523232,-0.36608751,-0.8951802,0.074405883,-0.22302806,-0.77703925,0.068353305,-0.349759,-0.84853211,0.35590634){0.75958282,0.92652515,0.64063544,0.94981364};
SS(0.09693172,0.3918681,-0.3370861,-0.029932551,0.40748663,-0.5,-0.010543702,0.17712261,-0.5,-0.12449617,0.36606215,-0.28273955){0.26256104,0.4038008,0.25750364,0.21185338};
SS(-0.54640726,0.34339216,0.19847863,-0.52470763,0.46530444,0.33754711,-0.67801153,0.56076489,0.29217382,-0.61674646,0.25215289,0.3447871){0.43575493,0.59371518,0.83617727,0.54607287};
SS(-0.22223836,1,0.2622369,-0.043441254,0.79173928,0.29440137,-0.014815866,1,0.31001515,-0.1827732,0.83017807,0.5){1.0984067,0.69563564,1.0772324,0.95598938};
SS(-1,0.49991607,0.0031934521,-0.8068077,0.56885008,-0.063754108,-1,0.70529035,-0.21162945,-1,0.77631186,0.00053339564){1.2302733,0.96112076,1.520296,1.5817554};
SS(-0.35455825,0.80859576,-0.32177549,-0.50014045,0.79673357,-0.5,-0.48255002,0.69900846,-0.19155417,-0.65756371,0.81308934,-0.3429452){0.86460259,1.1145783,0.74365966,1.1958888};
SS(0.66554141,0.67524133,0.5,0.59365279,0.65503723,0.24444947,0.76099919,0.76690574,0.25750996,0.6902006,0.50015172,0.27072419){1.1271263,0.82252715,1.2143065,0.77938072};
SS(0.8781758,0.86708556,-0.1989731,0.75922048,0.56990614,-0.17060419,0.77861211,0.77861193,-0.067175459,0.85153485,0.65148612,-0.35468846){1.5462283,0.91133836,1.1981052,1.2568282};
SS(-0.3548152,-0.48825703,0.21848985,-0.10133362,-0.40777162,0.1162396,-0.23583358,-0.36008743,0.0071767184,-0.1853821,-0.42358473,0.30866054){0.38862106,0.17697987,0.16465457,0.29143101};
SS(0.87881231,0.64063264,0.37220388,0.66554141,0.67524133,0.5,0.76099919,0.76690574,0.25750996,0.6902006,0.50015172,0.27072419){1.3069719,1.1271263,1.2143065,0.77938072};
SS(-0.23583358,-0.36008743,0.0071767184,-0.10133362,-0.40777162,0.1162396,-0.25897908,-0.24013326,0.26450313,-0.1853821,-0.42358473,0.30866054){0.16465457,0.17697987,0.17775565,0.29143101};
SS(0.8988736,0.63809662,-0.070284173,1,0.70844226,-0.20827687,0.85153485,0.65148612,-0.35468846,0.8781758,0.86708556,-0.1989731){1.2046527,1.5310675,1.2568282,1.5462283};
SS(0.08017426,0.31429474,-0.16745504,-0.056808231,0.14323286,-0.13367928,-0.13709741,0.19518884,0.034033465,-0.096302334,0.43534175,-0.056072844){0.11103103,0.022140076,0.040184006,0.18078295};
SS(0.8988736,0.63809662,-0.070284173,0.75922048,0.56990614,-0.17060419,0.77861211,0.77861193,-0.067175459,0.8781758,0.86708556,-0.1989731){1.2046527,0.91133836,1.1981052,1.5462283};
SS(-0.36608751,-0.8951802,0.074405883,-0.22302806,-0.77703925,0.068353305,-0.349759,-0.84853211,0.35590634,-0.12988976,-0.86995226,0.20452896){0.92652515,0.64063544,0.94981364,0.79894991};
SS(0.26064395,0.61953306,0.12890567,0.30434906,0.49798107,-4.0114635e-05,0.25248643,0.73785598,-0.13082591,0.45788353,0.76094781,-0.0096633567){0.45328252,0.32377482,0.60350215,0.76853994};
SS(-0.8068077,0.56885008,-0.063754108,-1,0.70529035,-0.21162945,-1,0.77631186,0.00053339564,-0.74249217,0.75399014,-0.15399718){0.96112076,1.520296,1.5817554,1.1267767};
SS(0.75922048,0.56990614,-0.17060419,0.8988736,0.63809662,-0.070284173,0.85153485,0.65148612,-0.35468846,0.8781758,0.86708556,-0.1989731){0.91133836,1.2046527,1.2568282,1.5462283};
SS(-0.3548152,-0.48825703,0.21848985,-0.1853821,-0.42358473,0.30866054,-0.23583358,-0.36008743,0.0071767184,-0.25897908,-0.24013326,0.26450313){0.38862106,0.29143101,0.16465457,0.17775565};
SS(0,0,0.25,-0.20045203,0.067929244,0.29301468,-0.045146113,0.19012269,0.5,0.050277172,0.20853018,0.30186362){0.045060365,0.10955402,0.27176836,0.12181545};
SS(-0.45563594,0.60375179,0.095527884,-0.32064519,0.49448821,1.4739833e-06,-0.20984637,0.69532212,0.20809493,-0.35521568,0.4957142,0.26668635){0.56263538,0.32892635,0.55022745,0.42001946};
SS(0.59365279,0.65503723,0.24444947,0.55555177,0.82262944,0.31125158,0.62860594,0.86645525,0.049037492,0.40637652,0.87094343,0.13060843){0.82252715,1.0671623,1.1303867,0.92399337};
SS(-0.50014045,0.79673357,-0.5,-0.39032311,0.63241857,-0.34621958,-0.48255002,0.69900846,-0.19155417,-0.65756371,0.81308934,-0.3429452){1.1145783,0.65630059,0.74365966,1.1958888};
SS(-0.70236545,-0.13062851,-0.19140485,-0.64012388,-0.10177177,-0.37237302,-0.77267892,0.13105707,-0.24874664,-0.49808619,0.0026201378,-0.26387206){0.5265969,0.54269073,0.65386325,0.29810596};
SS(0.76099919,0.76690574,0.25750996,0.88049681,0.87960137,0.13412341,0.77861211,0.77861193,-0.067175459,0.62860594,0.86645525,0.049037492){1.2143065,1.5518824,1.1981052,1.1303867};
SS(-0.68637718,0.43295764,-0.18031685,-0.4433427,0.53576375,-0.12560501,-0.49391083,0.27907498,-0.27264436,-0.50782983,0.50249565,-0.29902586){0.67437813,0.48429505,0.37398026,0.58612549};
SS(0.52843461,0.32737897,0.19102935,0.46476684,0.14382827,0.12247557,0.36021608,0.23247759,-0.012351094,0.63998586,0.17856447,0.051345521){0.40790135,0.23450402,0.16110593,0.42570365};
SS(-0.3533559,-0.49437708,0.037576204,-0.3548152,-0.48825703,0.21848985,-0.42889738,-0.75253072,0.17523232,-0.19247216,-0.56000521,0.088357129){0.35575629,0.38862106,0.75958282,0.34206231};
SS(0.11111111,1,-0.5,0.0011150345,0.93517443,-0.37389303,0.00024312215,0.80750011,-0.5,0.081865095,0.80626877,-0.27867109){1.2422682,1.0026385,0.88610119,0.71703623};
SS(-0.10037172,0.18891947,0.20844359,0,0,-6.9388939e-15,-0.13709741,0.19518884,0.034033465,0.13261259,0.21336316,0.036566127){0.074828316,-0.017891206,0.040184006,0.046199082};
SS(-0.20045203,0.067929244,0.29301468,0,0,0.5,0,0,0.25,-0.045146113,0.19012269,0.5){0.10955402,0.23153294,0.045060365,0.27176836};
SS(-0.88905946,-0.098697315,-0.13184676,-0.70236545,-0.13062851,-0.19140485,-0.76760867,-0.33664988,-0.028298027,-0.7907607,-0.33838097,-0.28342271){0.8023886,0.5265969,0.68479998,0.80149819};
SS(-0.3727858,-0.19869367,0.11195566,-0.50159539,-0.29258506,7.2987381e-06,-0.50874333,-0.23900991,0.2620444,-0.65367362,-0.16081953,0.0014934597){0.16948569,0.32068114,0.36443271,0.4344691};
SS(-0.10037172,0.18891947,0.20844359,0,0,-6.9388939e-15,0.13261259,0.21336316,0.036566127,0.13913358,0.10014326,0.18199659){0.074828316,-0.017891206,0.046199082,0.045990896};
SS(0.26083053,0.15082484,0.37728795,0.5,0,0.5,0.50011436,0,0.27961788,0.42621669,0.19017509,0.30505062){0.21918499,0.47735984,0.30940041,0.29714896};
SS(0.87867265,0.36391919,-0.37720578,0.75922048,0.56990614,-0.17060419,0.85153485,0.65148612,-0.35468846,0.67125235,0.44297685,-0.31879306){1.03034,0.91133836,1.2568282,0.72773009};
SS(1,0.50010355,-0.27968748,0.87867265,0.36391919,-0.37720578,0.75922048,0.56990614,-0.17060419,0.85153485,0.65148612,-0.35468846){1.3071084,1.03034,0.91133836,1.2568282};
SS(-0.3533559,-0.49437708,0.037576204,-0.50159539,-0.29258506,7.2987381e-06,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.35575629,0.32068114,0.32480953,0.51812974};
SS(-0.4581749,-0.5263483,-0.32801665,-0.35582611,-0.64426575,-0.070000747,-0.49676106,-0.69523221,-0.26913048,-0.26056819,-0.54975154,-0.34323516){0.57811658,0.52757348,0.78043195,0.46884495};
SS(-0.19461387,0.3919517,0.10437587,-0.32064519,0.49448821,1.4739833e-06,-0.35521568,0.4957142,0.26668635,-0.098708274,0.55956225,0.10505678){0.19075448,0.32892635,0.42001946,0.31633913};
SS(-0.3727858,-0.19869367,0.11195566,-0.3548152,-0.48825703,0.21848985,-0.23583358,-0.36008743,0.0071767184,-0.25897908,-0.24013326,0.26450313){0.16948569,0.38862106,0.16465457,0.17775565};
SS(0.81149777,0.18885984,-0.5,0.77491511,0.22516452,-0.26425516,0.6251418,0.1440922,-0.5,0.83867599,0,-0.33865964){0.92750237,0.70313431,0.63751638,0.80182539};
SS(-0.40408872,0.18166381,-0.5,-0.41767704,0.010770256,-0.44072823,-0.49391083,0.27907498,-0.27264436,-0.62938155,0.17932964,-0.37445272){0.42526168,0.35514259,0.37398026,0.55109073};
SS(0.67112401,0.32933441,0.5,0.87272604,0.35900693,0.37172569,0.78912399,0.50423732,0.5,0.81143387,0.18901581,0.5){0.79210069,1.0107603,1.1096027,0.9265446};
SS(0.40637652,0.87094343,0.13060843,0.4450496,1,-0.00012892076,0.43654676,1,0.2604635,0.62860594,0.86645525,0.049037492){0.92399337,1.179155,1.2403655,1.1303867};
SS(-0.3548152,-0.48825703,0.21848985,-0.37661764,-0.26006406,0.40868766,-0.50874333,-0.23900991,0.2620444,-0.56348952,-0.47594309,0.3052276){0.38862106,0.36234206,0.36443271,0.61776713};
SS(0.65062064,0.64268786,0.069510863,0.62860594,0.86645525,0.049037492,0.76099919,0.76690574,0.25750996,0.77861211,0.77861193,-0.067175459){0.82620698,1.1303867,1.2143065,1.1981052};
SS(-0.24000819,0.17660305,0.5,-0.41843781,0.30742585,0.3397996,-0.18136176,0.40461939,0.5,-0.11614487,0.30919383,0.33918095){0.3210912,0.37011438,0.42386795,0.20820823};
SS(-0.49292178,-0.37477565,-0.5,-0.38492375,-0.20017574,-0.33650716,-0.30131805,-0.11512588,-0.5,-0.50815189,-0.16301678,-0.5){0.6115465,0.28705324,0.3368451,0.52110597};
SS(-0.80558396,0.5878127,-0.29244037,-0.66548665,0.66585508,-0.5,-0.79644003,0.50064951,-0.5,-0.80479144,0.80504612,-0.5){1.0616703,1.1221664,1.115532,1.5255891};
SS(0.52218723,0.46943947,0.022097553,0.60662231,0.34516964,-0.13972301,0.77315808,0.36766952,0.075951375,0.63998586,0.17856447,0.051345521){0.46892029,0.48782847,0.71793497,0.42570365};
SS(-0.222315,1,-0.00011890035,-0.10743676,0.85847111,-0.11136175,-0.36992714,1,-0.22970445,-0.088882135,1,-0.23281641){1.0307381,0.7462212,1.1684568,1.0431215};
SS(-0.19247216,-0.56000521,0.088357129,0,-0.49997946,0.00010199173,0,-0.7082575,0.2084616,0,-0.77970171,0.00010845427){0.34206231,0.22811872,0.52387062,0.58842154};
SS(-0.80728146,0.00010990719,-0.5,-0.64012388,-0.10177177,-0.37237302,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){0.88195685,0.54269073,0.65386325,0.85441326};
SS(-0.89426176,0.41257007,-0.12932618,-1,0.49991607,0.0031934521,-1,0.70529035,-0.21162945,-1,0.47527469,-0.27513051){0.974079,1.2302733,1.520296,1.2834809};
SS(-0.48141868,0.60085372,0.5,-0.52470763,0.46530444,0.33754711,-0.47185361,0.73769401,0.24072705,-0.35521568,0.4957142,0.26668635){0.82306978,0.59371518,0.80384956,0.42001946};
SS(-0.49391083,0.27907498,-0.27264436,-0.41767704,0.010770256,-0.44072823,-0.49808619,0.0026201378,-0.26387206,-0.62938155,0.17932964,-0.37445272){0.37398026,0.35514259,0.29810596,0.55109073};
SS(-0.42889738,-0.75253072,0.17523232,-0.63815223,-0.88141187,0.37488811,-0.349759,-0.84853211,0.35590634,-0.57994589,-0.69256437,0.31204703){0.75958282,1.3088768,0.94981364,0.89957508};
SS(-0.63815223,-0.88141187,0.37488811,-0.4999534,-1,0.27968311,-0.42889738,-0.75253072,0.17523232,-0.349759,-0.84853211,0.35590634){1.3088768,1.3075402,0.75958282,0.94981364};
SS(0.30434906,0.49798107,-4.0114635e-05,0.42864323,0.48543211,-0.13804456,0.45788353,0.76094781,-0.0096633567,0.52218723,0.46943947,0.022097553){0.32377482,0.42022283,0.76853994,0.46892029};
SS(0.57129187,0.13526053,-0.13726946,0.60662231,0.34516964,-0.13972301,0.36021608,0.23247759,-0.012351094,0.63998586,0.17856447,0.051345521){0.35115136,0.48782847,0.16110593,0.42570365};
SS(-0.033588837,0.5879061,0.5,-0.16015893,0.67694077,0.39025863,-0.3132159,0.69976014,0.5,-0.1827732,0.83017807,0.5){0.57806214,0.6265216,0.82050522,0.95598938};
SS(-0.80728146,0.00010990719,-0.5,-0.92571354,0.17249619,-0.34283108,-1,-0.00018427889,-0.26378916,-0.85707128,-0.1416783,-0.34083416){0.88195685,0.99158484,1.0508045,0.85441326};
SS(0.11136938,1,0.13859714,0.40637652,0.87094343,0.13060843,0.23106485,1,0.31398279,0.24404834,0.79519787,0.082231238){1.0072058,0.92399337,1.1340577,0.68472542};
SS(-0.098708274,0.55956225,0.10505678,-0.32064519,0.49448821,1.4739833e-06,-0.20984637,0.69532212,0.20809493,-0.2401666,0.74114092,-0.051302261){0.31633913,0.32892635,0.55022745,0.58653028};
SS(-0.12233239,-0.87748906,-0.13583418,-0.22019153,-1,-0.00010416607,-0.22302806,-0.77703925,0.068353305,-0.42066299,-0.84356131,-0.12906413){0.78823805,1.0287732,0.64063544,0.88525127};
SS(-0.62450053,-0.31310845,0.38575928,-0.49284988,-0.37485679,0.5,-0.78327322,-0.45013966,0.5,-0.56348952,-0.47594309,0.3052276){0.62379151,0.6163523,1.0435491,0.61776713};
SS(-0.79370724,0.81084643,0.045877226,-0.65776896,0.64141588,0.074371921,-0.74249217,0.75399014,-0.15399718,-0.48952189,0.78345034,0.019065462){1.270911,0.83514199,1.1267767,0.83409809};
SS(-0.61311838,0.85766427,0.15491279,-0.61115597,1,-0.10200355,-0.47972312,1,0.18932995,-0.42762906,1,-0.0094860889){1.1216468,1.3611038,1.2473472,1.169501};
SS(-1,0.49991607,0.0031934521,-0.89426176,0.41257007,-0.12932618,-1,0.70529035,-0.21162945,-0.8068077,0.56885008,-0.063754108){1.2302733,0.974079,1.520296,0.96112076};
SS(-0.67513028,-0.66529728,0.5,-0.56348952,-0.47594309,0.3052276,-0.78327322,-0.45013966,0.5,-0.50050976,-0.57246927,0.5){1.1284607,0.61776713,1.0435491,0.81219504};
SS(-0.33333333,1,-0.5,-0.35455825,0.80859576,-0.32177549,-0.36992714,1,-0.22970445,-0.56041637,1,-0.29784853){1.3407278,0.86460259,1.1684568,1.3856141};
SS(0.77777778,1,-0.5,0.68900489,0.77311276,-0.28043733,0.81205362,0.80656044,-0.5,0.82865019,1,-0.3214153){1.8341362,1.1326816,1.5391707,1.7714679};
SS(-0.83248216,0.76782327,-0.31292259,-1,0.77777778,-0.5,-1,0.70529035,-0.21162945,-1,0.83964442,-0.3309874){1.366757,1.8398372,1.520296,1.7979585};
SS(0.22886345,0.79287946,0.30210005,0.11136938,1,0.13859714,-0.014815866,1,0.31001515,0.23106485,1,0.31398279){0.75332396,1.0072058,1.0772324,1.1340577};
SS(-0.87046532,0.63071146,0.35630423,-1,0.4752276,0.27420758,-0.79172217,0.43302343,0.13373134,-0.83851866,0.33014205,0.32623765){1.2666006,1.2803563,0.80968993,0.89937894};
SS(0.39612945,0.70614162,0.21524614,0.59365279,0.65503723,0.24444947,0.45788353,0.76094781,-0.0096633567,0.40637652,0.87094343,0.13060843){0.68453461,0.82252715,0.76853994,0.92399337};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.5000565,0.0033661208,-1,-0.25140376,-0.1934451,-1,-0.47540235,-0.27521785){0.95161001,1.2263361,1.0790534,1.2841965};
SS(0.25248643,0.73785598,-0.13082591,0.42864323,0.48543211,-0.13804456,0.17777709,0.54047543,-0.2567554,0.34412919,0.6158316,-0.3427703){0.60350215,0.42022283,0.36840304,0.59958408};
SS(-0.32879066,-0.67072359,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.50377808,-0.78884267,-0.5,-0.18848435,-0.81110947,-0.5){0.79007105,1.0307746,1.1087956,0.92571371};
SS(0.081865095,0.80626877,-0.27867109,0.11111111,1,-0.5,0.21543771,0.73213875,-0.5,0.00024312215,0.80750011,-0.5){0.71703623,1.2422682,0.81134051,0.88610119};
SS(-0.35582611,-0.64426575,-0.070000747,-0.19247216,-0.56000521,0.088357129,-0.42889738,-0.75253072,0.17523232,-0.22302806,-0.77703925,0.068353305){0.52757348,0.34206231,0.75958282,0.64063544};
SS(0,-0.16137283,0.3386068,-0.16643696,-0.21791406,0.42402077,-0.20045203,0.067929244,0.29301468,-0.1159097,-0.14329028,0.19302206){0.12565914,0.23818505,0.10955402,0.055235283};
SS(-0.35582611,-0.64426575,-0.070000747,-0.26056819,-0.54975154,-0.34323516,-0.2399131,-0.76005145,-0.25989531,-0.49676106,-0.69523221,-0.26913048){0.52757348,0.46884495,0.6848256,0.78043195};
SS(-0.4581749,-0.5263483,-0.32801665,-0.23055166,-0.37480907,-0.5,-0.36174,-0.40052234,-0.23665811,-0.26056819,-0.54975154,-0.34323516){0.57811658,0.41992239,0.32480953,0.46884495};
SS(-0.48255002,0.69900846,-0.19155417,-0.4433427,0.53576375,-0.12560501,-0.2401666,0.74114092,-0.051302261,-0.48952189,0.78345034,0.019065462){0.74365966,0.48429505,0.58653028,0.83409809};
SS(-0.010543702,0.17712261,-0.5,0.09693172,0.3918681,-0.3370861,-0.12449617,0.36606215,-0.28273955,-0.1182182,0.15955837,-0.3159857){0.25750364,0.26256104,0.21185338,0.11990198};
SS(-0.056808231,0.14323286,-0.13367928,0.08017426,0.31429474,-0.16745504,-0.13709741,0.19518884,0.034033465,0.13261259,0.21336316,0.036566127){0.022140076,0.11103103,0.040184006,0.046199082};
SS(-1,-0.25140376,-0.1934451,-0.88905946,-0.098697315,-0.13184676,-0.76760867,-0.33664988,-0.028298027,-0.7907607,-0.33838097,-0.28342271){1.0790534,0.8023886,0.68479998,0.80149819};
SS(0.87867265,0.36391919,-0.37720578,0.671223,0.32907594,-0.5,0.78906409,0.5041626,-0.5,0.81149777,0.18885984,-0.5){1.03034,0.79435762,1.1105402,0.92750237};
SS(-0.69937107,0.31347586,0.5,-0.83851866,0.33014205,0.32623765,-0.79641575,0.50054117,0.5,-0.61509744,0.47589965,0.5){0.8165723,0.89937894,1.1180299,0.84259202};
SS(0.4450496,1,-0.00012892076,0.54700908,0.85955032,-0.16345766,0.68985253,1,-0.19792707,0.62860594,0.86645525,0.049037492){1.179155,1.0528061,1.495304,1.1303867};
SS(0.085954007,0.41736025,0.32943097,-0.033588837,0.5879061,0.5,0.11523872,0.30161582,0.5,0.16321322,0.50838432,0.5){0.27115576,0.57806214,0.33546792,0.52238519};
SS(0.11111111,1,0.5,0.22886345,0.79287946,0.30210005,0.21512427,0.73211919,0.5,0.00029730467,0.80760978,0.5){1.2368521,0.75332396,0.81521474,0.88423684};
SS(-0.14850787,-0.69358405,-0.087583548,-0.22302806,-0.77703925,0.068353305,-0.42066299,-0.84356131,-0.12906413,-0.12233239,-0.87748906,-0.13583418){0.49763432,0.64063544,0.88525127,0.78823805};
SS(-0.87046532,0.63071146,0.35630423,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382,-0.83851866,0.33014205,0.32623765){1.2666006,0.80968993,0.83617727,0.89937894};
SS(-0.35582611,-0.64426575,-0.070000747,-0.3533559,-0.49437708,0.037576204,-0.42889738,-0.75253072,0.17523232,-0.19247216,-0.56000521,0.088357129){0.52757348,0.35575629,0.75958282,0.34206231};
SS(-0.4581749,-0.5263483,-0.32801665,-0.65956212,-0.52273243,-0.19262862,-0.49676106,-0.69523221,-0.26913048,-0.52487586,-0.5117405,-0.017639258){0.57811658,0.7287475,0.78043195,0.51812974};
SS(-1,-0.00018427889,-0.26378916,-0.92571354,0.17249619,-0.34283108,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){1.0508045,0.99158484,0.65386325,0.85441326};
SS(-0.75,-1,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.5,-1,-0.5,-0.50377808,-0.78884267,-0.5){1.7946951,1.296688,1.4844013,1.1087956};
SS(-0.83851866,0.33014205,0.32623765,-0.79641575,0.50054117,0.5,-0.61509744,0.47589965,0.5,-0.67801153,0.56076489,0.29217382){0.89937894,1.1180299,0.84259202,0.83617727};
SS(0.37137652,0.1767682,-0.19801193,0.60662231,0.34516964,-0.13972301,0.34662081,0.36199915,-0.25068724,0.36021608,0.23247759,-0.012351094){0.19205628,0.48782847,0.29696992,0.16110593};
SS(0.54700908,0.85955032,-0.16345766,0.4450496,1,-0.00012892076,0.68985253,1,-0.19792707,0.43683247,1,-0.26068681){1.0528061,1.179155,1.495304,1.2435523};
SS(-0.10743676,0.85847111,-0.11136175,-0.36992714,1,-0.22970445,-0.088882135,1,-0.23281641,-0.23070339,1,-0.34855306){0.7462212,1.1684568,1.0431215,1.1599423};
SS(0.08017426,0.31429474,-0.16745504,0.24635331,0.35131343,-0.096025322,0.17777709,0.54047543,-0.2567554,0.086744979,0.52712982,0.027891324){0.11103103,0.18045455,0.36840304,0.26660844};
SS(0,0,-0.25,-0.16707278,-0.087678023,-0.31121894,-0.098950987,-0.13391411,-0.14594667,-0.056808231,0.14323286,-0.13367928){0.044304329,0.11599041,0.03512721,0.022140076};
SS(0.40637652,0.87094343,0.13060843,0.11136938,1,0.13859714,0.23106485,1,0.31398279,0.24937941,1,-0.00011138016){0.92399337,1.0072058,1.1340577,1.0446566};
SS(0.36841015,0.87909734,0.37310922,0.33333333,1,0.5,0.11111111,1,0.5,0.21512427,0.73211919,0.5){1.0362544,1.3466764,1.2368521,0.81521474};
SS(0.42864323,0.48543211,-0.13804456,0.30434906,0.49798107,-4.0114635e-05,0.25248643,0.73785598,-0.13082591,0.17777709,0.54047543,-0.2567554){0.42022283,0.32377482,0.60350215,0.36840304};
SS(-0.16643696,-0.21791406,0.42402077,-0.25897908,-0.24013326,0.26450313,-0.20045203,0.067929244,0.29301468,-0.1159097,-0.14329028,0.19302206){0.23818505,0.17775565,0.10955402,0.055235283};
SS(0.45788353,0.76094781,-0.0096633567,0.59365279,0.65503723,0.24444947,0.62860594,0.86645525,0.049037492,0.40637652,0.87094343,0.13060843){0.76853994,0.82252715,1.1303867,0.92399337};
SS(0.4450496,1,-0.00012892076,0.62860594,0.86645525,0.049037492,0.68966181,1,0.19790566,0.43654676,1,0.2604635){1.179155,1.1303867,1.492557,1.2403655};
SS(-0.92571354,0.17249619,-0.34283108,-0.80728146,0.00010990719,-0.5,-0.77267892,0.13105707,-0.24874664,-0.85707128,-0.1416783,-0.34083416){0.99158484,0.88195685,0.65386325,0.85441326};
SS(-0.098950987,-0.13391411,-0.14594667,0,0,-0.25,0,-0.29164705,-0.20823955,0,-0.16143077,-0.33843101){0.03512721,0.044304329,0.11473247,0.12966739};
SS(0.60662231,0.34516964,-0.13972301,0.42864323,0.48543211,-0.13804456,0.49866453,0.63973666,-0.21510859,0.67125235,0.44297685,-0.31879306){0.48782847,0.42022283,0.68344633,0.72773009};
SS(0.60662231,0.34516964,-0.13972301,0.42864323,0.48543211,-0.13804456,0.34662081,0.36199915,-0.25068724,0.36021608,0.23247759,-0.012351094){0.48782847,0.42022283,0.29696992,0.16110593};
SS(-0.69937107,0.31347586,0.5,-0.83851866,0.33014205,0.32623765,-0.61509744,0.47589965,0.5,-0.67801153,0.56076489,0.29217382){0.8165723,0.89937894,0.84259202,0.83617727};
SS(0.59365279,0.65503723,0.24444947,0.45042372,0.78359022,0.5,0.55555177,0.82262944,0.31125158,0.35567295,0.65317229,0.39545235){0.82252715,1.0496179,1.0671623,0.69293227};
SS(-0.3727858,-0.19869367,0.11195566,-0.34310942,-0.010167032,0.1509038,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.16948569,0.12661586,0.36443271,0.26156128};
SS(-0.65756371,0.81308934,-0.3429452,-0.55555556,1,-0.5,-0.50014045,0.79673357,-0.5,-0.80479144,0.80504612,-0.5){1.1958888,1.5379273,1.1145783,1.5255891};
SS(-0.55555556,1,0.5,-0.60421932,0.82298164,0.34468578,-0.50037,0.79662088,0.5,-0.80481649,0.80494069,0.5){1.5418081,1.1449713,1.1183194,1.5232843};
SS(0.36841015,0.87909734,0.37310922,0.11111111,1,0.5,0.23106485,1,0.31398279,0.22886345,0.79287946,0.30210005){1.0362544,1.2368521,1.1340577,0.75332396};
SS(-0.81387526,-0.53653555,-0.3209601,-0.78315651,-0.45008839,-0.5,-1,-0.47540235,-0.27521785,-0.7907607,-0.33838097,-0.28342271){1.0406635,1.0467962,1.2841965,0.80149819};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.3533559,-0.49437708,0.037576204,-0.3548152,-0.48825703,0.21848985,-0.59094649,-0.40495207,0.12834587){0.32068114,0.35575629,0.38862106,0.51475101};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.3533559,-0.49437708,0.037576204,-0.36174,-0.40052234,-0.23665811,-0.23583358,-0.36008743,0.0071767184){0.32068114,0.35575629,0.32480953,0.16465457};
SS(-0.70236545,-0.13062851,-0.19140485,-0.65367362,-0.16081953,0.0014934597,-0.4720473,-0.063494476,-0.036829327,-0.76752638,0.004448061,-0.013214377){0.5265969,0.4344691,0.21285629,0.5734925};
SS(-0.7907607,-0.33838097,-0.28342271,-1,-0.33333333,-0.5,-0.73174678,-0.21478859,-0.5,-0.78315651,-0.45008839,-0.5){0.80149819,1.3342594,0.81151292,1.0467962};
SS(0.35567295,0.65317229,0.39545235,0.66554141,0.67524133,0.5,0.45042372,0.78359022,0.5,0.5725222,0.50074158,0.5){0.69293227,1.1271263,1.0496179,0.8121357};
SS(-0.38143574,0.84373572,-0.12387887,-0.48255002,0.69900846,-0.19155417,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.85864479,0.74365966,0.42991415,0.58653028};
SS(0.26064395,0.61953306,0.12890567,0.30434906,0.49798107,-4.0114635e-05,0.52218723,0.46943947,0.022097553,0.36016656,0.41044152,0.1594367){0.45328252,0.32377482,0.46892029,0.3073722};
SS(-0.24654336,0.57133462,-0.25396354,-0.38143574,0.84373572,-0.12387887,-0.2401666,0.74114092,-0.051302261,-0.35455825,0.80859576,-0.32177549){0.42991415,0.85864479,0.58653028,0.86460259};
SS(-0.52470763,0.46530444,0.33754711,-0.48141868,0.60085372,0.5,-0.47185361,0.73769401,0.24072705,-0.67801153,0.56076489,0.29217382){0.59371518,0.82306978,0.80384956,0.83617727};
SS(0.29175541,0,0.20824909,0.46476684,0.14382827,0.12247557,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094){0.1093371,0.23450402,0.027339551,0.16110593};
SS(-0.48255002,0.69900846,-0.19155417,-0.38143574,0.84373572,-0.12387887,-0.24654336,0.57133462,-0.25396354,-0.35455825,0.80859576,-0.32177549){0.74365966,0.85864479,0.42991415,0.86460259};
SS(-0.3533559,-0.49437708,0.037576204,-0.3548152,-0.48825703,0.21848985,-0.52487586,-0.5117405,-0.017639258,-0.50537844,-0.68762812,0.023695348){0.35575629,0.38862106,0.51812974,0.71483247};
SS(-0.29261734,0.53193925,0.43339885,-0.20984637,0.69532212,0.20809493,-0.30949447,0.8262402,0.33528492,-0.16015893,0.67694077,0.39025863){0.53993003,0.55022745,0.87388961,0.6265216};
SS(0,-0.16137283,0.3386068,-0.16643696,-0.21791406,0.42402077,-0.17669296,0.011023676,0.5,-0.20045203,0.067929244,0.29301468){0.12565914,0.23818505,0.26322593,0.10955402};
SS(-0.32897755,-0.67088709,0.5,-0.34549718,-0.50098866,0.4105565,-0.23048975,-0.37484721,0.5,-0.14394692,-0.62481063,0.5){0.79643001,0.5260109,0.42714666,0.63866347};
SS(-0.58934795,0.84141567,-0.18062024,-0.74249217,0.75399014,-0.15399718,-0.48952189,0.78345034,0.019065462,-0.79370724,0.81084643,0.045877226){1.0736489,1.1267767,0.83409809,1.270911};
SS(-0.1853821,-0.42358473,0.30866054,0,-0.5,0.5,0,-0.25,0.5,-0.23048975,-0.37484721,0.5){0.29143101,0.48207879,0.28720824,0.42714666};
SS(-0.83248216,0.76782327,-0.31292259,-0.77777778,1,-0.5,-0.80479144,0.80504612,-0.5,-1,0.83964442,-0.3309874){1.366757,1.8319852,1.5255891,1.7979585};
SS(-0.4433427,0.53576375,-0.12560501,-0.2401666,0.74114092,-0.051302261,-0.48952189,0.78345034,0.019065462,-0.45563594,0.60375179,0.095527884){0.48429505,0.58653028,0.83409809,0.56263538};
SS(-0.222315,1,-0.00011890035,-0.32294154,0.86180803,0.13108841,-0.47972312,1,0.18932995,-0.42762906,1,-0.0094860889){1.0307381,0.84829643,1.2473472,1.169501};
SS(-0.45563594,0.60375179,0.095527884,-0.20984637,0.69532212,0.20809493,-0.2401666,0.74114092,-0.051302261,-0.32294154,0.86180803,0.13108841){0.56263538,0.55022745,0.58653028,0.84829643};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.098708274,0.55956225,0.10505678,-0.20984637,0.69532212,0.20809493,-0.35521568,0.4957142,0.26668635){0.32892635,0.31633913,0.55022745,0.42001946};
SS(-0.68637718,0.43295764,-0.18031685,-0.65355936,0.25468043,-0.1897796,-0.49391083,0.27907498,-0.27264436,-0.54631436,0.45612147,-0.00074796238){0.67437813,0.51379882,0.37398026,0.48593017};
SS(-0.85707128,-0.1416783,-0.34083416,-1,-0.33333333,-0.5,-0.73174678,-0.21478859,-0.5,-0.7907607,-0.33838097,-0.28342271){0.85441326,1.3342594,0.81151292,0.80149819};
SS(-0.349759,-0.84853211,0.35590634,-0.5,-1,0.5,-0.25,-1,0.5,-0.50400314,-0.78879927,0.5){0.94981364,1.4840089,1.2918821,1.1086821};
SS(-1,-0.5000565,0.0033661208,-0.82595855,-0.48031431,0.11444494,-1,-0.24887753,0.1953112,-1,-0.47520831,0.27427507){1.2263361,0.90887195,1.0768014,1.2822693};
SS(-0.59094649,-0.40495207,0.12834587,-0.65631386,-0.59724887,0.13822882,-0.3548152,-0.48825703,0.21848985,-0.56348952,-0.47594309,0.3052276){0.51475101,0.7890621,0.38862106,0.61776713};
SS(-0.56348952,-0.47594309,0.3052276,-0.49284988,-0.37485679,0.5,-0.78327322,-0.45013966,0.5,-0.50050976,-0.57246927,0.5){0.61776713,0.6163523,1.0435491,0.81219504};
SS(-0.29413589,0.046284299,-0.31274881,-0.16707278,-0.087678023,-0.31121894,-0.1182182,0.15955837,-0.3159857,-0.056808231,0.14323286,-0.13367928){0.1681493,0.11599041,0.11990198,0.022140076};
SS(0.22032809,0,-9.1119885e-05,0.13913358,0.10014326,0.18199659,0.36021608,0.23247759,-0.012351094,0.13261259,0.21336316,0.036566127){0.027339551,0.045990896,0.16110593,0.046199082};
SS(-0.15923414,-0.34171533,-0.15079999,-0.29237157,-0.11865629,-0.17606411,-0.36174,-0.40052234,-0.23665811,-0.45843014,-0.20445062,-0.15988901){0.14783141,0.11404163,0.32480953,0.26094507};
SS(-0.49391083,0.27907498,-0.27264436,-0.4433427,0.53576375,-0.12560501,-0.54631436,0.45612147,-0.00074796238,-0.34372617,0.39779568,-0.18541051){0.37398026,0.48429505,0.48593017,0.29650146};
SS(-0.63348211,-0.7706683,-0.074889286,-0.50537844,-0.68762812,0.023695348,-0.49676106,-0.69523221,-0.26913048,-0.52487586,-0.5117405,-0.017639258){0.97907785,0.71483247,0.78043195,0.51812974};
SS(0.11111111,1,-0.5,0.081865095,0.80626877,-0.27867109,0.21543771,0.73213875,-0.5,0.2222976,1,-0.35617554){1.2422682,0.71703623,0.81134051,1.1585843};
SS(-0.41843781,0.30742585,0.3397996,-0.26986228,0.26051837,0.22418657,-0.35521568,0.4957142,0.26668635,-0.11614487,0.30919383,0.33918095){0.37011438,0.1749353,0.42001946,0.20820823};
SS(0.87881231,0.64063264,0.37220388,1,0.5,0.5,1,0.75,0.5,0.78912399,0.50423732,0.5){1.3069719,1.484684,1.7930237,1.1096027};
SS(-0.45843014,-0.20445062,-0.15988901,-0.50159539,-0.29258506,7.2987381e-06,-0.23583358,-0.36008743,0.0071767184,-0.3727858,-0.19869367,0.11195566){0.26094507,0.32068114,0.16465457,0.16948569};
SS(-0.45843014,-0.20445062,-0.15988901,-0.50159539,-0.29258506,7.2987381e-06,-0.36174,-0.40052234,-0.23665811,-0.23583358,-0.36008743,0.0071767184){0.26094507,0.32068114,0.32480953,0.16465457};
SS(-0.65956212,-0.52273243,-0.19262862,-0.52487586,-0.5117405,-0.017639258,-0.63348211,-0.7706683,-0.074889286,-0.49676106,-0.69523221,-0.26913048){0.7287475,0.51812974,0.97907785,0.78043195};
SS(-0.6448883,-0.87343314,-0.36731947,-0.49995867,-1,-0.27986665,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){1.296688,1.3082069,0.78043195,0.88525127};
SS(0.8988736,0.63809662,-0.070284173,1,0.77979347,0.00010253841,0.77861211,0.77861193,-0.067175459,0.84582719,0.572243,0.1361951){1.2046527,1.5887874,1.1981052,1.0417018};
SS(-0.35455825,0.80859576,-0.32177549,-0.55555556,1,-0.5,-0.50014045,0.79673357,-0.5,-0.56041637,1,-0.29784853){0.86460259,1.5379273,1.1145783,1.3856141};
SS(0.11111111,1,0.5,0.36841015,0.87909734,0.37310922,0.21512427,0.73211919,0.5,0.22886345,0.79287946,0.30210005){1.2368521,1.0362544,0.81521474,0.75332396};
SS(-0.61674646,0.25215289,0.3447871,-0.54640726,0.34339216,0.19847863,-0.7489605,0.18190923,0.13647301,-0.83851866,0.33014205,0.32623765){0.54607287,0.43575493,0.59564173,0.89937894};
SS(-0.8068077,0.56885008,-0.063754108,-0.68637718,0.43295764,-0.18031685,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){0.96112076,0.67437813,0.80968993,0.68151298};
SS(0.098704003,0.67249079,0.1943501,0.18202227,0.38279251,0.10350409,0.26138985,0.51848551,0.281015,0.085954007,0.41736025,0.32943097){0.47957633,0.17617817,0.40200156,0.27115576};
SS(-0.33333333,1,-0.5,-0.35455825,0.80859576,-0.32177549,-0.55555556,1,-0.5,-0.50014045,0.79673357,-0.5){1.3407278,0.86460259,1.5379273,1.1145783};
SS(0.77777778,1,-0.5,0.54326203,0.87223293,-0.356993,0.55555556,1,-0.5,0.81205362,0.80656044,-0.5){1.8341362,1.1662147,1.5352494,1.5391707};
SS(-0.41767704,0.010770256,-0.44072823,-0.40408872,0.18166381,-0.5,-0.49391083,0.27907498,-0.27264436,-0.29413589,0.046284299,-0.31274881){0.35514259,0.42526168,0.37398026,0.1681493};
SS(-1,-0.55555556,-0.5,-0.91414606,-0.68082467,-0.37109558,-1,-0.77777778,-0.5,-0.80632325,-0.81147186,-0.5){1.5366945,1.4249306,1.8436809,1.5409894};
SS(-0.033284914,0.58770906,-0.5,-0.17097214,0.64900986,-0.39927747,-0.31289368,0.69974287,-0.5,-0.20381263,0.45499536,-0.5){0.58301644,0.59741335,0.82323564,0.478983};
SS(-0.41767704,0.010770256,-0.44072823,-0.49391083,0.27907498,-0.27264436,-0.49808619,0.0026201378,-0.26387206,-0.29413589,0.046284299,-0.31274881){0.35514259,0.37398026,0.29810596,0.1681493};
SS(-0.83851866,0.33014205,0.32623765,-0.54640726,0.34339216,0.19847863,-0.7489605,0.18190923,0.13647301,-0.79172217,0.43302343,0.13373134){0.89937894,0.43575493,0.59564173,0.80968993};
SS(-0.85520613,-0.46088631,-0.14784569,-1,-0.25140376,-0.1934451,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027){0.95161001,1.0790534,1.0172898,0.68479998};
SS(-0.7907607,-0.33838097,-0.28342271,-1,-0.33333333,-0.5,-0.78315651,-0.45008839,-0.5,-1,-0.47540235,-0.27521785){0.80149819,1.3342594,1.0467962,1.2841965};
SS(-0.0073778212,0.36022468,0.15230712,-0.098708274,0.55956225,0.10505678,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.13675819,0.31633913,0.33969293,0.27115576};
SS(0.42864323,0.48543211,-0.13804456,0.49866453,0.63973666,-0.21510859,0.25248643,0.73785598,-0.13082591,0.45788353,0.76094781,-0.0096633567){0.42022283,0.68344633,0.60350215,0.76853994};
SS(-0.10743676,0.85847111,-0.11136175,-0.36992714,1,-0.22970445,-0.23070339,1,-0.34855306,-0.14847812,0.78021305,-0.27623142){0.7462212,1.1684568,1.1599423,0.68882385};
SS(0.18202227,0.38279251,0.10350409,0.098704003,0.67249079,0.1943501,0.086744979,0.52712982,0.027891324,0.085954007,0.41736025,0.32943097){0.17617817,0.47957633,0.26660844,0.27115576};
SS(1,1,-0.25,0.8781758,0.86708556,-0.1989731,1,1,-6.9388939e-15,0.78186447,1,3.3673518e-05){2.0438315,1.5462283,1.9807485,1.5923176};
SS(0.66554141,0.67524133,0.5,0.59365279,0.65503723,0.24444947,0.45042372,0.78359022,0.5,0.55555177,0.82262944,0.31125158){1.1271263,0.82252715,1.0496179,1.0671623};
SS(-0.87046532,0.63071146,0.35630423,-1,0.55555556,0.5,-0.79641575,0.50054117,0.5,-0.80481649,0.80494069,0.5){1.2666006,1.5401154,1.1180299,1.5232843};
SS(0.08017426,0.31429474,-0.16745504,-0.056808231,0.14323286,-0.13367928,-0.1182182,0.15955837,-0.3159857,0.13402468,0.11673163,-0.1460819){0.11103103,0.022140076,0.11990198,0.039337265};
SS(0.13913358,0.10014326,0.18199659,0.25126435,0.28098512,0.24657435,0.36021608,0.23247759,-0.012351094,0.13261259,0.21336316,0.036566127){0.045990896,0.18575023,0.16110593,0.046199082};
SS(-0.77777778,1,-0.5,-0.83248216,0.76782327,-0.31292259,-0.76988954,1,-0.26944904,-1,0.83964442,-0.3309874){1.8319852,1.366757,1.6463902,1.7979585};
SS(-0.10743676,0.85847111,-0.11136175,-0.36992714,1,-0.22970445,-0.14847812,0.78021305,-0.27623142,-0.38143574,0.84373572,-0.12387887){0.7462212,1.1684568,0.68882385,0.85864479};
SS(-0.87046532,0.63071146,0.35630423,-1,0.55555556,0.5,-1,0.70725984,0.21334539,-1,0.4752276,0.27420758){1.2666006,1.5401154,1.5286486,1.2803563};
SS(0.5,0,0.5,0.59416595,0.14141347,0.32656529,0.75,0,0.5,0.50011436,0,0.27961788){0.47735984,0.46498444,0.79262349,0.30940041};
SS(-0.68637718,0.43295764,-0.18031685,-0.63246299,0.29145388,0.035195127,-0.79172217,0.43302343,0.13373134,-0.78848723,0.26584533,-0.068869999){0.67437813,0.47226275,0.80968993,0.68151298};
SS(-0.5,-1,0.5,-0.349759,-0.84853211,0.35590634,-0.25,-1,0.5,-0.4999534,-1,0.27968311){1.4840089,0.94981364,1.2918821,1.3075402};
SS(-0.0073778212,0.36022468,0.15230712,-0.098708274,0.55956225,0.10505678,0.098704003,0.67249079,0.1943501,0.086744979,0.52712982,0.027891324){0.13675819,0.31633913,0.47957633,0.26660844};
SS(0.77315808,0.36766952,0.075951375,1,0.50009037,3.487572e-05,1,0.29178008,0.20838772,1,0.2203628,5.6826691e-05){0.71793497,1.2275825,1.1084285,1.0268649};
SS(-0.4581749,-0.5263483,-0.32801665,-0.65956212,-0.52273243,-0.19262862,-0.36174,-0.40052234,-0.23665811,-0.61549046,-0.35581383,-0.12962263){0.57811658,0.7287475,0.32480953,0.50877487};
SS(-0.61549046,-0.35581383,-0.12962263,-0.65956212,-0.52273243,-0.19262862,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.50877487,0.7287475,0.32480953,0.51812974};
SS(-0.65956212,-0.52273243,-0.19262862,-0.4581749,-0.5263483,-0.32801665,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.7287475,0.57811658,0.32480953,0.51812974};
SS(0.6657623,0.67544754,-0.5,0.51674933,0.64481281,-0.39755292,0.45062041,0.7833899,-0.5,0.57309542,0.50075776,-0.5){1.1304562,0.82858869,1.0506853,0.81773274};
SS(0.85153485,0.65148612,-0.35468846,1,0.5,-0.5,1,0.75,-0.5,1,0.50010355,-0.27968748){1.2568282,1.4840091,1.7924126,1.3071084};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.55555556,0.5,-1,-0.77777778,0.5,-0.80635543,-0.81164184,0.5){1.4119512,1.5359657,1.8434331,1.5410993};
SS(0.77777778,1,-0.5,0.54326203,0.87223293,-0.356993,0.81205362,0.80656044,-0.5,0.68900489,0.77311276,-0.28043733){1.8341362,1.1662147,1.5391707,1.1326816};
SS(-0.58258855,0.14037208,-0.067351147,-0.4720473,-0.063494476,-0.036829327,-0.70236545,-0.13062851,-0.19140485,-0.49808619,0.0026201378,-0.26387206){0.34532741,0.21285629,0.5265969,0.29810596};
SS(0.85153485,0.65148612,-0.35468846,1,0.75,-0.5,0.78906409,0.5041626,-0.5,0.81205362,0.80656044,-0.5){1.2568282,1.7924126,1.1105402,1.5391707};
SS(0.84582719,0.572243,0.1361951,1,0.29178008,0.20838772,1,0.50005385,0.27984222,0.77315808,0.36766952,0.075951375){1.0417018,1.1084285,1.3085441,0.71793497};
SS(-0.49292178,-0.37477565,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.23055166,-0.37480907,-0.5,-0.36174,-0.40052234,-0.23665811){0.6115465,0.57811658,0.41992239,0.32480953};
SS(-0.63815223,-0.88141187,0.37488811,-0.75,-1,0.5,-0.5,-1,0.5,-0.50400314,-0.78879927,0.5){1.3088768,1.7943537,1.4840089,1.1086821};
SS(-1,-0.00021427218,0.00011802244,-0.82279039,-0.18997945,0.10657137,-1,-0.24887753,0.1953112,-1,-0.20076836,0.00061221676){0.98080906,0.70945047,1.0768014,1.0172898};
SS(0,-1,-6.9388939e-15,-0.12988976,-0.86995226,0.20452896,0,-1,0.25,0,-0.77970171,0.00010845427){0.98008605,0.79894991,1.0438639,0.58842154};
SS(-0.4433427,0.53576375,-0.12560501,-0.68637718,0.43295764,-0.18031685,-0.49391083,0.27907498,-0.27264436,-0.54631436,0.45612147,-0.00074796238){0.48429505,0.67437813,0.37398026,0.48593017};
SS(0.11523872,0.30161582,0.5,-0.11614487,0.30919383,0.33918095,-0.18136176,0.40461939,0.5,0.085954007,0.41736025,0.32943097){0.33546792,0.20820823,0.42386795,0.27115576};
SS(-0.12988976,-0.86995226,0.20452896,0,-1,-6.9388939e-15,0,-1,0.25,-0.22019153,-1,-0.00010416607){0.79894991,0.98008605,1.0438639,1.0287732};
SS(0.30434906,0.49798107,-4.0114635e-05,0.11458044,0.70010244,0.010073529,0.25248643,0.73785598,-0.13082591,0.086744979,0.52712982,0.027891324){0.32377482,0.49378055,0.60350215,0.26660844};
SS(-0.58258855,0.14037208,-0.067351147,-0.76752638,0.004448061,-0.013214377,-0.70236545,-0.13062851,-0.19140485,-0.4720473,-0.063494476,-0.036829327){0.34532741,0.5734925,0.5265969,0.21285629};
SS(-0.62450053,-0.31310845,0.38575928,-0.49284988,-0.37485679,0.5,-0.73174745,-0.21491043,0.5,-0.78327322,-0.45013966,0.5){0.62379151,0.6163523,0.81377033,1.0435491};
SS(0.13261259,0.21336316,0.036566127,-0.0073778212,0.36022468,0.15230712,0.08017426,0.31429474,-0.16745504,-0.096302334,0.43534175,-0.056072844){0.046199082,0.13675819,0.11103103,0.18078295};
SS(-0.52470763,0.46530444,0.33754711,-0.54640726,0.34339216,0.19847863,-0.67801153,0.56076489,0.29217382,-0.45563594,0.60375179,0.095527884){0.59371518,0.43575493,0.83617727,0.56263538};
SS(0.54326203,0.87223293,-0.356993,0.77777778,1,-0.5,0.82865019,1,-0.3214153,0.68900489,0.77311276,-0.28043733){1.1662147,1.8341362,1.7714679,1.1326816};
SS(0.5,0,-0.5,0.75,0,-0.5,0.6251418,0.1440922,-0.5,0.50007058,0,-0.27987971){0.48471812,0.79494611,0.63751638,0.31006895};
SS(-0.35582611,-0.64426575,-0.070000747,-0.4581749,-0.5263483,-0.32801665,-0.49676106,-0.69523221,-0.26913048,-0.52487586,-0.5117405,-0.017639258){0.52757348,0.57811658,0.78043195,0.51812974};
SS(0,-0.49989758,0.27983937,0,-0.75,0.5,0,-0.5,0.5,-0.14394692,-0.62481063,0.5){0.30650831,0.79557901,0.48207879,0.63866347};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.45563594,0.60375179,0.095527884,-0.20984637,0.69532212,0.20809493,-0.2401666,0.74114092,-0.051302261){0.32892635,0.56263538,0.55022745,0.58653028};
SS(-0.38492375,-0.20017574,-0.33650716,-0.49292178,-0.37477565,-0.5,-0.23055166,-0.37480907,-0.5,-0.36174,-0.40052234,-0.23665811){0.28705324,0.6115465,0.41992239,0.32480953};
SS(-0.17097214,0.64900986,-0.39927747,-0.033284914,0.58770906,-0.5,-0.31289368,0.69974287,-0.5,-0.18268367,0.83021756,-0.5){0.59741335,0.58301644,0.82323564,0.9573479};
SS(1,0.77979347,0.00010253841,0.88049681,0.87960137,0.13412341,0.77861211,0.77861193,-0.067175459,0.84582719,0.572243,0.1361951){1.5887874,1.5518824,1.1981052,1.0417018};
SS(-0.49998858,-1,-4.7037318e-05,-0.63348211,-0.7706683,-0.074889286,-0.70823063,-1,-0.20843533,-0.77973152,-1,-0.0001062007){1.2276085,0.97907785,1.5240742,1.588155};
SS(-1,1,-0.5,-0.76988954,1,-0.26944904,-1,1,-0.25,-1,0.83964442,-0.3309874){2.2287589,1.6463902,2.0450698,1.7979585};
SS(-0.056808231,0.14323286,-0.13367928,-0.26297351,0.20404986,-0.17122089,-0.29413589,0.046284299,-0.31274881,-0.28278924,0.041190137,-0.04219563){0.022140076,0.12773981,0.1681493,0.063480395};
SS(-0.29261734,0.53193925,0.43339885,-0.18136176,0.40461939,0.5,-0.35521568,0.4957142,0.26668635,-0.11614487,0.30919383,0.33918095){0.53993003,0.42386795,0.42001946,0.20820823};
SS(0.13913358,0.10014326,0.18199659,0,0,0.25,0.29175541,0,0.20824909,0.1615172,0,0.33845519){0.045990896,0.045060365,0.1093371,0.13068911};
SS(0.11136938,1,0.13859714,0.22886345,0.79287946,0.30210005,0.098704003,0.67249079,0.1943501,0.24404834,0.79519787,0.082231238){1.0072058,0.75332396,0.47957633,0.68472542};
SS(-0.38492375,-0.20017574,-0.33650716,-0.49292178,-0.37477565,-0.5,-0.30131805,-0.11512588,-0.5,-0.23055166,-0.37480907,-0.5){0.28705324,0.6115465,0.3368451,0.41992239};
SS(-0.35455825,0.80859576,-0.32177549,-0.33333333,1,-0.5,-0.55555556,1,-0.5,-0.56041637,1,-0.29784853){0.86460259,1.3407278,1.5379273,1.3856141};
SS(-1,0.55555556,-0.5,-0.80558396,0.5878127,-0.29244037,-1,0.70529035,-0.21162945,-1,0.47527469,-0.27513051){1.5309384,1.0616703,1.520296,1.2834809};
SS(1,0.50009037,3.487572e-05,0.84582719,0.572243,0.1361951,1,0.29178008,0.20838772,1,0.50005385,0.27984222){1.2275825,1.0417018,1.1084285,1.3085441};
SS(0.085954007,0.41736025,0.32943097,-0.0073778212,0.36022468,0.15230712,0.098704003,0.67249079,0.1943501,0.086744979,0.52712982,0.027891324){0.27115576,0.13675819,0.47957633,0.26660844};
SS(-0.3533559,-0.49437708,0.037576204,-0.50159539,-0.29258506,7.2987381e-06,-0.3548152,-0.48825703,0.21848985,-0.3727858,-0.19869367,0.11195566){0.35575629,0.32068114,0.38862106,0.16948569};
SS(0.64232771,0.84838332,0.46476191,0.55555556,1,0.5,0.68966181,1,0.19790566,0.55555177,0.82262944,0.31125158){1.3339184,1.5357742,1.492557,1.0671623};
SS(-0.49998858,-1,-4.7037318e-05,-0.36608751,-0.8951802,0.074405883,-0.29157863,-1,0.20827581,-0.22019153,-1,-0.00010416607){1.2276085,0.92652515,1.1139248,1.0287732};
SS(-0.68637718,0.43295764,-0.18031685,-0.8068077,0.56885008,-0.063754108,-0.79172217,0.43302343,0.13373134,-0.54631436,0.45612147,-0.00074796238){0.67437813,0.96112076,0.80968993,0.48593017};
SS(0.35567295,0.65317229,0.39545235,0.66554141,0.67524133,0.5,0.5725222,0.50074158,0.5,0.47723835,0.52605258,0.30619083){0.69293227,1.1271263,0.8121357,0.58228229};
SS(-0.29237157,-0.11865629,-0.17606411,-0.16707278,-0.087678023,-0.31121894,-0.29413589,0.046284299,-0.31274881,-0.28278924,0.041190137,-0.04219563){0.11404163,0.11599041,0.1681493,0.063480395};
SS(-0.63246299,0.29145388,0.035195127,-0.68637718,0.43295764,-0.18031685,-0.79172217,0.43302343,0.13373134,-0.54631436,0.45612147,-0.00074796238){0.47226275,0.67437813,0.80968993,0.48593017};
SS(0.75922048,0.56990614,-0.17060419,0.67125235,0.44297685,-0.31879306,0.68900489,0.77311276,-0.28043733,0.85153485,0.65148612,-0.35468846){0.91133836,0.72773009,1.1326816,1.2568282};
SS(-0.16643696,-0.21791406,0.42402077,-0.30122568,-0.11513872,0.5,-0.20045203,0.067929244,0.29301468,-0.40506391,-0.079541407,0.3303193){0.23818505,0.33848202,0.10955402,0.26156128};
SS(-0.31377045,0.30492781,-0.36427962,-0.24654336,0.57133462,-0.25396354,-0.50782983,0.50249565,-0.29902586,-0.34372617,0.39779568,-0.18541051){0.30770932,0.42991415,0.58612549,0.29650146};
SS(-0.76752638,0.004448061,-0.013214377,-0.58258855,0.14037208,-0.067351147,-0.70236545,-0.13062851,-0.19140485,-0.77267892,0.13105707,-0.24874664){0.5734925,0.34532741,0.5265969,0.65386325};
SS(0,0,-0.25,0.13402468,0.11673163,-0.1460819,0.20129651,0.21389912,-0.31902192,-0.1182182,0.15955837,-0.3159857){0.044304329,0.039337265,0.16839385,0.11990198};
SS(-0.65956212,-0.52273243,-0.19262862,-0.62341011,-0.46880832,-0.38153973,-0.49676106,-0.69523221,-0.26913048,-0.76546557,-0.72634686,-0.27513208){0.7287475,0.73807879,0.78043195,1.1696133};
SS(-1,-0.11111111,0.5,-0.89646962,-0.32955067,0.34017365,-1,-0.24887753,0.1953112,-0.84084014,-0.14895162,0.31636914){1.2390062,1.0133061,1.0768014,0.81273381};
SS(-0.10133362,-0.40777162,0.1162396,0,-0.49997946,0.00010199173,0,-0.29157012,0.20836692,0,-0.49989758,0.27983937){0.17697987,0.22811872,0.11172813,0.30650831};
SS(-0.4581749,-0.5263483,-0.32801665,-0.35582611,-0.64426575,-0.070000747,-0.36174,-0.40052234,-0.23665811,-0.52487586,-0.5117405,-0.017639258){0.57811658,0.52757348,0.32480953,0.51812974};
SS(0.11136938,1,0.13859714,0.22886345,0.79287946,0.30210005,-0.014815866,1,0.31001515,-0.043441254,0.79173928,0.29440137){1.0072058,0.75332396,1.0772324,0.69563564};
SS(0.11136938,1,0.13859714,0.24404834,0.79519787,0.082231238,0.098704003,0.67249079,0.1943501,-0.035654771,0.78507762,0.045007896){1.0072058,0.68472542,0.47957633,0.60161266};
SS(0.25248643,0.73785598,-0.13082591,0.11458044,0.70010244,0.010073529,0.17777709,0.54047543,-0.2567554,0.086744979,0.52712982,0.027891324){0.60350215,0.49378055,0.36840304,0.26660844};
SS(-0.098708274,0.55956225,0.10505678,0.098704003,0.67249079,0.1943501,-0.11618574,0.50328545,0.29980467,0.085954007,0.41736025,0.32943097){0.31633913,0.47957633,0.33969293,0.27115576};
SS(-1,-0.00021427218,0.00011802244,-0.84289574,0.018333867,0.1608607,-1,-0.24887753,0.1953112,-0.82279039,-0.18997945,0.10657137){0.98080906,0.72430843,1.0768014,0.70945047};
SS(-0.41651431,0.41690828,-0.5,-0.39032311,0.63241857,-0.34621958,-0.50782983,0.50249565,-0.29902586,-0.31377045,0.30492781,-0.36427962){0.57523437,0.65630059,0.58612549,0.30770932};
SS(-0.49292178,-0.37477565,-0.5,-0.62341011,-0.46880832,-0.38153973,-0.78315651,-0.45008839,-0.5,-0.50036547,-0.57239096,-0.5){0.6115465,0.73807879,1.0467962,0.81333009};
SS(-0.18136176,0.40461939,0.5,-0.29261734,0.53193925,0.43339885,-0.35521568,0.4957142,0.26668635,-0.41843781,0.30742585,0.3397996){0.42386795,0.53993003,0.42001946,0.37011438};
SS(-0.69937107,0.31347586,0.5,-0.52470763,0.46530444,0.33754711,-0.61509744,0.47589965,0.5,-0.61674646,0.25215289,0.3447871){0.8165723,0.59371518,0.84259202,0.54607287};
SS(-0.52470763,0.46530444,0.33754711,-0.69937107,0.31347586,0.5,-0.61509744,0.47589965,0.5,-0.67801153,0.56076489,0.29217382){0.59371518,0.8165723,0.84259202,0.83617727};
SS(-0.52470763,0.46530444,0.33754711,-0.69937107,0.31347586,0.5,-0.67801153,0.56076489,0.29217382,-0.61674646,0.25215289,0.3447871){0.59371518,0.8165723,0.83617727,0.54607287};
SS(0,-0.25,0.5,-0.16643696,-0.21791406,0.42402077,0,-0.29157012,0.20836692,-0.1853821,-0.42358473,0.30866054){0.28720824,0.23818505,0.11172813,0.29143101};
SS(-0.84289574,0.018333867,0.1608607,-1,-0.00021427218,0.00011802244,-1,-0.24887753,0.1953112,-1,-0.00012222908,0.26646899){0.72430843,0.98080906,1.0768014,1.0506696};
SS(-0.79172217,0.43302343,0.13373134,-1,0.24865949,0.19540364,-1,0.4752276,0.27420758,-1,0.29928494,0.0012550607){0.80968993,1.0814407,1.2803563,1.0718665};
SS(-1,0.49991607,0.0031934521,-0.79172217,0.43302343,0.13373134,-1,0.4752276,0.27420758,-1,0.29928494,0.0012550607){1.2302733,0.80968993,1.2803563,1.0718665};
SS(-0.66546973,0.66566005,0.5,-0.60421932,0.82298164,0.34468578,-0.48141868,0.60085372,0.5,-0.67801153,0.56076489,0.29217382){1.1224691,1.1449713,0.82306978,0.83617727};
SS(0.43654676,1,0.2604635,0.40637652,0.87094343,0.13060843,0.23106485,1,0.31398279,0.24937941,1,-0.00011138016){1.2403655,0.92399337,1.1340577,1.0446566};
SS(-0.29237157,-0.11865629,-0.17606411,-0.16707278,-0.087678023,-0.31121894,-0.28278924,0.041190137,-0.04219563,-0.098950987,-0.13391411,-0.14594667){0.11404163,0.11599041,0.063480395,0.03512721};
SS(0.30434906,0.49798107,-4.0114635e-05,0.42864323,0.48543211,-0.13804456,0.25248643,0.73785598,-0.13082591,0.45788353,0.76094781,-0.0096633567){0.32377482,0.42022283,0.60350215,0.76853994};
SS(-0.76988954,1,-0.26944904,-1,1,-0.5,-0.77777778,1,-0.5,-1,0.83964442,-0.3309874){1.6463902,2.2287589,1.8319852,1.7979585};
SS(-0.65631386,-0.59724887,0.13822882,-0.59094649,-0.40495207,0.12834587,-0.3548152,-0.48825703,0.21848985,-0.52487586,-0.5117405,-0.017639258){0.7890621,0.51475101,0.38862106,0.51812974};
SS(-0.15923414,-0.34171533,-0.15079999,-0.36174,-0.40052234,-0.23665811,-0.23583358,-0.36008743,0.0071767184,-0.45843014,-0.20445062,-0.15988901){0.14783141,0.32480953,0.16465457,0.26094507};
SS(-0.25897908,-0.24013326,0.26450313,-0.16643696,-0.21791406,0.42402077,-0.20045203,0.067929244,0.29301468,-0.40506391,-0.079541407,0.3303193){0.17775565,0.23818505,0.10955402,0.26156128};
SS(-0.29237157,-0.11865629,-0.17606411,-0.15923414,-0.34171533,-0.15079999,-0.23583358,-0.36008743,0.0071767184,-0.45843014,-0.20445062,-0.15988901){0.11404163,0.14783141,0.16465457,0.26094507};
SS(0.25,0,0.5,0.26083053,0.15082484,0.37728795,0.5,0,0.5,0.50011436,0,0.27961788){0.29281005,0.21918499,0.47735984,0.30940041};
SS(0.13402468,0.11673163,-0.1460819,0.08017426,0.31429474,-0.16745504,0.20129651,0.21389912,-0.31902192,-0.1182182,0.15955837,-0.3159857){0.039337265,0.11103103,0.16839385,0.11990198};
SS(-0.54631436,0.45612147,-0.00074796238,-0.32064519,0.49448821,1.4739833e-06,-0.54640726,0.34339216,0.19847863,-0.39654734,0.26661646,0.019312696){0.48593017,0.32892635,0.43575493,0.20710489};
SS(-0.32294154,0.86180803,0.13108841,-0.222315,1,-0.00011890035,-0.47972312,1,0.18932995,-0.22223836,1,0.2622369){0.84829643,1.0307381,1.2473472,1.0984067};
SS(-0.084253952,1,0.13733396,-0.043441254,0.79173928,0.29440137,-0.20984637,0.69532212,0.20809493,-0.035654771,0.78507762,0.045007896){1.0073117,0.69563564,0.55022745,0.60161266};
SS(-0.39032311,0.63241857,-0.34621958,-0.24654336,0.57133462,-0.25396354,-0.50782983,0.50249565,-0.29902586,-0.31377045,0.30492781,-0.36427962){0.65630059,0.42991415,0.58612549,0.30770932};
SS(-0.61115597,1,-0.10200355,-0.61311838,0.85766427,0.15491279,-0.47972312,1,0.18932995,-0.74954172,1,0.13574231){1.3611038,1.1216468,1.2473472,1.562759};
SS(0.87272604,0.35900693,0.37172569,0.77315808,0.36766952,0.075951375,0.6902006,0.50015172,0.27072419,0.84582719,0.572243,0.1361951){1.0107603,0.71793497,0.77938072,1.0417018};
SS(0.8781758,0.86708556,-0.1989731,1,1,-0.25,1,1,-6.9388939e-15,1,0.77979347,0.00010253841){1.5462283,2.0438315,1.9807485,1.5887874};
SS(0.59365279,0.65503723,0.24444947,0.39612945,0.70614162,0.21524614,0.52218723,0.46943947,0.022097553,0.47723835,0.52605258,0.30619083){0.82252715,0.68453461,0.46892029,0.58228229};
SS(0.60662231,0.34516964,-0.13972301,0.69383766,0.49492178,-0.021800115,0.49866453,0.63973666,-0.21510859,0.42864323,0.48543211,-0.13804456){0.48782847,0.71284258,0.68344633,0.42022283};
SS(-0.18136176,0.40461939,0.5,-0.41843781,0.30742585,0.3397996,-0.35521568,0.4957142,0.26668635,-0.11614487,0.30919383,0.33918095){0.42386795,0.37011438,0.42001946,0.20820823};
SS(-0.29261734,0.53193925,0.43339885,-0.48141868,0.60085372,0.5,-0.47185361,0.73769401,0.24072705,-0.35521568,0.4957142,0.26668635){0.53993003,0.82306978,0.80384956,0.42001946};
SS(0,-0.75,0.5,0,-0.7082575,0.2084616,0,-0.83845667,0.33864852,-0.14394692,-0.62481063,0.5){0.79557901,0.52387062,0.80178572,0.63866347};
SS(0.75,0,-0.5,0.83867599,0,-0.33865964,0.6251418,0.1440922,-0.5,0.70841775,0,-0.20847891){0.79494611,0.80182539,0.63751638,0.52293439};
SS(-0.39032311,0.63241857,-0.34621958,-0.41651431,0.41690828,-0.5,-0.20381263,0.45499536,-0.5,-0.24654336,0.57133462,-0.25396354){0.65630059,0.57523437,0.478983,0.42991415};
SS(-1,-0.55555556,0.5,-0.89663862,-0.69397302,0.37275403,-1,-0.70710233,0.21356199,-1,-0.47520831,0.27427507){1.5359657,1.4119512,1.5280688,1.2822693};
SS(-0.59094649,-0.40495207,0.12834587,-0.73479965,-0.34302295,0.24038072,-0.50874333,-0.23900991,0.2620444,-0.65367362,-0.16081953,0.0014934597){0.51475101,0.69668046,0.36443271,0.4344691};
SS(0,-0.7082575,0.2084616,-0.22656331,-0.68065623,0.28194433,0,-0.83845667,0.33864852,-0.14394692,-0.62481063,0.5){0.52387062,0.57683818,0.80178572,0.63866347};
SS(-0.48141868,0.60085372,0.5,-0.60421932,0.82298164,0.34468578,-0.47185361,0.73769401,0.24072705,-0.67801153,0.56076489,0.29217382){0.82306978,1.1449713,0.80384956,0.83617727};
SS(0.60662231,0.34516964,-0.13972301,0.67125235,0.44297685,-0.31879306,0.49866453,0.63973666,-0.21510859,0.75922048,0.56990614,-0.17060419){0.48782847,0.72773009,0.68344633,0.91133836};
SS(-0.098708274,0.55956225,0.10505678,-0.0073778212,0.36022468,0.15230712,0.098704003,0.67249079,0.1943501,0.085954007,0.41736025,0.32943097){0.31633913,0.13675819,0.47957633,0.27115576};
SS(-0.65367362,-0.16081953,0.0014934597,-0.67616985,-0.069078192,0.18801024,-0.73479965,-0.34302295,0.24038072,-0.50874333,-0.23900991,0.2620444){0.4344691,0.47948004,0.69668046,0.36443271};
SS(0.65062064,0.64268786,0.069510863,0.59365279,0.65503723,0.24444947,0.39612945,0.70614162,0.21524614,0.52218723,0.46943947,0.022097553){0.82620698,0.82252715,0.68453461,0.46892029};
SS(-0.61674646,0.25215289,0.3447871,-0.69937107,0.31347586,0.5,-0.67801153,0.56076489,0.29217382,-0.83851866,0.33014205,0.32623765){0.54607287,0.8165723,0.83617727,0.89937894};
SS(-0.83851866,0.33014205,0.32623765,-0.54640726,0.34339216,0.19847863,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382){0.89937894,0.43575493,0.80968993,0.83617727};
SS(0.35689191,0.091376279,-0.36932783,0.25,0,-0.5,0.29173763,0,-0.20843742,0.16149165,0,-0.33864688){0.26145514,0.28810477,0.1134179,0.12746835};
SS(0.18202227,0.38279251,0.10350409,-0.0073778212,0.36022468,0.15230712,0.08017426,0.31429474,-0.16745504,0.13261259,0.21336316,0.036566127){0.17617817,0.13675819,0.11103103,0.046199082};
SS(-1,-0.33333333,-0.5,-0.7907607,-0.33838097,-0.28342271,-1,-0.25140376,-0.1934451,-1,-0.47540235,-0.27521785){1.3342594,0.80149819,1.0790534,1.2841965};
SS(-0.5555987,0.045150158,0.095162244,-0.67616985,-0.069078192,0.18801024,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.29993682,0.47948004,0.36443271,0.26156128};
SS(-1,-0.33333333,-0.5,-0.85707128,-0.1416783,-0.34083416,-1,-0.25140376,-0.1934451,-0.7907607,-0.33838097,-0.28342271){1.3342594,0.85441326,1.0790534,0.80149819};
SS(0.84582719,0.572243,0.1361951,1,0.50009037,3.487572e-05,1,0.29178008,0.20838772,0.77315808,0.36766952,0.075951375){1.0417018,1.2275825,1.1084285,0.71793497};
SS(-0.3132159,0.69976014,0.5,-0.29261734,0.53193925,0.43339885,-0.48141868,0.60085372,0.5,-0.47185361,0.73769401,0.24072705){0.82050522,0.53993003,0.82306978,0.80384956};
SS(-0.3548152,-0.48825703,0.21848985,-0.59094649,-0.40495207,0.12834587,-0.50874333,-0.23900991,0.2620444,-0.3727858,-0.19869367,0.11195566){0.38862106,0.51475101,0.36443271,0.16948569};
SS(-1,-1,-0.25,-0.86742481,-0.86548068,-0.14483364,-1,-1,-6.9388939e-15,-0.77973152,-1,-0.0001062007){2.0422973,1.5085891,1.9831286,1.588155};
SS(-0.54640726,0.34339216,0.19847863,-0.61674646,0.25215289,0.3447871,-0.67801153,0.56076489,0.29217382,-0.83851866,0.33014205,0.32623765){0.43575493,0.54607287,0.83617727,0.89937894};
SS(0.50010751,0,-0.00013054911,0.37137652,0.1767682,-0.19801193,0.29173763,0,-0.20843742,0.22032809,0,-9.1119885e-05){0.22823279,0.19205628,0.1134179,0.027339551};
SS(-1,-0.11111111,0.5,-0.84084014,-0.14895162,0.31636914,-1,-0.24887753,0.1953112,-1,-0.00012222908,0.26646899){1.2390062,0.81273381,1.0768014,1.0506696};
SS(0.55555177,0.82262944,0.31125158,0.55555556,1,0.5,0.68966181,1,0.19790566,0.43654676,1,0.2604635){1.0671623,1.5357742,1.492557,1.2403655};
SS(-0.62341011,-0.46880832,-0.38153973,-0.67495489,-0.6652659,-0.5,-0.49676106,-0.69523221,-0.26913048,-0.76546557,-0.72634686,-0.27513208){0.73807879,1.1276355,0.78043195,1.1696133};
SS(-0.70823063,-1,-0.20843533,-0.42066299,-0.84356131,-0.12906413,-0.49995867,-1,-0.27986665,-0.63348211,-0.7706683,-0.074889286){1.5240742,0.88525127,1.3082069,0.97907785};
SS(0.30434906,0.49798107,-4.0114635e-05,0.086744979,0.52712982,0.027891324,0.25248643,0.73785598,-0.13082591,0.17777709,0.54047543,-0.2567554){0.32377482,0.26660844,0.60350215,0.36840304};
SS(-0.65355936,0.25468043,-0.1897796,-0.58258855,0.14037208,-0.067351147,-0.39654734,0.26661646,0.019312696,-0.63246299,0.29145388,0.035195127){0.51379882,0.34532741,0.20710489,0.47226275};
SS(0.13913358,0.10014326,0.18199659,0.29175541,0,0.20824909,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094){0.045990896,0.1093371,0.027339551,0.16110593};
SS(-0.63048479,0.37587985,-0.34368186,-0.40408872,0.18166381,-0.5,-0.49391083,0.27907498,-0.27264436,-0.62938155,0.17932964,-0.37445272){0.64388066,0.42526168,0.37398026,0.55109073};
SS(0.67125235,0.44297685,-0.31879306,0.49866453,0.63973666,-0.21510859,0.75922048,0.56990614,-0.17060419,0.68900489,0.77311276,-0.28043733){0.72773009,0.68344633,0.91133836,1.1326816};
SS(-1,-0.5000565,0.0033661208,-0.85520613,-0.46088631,-0.14784569,-1,-0.25140376,-0.1934451,-1,-0.20076836,0.00061221676){1.2263361,0.95161001,1.0790534,1.0172898};
SS(0.08017426,0.31429474,-0.16745504,0.09693172,0.3918681,-0.3370861,0.20129651,0.21389912,-0.31902192,-0.1182182,0.15955837,-0.3159857){0.11103103,0.26256104,0.16839385,0.11990198};
SS(-1,-0.5000565,0.0033661208,-0.85520613,-0.46088631,-0.14784569,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027){1.2263361,0.95161001,1.0172898,0.68479998};
SS(0.87272604,0.35900693,0.37172569,1,0.25,0.5,1,0.5,0.5,0.78912399,0.50423732,0.5){1.0107603,1.2942978,1.484684,1.1096027};
SS(0.26083053,0.15082484,0.37728795,0.25,0,0.5,0.29175541,0,0.20824909,0.50011436,0,0.27961788){0.21918499,0.29281005,0.1093371,0.30940041};
SS(0.49866453,0.63973666,-0.21510859,0.51674933,0.64481281,-0.39755292,0.68900489,0.77311276,-0.28043733,0.67125235,0.44297685,-0.31879306){0.68344633,0.82858869,1.1326816,0.72773009};
SS(0.69383766,0.49492178,-0.021800115,0.60662231,0.34516964,-0.13972301,0.49866453,0.63973666,-0.21510859,0.75922048,0.56990614,-0.17060419){0.71284258,0.48782847,0.68344633,0.91133836};
SS(0.50010751,0,-0.00013054911,0.37137652,0.1767682,-0.19801193,0.22032809,0,-9.1119885e-05,0.36021608,0.23247759,-0.012351094){0.22823279,0.19205628,0.027339551,0.16110593};
SS(-0.39032311,0.63241857,-0.34621958,-0.41651431,0.41690828,-0.5,-0.24654336,0.57133462,-0.25396354,-0.31377045,0.30492781,-0.36427962){0.65630059,0.57523437,0.42991415,0.30770932};
SS(0.77777778,1,0.5,0.64232771,0.84838332,0.46476191,0.55555556,1,0.5,0.82853688,1,0.32125076){1.8450917,1.3339184,1.5357742,1.7703132};
SS(-0.50159539,-0.29258506,7.2987381e-06,-0.59094649,-0.40495207,0.12834587,-0.3548152,-0.48825703,0.21848985,-0.3727858,-0.19869367,0.11195566){0.32068114,0.51475101,0.38862106,0.16948569};
SS(-0.65355936,0.25468043,-0.1897796,-0.58258855,0.14037208,-0.067351147,-0.49391083,0.27907498,-0.27264436,-0.39654734,0.26661646,0.019312696){0.51379882,0.34532741,0.37398026,0.20710489};
SS(-0.098708274,0.55956225,0.10505678,-0.32064519,0.49448821,1.4739833e-06,-0.2401666,0.74114092,-0.051302261,-0.096302334,0.43534175,-0.056072844){0.31633913,0.32892635,0.58653028,0.18078295};
SS(-0.41651431,0.41690828,-0.5,-0.31377045,0.30492781,-0.36427962,-0.20381263,0.45499536,-0.5,-0.24654336,0.57133462,-0.25396354){0.57523437,0.30770932,0.478983,0.42991415};
SS(0.54326203,0.87223293,-0.356993,0.77777778,1,-0.5,0.55555556,1,-0.5,0.82865019,1,-0.3214153){1.1662147,1.8341362,1.5352494,1.7714679};
SS(-0.41651431,0.41690828,-0.5,-0.63048479,0.37587985,-0.34368186,-0.40408872,0.18166381,-0.5,-0.49391083,0.27907498,-0.27264436){0.57523437,0.64388066,0.42526168,0.37398026};
SS(-0.0073778212,0.36022468,0.15230712,0.13261259,0.21336316,0.036566127,-0.13709741,0.19518884,0.034033465,-0.096302334,0.43534175,-0.056072844){0.13675819,0.046199082,0.040184006,0.18078295};
SS(-0.0089783977,0.64320989,-0.13441642,-0.098708274,0.55956225,0.10505678,-0.2401666,0.74114092,-0.051302261,-0.096302334,0.43534175,-0.056072844){0.41358858,0.31633913,0.58653028,0.18078295};
SS(-0.75,-1,-0.5,-0.6448883,-0.87343314,-0.36731947,-0.50377808,-0.78884267,-0.5,-0.80632325,-0.81147186,-0.5){1.7946951,1.296688,1.1087956,1.5409894};
SS(-0.32294154,0.86180803,0.13108841,-0.084253952,1,0.13733396,-0.20984637,0.69532212,0.20809493,-0.035654771,0.78507762,0.045007896){0.84829643,1.0073117,0.55022745,0.60161266};
SS(-0.0073778212,0.36022468,0.15230712,0.086744979,0.52712982,0.027891324,0.08017426,0.31429474,-0.16745504,-0.096302334,0.43534175,-0.056072844){0.13675819,0.26660844,0.11103103,0.18078295};
SS(0.84582719,0.572243,0.1361951,0.65062064,0.64268786,0.069510863,0.76099919,0.76690574,0.25750996,0.77861211,0.77861193,-0.067175459){1.0417018,0.82620698,1.2143065,1.1981052};
SS(0.25,0,-0.5,0.20129651,0.21389912,-0.31902192,-0.010543702,0.17712261,-0.5,0.16149165,0,-0.33864688){0.28810477,0.16839385,0.25750364,0.12746835};
SS(-0.0073778212,0.36022468,0.15230712,0.18202227,0.38279251,0.10350409,0.08017426,0.31429474,-0.16745504,0.086744979,0.52712982,0.027891324){0.13675819,0.17617817,0.11103103,0.26660844};
SS(0.39612945,0.70614162,0.21524614,0.36016656,0.41044152,0.1594367,0.52218723,0.46943947,0.022097553,0.47723835,0.52605258,0.30619083){0.68453461,0.3073722,0.46892029,0.58228229};
SS(0.75,0,0.5,0.59416595,0.14141347,0.32656529,0.70845584,0,0.20819814,0.50011436,0,0.27961788){0.79262349,0.46498444,0.52761363,0.30940041};
SS(-0.22656331,-0.68065623,0.28194433,0,-0.7082575,0.2084616,0,-0.49989758,0.27983937,-0.14394692,-0.62481063,0.5){0.57683818,0.52387062,0.30650831,0.63866347};
SS(-0.22223836,1,0.2622369,-0.32294154,0.86180803,0.13108841,-0.084253952,1,0.13733396,-0.20984637,0.69532212,0.20809493){1.0984067,0.84829643,1.0073117,0.55022745};
SS(-0.043441254,0.79173928,0.29440137,-0.22223836,1,0.2622369,-0.084253952,1,0.13733396,-0.20984637,0.69532212,0.20809493){0.69563564,1.0984067,1.0073117,0.55022745};
SS(0.8988736,0.63809662,-0.070284173,1,0.50009037,3.487572e-05,1,0.70844226,-0.20827687,1,0.77979347,0.00010253841){1.2046527,1.2275825,1.5310675,1.5887874};
SS(0.20129651,0.21389912,-0.31902192,0.25,0,-0.5,0.11583535,0.30145324,-0.5,0.37532516,0.23078833,-0.5){0.16839385,0.28810477,0.33954703,0.42551454};
SS(-0.19461387,0.3919517,0.10437587,-0.26986228,0.26051837,0.22418657,-0.35521568,0.4957142,0.26668635,-0.39654734,0.26661646,0.019312696){0.19075448,0.1749353,0.42001946,0.20710489};
SS(-0.1159097,-0.14329028,0.19302206,0,0,-6.9388939e-15,0,0,0.25,0,-0.22019801,5.0496855e-05){0.055235283,-0.017891206,0.045060365,0.029059683};
SS(-0.45563594,0.60375179,0.095527884,-0.32064519,0.49448821,1.4739833e-06,-0.54640726,0.34339216,0.19847863,-0.54631436,0.45612147,-0.00074796238){0.56263538,0.32892635,0.43575493,0.48593017};
SS(-0.25,-1,0.5,-0.349759,-0.84853211,0.35590634,-0.29157863,-1,0.20827581,-0.4999534,-1,0.27968311){1.2918821,0.94981364,1.1139248,1.3075402};
SS(0.11111111,1,0.5,0.22886345,0.79287946,0.30210005,-0.014815866,1,0.31001515,0.23106485,1,0.31398279){1.2368521,0.75332396,1.0772324,1.1340577};
SS(-1,0.77777778,-0.5,-0.83248216,0.76782327,-0.31292259,-1,0.55555556,-0.5,-0.80479144,0.80504612,-0.5){1.8398372,1.366757,1.5309384,1.5255891};
SS(0.88049681,0.87960137,0.13412341,0.76099919,0.76690574,0.25750996,0.77861211,0.77861193,-0.067175459,0.84582719,0.572243,0.1361951){1.5518824,1.2143065,1.1981052,1.0417018};
SS(0.39612945,0.70614162,0.21524614,0.26064395,0.61953306,0.12890567,0.52218723,0.46943947,0.022097553,0.36016656,0.41044152,0.1594367){0.68453461,0.45328252,0.46892029,0.3073722};
SS(-0.61311838,0.85766427,0.15491279,-0.61115597,1,-0.10200355,-0.48952189,0.78345034,0.019065462,-0.79370724,0.81084643,0.045877226){1.1216468,1.3611038,0.83409809,1.270911};
SS(-0.67616985,-0.069078192,0.18801024,-0.5555987,0.045150158,0.095162244,-0.50874333,-0.23900991,0.2620444,-0.3727858,-0.19869367,0.11195566){0.47948004,0.29993682,0.36443271,0.16948569};
SS(-0.29237157,-0.11865629,-0.17606411,-0.3727858,-0.19869367,0.11195566,-0.23583358,-0.36008743,0.0071767184,-0.20656092,-0.13938028,0.029547229){0.11404163,0.16948569,0.16465457,0.048278496};
SS(-0.043441254,0.79173928,0.29440137,0.11136938,1,0.13859714,0.098704003,0.67249079,0.1943501,-0.035654771,0.78507762,0.045007896){0.69563564,1.0072058,0.47957633,0.60161266};
SS(-0.39654734,0.26661646,0.019312696,-0.54640726,0.34339216,0.19847863,-0.26986228,0.26051837,0.22418657,-0.35521568,0.4957142,0.26668635){0.20710489,0.43575493,0.1749353,0.42001946};
SS(-1,0.77777778,0.5,-0.87046532,0.63071146,0.35630423,-1,0.55555556,0.5,-1,0.70725984,0.21334539){1.8402752,1.2666006,1.5401154,1.5286486};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.45563594,0.60375179,0.095527884,-0.54640726,0.34339216,0.19847863,-0.35521568,0.4957142,0.26668635){0.32892635,0.56263538,0.43575493,0.42001946};
SS(-0.20984637,0.69532212,0.20809493,-0.30949447,0.8262402,0.33528492,-0.47185361,0.73769401,0.24072705,-0.35521568,0.4957142,0.26668635){0.55022745,0.87388961,0.80384956,0.42001946};
SS(0.09693172,0.3918681,-0.3370861,-0.010543702,0.17712261,-0.5,0.20129651,0.21389912,-0.31902192,-0.1182182,0.15955837,-0.3159857){0.26256104,0.25750364,0.16839385,0.11990198};
SS(-0.4581749,-0.5263483,-0.32801665,-0.49292178,-0.37477565,-0.5,-0.23055166,-0.37480907,-0.5,-0.26056819,-0.54975154,-0.34323516){0.57811658,0.6115465,0.41992239,0.46884495};
SS(0.85153485,0.65148612,-0.35468846,1,0.75,-0.5,1,0.70844226,-0.20827687,1,0.50010355,-0.27968748){1.2568282,1.7924126,1.5310675,1.3071084};
SS(1,0,-6.9388939e-15,0.86971177,0.13024645,0.1427188,1,0,0.25,1,0.2203628,5.6826691e-05){0.9846322,0.77797836,1.0436257,1.0268649};
SS(-0.29237157,-0.11865629,-0.17606411,-0.45843014,-0.20445062,-0.15988901,-0.23583358,-0.36008743,0.0071767184,-0.3727858,-0.19869367,0.11195566){0.11404163,0.26094507,0.16465457,0.16948569};
SS(0.86971177,0.13024645,0.1427188,1,0,-6.9388939e-15,1,0,0.25,0.77985819,0,-0.00014691753){0.77797836,0.9846322,1.0436257,0.58919206};
SS(0.66554141,0.67524133,0.5,0.59365279,0.65503723,0.24444947,0.47723835,0.52605258,0.30619083,0.35567295,0.65317229,0.39545235){1.1271263,0.82252715,0.58228229,0.69293227};
SS(-0.54631436,0.45612147,-0.00074796238,-0.65776896,0.64141588,0.074371921,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382){0.48593017,0.83514199,0.80968993,0.83617727};
SS(0.59365279,0.65503723,0.24444947,0.66554141,0.67524133,0.5,0.45042372,0.78359022,0.5,0.35567295,0.65317229,0.39545235){0.82252715,1.1271263,1.0496179,0.69293227};
SS(-0.42066299,-0.84356131,-0.12906413,-0.49998858,-1,-4.7037318e-05,-0.70823063,-1,-0.20843533,-0.49995867,-1,-0.27986665){0.88525127,1.2276085,1.5240742,1.3082069};
SS(-0.61115597,1,-0.10200355,-0.58934795,0.84141567,-0.18062024,-0.48952189,0.78345034,0.019065462,-0.79370724,0.81084643,0.045877226){1.3611038,1.0736489,0.83409809,1.270911};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.19461387,0.3919517,0.10437587,-0.35521568,0.4957142,0.26668635,-0.39654734,0.26661646,0.019312696){0.32892635,0.19075448,0.42001946,0.20710489};
SS(0.29175541,0,0.20824909,0.13913358,0.10014326,0.18199659,0.25126435,0.28098512,0.24657435,0.46476684,0.14382827,0.12247557){0.1093371,0.045990896,0.18575023,0.23450402};
SS(1,0.75,0.5,0.87881231,0.64063264,0.37220388,0.78912399,0.50423732,0.5,0.81191124,0.80644944,0.5){1.7930237,1.3069719,1.1096027,1.5425973};
SS(-0.0089783977,0.64320989,-0.13441642,-0.01813809,0.53618118,-0.30537166,0.08017426,0.31429474,-0.16745504,-0.096302334,0.43534175,-0.056072844){0.41358858,0.36567785,0.11103103,0.18078295};
SS(1,0.29178008,0.20838772,0.87272604,0.35900693,0.37172569,1,0.50005385,0.27984222,0.77315808,0.36766952,0.075951375){1.1084285,1.0107603,1.3085441,0.71793497};
SS(-0.11754465,-0.65214472,-0.32749638,0,-0.75,-0.5,0,-0.5,-0.5,0,-0.49997234,-0.27965571){0.53347202,0.79460868,0.4845449,0.30906942};
SS(0.54326203,0.87223293,-0.356993,0.55555556,1,-0.5,0.68985253,1,-0.19792707,0.82865019,1,-0.3214153){1.1662147,1.5352494,1.495304,1.7714679};
SS(-0.32879066,-0.67072359,-0.5,-0.26056819,-0.54975154,-0.34323516,-0.23055166,-0.37480907,-0.5,-0.50036547,-0.57239096,-0.5){0.79007105,0.46884495,0.41992239,0.81333009};
SS(-0.54640726,0.34339216,0.19847863,-0.54631436,0.45612147,-0.00074796238,-0.79172217,0.43302343,0.13373134,-0.67801153,0.56076489,0.29217382){0.43575493,0.48593017,0.80968993,0.83617727};
SS(-0.34310942,-0.010167032,0.1509038,-0.5555987,0.045150158,0.095162244,-0.50874333,-0.23900991,0.2620444,-0.40506391,-0.079541407,0.3303193){0.12661586,0.29993682,0.36443271,0.26156128};
SS(-0.3132159,0.69976014,0.5,-0.29261734,0.53193925,0.43339885,-0.47185361,0.73769401,0.24072705,-0.30949447,0.8262402,0.33528492){0.82050522,0.53993003,0.80384956,0.87388961};
SS(-0.5555987,0.045150158,0.095162244,-0.34310942,-0.010167032,0.1509038,-0.50874333,-0.23900991,0.2620444,-0.3727858,-0.19869367,0.11195566){0.29993682,0.12661586,0.36443271,0.16948569};
SS(0.13261259,0.21336316,0.036566127,0.08017426,0.31429474,-0.16745504,-0.13709741,0.19518884,0.034033465,-0.096302334,0.43534175,-0.056072844){0.046199082,0.11103103,0.040184006,0.18078295};
SS(-0.65776896,0.64141588,0.074371921,-0.45563594,0.60375179,0.095527884,-0.67801153,0.56076489,0.29217382,-0.54631436,0.45612147,-0.00074796238){0.83514199,0.56263538,0.83617727,0.48593017};
SS(-0.49292178,-0.37477565,-0.5,-0.4581749,-0.5263483,-0.32801665,-0.50036547,-0.57239096,-0.5,-0.26056819,-0.54975154,-0.34323516){0.6115465,0.57811658,0.81333009,0.46884495};
SS(0.51674933,0.64481281,-0.39755292,0.6657623,0.67544754,-0.5,0.68900489,0.77311276,-0.28043733,0.67125235,0.44297685,-0.31879306){0.82858869,1.1304562,1.1326816,0.72773009};
SS(0.87272604,0.35900693,0.37172569,1,0.50005385,0.27984222,0.77315808,0.36766952,0.075951375,0.84582719,0.572243,0.1361951){1.0107603,1.3085441,0.71793497,1.0417018};
SS(-0.50537844,-0.68762812,0.023695348,-0.65631386,-0.59724887,0.13822882,-0.3548152,-0.48825703,0.21848985,-0.52487586,-0.5117405,-0.017639258){0.71483247,0.7890621,0.38862106,0.51812974};
SS(-0.26056819,-0.54975154,-0.34323516,-0.49292178,-0.37477565,-0.5,-0.23055166,-0.37480907,-0.5,-0.50036547,-0.57239096,-0.5){0.46884495,0.6115465,0.41992239,0.81333009};
SS(0.22886345,0.79287946,0.30210005,0.11136938,1,0.13859714,0.098704003,0.67249079,0.1943501,-0.043441254,0.79173928,0.29440137){0.75332396,1.0072058,0.47957633,0.69563564};
SS(0.55555556,1,0.5,0.64232771,0.84838332,0.46476191,0.68966181,1,0.19790566,0.82853688,1,0.32125076){1.5357742,1.3339184,1.492557,1.7703132};
SS(-0.29261734,0.53193925,0.43339885,-0.20984637,0.69532212,0.20809493,-0.35521568,0.4957142,0.26668635,-0.30949447,0.8262402,0.33528492){0.53993003,0.55022745,0.42001946,0.87388961};
SS(0.67125235,0.44297685,-0.31879306,0.6657623,0.67544754,-0.5,0.68900489,0.77311276,-0.28043733,0.85153485,0.65148612,-0.35468846){0.72773009,1.1304562,1.1326816,1.2568282};
SS(0.54326203,0.87223293,-0.356993,0.55555556,1,-0.5,0.6657623,0.67544754,-0.5,0.45062041,0.7833899,-0.5){1.1662147,1.5352494,1.1304562,1.0506853};
SS(-0.63348211,-0.7706683,-0.074889286,-0.6448883,-0.87343314,-0.36731947,-0.49676106,-0.69523221,-0.26913048,-0.42066299,-0.84356131,-0.12906413){0.97907785,1.296688,0.78043195,0.88525127};
SS(0,-0.5,0.5,-0.1853821,-0.42358473,0.30866054,0,-0.25,0.5,0,-0.49989758,0.27983937){0.48207879,0.29143101,0.28720824,0.30650831};
SS(-0.6448883,-0.87343314,-0.36731947,-0.70823063,-1,-0.20843533,-0.49995867,-1,-0.27986665,-0.63348211,-0.7706683,-0.074889286){1.296688,1.5240742,1.3082069,0.97907785};
SS(-0.67616985,-0.069078192,0.18801024,-0.3727858,-0.19869367,0.11195566,-0.50874333,-0.23900991,0.2620444,-0.65367362,-0.16081953,0.0014934597){0.47948004,0.16948569,0.36443271,0.4344691};
SS(-1,-0.55555556,-0.5,-0.91414606,-0.68082467,-0.37109558,-0.67495489,-0.6652659,-0.5,-0.78315651,-0.45008839,-0.5){1.5366945,1.4249306,1.1276355,1.0467962};
SS(0.25,0,-0.5,0.20129651,0.21389912,-0.31902192,0.11583535,0.30145324,-0.5,-0.010543702,0.17712261,-0.5){0.28810477,0.16839385,0.33954703,0.25750364};
SS(-0.5,-1,-0.5,-0.36340067,-0.87821042,-0.37678589,-0.25,-1,-0.5,-0.50377808,-0.78884267,-0.5){1.4844013,1.0307746,1.2929607,1.1087956};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.55555556,0.5,-0.67513028,-0.66529728,0.5,-0.78327322,-0.45013966,0.5){1.4119512,1.5359657,1.1284607,1.0435491};
SS(0.20129651,0.21389912,-0.31902192,0,0,-0.25,-0.010543702,0.17712261,-0.5,0.16149165,0,-0.33864688){0.16839385,0.044304329,0.25750364,0.12746835};
SS(-0.01813809,0.53618118,-0.30537166,-0.0089783977,0.64320989,-0.13441642,0.08017426,0.31429474,-0.16745504,0.17777709,0.54047543,-0.2567554){0.36567785,0.41358858,0.11103103,0.36840304};
SS(-0.6448883,-0.87343314,-0.36731947,-0.75,-1,-0.5,-0.5,-1,-0.5,-0.49995867,-1,-0.27986665){1.296688,1.7946951,1.4844013,1.3082069};
SS(-0.58258855,0.14037208,-0.067351147,-0.49808619,0.0026201378,-0.26387206,-0.70236545,-0.13062851,-0.19140485,-0.77267892,0.13105707,-0.24874664){0.34532741,0.29810596,0.5265969,0.65386325};
SS(-0.45563594,0.60375179,0.095527884,-0.54640726,0.34339216,0.19847863,-0.67801153,0.56076489,0.29217382,-0.54631436,0.45612147,-0.00074796238){0.56263538,0.43575493,0.83617727,0.48593017};
SS(0.13913358,0.10014326,0.18199659,0.29175541,0,0.20824909,0.36021608,0.23247759,-0.012351094,0.46476684,0.14382827,0.12247557){0.045990896,0.1093371,0.16110593,0.23450402};
SS(-0.86742481,-0.86548068,-0.14483364,-1,-1,-0.25,-1,-1,-6.9388939e-15,-1,-0.77608598,0.00064487429){1.5085891,2.0422973,1.9831286,1.5844414};
SS(1,0.25,0.5,0.87272604,0.35900693,0.37172569,1,0.5,0.5,1,0.50005385,0.27984222){1.2942978,1.0107603,1.484684,1.3085441};
SS(1,0.25,-0.5,0.87867265,0.36391919,-0.37720578,1,0.5,-0.5,0.78906409,0.5041626,-0.5){1.2935113,1.03034,1.4840091,1.1105402};
SS(-0.63815223,-0.88141187,0.37488811,-0.75,-1,0.5,-0.50400314,-0.78879927,0.5,-0.80635543,-0.81164184,0.5){1.3088768,1.7943537,1.1086821,1.5410993};
SS(-0.49998858,-1,-4.7037318e-05,-0.42066299,-0.84356131,-0.12906413,-0.70823063,-1,-0.20843533,-0.63348211,-0.7706683,-0.074889286){1.2276085,0.88525127,1.5240742,0.97907785};
SS(0.51674933,0.64481281,-0.39755292,0.37549445,0.49317282,-0.5,0.45062041,0.7833899,-0.5,0.57309542,0.50075776,-0.5){0.82858869,0.61648995,1.0506853,0.81773274};
SS(-0.1182182,0.15955837,-0.3159857,0,0,-0.25,-0.010543702,0.17712261,-0.5,0.20129651,0.21389912,-0.31902192){0.11990198,0.044304329,0.25750364,0.16839385};
SS(-0.89663862,-0.69397302,0.37275403,-1,-0.77777778,0.5,-1,-0.70710233,0.21356199,-1,-0.84092895,0.33252059){1.4119512,1.8434331,1.5280688,1.8030746};
SS(-1,0.55555556,-0.5,-0.83248216,0.76782327,-0.31292259,-1,0.70529035,-0.21162945,-0.80558396,0.5878127,-0.29244037){1.5309384,1.366757,1.520296,1.0616703};
SS(-0.63048479,0.37587985,-0.34368186,-0.41651431,0.41690828,-0.5,-0.40408872,0.18166381,-0.5,-0.62938155,0.17932964,-0.37445272){0.64388066,0.57523437,0.42526168,0.55109073};
SS(-0.25,-1,0.5,-0.349759,-0.84853211,0.35590634,-0.50400314,-0.78879927,0.5,-0.18863677,-0.81113033,0.5){1.2918821,0.94981364,1.1086821,0.92459822};
SS(0.6251418,0.1440922,-0.5,0.77491511,0.22516452,-0.26425516,0.70841775,0,-0.20847891,0.83867599,0,-0.33865964){0.63751638,0.70313431,0.52293439,0.80182539};
SS(0.65062064,0.64268786,0.069510863,0.52218723,0.46943947,0.022097553,0.39612945,0.70614162,0.21524614,0.45788353,0.76094781,-0.0096633567){0.82620698,0.46892029,0.68453461,0.76853994};
SS(0.8781758,0.86708556,-0.1989731,0.68985253,1,-0.19792707,0.78186447,1,3.3673518e-05,0.82865019,1,-0.3214153){1.5462283,1.495304,1.5923176,1.7714679};
SS(-0.34372617,0.39779568,-0.18541051,-0.49391083,0.27907498,-0.27264436,-0.39654734,0.26661646,0.019312696,-0.54631436,0.45612147,-0.00074796238){0.29650146,0.37398026,0.20710489,0.48593017};
SS(-0.4720473,-0.063494476,-0.036829327,-0.5555987,0.045150158,0.095162244,-0.65367362,-0.16081953,0.0014934597,-0.3727858,-0.19869367,0.11195566){0.21285629,0.29993682,0.4344691,0.16948569};
SS(0.086744979,0.52712982,0.027891324,-0.0089783977,0.64320989,-0.13441642,0.08017426,0.31429474,-0.16745504,-0.096302334,0.43534175,-0.056072844){0.26660844,0.41358858,0.11103103,0.18078295};
SS(-0.12988976,-0.86995226,0.20452896,0,-1,0.25,-0.29157863,-1,0.20827581,-0.22019153,-1,-0.00010416607){0.79894991,1.0438639,1.1139248,1.0287732};
SS(0,-1,0.25,-0.12988976,-0.86995226,0.20452896,0,-0.7082575,0.2084616,0,-0.77970171,0.00010845427){1.0438639,0.79894991,0.52387062,0.58842154};
SS(-0.83248216,0.76782327,-0.31292259,-1,0.77777778,-0.5,-1,0.55555556,-0.5,-1,0.70529035,-0.21162945){1.366757,1.8398372,1.5309384,1.520296};
SS(-0.47185361,0.73769401,0.24072705,-0.29261734,0.53193925,0.43339885,-0.35521568,0.4957142,0.26668635,-0.30949447,0.8262402,0.33528492){0.80384956,0.53993003,0.42001946,0.87388961};
SS(-0.62938155,0.17932964,-0.37445272,-0.58754442,0.033885734,-0.5,-0.69937066,0.31351533,-0.5,-0.40408872,0.18166381,-0.5){0.55109073,0.58180393,0.81965428,0.42526168};
SS(0.52218723,0.46943947,0.022097553,0.52843461,0.32737897,0.19102935,0.36021608,0.23247759,-0.012351094,0.63998586,0.17856447,0.051345521){0.46892029,0.40790135,0.16110593,0.42570365};
SS(-0.5555987,0.045150158,0.095162244,-0.67616985,-0.069078192,0.18801024,-0.65367362,-0.16081953,0.0014934597,-0.3727858,-0.19869367,0.11195566){0.29993682,0.47948004,0.4344691,0.16948569};
SS(0.25126435,0.28098512,0.24657435,0.13913358,0.10014326,0.18199659,0.36021608,0.23247759,-0.012351094,0.46476684,0.14382827,0.12247557){0.18575023,0.045990896,0.16110593,0.23450402};
SS(-0.42889738,-0.75253072,0.17523232,-0.40125956,-0.65699374,0.33213173,-0.56348952,-0.47594309,0.3052276,-0.57994589,-0.69256437,0.31204703){0.75958282,0.69449311,0.61776713,0.89957508};
SS(-0.49995867,-1,-0.27986665,-0.6448883,-0.87343314,-0.36731947,-0.63348211,-0.7706683,-0.074889286,-0.42066299,-0.84356131,-0.12906413){1.3082069,1.296688,0.97907785,0.88525127};
SS(-0.41651431,0.41690828,-0.5,-0.63048479,0.37587985,-0.34368186,-0.69937066,0.31351533,-0.5,-0.62938155,0.17932964,-0.37445272){0.57523437,0.64388066,0.81965428,0.55109073};
SS(-0.65631386,-0.59724887,0.13822882,-0.57994589,-0.69256437,0.31204703,-0.42889738,-0.75253072,0.17523232,-0.56348952,-0.47594309,0.3052276){0.7890621,0.89957508,0.75958282,0.61776713};
SS(0.6251418,0.1440922,-0.5,0.75,0,-0.5,0.70841775,0,-0.20847891,0.50007058,0,-0.27987971){0.63751638,0.79494611,0.52293439,0.31006895};
SS(-0.65631386,-0.59724887,0.13822882,-0.50537844,-0.68762812,0.023695348,-0.3548152,-0.48825703,0.21848985,-0.42889738,-0.75253072,0.17523232){0.7890621,0.71483247,0.38862106,0.75958282};
SS(0,-0.75,0.5,0,-0.49989758,0.27983937,0,-0.7082575,0.2084616,-0.14394692,-0.62481063,0.5){0.79557901,0.30650831,0.52387062,0.63866347};
SS(-0.40125956,-0.65699374,0.33213173,-0.3548152,-0.48825703,0.21848985,-0.42889738,-0.75253072,0.17523232,-0.56348952,-0.47594309,0.3052276){0.69449311,0.38862106,0.75958282,0.61776713};
SS(-0.033588837,0.5879061,0.5,0.085954007,0.41736025,0.32943097,0.11523872,0.30161582,0.5,-0.18136176,0.40461939,0.5){0.57806214,0.27115576,0.33546792,0.42386795};
SS(-0.41651431,0.41690828,-0.5,-0.62938155,0.17932964,-0.37445272,-0.69937066,0.31351533,-0.5,-0.40408872,0.18166381,-0.5){0.57523437,0.55109073,0.81965428,0.42526168};
SS(0.55555556,1,-0.5,0.54326203,0.87223293,-0.356993,0.68985253,1,-0.19792707,0.43683247,1,-0.26068681){1.5352494,1.1662147,1.495304,1.2435523};
SS(-0.85707128,-0.1416783,-0.34083416,-1,-0.11111111,-0.5,-1,-0.25140376,-0.1934451,-1,-0.00018427889,-0.26378916){0.85441326,1.2438655,1.0790534,1.0508045};
SS(0,-0.25,0.5,-0.1853821,-0.42358473,0.30866054,0,-0.29157012,0.20836692,0,-0.49989758,0.27983937){0.28720824,0.29143101,0.11172813,0.30650831};
SS(-0.014815866,1,0.31001515,0.22886345,0.79287946,0.30210005,0.00029730467,0.80760978,0.5,-0.043441254,0.79173928,0.29440137){1.0772324,0.75332396,0.88423684,0.69563564};
SS(-0.0089783977,0.64320989,-0.13441642,0.086744979,0.52712982,0.027891324,0.08017426,0.31429474,-0.16745504,0.17777709,0.54047543,-0.2567554){0.41358858,0.26660844,0.11103103,0.36840304};
SS(-0.16707278,-0.087678023,-0.31121894,-0.28278924,0.041190137,-0.04219563,-0.098950987,-0.13391411,-0.14594667,-0.056808231,0.14323286,-0.13367928){0.11599041,0.063480395,0.03512721,0.022140076};
SS(-0.56348952,-0.47594309,0.3052276,-0.65631386,-0.59724887,0.13822882,-0.3548152,-0.48825703,0.21848985,-0.42889738,-0.75253072,0.17523232){0.61776713,0.7890621,0.38862106,0.75958282};
SS(0.60662231,0.34516964,-0.13972301,0.52218723,0.46943947,0.022097553,0.36021608,0.23247759,-0.012351094,0.63998586,0.17856447,0.051345521){0.48782847,0.46892029,0.16110593,0.42570365};
SS(0.51910919,0.22553632,-0.31417891,0.6251418,0.1440922,-0.5,0.70841775,0,-0.20847891,0.50007058,0,-0.27987971){0.40112301,0.63751638,0.52293439,0.31006895};
SS(-0.39654734,0.26661646,0.019312696,-0.32064519,0.49448821,1.4739833e-06,-0.54640726,0.34339216,0.19847863,-0.35521568,0.4957142,0.26668635){0.20710489,0.32892635,0.43575493,0.42001946};
SS(-0.65355936,0.25468043,-0.1897796,-0.39654734,0.26661646,0.019312696,-0.54631436,0.45612147,-0.00074796238,-0.63246299,0.29145388,0.035195127){0.51379882,0.20710489,0.48593017,0.47226275};
SS(-0.12233239,-0.87748906,-0.13583418,0,-1,-0.25,0,-1,-6.9388939e-15,-0.22019153,-1,-0.00010416607){0.78823805,1.0435946,0.98008605,1.0287732};
SS(0.4450496,1,-0.00012892076,0.62860594,0.86645525,0.049037492,0.68985253,1,-0.19792707,0.78186447,1,3.3673518e-05){1.179155,1.1303867,1.495304,1.5923176};
SS(-0.86742481,-0.86548068,-0.14483364,-1,-1,-0.25,-0.70823063,-1,-0.20843533,-0.77973152,-1,-0.0001062007){1.5085891,2.0422973,1.5240742,1.588155};
SS(1,1,-6.9388939e-15,0.88049681,0.87960137,0.13412341,1,1,0.25,0.78186447,1,3.3673518e-05){1.9807485,1.5518824,2.0447444,1.5923176};
SS(1,0.5,0.5,0.87881231,0.64063264,0.37220388,1,0.75,0.5,1,0.50005385,0.27984222){1.484684,1.3069719,1.7930237,1.3085441};
SS(0,-1,-0.25,-0.12233239,-0.87748906,-0.13583418,0,-1,-6.9388939e-15,0,-0.77970171,0.00010845427){1.0435946,0.78823805,0.98008605,0.58842154};
SS(-0.83248216,0.76782327,-0.31292259,-1,0.55555556,-0.5,-0.80479144,0.80504612,-0.5,-0.80558396,0.5878127,-0.29244037){1.366757,1.5309384,1.5255891,1.0616703};
SS(-0.36340067,-0.87821042,-0.37678589,-0.5,-1,-0.5,-0.25,-1,-0.5,-0.49995867,-1,-0.27986665){1.0307746,1.4844013,1.2929607,1.3082069};
SS(-0.16707278,-0.087678023,-0.31121894,-0.29413589,0.046284299,-0.31274881,-0.28278924,0.041190137,-0.04219563,-0.056808231,0.14323286,-0.13367928){0.11599041,0.1681493,0.063480395,0.022140076};
SS(-1,0.55555556,-0.5,-0.80558396,0.5878127,-0.29244037,-0.79644003,0.50064951,-0.5,-0.80479144,0.80504612,-0.5){1.5309384,1.0616703,1.115532,1.5255891};
SS(0.22886345,0.79287946,0.30210005,0.11111111,1,0.5,-0.014815866,1,0.31001515,0.00029730467,0.80760978,0.5){0.75332396,1.2368521,1.0772324,0.88423684};
SS(-0.32064519,0.49448821,1.4739833e-06,-0.096302334,0.43534175,-0.056072844,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.32892635,0.18078295,0.42991415,0.58653028};
SS(-0.096302334,0.43534175,-0.056072844,-0.0089783977,0.64320989,-0.13441642,-0.24654336,0.57133462,-0.25396354,-0.2401666,0.74114092,-0.051302261){0.18078295,0.41358858,0.42991415,0.58653028};
SS(0.55555556,1,-0.5,0.54326203,0.87223293,-0.356993,0.6657623,0.67544754,-0.5,0.81205362,0.80656044,-0.5){1.5352494,1.1662147,1.1304562,1.5391707};
SS(1,1,-0.25,0.8781758,0.86708556,-0.1989731,0.78186447,1,3.3673518e-05,0.82865019,1,-0.3214153){2.0438315,1.5462283,1.5923176,1.7714679};
SS(-0.82279039,-0.18997945,0.10657137,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027,-0.82595855,-0.48031431,0.11444494){0.70945047,1.0172898,0.68479998,0.90887195};
SS(0.87867265,0.36391919,-0.37720578,1,0.25,-0.5,1,0.5,-0.5,1,0.50010355,-0.27968748){1.03034,1.2935113,1.4840091,1.3071084};
SS(-0.85707128,-0.1416783,-0.34083416,-1,-0.11111111,-0.5,-1,-0.33333333,-0.5,-1,-0.25140376,-0.1934451){0.85441326,1.2438655,1.3342594,1.0790534};
SS(-0.82279039,-0.18997945,0.10657137,-1,-0.24887753,0.1953112,-1,-0.20076836,0.00061221676,-0.82595855,-0.48031431,0.11444494){0.70945047,1.0768014,1.0172898,0.90887195};
SS(-0.82595855,-0.48031431,0.11444494,-1,-0.5000565,0.0033661208,-1,-0.20076836,0.00061221676,-0.76760867,-0.33664988,-0.028298027){0.90887195,1.2263361,1.0172898,0.68479998};
SS(0.26064395,0.61953306,0.12890567,0.30434906,0.49798107,-4.0114635e-05,0.45788353,0.76094781,-0.0096633567,0.52218723,0.46943947,0.022097553){0.45328252,0.32377482,0.76853994,0.46892029};
SS(0.6251418,0.1440922,-0.5,0.51910919,0.22553632,-0.31417891,0.70841775,0,-0.20847891,0.77491511,0.22516452,-0.26425516){0.63751638,0.40112301,0.52293439,0.70313431};
SS(0.86971177,0.13024645,0.1427188,1,0,0.25,0.70845584,0,0.20819814,0.77985819,0,-0.00014691753){0.77797836,1.0436257,0.52761363,0.58919206};
SS(1,0,0.25,0.86971177,0.13024645,0.1427188,1,0.29178008,0.20838772,1,0.2203628,5.6826691e-05){1.0436257,0.77797836,1.1084285,1.0268649};
SS(0.88049681,0.87960137,0.13412341,1,1,-6.9388939e-15,1,1,0.25,1,0.77979347,0.00010253841){1.5518824,1.9807485,2.0447444,1.5887874};
SS(1,1,-0.25,0.8781758,0.86708556,-0.1989731,1,0.70844226,-0.20827687,1,0.77979347,0.00010253841){2.0438315,1.5462283,1.5310675,1.5887874};
SS(0,0,-0.25,0.13402468,0.11673163,-0.1460819,0,0,-6.9388939e-15,0.22032809,0,-9.1119885e-05){0.044304329,0.039337265,-0.017891206,0.027339551};
SS(-0.75,-1,0.5,-0.63815223,-0.88141187,0.37488811,-0.5,-1,0.5,-0.4999534,-1,0.27968311){1.7943537,1.3088768,1.4840089,1.3075402};
SS(-0.49391083,0.27907498,-0.27264436,-0.65355936,0.25468043,-0.1897796,-0.39654734,0.26661646,0.019312696,-0.54631436,0.45612147,-0.00074796238){0.37398026,0.51379882,0.20710489,0.48593017};
SS(0.39612945,0.70614162,0.21524614,0.26064395,0.61953306,0.12890567,0.45788353,0.76094781,-0.0096633567,0.52218723,0.46943947,0.022097553){0.68453461,0.45328252,0.76853994,0.46892029};
SS(-0.82595855,-0.48031431,0.11444494,-1,-0.5000565,0.0033661208,-1,-0.24887753,0.1953112,-1,-0.20076836,0.00061221676){0.90887195,1.2263361,1.0768014,1.0172898};
};
