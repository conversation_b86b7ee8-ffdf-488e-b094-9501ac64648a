ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: using custom schema-name function */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'component8',
/* time_stamp */ '2006-02-17T14:13:38+01:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v8',
/* originating_system */ '',
/* authorisation */ '');

FILE_SCHEMA (('CONFIG_CONTROL_DESIGN'));
ENDSEC;

DATA;
#10=DESIGN_CONTEXT('3D Mechanical Parts',#83,'design');
#11=PRODUCT_DEFINITION('A','First version',#53,#10);
#12=DATE_TIME_ROLE('classification_date');
#13=DATE_TIME_ROLE('creation_date');
#14=CC_DESIGN_DATE_AND_TIME_ASSIGNMENT(#38,#12,(#17));
#15=CC_DESIGN_DATE_AND_TIME_ASSIGNMENT(#40,#13,(#11));
#16=SECURITY_CLASSIFICATION_LEVEL('unclassified');
#17=SECURITY_CLASSIFICATION('A','Security for version',#16);
#18=CC_DESIGN_SECURITY_CLASSIFICATION(#17,(#53));
#19=APPROVAL_ROLE('Version approval');
#20=APPROVAL_ROLE('Version Security approval');
#21=APPROVAL_ROLE('Definition approval');
#22=APPROVAL_PERSON_ORGANIZATION(#71,#47,#19);
#23=APPROVAL_PERSON_ORGANIZATION(#72,#48,#20);
#24=APPROVAL_PERSON_ORGANIZATION(#75,#49,#21);
#25=COORDINATED_UNIVERSAL_TIME_OFFSET(5,0,.BEHIND.);
#26=LOCAL_TIME(0,0,0.,#25);
#27=LOCAL_TIME(0,0,0.,#25);
#28=LOCAL_TIME(0,0,0.,#25);
#29=LOCAL_TIME(0,0,0.,#25);
#30=LOCAL_TIME(0,0,0.,#25);
#31=CALENDAR_DATE(1999,1,1);
#32=CALENDAR_DATE(1999,1,1);
#33=CALENDAR_DATE(1999,1,1);
#34=CALENDAR_DATE(1999,1,1);
#35=CALENDAR_DATE(1999,1,1);
#36=DATE_AND_TIME(#31,#26);
#37=DATE_AND_TIME(#32,#27);
#38=DATE_AND_TIME(#33,#28);
#39=DATE_AND_TIME(#34,#29);
#40=DATE_AND_TIME(#35,#30);
#41=APPROVAL_DATE_TIME(#36,#47);
#42=APPROVAL_DATE_TIME(#37,#48);
#43=APPROVAL_DATE_TIME(#39,#49);
#44=APPROVAL_STATUS('not_yet_approved');
#45=APPROVAL_STATUS('not_yet_approved');
#46=APPROVAL_STATUS('not_yet_approved');
#47=APPROVAL(#44,'Version approval');
#48=APPROVAL(#45,'Version Security approval');
#49=APPROVAL(#46,'Definition approval');
#50=CC_DESIGN_APPROVAL(#47,(#53));
#51=CC_DESIGN_APPROVAL(#48,(#17));
#52=CC_DESIGN_APPROVAL(#49,(#11));
#53=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('A',
'First version',#85,.MADE.);
#54=PERSON_AND_ORGANIZATION_ROLE('design_owner');
#55=PERSON_AND_ORGANIZATION_ROLE('creator');
#56=PERSON_AND_ORGANIZATION_ROLE('design_supplier');
#57=PERSON_AND_ORGANIZATION_ROLE('classification_officer');
#58=PERSON_AND_ORGANIZATION_ROLE('creator');
#59=ORGANIZATION('STI','lab','lab');
#60=ORGANIZATION('STI','unknown','unknown');
#61=ORGANIZATION('STI','unknown','unknown');
#62=ORGANIZATION('STI','unknown','unknown');
#63=ORGANIZATION('STI','unknown','unknown');
#64=ORGANIZATION('STI','unknown','unknown');
#65=ORGANIZATION('STI','unknown','unknown');
#66=PERSON('1','Box','vc60',$,$,$);
#67=PERSON('2','last','first',$,$,$);
#68=PERSON('3','President','Mr.',$,$,$);
#69=PERSON_AND_ORGANIZATION(#66,#59);
#70=PERSON_AND_ORGANIZATION(#67,#60);
#71=PERSON_AND_ORGANIZATION(#68,#61);
#72=PERSON_AND_ORGANIZATION(#68,#62);
#73=PERSON_AND_ORGANIZATION(#68,#63);
#74=PERSON_AND_ORGANIZATION(#67,#64);
#75=PERSON_AND_ORGANIZATION(#68,#65);
#76=CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#69,#54,(#85));
#77=CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#70,#55,(#53));
#78=CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#70,#56,(#53));
#79=CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#73,#57,(#17));
#80=CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#74,#58,(#11));
#81=PRODUCT_RELATED_PRODUCT_CATEGORY('detail','detail',(#85));
#82=APPLICATION_PROTOCOL_DEFINITION('International Standard',
'config_control_design',1994,#83);
#83=APPLICATION_CONTEXT(
'configuration controlled 3d designs of mechanical parts and assemblies
');
#84=MECHANICAL_CONTEXT('3D Mechanical Parts',#83,'mechanical');
#85=PRODUCT('8','Rhino Product','Rhino converted to STEP',(#84));
#86=PRODUCT_DEFINITION_SHAPE('A','First version',#11);
#87=SHAPE_DEFINITION_REPRESENTATION(#86,#97);
#88=MANIFOLD_SOLID_BREP('Default_brep\X\01',#98);
#89=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#90=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#91=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#92=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.01745329252),#90);
#93=(
CONVERSION_BASED_UNIT('DEGREES',#92)
NAMED_UNIT(#91)
PLANE_ANGLE_UNIT()
);
#94=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#95=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.0741808824497),#89,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted co
nnectivities');
#96=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#95))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#94,#93,#89))
REPRESENTATION_CONTEXT('ID1','3D')
);
#97=ADVANCED_BREP_SHAPE_REPRESENTATION('brep_rep',(#88),#96);
#98=CLOSED_SHELL('',(#99,#100,#101,#102,#103,#104,#105,#106,#107,#108,#109,
#110,#111,#112,#113,#114,#115,#116,#117,#118,#119));
#99=ADVANCED_FACE('',(#121,#120),#350,.T.);
#100=ADVANCED_FACE('',(#122),#336,.T.);
#101=ADVANCED_FACE('',(#123),#351,.T.);
#102=ADVANCED_FACE('',(#124),#337,.T.);
#103=ADVANCED_FACE('',(#125),#352,.T.);
#104=ADVANCED_FACE('',(#126),#353,.T.);
#105=ADVANCED_FACE('',(#127),#354,.T.);
#106=ADVANCED_FACE('',(#128),#355,.T.);
#107=ADVANCED_FACE('',(#129),#356,.T.);
#108=ADVANCED_FACE('',(#130),#338,.T.);
#109=ADVANCED_FACE('',(#131),#339,.T.);
#110=ADVANCED_FACE('',(#132),#340,.T.);
#111=ADVANCED_FACE('',(#133),#341,.T.);
#112=ADVANCED_FACE('',(#134),#342,.T.);
#113=ADVANCED_FACE('',(#135),#343,.T.);
#114=ADVANCED_FACE('',(#136),#344,.T.);
#115=ADVANCED_FACE('',(#137),#345,.T.);
#116=ADVANCED_FACE('',(#138),#346,.T.);
#117=ADVANCED_FACE('',(#139),#347,.T.);
#118=ADVANCED_FACE('',(#140),#348,.T.);
#119=ADVANCED_FACE('',(#141),#349,.T.);
#120=FACE_BOUND('',#143,.T.);
#121=FACE_OUTER_BOUND('',#142,.T.);
#122=FACE_OUTER_BOUND('',#144,.T.);
#123=FACE_OUTER_BOUND('',#145,.T.);
#124=FACE_OUTER_BOUND('',#146,.T.);
#125=FACE_OUTER_BOUND('',#147,.T.);
#126=FACE_OUTER_BOUND('',#148,.T.);
#127=FACE_OUTER_BOUND('',#149,.T.);
#128=FACE_OUTER_BOUND('',#150,.T.);
#129=FACE_OUTER_BOUND('',#151,.T.);
#130=FACE_OUTER_BOUND('',#152,.T.);
#131=FACE_OUTER_BOUND('',#153,.T.);
#132=FACE_OUTER_BOUND('',#154,.T.);
#133=FACE_OUTER_BOUND('',#155,.T.);
#134=FACE_OUTER_BOUND('',#156,.T.);
#135=FACE_OUTER_BOUND('',#157,.T.);
#136=FACE_OUTER_BOUND('',#158,.T.);
#137=FACE_OUTER_BOUND('',#159,.T.);
#138=FACE_OUTER_BOUND('',#160,.T.);
#139=FACE_OUTER_BOUND('',#161,.T.);
#140=FACE_OUTER_BOUND('',#162,.T.);
#141=FACE_OUTER_BOUND('',#163,.T.);
#142=EDGE_LOOP('',(#164,#165,#166,#167,#168,#169));
#143=EDGE_LOOP('',(#170,#171));
#144=EDGE_LOOP('',(#172,#173,#174));
#145=EDGE_LOOP('',(#175,#176,#177,#178,#179));
#146=EDGE_LOOP('',(#180,#181,#182,#183,#184,#185,#186));
#147=EDGE_LOOP('',(#187,#188,#189,#190,#191,#192));
#148=EDGE_LOOP('',(#193,#194,#195,#196,#197));
#149=EDGE_LOOP('',(#198,#199,#200,#201,#202));
#150=EDGE_LOOP('',(#203,#204,#205,#206,#207,#208));
#151=EDGE_LOOP('',(#209,#210,#211,#212,#213));
#152=EDGE_LOOP('',(#214,#215,#216));
#153=EDGE_LOOP('',(#217,#218,#219));
#154=EDGE_LOOP('',(#220,#221,#222));
#155=EDGE_LOOP('',(#223,#224,#225));
#156=EDGE_LOOP('',(#226,#227,#228));
#157=EDGE_LOOP('',(#229,#230,#231,#232));
#158=EDGE_LOOP('',(#233,#234,#235,#236));
#159=EDGE_LOOP('',(#237,#238,#239,#240));
#160=EDGE_LOOP('',(#241,#242,#243,#244));
#161=EDGE_LOOP('',(#245,#246,#247,#248));
#162=EDGE_LOOP('',(#249,#250,#251,#252));
#163=EDGE_LOOP('',(#253,#254,#255,#256,#257,#258,#259));
#164=ORIENTED_EDGE('',*,*,#260,.T.);
#165=ORIENTED_EDGE('',*,*,#265,.T.);
#166=ORIENTED_EDGE('',*,*,#264,.T.);
#167=ORIENTED_EDGE('',*,*,#263,.T.);
#168=ORIENTED_EDGE('',*,*,#262,.T.);
#169=ORIENTED_EDGE('',*,*,#261,.T.);
#170=ORIENTED_EDGE('',*,*,#267,.T.);
#171=ORIENTED_EDGE('',*,*,#266,.T.);
#172=ORIENTED_EDGE('',*,*,#268,.T.);
#173=ORIENTED_EDGE('',*,*,#260,.F.);
#174=ORIENTED_EDGE('',*,*,#269,.T.);
#175=ORIENTED_EDGE('',*,*,#270,.T.);
#176=ORIENTED_EDGE('',*,*,#273,.T.);
#177=ORIENTED_EDGE('',*,*,#272,.T.);
#178=ORIENTED_EDGE('',*,*,#268,.F.);
#179=ORIENTED_EDGE('',*,*,#271,.T.);
#180=ORIENTED_EDGE('',*,*,#307,.T.);
#181=ORIENTED_EDGE('',*,*,#277,.T.);
#182=ORIENTED_EDGE('',*,*,#276,.T.);
#183=ORIENTED_EDGE('',*,*,#270,.F.);
#184=ORIENTED_EDGE('',*,*,#275,.T.);
#185=ORIENTED_EDGE('',*,*,#283,.F.);
#186=ORIENTED_EDGE('',*,*,#282,.T.);
#187=ORIENTED_EDGE('',*,*,#275,.F.);
#188=ORIENTED_EDGE('',*,*,#271,.F.);
#189=ORIENTED_EDGE('',*,*,#269,.F.);
#190=ORIENTED_EDGE('',*,*,#285,.T.);
#191=ORIENTED_EDGE('',*,*,#284,.T.);
#192=ORIENTED_EDGE('',*,*,#274,.F.);
#193=ORIENTED_EDGE('',*,*,#280,.F.);
#194=ORIENTED_EDGE('',*,*,#284,.F.);
#195=ORIENTED_EDGE('',*,*,#288,.T.);
#196=ORIENTED_EDGE('',*,*,#287,.T.);
#197=ORIENTED_EDGE('',*,*,#286,.T.);
#198=ORIENTED_EDGE('',*,*,#279,.F.);
#199=ORIENTED_EDGE('',*,*,#286,.F.);
#200=ORIENTED_EDGE('',*,*,#291,.T.);
#201=ORIENTED_EDGE('',*,*,#290,.T.);
#202=ORIENTED_EDGE('',*,*,#289,.T.);
#203=ORIENTED_EDGE('',*,*,#278,.F.);
#204=ORIENTED_EDGE('',*,*,#289,.F.);
#205=ORIENTED_EDGE('',*,*,#294,.T.);
#206=ORIENTED_EDGE('',*,*,#293,.T.);
#207=ORIENTED_EDGE('',*,*,#292,.T.);
#208=ORIENTED_EDGE('',*,*,#277,.F.);
#209=ORIENTED_EDGE('',*,*,#276,.F.);
#210=ORIENTED_EDGE('',*,*,#292,.F.);
#211=ORIENTED_EDGE('',*,*,#296,.T.);
#212=ORIENTED_EDGE('',*,*,#295,.T.);
#213=ORIENTED_EDGE('',*,*,#273,.F.);
#214=ORIENTED_EDGE('',*,*,#295,.F.);
#215=ORIENTED_EDGE('',*,*,#265,.F.);
#216=ORIENTED_EDGE('',*,*,#272,.F.);
#217=ORIENTED_EDGE('',*,*,#293,.F.);
#218=ORIENTED_EDGE('',*,*,#264,.F.);
#219=ORIENTED_EDGE('',*,*,#296,.F.);
#220=ORIENTED_EDGE('',*,*,#290,.F.);
#221=ORIENTED_EDGE('',*,*,#263,.F.);
#222=ORIENTED_EDGE('',*,*,#294,.F.);
#223=ORIENTED_EDGE('',*,*,#287,.F.);
#224=ORIENTED_EDGE('',*,*,#262,.F.);
#225=ORIENTED_EDGE('',*,*,#291,.F.);
#226=ORIENTED_EDGE('',*,*,#285,.F.);
#227=ORIENTED_EDGE('',*,*,#261,.F.);
#228=ORIENTED_EDGE('',*,*,#288,.F.);
#229=ORIENTED_EDGE('',*,*,#299,.F.);
#230=ORIENTED_EDGE('',*,*,#298,.F.);
#231=ORIENTED_EDGE('',*,*,#297,.F.);
#232=ORIENTED_EDGE('',*,*,#281,.F.);
#233=ORIENTED_EDGE('',*,*,#297,.T.);
#234=ORIENTED_EDGE('',*,*,#300,.F.);
#235=ORIENTED_EDGE('',*,*,#299,.T.);
#236=ORIENTED_EDGE('',*,*,#282,.F.);
#237=ORIENTED_EDGE('',*,*,#303,.T.);
#238=ORIENTED_EDGE('',*,*,#302,.F.);
#239=ORIENTED_EDGE('',*,*,#301,.F.);
#240=ORIENTED_EDGE('',*,*,#300,.T.);
#241=ORIENTED_EDGE('',*,*,#303,.F.);
#242=ORIENTED_EDGE('',*,*,#298,.T.);
#243=ORIENTED_EDGE('',*,*,#301,.T.);
#244=ORIENTED_EDGE('',*,*,#304,.F.);
#245=ORIENTED_EDGE('',*,*,#306,.F.);
#246=ORIENTED_EDGE('',*,*,#304,.T.);
#247=ORIENTED_EDGE('',*,*,#305,.F.);
#248=ORIENTED_EDGE('',*,*,#267,.F.);
#249=ORIENTED_EDGE('',*,*,#305,.T.);
#250=ORIENTED_EDGE('',*,*,#302,.T.);
#251=ORIENTED_EDGE('',*,*,#306,.T.);
#252=ORIENTED_EDGE('',*,*,#266,.F.);
#253=ORIENTED_EDGE('',*,*,#281,.T.);
#254=ORIENTED_EDGE('',*,*,#283,.T.);
#255=ORIENTED_EDGE('',*,*,#274,.T.);
#256=ORIENTED_EDGE('',*,*,#280,.T.);
#257=ORIENTED_EDGE('',*,*,#279,.T.);
#258=ORIENTED_EDGE('',*,*,#278,.T.);
#259=ORIENTED_EDGE('',*,*,#307,.F.);
#260=EDGE_CURVE('',#308,#309,#823,.T.);
#261=EDGE_CURVE('',#310,#308,#824,.T.);
#262=EDGE_CURVE('',#311,#310,#825,.T.);
#263=EDGE_CURVE('',#312,#311,#826,.T.);
#264=EDGE_CURVE('',#313,#312,#827,.T.);
#265=EDGE_CURVE('',#309,#313,#828,.T.);
#266=EDGE_CURVE('',#314,#315,#829,.T.);
#267=EDGE_CURVE('',#315,#314,#830,.T.);
#268=EDGE_CURVE('',#316,#309,#357,.T.);
#269=EDGE_CURVE('',#308,#316,#358,.T.);
#270=EDGE_CURVE('',#317,#318,#831,.T.);
#271=EDGE_CURVE('',#316,#317,#359,.T.);
#272=EDGE_CURVE('',#319,#309,#360,.T.);
#273=EDGE_CURVE('',#318,#319,#361,.T.);
#274=EDGE_CURVE('',#320,#321,#362,.T.);
#275=EDGE_CURVE('',#317,#320,#363,.T.);
#276=EDGE_CURVE('',#322,#318,#364,.T.);
#277=EDGE_CURVE('',#323,#322,#365,.T.);
#278=EDGE_CURVE('',#324,#323,#366,.T.);
#279=EDGE_CURVE('',#325,#324,#367,.T.);
#280=EDGE_CURVE('',#321,#325,#368,.T.);
#281=EDGE_CURVE('',#326,#327,#832,.T.);
#282=EDGE_CURVE('',#327,#326,#833,.T.);
#283=EDGE_CURVE('',#327,#320,#834,.T.);
#284=EDGE_CURVE('',#328,#321,#369,.T.);
#285=EDGE_CURVE('',#308,#328,#370,.T.);
#286=EDGE_CURVE('',#329,#325,#371,.T.);
#287=EDGE_CURVE('',#310,#329,#372,.T.);
#288=EDGE_CURVE('',#328,#310,#373,.T.);
#289=EDGE_CURVE('',#330,#324,#374,.T.);
#290=EDGE_CURVE('',#311,#330,#375,.T.);
#291=EDGE_CURVE('',#329,#311,#376,.T.);
#292=EDGE_CURVE('',#331,#322,#377,.T.);
#293=EDGE_CURVE('',#312,#331,#378,.T.);
#294=EDGE_CURVE('',#330,#312,#379,.T.);
#295=EDGE_CURVE('',#313,#319,#380,.T.);
#296=EDGE_CURVE('',#331,#313,#381,.T.);
#297=EDGE_CURVE('',#327,#332,#382,.T.);
#298=EDGE_CURVE('',#332,#333,#835,.T.);
#299=EDGE_CURVE('',#333,#326,#383,.T.);
#300=EDGE_CURVE('',#333,#332,#836,.T.);
#301=EDGE_CURVE('',#333,#334,#384,.T.);
#302=EDGE_CURVE('',#334,#335,#837,.T.);
#303=EDGE_CURVE('',#332,#335,#385,.T.);
#304=EDGE_CURVE('',#335,#334,#838,.T.);
#305=EDGE_CURVE('',#314,#334,#386,.T.);
#306=EDGE_CURVE('',#335,#315,#387,.T.);
#307=EDGE_CURVE('',#326,#323,#839,.T.);
#308=VERTEX_POINT('',#795);
#309=VERTEX_POINT('',#796);
#310=VERTEX_POINT('',#797);
#311=VERTEX_POINT('',#798);
#312=VERTEX_POINT('',#799);
#313=VERTEX_POINT('',#800);
#314=VERTEX_POINT('',#801);
#315=VERTEX_POINT('',#802);
#316=VERTEX_POINT('',#803);
#317=VERTEX_POINT('',#804);
#318=VERTEX_POINT('',#805);
#319=VERTEX_POINT('',#806);
#320=VERTEX_POINT('',#807);
#321=VERTEX_POINT('',#808);
#322=VERTEX_POINT('',#809);
#323=VERTEX_POINT('',#810);
#324=VERTEX_POINT('',#811);
#325=VERTEX_POINT('',#812);
#326=VERTEX_POINT('',#813);
#327=VERTEX_POINT('',#814);
#328=VERTEX_POINT('',#815);
#329=VERTEX_POINT('',#816);
#330=VERTEX_POINT('',#817);
#331=VERTEX_POINT('',#818);
#332=VERTEX_POINT('',#819);
#333=VERTEX_POINT('',#820);
#334=VERTEX_POINT('',#821);
#335=VERTEX_POINT('',#822);
#336=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#645,#646),(#647,#648),(#649,#650)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(2.09439510241284,3.14159264302763),
(18.4752086140631,21.3619145805212),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025406429892,0.866025406429892),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#337=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,2,((#655,#656,#657),(#658,#659,#660),(#661,#662,#663),
(#664,#665,#666),(#667,#668,#669)),.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,2,3),(3,3),(0.,1.5707963267949,3.14159265358979),
(-1.1954143478557,-0.876944115348796),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.987348854483275,1.),(0.707106781186548,
0.698161070401894,0.707106781186548),(1.,0.987348854483275,1.),(0.707106781186548,
0.698161070401894,0.707106781186548),(1.,0.987348854483275,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#338=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#690,#691),(#692,#693),(#694,#695)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(1.04719756076893,2.09439510241292),
(18.4752086140631,21.3619145805234),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025406172591,0.866025406172591),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#339=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#696,#697),(#698,#699),(#700,#701)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(0.,1.04719756076893),(18.475208614065,
21.3619145805216),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025401391356,0.866025401391356),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#340=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#702,#703),(#704,#705),(#706,#707)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(5.23598775599053,6.28318530717959),
(18.4752086140647,21.3619145805212),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025403786323,0.866025403786323),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#341=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#708,#709),(#710,#711),(#712,#713)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(4.18879021424446,5.23598775599054),
(18.4752086140623,21.3619145805234),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025406147067,0.866025406147067),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#342=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,1,((#714,#715),(#716,#717),(#718,#719)),.UNSPECIFIED.,
.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,3),(2,2),(3.14159264302763,4.18879021424457),
(18.4752086140623,21.3619145805217),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,1.),(0.866025398779352,0.866025398779352),
(1.,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#343=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#720,#721,#722,#723,#724),(#725,#726,#727,#728,#729)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(13.6427414595216,15.0362158111564),
(3.14159265358979,4.71238898038469,6.28318530717959),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#344=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#730,#731,#732,#733,#734),(#735,#736,#737,#738,#739)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(13.6427414595216,15.0362158111564),
(0.,1.5707963267949,3.14159265358979),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#345=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#740,#741,#742,#743,#744),(#745,#746,#747,#748,#749)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(3.0293749999999,33.3231249999989),
(0.,1.5707963267949,3.14159265358979),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#346=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#750,#751,#752,#753,#754),(#755,#756,#757,#758,#759)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(3.0293749999999,33.3231249999989),
(3.14159265358979,4.71238898038469,6.28318530717959),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#347=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#760,#761,#762,#763,#764),(#765,#766,#767,#768,#769)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(13.642741459518,15.556349186104),
(3.14159265358979,4.71238898038469,6.28318530717959),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#348=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(1,2,((#770,#771,#772,#773,#774),(#775,#776,#777,#778,#779)),
.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((2,2),(3,2,3),(13.642741459518,15.556349186104),
(0.,1.5707963267949,3.14159265358979),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.707106781186548,1.,0.707106781186548,1.),
(1.,0.707106781186548,1.,0.707106781186548,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#349=(
BOUNDED_SURFACE()
B_SPLINE_SURFACE(2,2,((#780,#781,#782),(#783,#784,#785),(#786,#787,#788),
(#789,#790,#791),(#792,#793,#794)),.UNSPECIFIED.,.F.,.F.,.F.)
B_SPLINE_SURFACE_WITH_KNOTS((3,2,3),(3,3),(3.14159265358979,4.71238898038469,
6.28318530717959),(-1.1954143478557,-0.876944115348777),.UNSPECIFIED.)
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_SURFACE(((1.,0.987348854483273,1.),(0.707106781186548,
0.698161070401892,0.707106781186548),(1.,0.987348854483273,1.),(0.707106781186548,
0.698161070401892,0.707106781186548),(1.,0.987348854483273,1.)))
REPRESENTATION_ITEM('')
SURFACE()
);
#350=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#641,#642),(#643,#644)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(-16.32,16.32),(-16.32,16.32),.UNSPECIFIED.);
#351=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#651,#652),(#653,#654)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(-2.38902752675769E-012,18.4752086140692),(2.86329509834288,
33.2983832168481),.UNSPECIFIED.);
#352=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#670,#671),(#672,#673)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(-4.2819081613743E-012,18.4752086140697),(2.8632950983419,
33.2983832168481),.UNSPECIFIED.);
#353=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#674,#675),(#676,#677)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(1.99747916609408E-013,18.475208614069),(2.86329509835079,
33.298383216848),.UNSPECIFIED.);
#354=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#678,#679),(#680,#681)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(-3.51828368147731E-012,18.4752086140692),(2.86329509834281,
33.2983832168481),.UNSPECIFIED.);
#355=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#682,#683),(#684,#685)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(-6.28119778411929E-012,18.475208614067),(2.8632950983419,
33.2983832168481),.UNSPECIFIED.);
#356=B_SPLINE_SURFACE_WITH_KNOTS('',1,1,((#686,#687),(#688,#689)),.UNSPECIFIED.,
.F.,.F.,.F.,(2,2),(2,2),(1.97237940780014E-013,18.475208614069),(2.86329509835088,
33.298383216848),.UNSPECIFIED.);
#357=B_SPLINE_CURVE_WITH_KNOTS('',3,(#416,#417,#418,#419,#420,#421,#422,
#423,#424,#425,#426,#427,#428,#429),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,2,4),
(-1.,-0.500198856225791,-0.250416226749761,-0.125448063373225,-0.0627240316866126,
-0.0313620158433063,0.),.UNSPECIFIED.);
#358=B_SPLINE_CURVE_WITH_KNOTS('',3,(#430,#431,#432,#433,#434,#435,#436,
#437,#438,#439,#440,#441,#442,#443),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,2,4),
(-0.999999992071867,-0.96862313067443,-0.937246269276993,-0.874492546482118,
-0.749715402946071,-0.499775239848227,0.),.UNSPECIFIED.);
#359=B_SPLINE_CURVE_WITH_KNOTS('',1,(#447,#448),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,26.575711052679),.UNSPECIFIED.);
#360=B_SPLINE_CURVE_WITH_KNOTS('',3,(#449,#450,#451,#452,#453,#454,#455,
#456),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.200000000000708,0.400000000001416,
0.600000000002124,0.800000000002832,0.999999995332692),.UNSPECIFIED.);
#361=B_SPLINE_CURVE_WITH_KNOTS('',1,(#457,#458),.UNSPECIFIED.,.F.,.F.,(2,
2),(-26.575711052679,0.),.UNSPECIFIED.);
#362=B_SPLINE_CURVE_WITH_KNOTS('',3,(#459,#460,#461,#462,#463,#464),.UNSPECIFIED.,
.F.,.F.,(4,2,4),(0.,4.72231211571594,9.47834833217417),.UNSPECIFIED.);
#363=B_SPLINE_CURVE_WITH_KNOTS('',3,(#465,#466,#467,#468,#469,#470),.UNSPECIFIED.,
.F.,.F.,(4,2,4),(0.,4.75565606535215,9.47834833217497),.UNSPECIFIED.);
#364=B_SPLINE_CURVE_WITH_KNOTS('',3,(#471,#472,#473,#474,#475,#476,#477,
#478,#479,#480),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,4),(0.,4.754965254404,9.47834833215305,
14.2006604478573,18.9566966643144),.UNSPECIFIED.);
#365=B_SPLINE_CURVE_WITH_KNOTS('',3,(#481,#482,#483,#484,#485,#486),.UNSPECIFIED.,
.F.,.F.,(4,2,4),(0.,4.72231211571582,9.47834833217417),.UNSPECIFIED.);
#366=B_SPLINE_CURVE_WITH_KNOTS('',3,(#487,#488,#489,#490,#491,#492),.UNSPECIFIED.,
.F.,.F.,(4,2,4),(0.,4.75565606535142,9.47834833217218),.UNSPECIFIED.);
#367=B_SPLINE_CURVE_WITH_KNOTS('',3,(#493,#494,#495,#496,#497,#498,#499,
#500,#501,#502),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,4),(0.,4.75496525441116,9.47834833216791,
14.2006604478793,18.9566966643339),.UNSPECIFIED.);
#368=B_SPLINE_CURVE_WITH_KNOTS('',3,(#503,#504,#505,#506,#507,#508,#509,
#510,#511,#512),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,4),(0.,4.75496525440376,9.47834833215352,
14.2006604478586,18.9566966643163),.UNSPECIFIED.);
#369=B_SPLINE_CURVE_WITH_KNOTS('',1,(#526,#527),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,26.575711052679),.UNSPECIFIED.);
#370=B_SPLINE_CURVE_WITH_KNOTS('',3,(#528,#529,#530,#531,#532,#533,#534,
#535),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),.UNSPECIFIED.);
#371=B_SPLINE_CURVE_WITH_KNOTS('',1,(#536,#537),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,26.575711052679),.UNSPECIFIED.);
#372=B_SPLINE_CURVE_WITH_KNOTS('',3,(#538,#539,#540,#541,#542,#543,#544,
#545),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),.UNSPECIFIED.);
#373=B_SPLINE_CURVE_WITH_KNOTS('',3,(#546,#547,#548,#549,#550,#551,#552,
#553),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.200000000000573,0.400000000001146,
0.60000000000172,0.800000000002293,0.999999997488464),.UNSPECIFIED.);
#374=B_SPLINE_CURVE_WITH_KNOTS('',1,(#554,#555),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,26.575711052679),.UNSPECIFIED.);
#375=B_SPLINE_CURVE_WITH_KNOTS('',3,(#556,#557,#558,#559,#560,#561,#562,
#563),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),.UNSPECIFIED.);
#376=B_SPLINE_CURVE_WITH_KNOTS('',3,(#564,#565,#566,#567,#568,#569,#570,
#571),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.200000000000607,0.400000000001214,
0.600000000001821,0.800000000002428,0.999999995951374),.UNSPECIFIED.);
#377=B_SPLINE_CURVE_WITH_KNOTS('',1,(#572,#573),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,26.575711052679),.UNSPECIFIED.);
#378=B_SPLINE_CURVE_WITH_KNOTS('',3,(#574,#575,#576,#577,#578,#579,#580,
#581),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(6.148701248843E-009,0.199999999993261,
0.399999999994946,0.59999999999663,0.799999999998315,1.),.UNSPECIFIED.);
#379=B_SPLINE_CURVE_WITH_KNOTS('',3,(#582,#583,#584,#585,#586,#587,#588,
#589),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),.UNSPECIFIED.);
#380=B_SPLINE_CURVE_WITH_KNOTS('',3,(#590,#591,#592,#593,#594,#595,#596,
#597),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(9.51828212105803E-009,0.2,0.4,
0.6,0.8,1.),.UNSPECIFIED.);
#381=B_SPLINE_CURVE_WITH_KNOTS('',3,(#598,#599,#600,#601,#602,#603,#604,
#605),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),.UNSPECIFIED.);
#382=B_SPLINE_CURVE_WITH_KNOTS('',1,(#606,#607),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,1.39347435163474),.UNSPECIFIED.);
#383=B_SPLINE_CURVE_WITH_KNOTS('',1,(#613,#614),.UNSPECIFIED.,.F.,.F.,(2,
2),(13.6427414595216,15.0362158111564),.UNSPECIFIED.);
#384=B_SPLINE_CURVE_WITH_KNOTS('',1,(#620,#621),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,30.293749999999),.UNSPECIFIED.);
#385=B_SPLINE_CURVE_WITH_KNOTS('',1,(#627,#628),.UNSPECIFIED.,.F.,.F.,(2,
2),(3.0293749999999,33.3231249999989),.UNSPECIFIED.);
#386=B_SPLINE_CURVE_WITH_KNOTS('',1,(#634,#635),.UNSPECIFIED.,.F.,.F.,(2,
2),(-1.91360772658609,0.),.UNSPECIFIED.);
#387=B_SPLINE_CURVE_WITH_KNOTS('',1,(#636,#637),.UNSPECIFIED.,.F.,.F.,(2,
2),(13.642741459518,15.556349186104),.UNSPECIFIED.);
#388=CARTESIAN_POINT('',(-1.68994742731324E-007,188.5,-16.));
#389=CARTESIAN_POINT('',(-9.23760436315522,188.5,-15.9999999024309));
#390=CARTESIAN_POINT('',(-13.8564064603933,188.5,-8.00000000027335));
#391=CARTESIAN_POINT('',(13.8564065362164,188.5,-7.99999986894361));
#392=CARTESIAN_POINT('',(9.23760435158966,188.5,-16.0000000975691));
#393=CARTESIAN_POINT('',(-1.68994742731324E-007,188.5,-16.));
#394=CARTESIAN_POINT('',(13.8564064604906,188.5,8.00000000010462));
#395=CARTESIAN_POINT('',(18.4752085636647,188.5,8.74408117831211E-008));
#396=CARTESIAN_POINT('',(13.8564065362164,188.5,-7.99999986894361));
#397=CARTESIAN_POINT('',(-8.74990178961606E-008,188.5,16.));
#398=CARTESIAN_POINT('',(9.23760427778725,188.5,16.0000000505175));
#399=CARTESIAN_POINT('',(13.8564064604906,188.5,8.00000000010462));
#400=CARTESIAN_POINT('',(-13.8564065371297,188.5,7.9999998673619));
#401=CARTESIAN_POINT('',(-9.23760443830517,188.5,15.9999999494824));
#402=CARTESIAN_POINT('',(-8.74990178961606E-008,188.5,16.));
#403=CARTESIAN_POINT('',(-13.8564064603933,188.5,-8.00000000027335));
#404=CARTESIAN_POINT('',(-18.4752085631208,188.5,-8.86076821103369E-008));
#405=CARTESIAN_POINT('',(-13.8564065371297,188.5,7.9999998673619));
#406=CARTESIAN_POINT('',(-5.84922662553323E-014,188.5,11.));
#407=CARTESIAN_POINT('',(-11.0000000000001,188.5,11.));
#408=CARTESIAN_POINT('',(-11.0000000000001,188.5,-1.35760917691323E-014));
#409=CARTESIAN_POINT('',(-11.0000000000001,188.5,-11.));
#410=CARTESIAN_POINT('',(-5.71451992661277E-014,188.5,-11.));
#411=CARTESIAN_POINT('',(-5.71451992661277E-014,188.5,-11.));
#412=CARTESIAN_POINT('',(10.9999999999999,188.5,-11.));
#413=CARTESIAN_POINT('',(10.9999999999999,188.5,-1.22290247799277E-014));
#414=CARTESIAN_POINT('',(10.9999999999999,188.5,11.));
#415=CARTESIAN_POINT('',(-5.84922662553323E-014,188.5,11.));
#416=CARTESIAN_POINT('',(-9.23760430703125,187.070937640364,-15.9999999999969));
#417=CARTESIAN_POINT('',(-9.66193489308507,187.315925018211,-15.2650378662195));
#418=CARTESIAN_POINT('',(-10.1196470402852,187.56074961539,-14.4719995549902));
#419=CARTESIAN_POINT('',(-10.9809122890529,187.926375478549,-12.9801832722312));
#420=CARTESIAN_POINT('',(-11.2954657850888,188.048070611898,-12.4365330331587));
#421=CARTESIAN_POINT('',(-11.8936109880142,188.228955023242,-11.3988535329474));
#422=CARTESIAN_POINT('',(-12.1141594978959,188.289061367233,-11.017608949138));
#423=CARTESIAN_POINT('',(-12.5431168789052,188.377703306205,-10.274871558727));
#424=CARTESIAN_POINT('',(-12.7004700156042,188.407126508322,-10.001979931619));
#425=CARTESIAN_POINT('',(-13.0101794053312,188.449223801943,-9.46569027547136));
#426=CARTESIAN_POINT('',(-13.1234536838651,188.462946955902,-9.26932302367545));
#427=CARTESIAN_POINT('',(-13.4202871667809,188.487839494213,-8.75545079397449));
#428=CARTESIAN_POINT('',(-13.5966863851631,188.499999998417,-8.44984837175837));
#429=CARTESIAN_POINT('',(-13.8564064603902,188.499999999996,-8.0000000002701));
#430=CARTESIAN_POINT('',(-1.68994740602384E-007,188.499999999996,-15.9999999999974));
#431=CARTESIAN_POINT('',(-0.519616250850604,188.499999996942,-15.9999999997974));
#432=CARTESIAN_POINT('',(-0.872704506581719,188.487822145271,-16.0000437496836));
#433=CARTESIAN_POINT('',(-1.46622992412083,188.462920145803,-15.9999084906373));
#434=CARTESIAN_POINT('',(-1.69304054355934,188.44918732612,-15.9999914520301));
#435=CARTESIAN_POINT('',(-2.3124806341035,188.407069988168,-15.9999130059247));
#436=CARTESIAN_POINT('',(-2.62762017754957,188.377629421637,-16.0000797577476));
#437=CARTESIAN_POINT('',(-3.48432444390869,188.289063591604,-15.9999611792704));
#438=CARTESIAN_POINT('',(-3.92412709811084,188.22904963396,-15.9996029279992));
#439=CARTESIAN_POINT('',(-5.1220306830116,188.048182255654,-16.0004456656311));
#440=CARTESIAN_POINT('',(-5.75055530492582,187.926411819033,-15.9998406549093));
#441=CARTESIAN_POINT('',(-7.47337392994487,187.560722982366,-15.9998786118199));
#442=CARTESIAN_POINT('',(-8.38898952522787,187.315911626813,-15.9999999998106));
#443=CARTESIAN_POINT('',(-9.23760430703125,187.070937640364,-15.9999999999969));
#444=CARTESIAN_POINT('',(-9.23760430703274,160.49522658769,-16.0000000000023));
#445=CARTESIAN_POINT('',(-13.8564064605508,156.677731195469,-8.00000000000089));
#446=CARTESIAN_POINT('',(-18.475208614069,160.495226587689,8.93039429819598E-013));
#447=CARTESIAN_POINT('',(-9.23760430703393,187.07093764037,-16.0000000000002));
#448=CARTESIAN_POINT('',(-9.23760430703274,160.49522658769,-16.0000000000023));
#449=CARTESIAN_POINT('',(-18.4752086140689,187.07093764037,6.55007613339964E-013));
#450=CARTESIAN_POINT('',(-18.1446105243676,187.261808536435,-0.572612688247159));
#451=CARTESIAN_POINT('',(-17.4973367723952,187.604109408756,-1.69372371306908));
#452=CARTESIAN_POINT('',(-16.5915506447553,187.991479449405,-3.26259130693238));
#453=CARTESIAN_POINT('',(-15.6816261846692,188.281649903215,-4.83862670285109));
#454=CARTESIAN_POINT('',(-14.7561739075697,188.461657262363,-6.4415570667677));
#455=CARTESIAN_POINT('',(-14.1551342564819,188.500000000192,-7.48258827981517));
#456=CARTESIAN_POINT('',(-13.8564064603941,188.5,-8.00000000027232));
#457=CARTESIAN_POINT('',(-18.475208614069,160.495226587689,8.93039429819598E-013));
#458=CARTESIAN_POINT('',(-18.4752086140689,187.07093764037,6.55007613339964E-013));
#459=CARTESIAN_POINT('',(-5.38999924284989E-014,158.661678315193,-15.999999999998));
#460=CARTESIAN_POINT('',(1.43926056654374,158.661678315193,-15.999999999998));
#461=CARTESIAN_POINT('',(2.87143629889494,158.782934636122,-15.9996513329272));
#462=CARTESIAN_POINT('',(5.91838746813942,159.345660719525,-16.000273918568));
#463=CARTESIAN_POINT('',(7.48664809650155,159.771633430469,-15.999999999998));
#464=CARTESIAN_POINT('',(9.23760430703945,160.495226587693,-15.9999999999996));
#465=CARTESIAN_POINT('',(-9.23760430703239,160.495226587691,-16.0000000000001));
#466=CARTESIAN_POINT('',(-7.48678805084267,159.77169126744,-15.999999999998));
#467=CARTESIAN_POINT('',(-5.91864721855774,159.345732945038,-16.0002737969211));
#468=CARTESIAN_POINT('',(-2.87166547859463,158.782952882561,-15.9996515532247));
#469=CARTESIAN_POINT('',(-1.43937642853664,158.661678315193,-15.9999999999978));
#470=CARTESIAN_POINT('',(-5.781873276073E-014,158.661678315193,-15.999999999998));
#471=CARTESIAN_POINT('',(-9.23760430703999,160.495226587693,15.9999999999988));
#472=CARTESIAN_POINT('',(-10.112885272549,159.771796368843,14.4839688968343));
#473=CARTESIAN_POINT('',(-10.8970837605399,159.345864201546,13.1262444220978));
#474=CARTESIAN_POINT('',(-12.4200640732166,158.782986042877,10.4871219009313));
#475=CARTESIAN_POINT('',(-13.1366129740235,158.661678315196,9.24671888962739));
#476=CARTESIAN_POINT('',(-14.5760367438228,158.661678315196,6.75356378671291));
#477=CARTESIAN_POINT('',(-15.2918226554567,158.782934636125,5.51308888627951));
#478=CARTESIAN_POINT('',(-16.8158374150592,159.345660719526,2.87466306244484));
#479=CARTESIAN_POINT('',(-17.5997305087977,159.771633430467,1.51637255924146));
#480=CARTESIAN_POINT('',(-18.4752086140669,160.49522658769,-1.4033801167379E-014));
#481=CARTESIAN_POINT('',(-5.97781029268456E-014,158.661678315193,15.999999999998));
#482=CARTESIAN_POINT('',(-1.43926056654384,158.661678315193,15.999999999998));
#483=CARTESIAN_POINT('',(-2.87143629889508,158.782934636122,15.9996513329272));
#484=CARTESIAN_POINT('',(-5.91838746813961,159.345660719525,16.0002739185679));
#485=CARTESIAN_POINT('',(-7.48664809650182,159.771633430469,15.999999999998));
#486=CARTESIAN_POINT('',(-9.23760430703999,160.495226587693,15.9999999999988));
#487=CARTESIAN_POINT('',(9.2376043070308,160.49522658769,16.0000000000005));
#488=CARTESIAN_POINT('',(7.48678805084121,159.77169126744,15.999999999998));
#489=CARTESIAN_POINT('',(5.91864721855632,159.345732945038,16.000273796921));
#490=CARTESIAN_POINT('',(2.87166547859373,158.782952882561,15.9996515532247));
#491=CARTESIAN_POINT('',(1.43937642853613,158.661678315193,15.9999999999977));
#492=CARTESIAN_POINT('',(-5.97781029268456E-014,158.661678315193,15.999999999998));
#493=CARTESIAN_POINT('',(18.4752086140668,160.49522658769,-9.50882959598297E-015));
#494=CARTESIAN_POINT('',(17.5999276485539,159.771796368841,1.51603110316515));
#495=CARTESIAN_POINT('',(16.8162030060862,159.345864201543,2.87402915274349));
#496=CARTESIAN_POINT('',(15.2921460153934,158.782986042874,5.51253005357021));
#497=CARTESIAN_POINT('',(14.5761999470764,158.661678315193,6.75328111037558));
#498=CARTESIAN_POINT('',(13.1367761772761,158.661678315193,9.24643621329184));
#499=CARTESIAN_POINT('',(12.4203863565602,158.782934636122,10.4865624466553));
#500=CARTESIAN_POINT('',(10.8974499469199,159.345660719524,13.1256108561303));
#501=CARTESIAN_POINT('',(10.1130824122991,159.771633430467,14.4836274407611));
#502=CARTESIAN_POINT('',(9.2376043070308,160.49522658769,16.0000000000005));
#503=CARTESIAN_POINT('',(9.23760430703945,160.495226587693,-15.9999999999996));
#504=CARTESIAN_POINT('',(10.1128852725483,159.771796368844,-14.4839688968352));
#505=CARTESIAN_POINT('',(10.8970837605392,159.345864201546,-13.1262444220989));
#506=CARTESIAN_POINT('',(12.4200640732159,158.782986042878,-10.4871219009323));
#507=CARTESIAN_POINT('',(13.1366129740229,158.661678315196,-9.24671888962832));
#508=CARTESIAN_POINT('',(14.5760367438223,158.661678315196,-6.75356378671363));
#509=CARTESIAN_POINT('',(15.2918226554563,158.782934636124,-5.51308888628009));
#510=CARTESIAN_POINT('',(16.8158374150589,159.345660719526,-2.87466306244516));
#511=CARTESIAN_POINT('',(17.5997305087975,159.771633430467,-1.51637255924165));
#512=CARTESIAN_POINT('',(18.4752086140668,160.49522658769,-9.50882959598297E-015));
#513=CARTESIAN_POINT('',(-5.91207599728636E-014,155.867789836548,10.6322101634505));
#514=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,10.6322101634505));
#515=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,-1.09495174563295E-014));
#516=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,-10.6322101634505));
#517=CARTESIAN_POINT('',(-5.52146783364627E-014,155.867789836548,-10.6322101634506));
#518=CARTESIAN_POINT('',(-5.781873276073E-014,155.867789836548,-10.6322101634506));
#519=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,-10.6322101634506));
#520=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,-1.35535718805968E-014));
#521=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,10.6322101634505));
#522=CARTESIAN_POINT('',(-5.91207599728636E-014,155.867789836548,10.6322101634505));
#523=CARTESIAN_POINT('',(-5.52146783364627E-014,155.867789836548,-10.6322101634506));
#524=CARTESIAN_POINT('',(-5.45214425747451E-014,156.983176404012,-13.4626534301941));
#525=CARTESIAN_POINT('',(-5.38999924284989E-014,158.661678315193,-15.999999999998));
#526=CARTESIAN_POINT('',(9.23760430703344,187.07093764037,-16.));
#527=CARTESIAN_POINT('',(9.23760430703872,160.495226587691,-16.));
#528=CARTESIAN_POINT('',(-1.6899474095498E-007,188.5,-16.));
#529=CARTESIAN_POINT('',(0.59780778711641,188.499999991877,-16.));
#530=CARTESIAN_POINT('',(1.8004791214957,188.46155325645,-16.));
#531=CARTESIAN_POINT('',(3.64851238590559,188.281890136025,-16.));
#532=CARTESIAN_POINT('',(5.4719945932921,187.991271840652,-16.));
#533=CARTESIAN_POINT('',(7.28797637824103,187.602641353627,-16.));
#534=CARTESIAN_POINT('',(8.57908539354299,187.261035675493,-16.));
#535=CARTESIAN_POINT('',(9.23760430703344,187.07093764037,-16.));
#536=CARTESIAN_POINT('',(18.4752086140699,187.07093764037,2.11624833928497E-014));
#537=CARTESIAN_POINT('',(18.4752086140678,160.495226587693,-3.64778263193949E-012));
#538=CARTESIAN_POINT('',(13.8564065362168,188.5,-7.99999986894597));
#539=CARTESIAN_POINT('',(14.1554723634967,188.499999993119,-7.48200266128962));
#540=CARTESIAN_POINT('',(14.7570472356487,188.461572301835,-6.44004441816566));
#541=CARTESIAN_POINT('',(15.6829506079087,188.281313309164,-4.83633273451195));
#542=CARTESIAN_POINT('',(16.5927619715385,187.991016483117,-3.26049322740183));
#543=CARTESIAN_POINT('',(17.498255268063,187.603650021332,-1.69213283190831));
#544=CARTESIAN_POINT('',(18.1449622007016,187.26160549575,-0.572003566971307));
#545=CARTESIAN_POINT('',(18.4752086140699,187.07093764037,2.11624833928497E-014));
#546=CARTESIAN_POINT('',(9.23760430703499,187.07093764037,-16.0000000000009));
#547=CARTESIAN_POINT('',(9.56820242851523,187.261808554788,-15.4273872567103));
#548=CARTESIAN_POINT('',(10.215476233213,187.604109451194,-14.3062761405654));
#549=CARTESIAN_POINT('',(11.1212623995694,187.991479497141,-12.7374084796432));
#550=CARTESIAN_POINT('',(12.0311868768658,188.281649938009,-11.1613730539153));
#551=CARTESIAN_POINT('',(12.9566390861603,188.461657269651,-9.55844280744038));
#552=CARTESIAN_POINT('',(13.5576787384657,188.500000003642,-8.51741159228403));
#553=CARTESIAN_POINT('',(13.8564065362168,188.5,-7.99999986894597));
#554=CARTESIAN_POINT('',(9.23760430703363,187.07093764037,16.0000000000005));
#555=CARTESIAN_POINT('',(9.23760430703224,160.49522658769,16.0000000000029));
#556=CARTESIAN_POINT('',(13.8564064604908,188.5,8.00000000010449));
#557=CARTESIAN_POINT('',(13.5573406720288,188.49999999371,8.51799714052632));
#558=CARTESIAN_POINT('',(12.9557658638655,188.461572321203,9.5599552728187));
#559=CARTESIAN_POINT('',(12.0298625094145,188.281313359068,11.1636669256261));
#560=CARTESIAN_POINT('',(11.1200511173904,187.991016546653,12.7395064819167));
#561=CARTESIAN_POINT('',(10.2145577708277,187.603650080082,14.3078669640789));
#562=CARTESIAN_POINT('',(9.56785076467022,187.261605521157,15.4279963563543));
#563=CARTESIAN_POINT('',(9.23760430703363,187.07093764037,16.0000000000005));
#564=CARTESIAN_POINT('',(18.4752086140687,187.07093764037,-6.66601875173822E-013));
#565=CARTESIAN_POINT('',(18.1446105201121,187.261808538897,0.572612695617705));
#566=CARTESIAN_POINT('',(17.4973367613044,187.604109414312,1.69372373227857));
#567=CARTESIAN_POINT('',(16.5915506301578,187.991479454977,3.26259133221572));
#568=CARTESIAN_POINT('',(15.6816261689037,188.281649907273,4.83862673015753));
#569=CARTESIAN_POINT('',(14.7561738950145,188.461657263762,6.44155708851363));
#570=CARTESIAN_POINT('',(14.1551342512471,188.500000000159,7.48258828888177));
#571=CARTESIAN_POINT('',(13.8564064604908,188.5,8.00000000010449));
#572=CARTESIAN_POINT('',(-9.23760430703456,187.07093764037,16.));
#573=CARTESIAN_POINT('',(-9.23760430703984,160.495226587691,16.));
#574=CARTESIAN_POINT('',(-8.74990187843368E-008,188.5,16.));
#575=CARTESIAN_POINT('',(-0.597807944735581,188.499999999519,16.));
#576=CARTESIAN_POINT('',(-1.80047923605423,188.461553259081,16.));
#577=CARTESIAN_POINT('',(-3.64851266917192,188.281890108752,16.));
#578=CARTESIAN_POINT('',(-5.47199484885885,187.991271795688,16.));
#579=CARTESIAN_POINT('',(-7.28797655705883,187.602641311235,16.));
#580=CARTESIAN_POINT('',(-8.57908546122756,187.261035656774,16.));
#581=CARTESIAN_POINT('',(-9.23760430703456,187.07093764037,16.));
#582=CARTESIAN_POINT('',(9.23760430703269,187.07093764037,16.));
#583=CARTESIAN_POINT('',(8.57941432274677,187.260940721999,16.));
#584=CARTESIAN_POINT('',(7.28842077615595,187.602561944746,16.));
#585=CARTESIAN_POINT('',(5.4694096032429,187.991821035004,16.));
#586=CARTESIAN_POINT('',(3.64405958759015,188.28245186897,16.));
#587=CARTESIAN_POINT('',(1.79850532534396,188.461624407804,16.));
#588=CARTESIAN_POINT('',(0.597038706718117,188.499999992898,16.));
#589=CARTESIAN_POINT('',(-8.74990187843368E-008,188.5,16.));
#590=CARTESIAN_POINT('',(-13.8564065371311,188.5,7.99999986736258));
#591=CARTESIAN_POINT('',(-14.1554723070353,188.499999997641,7.48200275908386));
#592=CARTESIAN_POINT('',(-14.7570471013037,188.46157232251,6.44004465085827));
#593=CARTESIAN_POINT('',(-15.682950579454,188.2813133229,4.83633278379725));
#594=CARTESIAN_POINT('',(-16.5927619608629,187.99101648823,3.26049324589263));
#595=CARTESIAN_POINT('',(-17.4982552655981,187.60365002374,1.69213283617786));
#596=CARTESIAN_POINT('',(-18.1449622005494,187.26160549611,0.572003567235192));
#597=CARTESIAN_POINT('',(-18.4752086140701,187.07093764037,-2.93437956015047E-014));
#598=CARTESIAN_POINT('',(-9.23760430703536,187.07093764037,16.0000000000005));
#599=CARTESIAN_POINT('',(-9.56820243412439,187.261808557048,15.4273872469952));
#600=CARTESIAN_POINT('',(-10.2154762467965,187.60410945497,14.3062761170383));
#601=CARTESIAN_POINT('',(-11.1212624082046,187.991479494627,12.7374084646867));
#602=CARTESIAN_POINT('',(-12.031186883258,188.281649932143,11.1613730428439));
#603=CARTESIAN_POINT('',(-12.9566391120803,188.4616572632,9.55844276254593));
#604=CARTESIAN_POINT('',(-13.5576787348445,188.499999989073,8.5174115985563));
#605=CARTESIAN_POINT('',(-13.8564065371311,188.5,7.99999986736258));
#606=CARTESIAN_POINT('',(-5.91207599728639E-014,155.867789836552,-10.6322101634531));
#607=CARTESIAN_POINT('',(-5.90000952026833E-014,156.853125000003,-9.64687500000261));
#608=CARTESIAN_POINT('',(-5.90000952026833E-014,156.853125000003,-9.64687500000261));
#609=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,-9.64687500000261));
#610=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,-1.467460193746E-014));
#611=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,9.64687500000258));
#612=CARTESIAN_POINT('',(-5.54560078768233E-014,156.853125000003,9.64687500000259));
#613=CARTESIAN_POINT('',(-5.54560078768233E-014,156.853125000003,9.64687500000259));
#614=CARTESIAN_POINT('',(-5.52146783364621E-014,155.867789836552,10.632210163453));
#615=CARTESIAN_POINT('',(-5.781873276073E-014,156.853125000003,9.64687500000259));
#616=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,9.64687500000259));
#617=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,-1.23118770535533E-014));
#618=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,-9.64687500000261));
#619=CARTESIAN_POINT('',(-5.90000952026833E-014,156.853125000003,-9.64687500000261));
#620=CARTESIAN_POINT('',(-5.9000095202683E-014,156.853125000001,9.64687499999999));
#621=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#622=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#623=CARTESIAN_POINT('',(-9.64687500000006,187.146875,9.64687499999999));
#624=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-1.34932394955065E-014));
#625=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-9.64687500000001));
#626=CARTESIAN_POINT('',(-5.781873276073E-014,187.146875,-9.64687500000001));
#627=CARTESIAN_POINT('',(-5.54560078768239E-014,156.853125000001,-9.64687500000001));
#628=CARTESIAN_POINT('',(-5.54560078768239E-014,187.146875,-9.64687500000001));
#629=CARTESIAN_POINT('',(-5.54560078768239E-014,187.146875,-9.64687500000001));
#630=CARTESIAN_POINT('',(9.64687499999994,187.146875,-9.64687500000001));
#631=CARTESIAN_POINT('',(9.64687499999994,187.146875,-1.11305146116005E-014));
#632=CARTESIAN_POINT('',(9.64687499999994,187.146875,9.64687499999999));
#633=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#634=CARTESIAN_POINT('',(-5.91657997499346E-014,188.5,11.));
#635=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#636=CARTESIAN_POINT('',(-5.54560078768239E-014,187.146875,-9.64687500000001));
#637=CARTESIAN_POINT('',(-5.51245987823208E-014,188.5,-11.));
#638=CARTESIAN_POINT('',(-5.91207599728636E-014,155.867789836548,10.6322101634505));
#639=CARTESIAN_POINT('',(-5.781873276073E-014,156.983176404012,13.462653430194));
#640=CARTESIAN_POINT('',(-5.97781029268456E-014,158.661678315193,15.999999999998));
#641=CARTESIAN_POINT('',(16.32,188.5,16.32));
#642=CARTESIAN_POINT('',(-16.32,188.5,16.32));
#643=CARTESIAN_POINT('',(16.32,188.5,-16.32));
#644=CARTESIAN_POINT('',(-16.32,188.5,-16.32));
#645=CARTESIAN_POINT('',(-13.8564064603902,188.499999999996,-8.0000000002701));
#646=CARTESIAN_POINT('',(-16.0214359352092,187.056647016767,-9.24998035041706));
#647=CARTESIAN_POINT('',(-9.23760436315361,188.499999999996,-15.9999999024266));
#648=CARTESIAN_POINT('',(-10.6809573551516,187.056647016767,-18.4999605873905));
#649=CARTESIAN_POINT('',(-1.68994742731337E-007,188.499999999996,-15.9999999999957));
#650=CARTESIAN_POINT('',(-1.95399745360186E-007,187.056647016767,-18.4999607002046));
#651=CARTESIAN_POINT('',(-9.23760430703286,158.363295098343,-16.0000000000021));
#652=CARTESIAN_POINT('',(-9.23760430703286,188.798383216848,-16.0000000000021));
#653=CARTESIAN_POINT('',(-18.475208614069,158.363295098343,8.43301438316391E-013));
#654=CARTESIAN_POINT('',(-18.475208614069,188.798383216848,8.43301438316391E-013));
#655=CARTESIAN_POINT('',(-5.781873276073E-014,155.867789836548,-10.6322101634506));
#656=CARTESIAN_POINT('',(-5.781873276073E-014,157.575267007727,-14.9651628842891));
#657=CARTESIAN_POINT('',(-5.781873276073E-014,160.553593099348,-18.5456001848276));
#658=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,-10.6322101634506));
#659=CARTESIAN_POINT('',(-14.9651628842891,157.575267007727,-14.9651628842891));
#660=CARTESIAN_POINT('',(-18.5456001848277,160.553593099348,-18.5456001848276));
#661=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,-1.35535718805968E-014));
#662=CARTESIAN_POINT('',(-14.9651628842892,157.575267007727,-1.38188799522344E-014));
#663=CARTESIAN_POINT('',(-18.5456001848277,160.553593099348,-1.40381112656195E-014));
#664=CARTESIAN_POINT('',(-10.6322101634506,155.867789836548,10.6322101634505));
#665=CARTESIAN_POINT('',(-14.9651628842891,157.575267007727,14.9651628842891));
#666=CARTESIAN_POINT('',(-18.5456001848277,160.553593099348,18.5456001848276));
#667=CARTESIAN_POINT('',(-5.91207599728636E-014,155.867789836548,10.6322101634505));
#668=CARTESIAN_POINT('',(-5.96513761161387E-014,157.575267007727,14.9651628842891));
#669=CARTESIAN_POINT('',(-6.00898387429089E-014,160.553593099348,18.5456001848276));
#670=CARTESIAN_POINT('',(9.23760430703822,158.363295098342,-16.));
#671=CARTESIAN_POINT('',(9.23760430703822,188.798383216848,-16.));
#672=CARTESIAN_POINT('',(-9.23760430703575,158.363295098342,-16.));
#673=CARTESIAN_POINT('',(-9.23760430703575,188.798383216848,-16.));
#674=CARTESIAN_POINT('',(18.4752086140698,158.363295098351,-1.66086318142271E-013));
#675=CARTESIAN_POINT('',(18.4752086140698,188.798383216848,-1.66086318142271E-013));
#676=CARTESIAN_POINT('',(9.23760430703511,158.363295098351,-16.0000000000007));
#677=CARTESIAN_POINT('',(9.23760430703511,188.798383216848,-16.0000000000007));
#678=CARTESIAN_POINT('',(9.23760430703218,158.363295098343,16.000000000003));
#679=CARTESIAN_POINT('',(9.23760430703218,188.798383216848,16.000000000003));
#680=CARTESIAN_POINT('',(18.4752086140689,158.363295098343,-8.51342986471448E-013));
#681=CARTESIAN_POINT('',(18.4752086140689,188.798383216848,-8.51342986471448E-013));
#682=CARTESIAN_POINT('',(-9.23760430704034,158.363295098342,16.));
#683=CARTESIAN_POINT('',(-9.23760430704034,188.798383216848,16.));
#684=CARTESIAN_POINT('',(9.23760430703294,158.363295098342,16.));
#685=CARTESIAN_POINT('',(9.23760430703294,188.798383216848,16.));
#686=CARTESIAN_POINT('',(-18.47520861407,158.363295098351,1.57910509031091E-013));
#687=CARTESIAN_POINT('',(-18.47520861407,188.798383216848,1.57910509031091E-013));
#688=CARTESIAN_POINT('',(-9.23760430703524,158.363295098351,16.0000000000007));
#689=CARTESIAN_POINT('',(-9.23760430703524,188.798383216848,16.0000000000007));
#690=CARTESIAN_POINT('',(-13.8564065371261,188.499999999996,7.99999986735968));
#691=CARTESIAN_POINT('',(-16.0214360239365,187.056647016766,9.24998019674066));
#692=CARTESIAN_POINT('',(-18.4752085631159,188.499999999996,-8.86076834367603E-008));
#693=CARTESIAN_POINT('',(-21.3619145216158,187.056647016766,-1.0245241502015E-007));
#694=CARTESIAN_POINT('',(-13.8564064603896,188.499999999996,-8.00000000027122));
#695=CARTESIAN_POINT('',(-16.0214359352101,187.056647016766,-9.24998035041929));
#696=CARTESIAN_POINT('',(-5.781873276073E-014,188.499999999996,15.9999999999974));
#697=CARTESIAN_POINT('',(-5.781873276073E-014,187.056647016767,18.4999607002049));
#698=CARTESIAN_POINT('',(-9.23760440913742,188.499999999996,15.9999999999974));
#699=CARTESIAN_POINT('',(-10.6809574083194,187.056647016767,18.4999607002049));
#700=CARTESIAN_POINT('',(-13.8564065371275,188.499999999996,7.9999998673606));
#701=CARTESIAN_POINT('',(-16.0214360239351,187.056647016767,9.24998019674));
#702=CARTESIAN_POINT('',(13.8564064604881,188.499999999996,8.00000000010296));
#703=CARTESIAN_POINT('',(16.0214359353211,187.056647016767,9.24998035022301));
#704=CARTESIAN_POINT('',(9.23760430695189,188.499999999996,15.9999999999971));
#705=CARTESIAN_POINT('',(10.6809572901676,187.056647016767,18.4999607002046));
#706=CARTESIAN_POINT('',(-5.42660190819295E-014,188.499999999996,15.9999999999971));
#707=CARTESIAN_POINT('',(-5.42660190819295E-014,187.056647016767,18.4999607002046));
#708=CARTESIAN_POINT('',(13.8564065362112,188.499999999997,-7.99999986894272));
#709=CARTESIAN_POINT('',(16.0214360228794,187.056647016766,-9.24998019857146));
#710=CARTESIAN_POINT('',(18.4752085636595,188.499999999997,8.74396070864688E-008));
#711=CARTESIAN_POINT('',(21.3619145222453,187.056647016766,1.01101832215465E-007));
#712=CARTESIAN_POINT('',(13.8564064604862,188.499999999997,8.00000000010213));
#713=CARTESIAN_POINT('',(16.0214359353226,187.056647016766,9.2499803502242));
#714=CARTESIAN_POINT('',(-1.68994741137941E-007,188.499999999997,-15.999999999995));
#715=CARTESIAN_POINT('',(-1.95399745315602E-007,187.056647016767,-18.499960700205));
#716=CARTESIAN_POINT('',(9.23760435158677,188.499999999997,-16.0000000975641));
#717=CARTESIAN_POINT('',(10.6809573417782,187.056647016767,-18.499960813019));
#718=CARTESIAN_POINT('',(13.8564065362121,188.499999999997,-7.99999986894112));
#719=CARTESIAN_POINT('',(16.0214360228792,187.056647016767,-9.24998019856886));
#720=CARTESIAN_POINT('',(-5.90000952026833E-014,156.853125000003,-9.64687500000261));
#721=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,-9.64687500000261));
#722=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,-1.467460193746E-014));
#723=CARTESIAN_POINT('',(9.64687500000254,156.853125000003,9.64687500000258));
#724=CARTESIAN_POINT('',(-5.54560078768233E-014,156.853125000003,9.64687500000259));
#725=CARTESIAN_POINT('',(-5.91207599728639E-014,155.867789836552,-10.6322101634531));
#726=CARTESIAN_POINT('',(10.632210163453,155.867789836552,-10.6322101634531));
#727=CARTESIAN_POINT('',(10.632210163453,155.867789836552,-1.48555990927309E-014));
#728=CARTESIAN_POINT('',(10.632210163453,155.867789836552,10.632210163453));
#729=CARTESIAN_POINT('',(-5.52146783364621E-014,155.867789836552,10.632210163453));
#730=CARTESIAN_POINT('',(-5.781873276073E-014,156.853125000003,9.64687500000259));
#731=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,9.64687500000259));
#732=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,-1.23118770535533E-014));
#733=CARTESIAN_POINT('',(-9.64687500000266,156.853125000003,-9.64687500000261));
#734=CARTESIAN_POINT('',(-5.90000952026833E-014,156.853125000003,-9.64687500000261));
#735=CARTESIAN_POINT('',(-5.781873276073E-014,155.867789836552,10.632210163453));
#736=CARTESIAN_POINT('',(-10.6322101634531,155.867789836552,10.632210163453));
#737=CARTESIAN_POINT('',(-10.6322101634531,155.867789836552,-1.2251544668463E-014));
#738=CARTESIAN_POINT('',(-10.6322101634531,155.867789836552,-10.6322101634531));
#739=CARTESIAN_POINT('',(-5.91207599728639E-014,155.867789836552,-10.6322101634531));
#740=CARTESIAN_POINT('',(-5.781873276073E-014,156.853125000001,-9.64687500000001));
#741=CARTESIAN_POINT('',(-9.64687500000006,156.853125000001,-9.64687500000001));
#742=CARTESIAN_POINT('',(-9.64687500000006,156.853125000001,-1.34932394955065E-014));
#743=CARTESIAN_POINT('',(-9.64687500000006,156.853125000001,9.64687499999999));
#744=CARTESIAN_POINT('',(-5.9000095202683E-014,156.853125000001,9.64687499999999));
#745=CARTESIAN_POINT('',(-5.781873276073E-014,187.146875,-9.64687500000001));
#746=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-9.64687500000001));
#747=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-1.34932394955065E-014));
#748=CARTESIAN_POINT('',(-9.64687500000006,187.146875,9.64687499999999));
#749=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#750=CARTESIAN_POINT('',(-5.9000095202683E-014,156.853125000001,9.64687499999999));
#751=CARTESIAN_POINT('',(9.64687499999994,156.853125000001,9.64687499999999));
#752=CARTESIAN_POINT('',(9.64687499999994,156.853125000001,-1.11305146116005E-014));
#753=CARTESIAN_POINT('',(9.64687499999994,156.853125000001,-9.64687500000001));
#754=CARTESIAN_POINT('',(-5.54560078768239E-014,156.853125000001,-9.64687500000001));
#755=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#756=CARTESIAN_POINT('',(9.64687499999994,187.146875,9.64687499999999));
#757=CARTESIAN_POINT('',(9.64687499999994,187.146875,-1.11305146116005E-014));
#758=CARTESIAN_POINT('',(9.64687499999994,187.146875,-9.64687500000001));
#759=CARTESIAN_POINT('',(-5.54560078768239E-014,187.146875,-9.64687500000001));
#760=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#761=CARTESIAN_POINT('',(9.64687499999994,187.146875,9.64687499999999));
#762=CARTESIAN_POINT('',(9.64687499999994,187.146875,-1.11305146116005E-014));
#763=CARTESIAN_POINT('',(9.64687499999994,187.146875,-9.64687500000001));
#764=CARTESIAN_POINT('',(-5.54560078768239E-014,187.146875,-9.64687500000001));
#765=CARTESIAN_POINT('',(-5.91657997499346E-014,188.5,11.));
#766=CARTESIAN_POINT('',(10.9999999999999,188.5,11.));
#767=CARTESIAN_POINT('',(10.9999999999999,188.5,-1.08819577907231E-014));
#768=CARTESIAN_POINT('',(10.9999999999999,188.5,-11.));
#769=CARTESIAN_POINT('',(-5.51245987823208E-014,188.5,-11.));
#770=CARTESIAN_POINT('',(-5.781873276073E-014,187.146875,-9.64687500000001));
#771=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-9.64687500000001));
#772=CARTESIAN_POINT('',(-9.64687500000006,187.146875,-1.34932394955065E-014));
#773=CARTESIAN_POINT('',(-9.64687500000006,187.146875,9.64687499999999));
#774=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#775=CARTESIAN_POINT('',(-5.781873276073E-014,188.5,-11.));
#776=CARTESIAN_POINT('',(-11.0000000000001,188.5,-11.));
#777=CARTESIAN_POINT('',(-11.0000000000001,188.5,-1.35760917691323E-014));
#778=CARTESIAN_POINT('',(-11.0000000000001,188.5,11.));
#779=CARTESIAN_POINT('',(-5.91657997499346E-014,188.5,11.));
#780=CARTESIAN_POINT('',(-5.91207599728636E-014,155.867789836548,10.6322101634505));
#781=CARTESIAN_POINT('',(-5.96513761161387E-014,157.575267007727,14.9651628842893));
#782=CARTESIAN_POINT('',(-6.0089838742909E-014,160.553593099348,18.545600184828));
#783=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,10.6322101634505));
#784=CARTESIAN_POINT('',(14.9651628842893,157.575267007727,14.9651628842893));
#785=CARTESIAN_POINT('',(18.545600184828,160.553593099348,18.545600184828));
#786=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,-1.09495174563295E-014));
#787=CARTESIAN_POINT('',(14.9651628842893,157.575267007727,-1.01535932414169E-014));
#788=CARTESIAN_POINT('',(18.545600184828,160.553593099348,-9.49589930126152E-015));
#789=CARTESIAN_POINT('',(10.6322101634505,155.867789836548,-10.6322101634505));
#790=CARTESIAN_POINT('',(14.9651628842893,157.575267007727,-14.9651628842894));
#791=CARTESIAN_POINT('',(18.545600184828,160.553593099348,-18.545600184828));
#792=CARTESIAN_POINT('',(-5.52146783364627E-014,155.867789836548,-10.6322101634506));
#793=CARTESIAN_POINT('',(-5.41534460499125E-014,157.575267007727,-14.9651628842894));
#794=CARTESIAN_POINT('',(-5.3276520796372E-014,160.553593099348,-18.545600184828));
#795=CARTESIAN_POINT('',(-1.68994741490559E-007,188.499999999998,-15.9999999999987));
#796=CARTESIAN_POINT('',(-13.8564064603918,188.499999999998,-8.00000000027175));
#797=CARTESIAN_POINT('',(13.8564065362141,188.499999999998,-7.99999986894336));
#798=CARTESIAN_POINT('',(13.8564064604894,188.499999999998,8.00000000010381));
#799=CARTESIAN_POINT('',(-6.56242771191596E-008,188.499999999998,15.9999999999987));
#800=CARTESIAN_POINT('',(-13.856406537129,188.499999999998,7.99999986736143));
#801=CARTESIAN_POINT('',(-5.89412885850672E-014,188.5,11.));
#802=CARTESIAN_POINT('',(-5.66961769363928E-014,188.5,-11.));
#803=CARTESIAN_POINT('',(-9.2376043070327,187.070937640368,-15.9999999999991));
#804=CARTESIAN_POINT('',(-9.23760430703367,160.49522658769,-16.0000000000007));
#805=CARTESIAN_POINT('',(-18.475208614068,160.495226587691,1.38193580935176E-012));
#806=CARTESIAN_POINT('',(-18.4752086140683,187.070937640368,4.05022751278259E-013));
#807=CARTESIAN_POINT('',(1.87152441082E-012,158.661678315192,-15.9999999999988));
#808=CARTESIAN_POINT('',(9.23760430703761,160.495226587693,-16.0000000000001));
#809=CARTESIAN_POINT('',(-9.23760430703885,160.495226587692,15.9999999999993));
#810=CARTESIAN_POINT('',(-2.46309739997203E-012,158.661678315192,15.999999999999));
#811=CARTESIAN_POINT('',(9.23760430703195,160.49522658769,16.0000000000012));
#812=CARTESIAN_POINT('',(18.4752086140678,160.495226587691,-1.39664508011873E-012));
#813=CARTESIAN_POINT('',(-5.73847236900186E-014,155.867789836551,10.6322101634522));
#814=CARTESIAN_POINT('',(-5.74467249858348E-014,155.86778983655,-10.632210163452));
#815=CARTESIAN_POINT('',(9.23760430703371,187.070937640368,-15.9999999999992));
#816=CARTESIAN_POINT('',(18.4752086140681,187.070937640368,-4.15682084080973E-013));
#817=CARTESIAN_POINT('',(9.23760430703259,187.070937640368,15.9999999999991));
#818=CARTESIAN_POINT('',(-9.23760430703383,187.070937640368,15.9999999999991));
#819=CARTESIAN_POINT('',(-5.78187327607302E-014,156.853125000002,-9.64687500000131));
#820=CARTESIAN_POINT('',(-5.78187327607298E-014,156.853125000002,9.64687500000129));
#821=CARTESIAN_POINT('',(-5.9000095202683E-014,187.146875,9.64687499999999));
#822=CARTESIAN_POINT('',(-5.6637370318777E-014,187.146875,-9.64687500000001));
#823=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#388,#389,#390),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.7551606498365,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025406429912,1.))
REPRESENTATION_ITEM('')
);
#824=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#391,#392,#393),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.755161139473,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025398779351,1.))
REPRESENTATION_ITEM('')
);
#825=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#394,#395,#396),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.7551606679376,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025406147092,1.))
REPRESENTATION_ITEM('')
);
#826=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#397,#398,#399),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.7551609065239,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025402419151,1.))
REPRESENTATION_ITEM('')
);
#827=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#400,#401,#402),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.7551608848038,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025402758529,1.))
REPRESENTATION_ITEM('')
);
#828=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#403,#404,#405),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-16.7551606663051,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.866025406172591,1.))
REPRESENTATION_ITEM('')
);
#829=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#406,#407,#408,#409,#410),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-34.5575191894877,-17.2787595947439,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#830=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#411,#412,#413,#414,#415),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-34.5575191894877,-17.2787595947439,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#831=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#444,#445,#446),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-18.9566966643331,0.),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.924192108888095,1.))
REPRESENTATION_ITEM('')
);
#832=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#513,#514,#515,#516,#517),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-33.4020733409173,-16.7010366704586,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#833=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#518,#519,#520,#521,#522),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-33.4020733409173,-16.7010366704586,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#834=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#523,#524,#525),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.1954143478557,-0.986366253453421),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.99454233337023,1.))
REPRESENTATION_ITEM('')
);
#835=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#608,#609,#610,#611,#612),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(0.,15.1532758150511,30.3065516301022),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#836=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#615,#616,#617,#618,#619),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(0.,15.1532758150511,30.3065516301022),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#837=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#622,#623,#624,#625,#626),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-30.306551630099,-15.1532758150495,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#838=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#629,#630,#631,#632,#633),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,2,3),(-30.306551630099,-15.1532758150495,0.),
.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.707106781186548,1.,0.707106781186548,1.))
REPRESENTATION_ITEM('')
);
#839=(
BOUNDED_CURVE()
B_SPLINE_CURVE(2,(#638,#639,#640),.UNSPECIFIED.,.F.,.F.)
B_SPLINE_CURVE_WITH_KNOTS((3,3),(-1.1954143478557,-0.986366253453421),.UNSPECIFIED.)
CURVE()
GEOMETRIC_REPRESENTATION_ITEM()
RATIONAL_B_SPLINE_CURVE((1.,0.99454233337023,1.))
REPRESENTATION_ITEM('')
);
ENDSEC;
END-ISO-10303-21;
