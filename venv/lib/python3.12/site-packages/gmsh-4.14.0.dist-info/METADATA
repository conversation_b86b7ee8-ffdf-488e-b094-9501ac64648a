Metadata-Version: 2.1
Name: gmsh
Version: 4.14.0
Summary: Gmsh is a three-dimensional finite element mesh generator with built-in pre- and post-processing facilities.
Home-page: https://gmsh.info
Maintainer: <PERSON>
Maintainer-email: c<PERSON><PERSON><EMAIL>
License: GPLv2+
Keywords: fem,mesh,finite element method,cad
Platform: Posix
Platform: Windows
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: GNU General Public License v2 or later (GPLv2+)
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: C
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Topic :: Scientific/Engineering
Description-Content-Type: text/x-rst

====
gmsh
====

`Gmsh <https://gmsh.info>`_ is an automatic three-dimensional finite element
mesh generator with built-in pre- and post-processing facilities.

The aim of this package is to install the official Gmsh app and binary Software
Development Kit (SDK) in a pythonic way, i.e. via the ``pip``
command. Installation should work under Linux, Windows and macOS for both Python
2 and 3.

To install or upgrade ``gmsh``::

    $ pip install --upgrade gmsh

After installation you can either run the Gmsh app::

    $ gmsh

or use the Gmsh Python API::

    $ python -c "import gmsh; gmsh.initialize(); gmsh.fltk.run(); gmsh.finalize()"

(Note that the header files for using the C++ and C API are also installed, as
is the Julia module.)


