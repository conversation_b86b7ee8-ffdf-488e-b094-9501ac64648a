# Gmsh - Copyright (C) 1997-2025 <PERSON><PERSON>, J.-<PERSON><PERSON>
#
# See the LICENSE.txt file in the Gmsh root directory for license information.
# Please report all issues on https://gitlab.onelab.info/gmsh/gmsh/issues.

# This file defines the Gmsh Python API (v4.14.0).
#
# Do not edit this file directly: it is automatically generated by `api/gen.py'.
#
# By design, the Gmsh Python API is purely functional, and only uses elementary
# Python types (as well as `numpy' arrays if `numpy' is available). See
# `tutorials/python' and `examples/api' for tutorials and examples.

"""
Gmsh is an automatic three-dimensional finite element mesh generator with a
built-in CAD engine and post-processor. Its design goal is to provide a fast,
light and user-friendly meshing tool with parametric input and flexible
visualization capabilities. Gmsh is built around four modules (geometry, mesh,
solver and post-processing), which can be controlled with the graphical user
interface, from the command line, using text files written in Gmsh's own
scripting language (.geo files), or through the C++, C, Python, Julia and
Fortran application programming interface (API).

This module defines the Gmsh Python API.
"""

from ctypes import *
from ctypes.util import find_library
import signal
import os
import platform
from math import pi

GMSH_API_VERSION = "4.14.0"
GMSH_API_VERSION_MAJOR = 4
GMSH_API_VERSION_MINOR = 14
GMSH_API_VERSION_PATCH = 0

__version__ = GMSH_API_VERSION

moduledir = os.path.dirname(os.path.realpath(__file__))
parentdir1 = os.path.dirname(moduledir)
parentdir2 = os.path.dirname(parentdir1)

if platform.system() == "Windows":
    libname = "gmsh-4.14.dll"
elif platform.system() == "Darwin":
    libname = "libgmsh.4.14.dylib"
else:
    libname = "libgmsh.so.4.14"

# Searching lib in various subfolders
libpath = None
possible_libpaths = [os.path.join(moduledir, libname),
                     os.path.join(moduledir, "lib", libname),
                     os.path.join(moduledir, "Lib", libname),
                     os.path.join(moduledir, "bin", libname),
                     # first parent dir
                     os.path.join(parentdir1, libname),
                     os.path.join(parentdir1, "lib", libname),
                     os.path.join(parentdir1, "Lib", libname),
                     os.path.join(parentdir1, "bin", libname),
                     # second parent dir
                     os.path.join(parentdir2, libname),
                     os.path.join(parentdir2, "lib", libname),
                     os.path.join(parentdir2, "Lib", libname),
                     os.path.join(parentdir2, "bin", libname),
                     # for Windows conda-forge
                     os.path.join(parentdir2, "Library", "bin", "gmsh.dll")
                     ]

for libpath_to_look in possible_libpaths:
    if os.path.exists(libpath_to_look):
        libpath = libpath_to_look
        break

# if we couldn't find it, use ctype's find_library utility...
if not libpath:
    if platform.system() == "Windows":
        libpath = find_library("gmsh-4.14")
        if not libpath:
            libpath = find_library("gmsh")
    else:
        libpath = find_library("gmsh")

# ... and print a warning if everything failed
if not libpath:
    print("Warning: could not find Gmsh shared library " + libname +
          " with ctypes.util.find_library() or in the following locations: " +
          str(possible_libpaths))

lib = CDLL(libpath)

try_numpy = True # set this to False to never use numpy

use_numpy = False
if try_numpy:
    try:
        import numpy
        try:
            from weakref import finalize as weakreffinalize
        except:
            from backports.weakref import finalize as weakreffinalize
        use_numpy = True
    except:
        pass

prev_interrupt_handler = None

# Utility functions, not part of the Gmsh Python API

def _ostring(s):
    sp = s.value.decode("utf-8")
    lib.gmshFree(s)
    return sp

def _ovectorpair(ptr, size):
    v = list((ptr[i * 2], ptr[i * 2 + 1]) for i in range(size//2))
    lib.gmshFree(ptr)
    return v

def _ovectorint(ptr, size):
    if use_numpy:
        if size == 0 :
            lib.gmshFree(ptr)
            return numpy.ndarray((0,),numpy.int32)
        v = numpy.ctypeslib.as_array(ptr, (size, ))
        weakreffinalize(v, lib.gmshFree, ptr)
    else:
        v = list(ptr[i] for i in range(size))
        lib.gmshFree(ptr)
    return v

def _ovectorsize(ptr, size):
    if use_numpy:
        if size == 0 :
            lib.gmshFree(ptr)
            return numpy.ndarray((0,),numpy.uintp)
        v = numpy.ctypeslib.as_array(ptr, (size, ))
        weakreffinalize(v, lib.gmshFree, ptr)
    else:
        v = list(ptr[i] for i in range(size))
        lib.gmshFree(ptr)
    return v

def _ovectordouble(ptr, size):
    if use_numpy:
        if size == 0 :
            lib.gmshFree(ptr)
            return numpy.ndarray((0,),numpy.float64)
        v = numpy.ctypeslib.as_array(ptr, (size, ))
        weakreffinalize(v, lib.gmshFree, ptr)
    else:
        v = list(ptr[i] for i in range(size))
        lib.gmshFree(ptr)
    return v

def _ovectorstring(ptr, size):
    v = list(_ostring(cast(ptr[i], c_char_p)) for i in range(size))
    lib.gmshFree(ptr)
    return v

def _ovectorvectorint(ptr, size, n):
    v = [_ovectorint(pointer(ptr[i].contents), size[i]) for i in range(n.value)]
    lib.gmshFree(size)
    lib.gmshFree(ptr)
    return v

def _ovectorvectorsize(ptr, size, n):
    v = [_ovectorsize(pointer(ptr[i].contents), size[i]) for i in range(n.value)]
    lib.gmshFree(size)
    lib.gmshFree(ptr)
    return v

def _ovectorvectordouble(ptr, size, n):
    v = [_ovectordouble(pointer(ptr[i].contents), size[i]) for i in range(n.value)]
    lib.gmshFree(size)
    lib.gmshFree(ptr)
    return v

def _ovectorvectorpair(ptr, size, n):
    v = [_ovectorpair(pointer(ptr[i].contents), size[i]) for i in range(n.value)]
    lib.gmshFree(size)
    lib.gmshFree(ptr)
    return v

def _ivectorint(o):
    if use_numpy:
        array = numpy.ascontiguousarray(o, numpy.int32)
        if(len(o) and array.ndim != 1):
            raise Exception("Invalid data for input vector of integers")
        ct = array.ctypes
        ct.array = array
        return ct, c_size_t(len(o))
    else:
        return (c_int * len(o))(*o), c_size_t(len(o))

def _ivectorsize(o):
    if use_numpy:
        array = numpy.ascontiguousarray(o, numpy.uintp)
        if(len(o) and array.ndim != 1):
            raise Exception("Invalid data for input vector of sizes")
        ct = array.ctypes
        ct.array = array
        return ct, c_size_t(len(o))
    else:
        return (c_size_t * len(o))(*o), c_size_t(len(o))

def _ivectordouble(o):
    if use_numpy:
        array = numpy.ascontiguousarray(o, numpy.float64)
        if(len(o) and array.ndim != 1):
            raise Exception("Invalid data for input vector of doubles")
        ct = array.ctypes
        ct.array = array
        return  ct, c_size_t(len(o))
    else:
        return (c_double * len(o))(*o), c_size_t(len(o))

def _ivectorpair(o):
    if use_numpy:
        array = numpy.ascontiguousarray(o, numpy.int32)
        if(len(o) and (array.ndim != 2 or array.shape[1] != 2)):
            raise Exception("Invalid data for input vector of pairs")
        ct = array.ctypes
        ct.array = array
        return ct, c_size_t(len(o) * 2)
    else:
        if(len(o) and len(o[0]) != 2):
            raise Exception("Invalid data for input vector of pairs")
        return ((c_int * 2) * len(o))(*o), c_size_t(len(o) * 2)

def _ivectorstring(o):
    return (c_char_p * len(o))(*(s.encode() for s in o)), c_size_t(len(o))

def _ivectorvectorint(os):
    n = len(os)
    parrays = [_ivectorint(o) for o in os]
    sizes = (c_size_t * n)(*(a[1] for a in parrays))
    arrays = (POINTER(c_int) * n)(*(cast(a[0], POINTER(c_int)) for a in parrays))
    arrays.ref = [a[0] for a in parrays]
    size = c_size_t(n)
    return arrays, sizes, size

def _ivectorvectorsize(os):
    n = len(os)
    parrays = [_ivectorsize(o) for o in os]
    sizes = (c_size_t * n)(*(a[1] for a in parrays))
    arrays = (POINTER(c_size_t) * n)(*(cast(a[0], POINTER(c_size_t)) for a in parrays))
    arrays.ref = [a[0] for a in parrays]
    size = c_size_t(n)
    return arrays, sizes, size

def _ivectorvectordouble(os):
    n = len(os)
    parrays = [_ivectordouble(o) for o in os]
    sizes = (c_size_t * n)(*(a[1] for a in parrays))
    arrays = (POINTER(c_double) * n)(*(cast(a[0], POINTER(c_double)) for a in parrays))
    arrays.ref = [a[0] for a in parrays]
    size = c_size_t(n)
    return arrays, sizes, size

def _iargcargv(o):
    return c_int(len(o)), (c_char_p * len(o))(*(s.encode() for s in o))

# Gmsh Python API begins here

def initialize(argv=[], readConfigFiles=True, run=False, interruptible=True):
    """
    gmsh.initialize(argv=[], readConfigFiles=True, run=False)

    Initialize the Gmsh API. This must be called before any call to the other
    functions in the API. If `argc' and `argv' (or just `argv' in Python or
    Julia) are provided, they will be handled in the same way as the command
    line arguments in the Gmsh app. If `readConfigFiles' is set, read system
    Gmsh configuration files (gmshrc and gmsh-options). If `run' is set, run in
    the same way as the Gmsh app, either interactively or in batch mode
    depending on the command line arguments. If `run' is not set, initializing
    the API sets the options "General.AbortOnError" to 2 and "General.Terminal"
    to 1.

    Types:
    - `argv': command line arguments
    - `readConfigFiles': boolean
    - `run': boolean
    """
    api_argc_, api_argv_ = _iargcargv(argv)
    ierr = c_int()
    lib.gmshInitialize(
        api_argc_, api_argv_,
        c_int(bool(readConfigFiles)),
        c_int(bool(run)),
        byref(ierr))
    if interruptible == True:
        prev_interrupt_handler = signal.signal(signal.SIGINT, signal.SIG_DFL)
    if ierr.value != 0:
        raise Exception(logger.getLastError())

def isInitialized():
    """
    gmsh.isInitialized()

    Return 1 if the Gmsh API is initialized, and 0 if not.

    Return an integer.
    """
    ierr = c_int()
    api_result_ = lib.gmshIsInitialized(
        byref(ierr))
    if ierr.value != 0:
        raise Exception(logger.getLastError())
    return api_result_
is_initialized = isInitialized

def finalize():
    """
    gmsh.finalize()

    Finalize the Gmsh API. This must be called when you are done using the Gmsh
    API.
    """
    ierr = c_int()
    lib.gmshFinalize(
        byref(ierr))
    if prev_interrupt_handler is not None:
        signal.signal(signal.SIGINT, prev_interrupt_handler)
    if ierr.value != 0:
        raise Exception(logger.getLastError())

def open(fileName):
    """
    gmsh.open(fileName)

    Open a file. Equivalent to the `File->Open' menu in the Gmsh app. Handling
    of the file depends on its extension and/or its contents: opening a file
    with model data will create a new model.

    Types:
    - `fileName': string
    """
    ierr = c_int()
    lib.gmshOpen(
        c_char_p(fileName.encode()),
        byref(ierr))
    if ierr.value != 0:
        raise Exception(logger.getLastError())

def merge(fileName):
    """
    gmsh.merge(fileName)

    Merge a file. Equivalent to the `File->Merge' menu in the Gmsh app.
    Handling of the file depends on its extension and/or its contents. Merging
    a file with model data will add the data to the current model.

    Types:
    - `fileName': string
    """
    ierr = c_int()
    lib.gmshMerge(
        c_char_p(fileName.encode()),
        byref(ierr))
    if ierr.value != 0:
        raise Exception(logger.getLastError())

def write(fileName):
    """
    gmsh.write(fileName)

    Write a file. The export format is determined by the file extension.

    Types:
    - `fileName': string
    """
    ierr = c_int()
    lib.gmshWrite(
        c_char_p(fileName.encode()),
        byref(ierr))
    if ierr.value != 0:
        raise Exception(logger.getLastError())

def clear():
    """
    gmsh.clear()

    Clear all loaded models and post-processing data, and add a new empty
    model.
    """
    ierr = c_int()
    lib.gmshClear(
        byref(ierr))
    if ierr.value != 0:
        raise Exception(logger.getLastError())


class option:
    """
    Option handling functions
    """

    @staticmethod
    def setNumber(name, value):
        """
        gmsh.option.setNumber(name, value)

        Set a numerical option to `value'. `name' is of the form "Category.Option"
        or "Category[num].Option". Available categories and options are listed in
        the "Gmsh options" chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

        Types:
        - `name': string
        - `value': double
        """
        ierr = c_int()
        lib.gmshOptionSetNumber(
            c_char_p(name.encode()),
            c_double(value),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_number = setNumber

    @staticmethod
    def getNumber(name):
        """
        gmsh.option.getNumber(name)

        Get the `value' of a numerical option. `name' is of the form
        "Category.Option" or "Category[num].Option". Available categories and
        options are listed in the "Gmsh options" chapter of the Gmsh reference
        manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

        Return `value'.

        Types:
        - `name': string
        - `value': double
        """
        api_value_ = c_double()
        ierr = c_int()
        lib.gmshOptionGetNumber(
            c_char_p(name.encode()),
            byref(api_value_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_value_.value
    get_number = getNumber

    @staticmethod
    def setString(name, value):
        """
        gmsh.option.setString(name, value)

        Set a string option to `value'. `name' is of the form "Category.Option" or
        "Category[num].Option". Available categories and options are listed in the
        "Gmsh options" chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

        Types:
        - `name': string
        - `value': string
        """
        ierr = c_int()
        lib.gmshOptionSetString(
            c_char_p(name.encode()),
            c_char_p(value.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_string = setString

    @staticmethod
    def getString(name):
        """
        gmsh.option.getString(name)

        Get the `value' of a string option. `name' is of the form "Category.Option"
        or "Category[num].Option". Available categories and options are listed in
        the "Gmsh options" chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

        Return `value'.

        Types:
        - `name': string
        - `value': string
        """
        api_value_ = c_char_p()
        ierr = c_int()
        lib.gmshOptionGetString(
            c_char_p(name.encode()),
            byref(api_value_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_value_)
    get_string = getString

    @staticmethod
    def setColor(name, r, g, b, a=255):
        """
        gmsh.option.setColor(name, r, g, b, a=255)

        Set a color option to the RGBA value (`r', `g', `b', `a'), where where `r',
        `g', `b' and `a' should be integers between 0 and 255. `name' is of the
        form "Category.Color.Option" or "Category[num].Color.Option". Available
        categories and options are listed in the "Gmsh options" chapter of the Gmsh
        reference manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).
        For conciseness "Color." can be ommitted in `name'.

        Types:
        - `name': string
        - `r': integer
        - `g': integer
        - `b': integer
        - `a': integer
        """
        ierr = c_int()
        lib.gmshOptionSetColor(
            c_char_p(name.encode()),
            c_int(r),
            c_int(g),
            c_int(b),
            c_int(a),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_color = setColor

    @staticmethod
    def getColor(name):
        """
        gmsh.option.getColor(name)

        Get the `r', `g', `b', `a' value of a color option. `name' is of the form
        "Category.Color.Option" or "Category[num].Color.Option". Available
        categories and options are listed in the "Gmsh options" chapter of the Gmsh
        reference manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).
        For conciseness "Color." can be ommitted in `name'.

        Return `r', `g', `b', `a'.

        Types:
        - `name': string
        - `r': integer
        - `g': integer
        - `b': integer
        - `a': integer
        """
        api_r_ = c_int()
        api_g_ = c_int()
        api_b_ = c_int()
        api_a_ = c_int()
        ierr = c_int()
        lib.gmshOptionGetColor(
            c_char_p(name.encode()),
            byref(api_r_),
            byref(api_g_),
            byref(api_b_),
            byref(api_a_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_r_.value,
            api_g_.value,
            api_b_.value,
            api_a_.value)
    get_color = getColor

    @staticmethod
    def restoreDefaults():
        """
        gmsh.option.restoreDefaults()

        Restore all options to default settings.
        """
        ierr = c_int()
        lib.gmshOptionRestoreDefaults(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    restore_defaults = restoreDefaults


class model:
    """
    Model functions
    """

    @staticmethod
    def add(name):
        """
        gmsh.model.add(name)

        Add a new model, with name `name', and set it as the current model.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelAdd(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def remove():
        """
        gmsh.model.remove()

        Remove the current model.
        """
        ierr = c_int()
        lib.gmshModelRemove(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def list():
        """
        gmsh.model.list()

        List the names of all models.

        Return `names'.

        Types:
        - `names': vector of strings
        """
        api_names_, api_names_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshModelList(
            byref(api_names_), byref(api_names_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_names_, api_names_n_.value)

    @staticmethod
    def getCurrent():
        """
        gmsh.model.getCurrent()

        Get the name of the current model.

        Return `name'.

        Types:
        - `name': string
        """
        api_name_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetCurrent(
            byref(api_name_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_name_)
    get_current = getCurrent

    @staticmethod
    def setCurrent(name):
        """
        gmsh.model.setCurrent(name)

        Set the current model to the model with name `name'. If several models have
        the same name, select the one that was added first.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelSetCurrent(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_current = setCurrent

    @staticmethod
    def getFileName():
        """
        gmsh.model.getFileName()

        Get the file name (if any) associated with the current model. A file name
        is associated when a model is read from a file on disk.

        Return `fileName'.

        Types:
        - `fileName': string
        """
        api_fileName_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetFileName(
            byref(api_fileName_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_fileName_)
    get_file_name = getFileName

    @staticmethod
    def setFileName(fileName):
        """
        gmsh.model.setFileName(fileName)

        Set the file name associated with the current model.

        Types:
        - `fileName': string
        """
        ierr = c_int()
        lib.gmshModelSetFileName(
            c_char_p(fileName.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_file_name = setFileName

    @staticmethod
    def getEntities(dim=-1):
        """
        gmsh.model.getEntities(dim=-1)

        Get all the entities in the current model. A model entity is represented by
        two integers: its dimension (dim == 0, 1, 2 or 3) and its tag (its unique,
        strictly positive identifier). If `dim' is >= 0, return only the entities
        of the specified dimension (e.g. points if `dim' == 0). The entities are
        returned as a vector of (dim, tag) pairs.

        Return `dimTags'.

        Types:
        - `dimTags': vector of pairs of integers
        - `dim': integer
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetEntities(
            byref(api_dimTags_), byref(api_dimTags_n_),
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
    get_entities = getEntities

    @staticmethod
    def setEntityName(dim, tag, name):
        """
        gmsh.model.setEntityName(dim, tag, name)

        Set the name of the entity of dimension `dim' and tag `tag'.

        Types:
        - `dim': integer
        - `tag': integer
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelSetEntityName(
            c_int(dim),
            c_int(tag),
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_entity_name = setEntityName

    @staticmethod
    def getEntityName(dim, tag):
        """
        gmsh.model.getEntityName(dim, tag)

        Get the name of the entity of dimension `dim' and tag `tag'.

        Return `name'.

        Types:
        - `dim': integer
        - `tag': integer
        - `name': string
        """
        api_name_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetEntityName(
            c_int(dim),
            c_int(tag),
            byref(api_name_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_name_)
    get_entity_name = getEntityName

    @staticmethod
    def removeEntityName(name):
        """
        gmsh.model.removeEntityName(name)

        Remove the entity name `name' from the current model.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelRemoveEntityName(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    remove_entity_name = removeEntityName

    @staticmethod
    def getPhysicalGroups(dim=-1):
        """
        gmsh.model.getPhysicalGroups(dim=-1)

        Get the physical groups in the current model. The physical groups are
        returned as a vector of (dim, tag) pairs. If `dim' is >= 0, return only the
        groups of the specified dimension (e.g. physical points if `dim' == 0).

        Return `dimTags'.

        Types:
        - `dimTags': vector of pairs of integers
        - `dim': integer
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetPhysicalGroups(
            byref(api_dimTags_), byref(api_dimTags_n_),
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
    get_physical_groups = getPhysicalGroups

    @staticmethod
    def getPhysicalGroupsEntities(dim=-1):
        """
        gmsh.model.getPhysicalGroupsEntities(dim=-1)

        Get the physical groups in the current model as well as the model entities
        that make them up. The physical groups are returned as the vector of (dim,
        tag) pairs `dimTags'. The model entities making up the corresponding
        physical groups are returned in `entities'. If `dim' is >= 0, return only
        the groups of the specified dimension (e.g. physical points if `dim' == 0).

        Return `dimTags', `entities'.

        Types:
        - `dimTags': vector of pairs of integers
        - `entities': vector of vectors of pairs of integers
        - `dim': integer
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        api_entities_, api_entities_n_, api_entities_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetPhysicalGroupsEntities(
            byref(api_dimTags_), byref(api_dimTags_n_),
            byref(api_entities_), byref(api_entities_n_), byref(api_entities_nn_),
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectorpair(api_dimTags_, api_dimTags_n_.value),
            _ovectorvectorpair(api_entities_, api_entities_n_, api_entities_nn_))
    get_physical_groups_entities = getPhysicalGroupsEntities

    @staticmethod
    def getEntitiesForPhysicalGroup(dim, tag):
        """
        gmsh.model.getEntitiesForPhysicalGroup(dim, tag)

        Get the tags of the model entities making up the physical group of
        dimension `dim' and tag `tag'.

        Return `tags'.

        Types:
        - `dim': integer
        - `tag': integer
        - `tags': vector of integers
        """
        api_tags_, api_tags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetEntitiesForPhysicalGroup(
            c_int(dim),
            c_int(tag),
            byref(api_tags_), byref(api_tags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorint(api_tags_, api_tags_n_.value)
    get_entities_for_physical_group = getEntitiesForPhysicalGroup

    @staticmethod
    def getEntitiesForPhysicalName(name):
        """
        gmsh.model.getEntitiesForPhysicalName(name)

        Get the model entities (as a vector (dim, tag) pairs) making up the
        physical group with name `name'.

        Return `dimTags'.

        Types:
        - `name': string
        - `dimTags': vector of pairs of integers
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetEntitiesForPhysicalName(
            c_char_p(name.encode()),
            byref(api_dimTags_), byref(api_dimTags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
    get_entities_for_physical_name = getEntitiesForPhysicalName

    @staticmethod
    def getPhysicalGroupsForEntity(dim, tag):
        """
        gmsh.model.getPhysicalGroupsForEntity(dim, tag)

        Get the tags of the physical groups (if any) to which the model entity of
        dimension `dim' and tag `tag' belongs.

        Return `physicalTags'.

        Types:
        - `dim': integer
        - `tag': integer
        - `physicalTags': vector of integers
        """
        api_physicalTags_, api_physicalTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetPhysicalGroupsForEntity(
            c_int(dim),
            c_int(tag),
            byref(api_physicalTags_), byref(api_physicalTags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorint(api_physicalTags_, api_physicalTags_n_.value)
    get_physical_groups_for_entity = getPhysicalGroupsForEntity

    @staticmethod
    def addPhysicalGroup(dim, tags, tag=-1, name=""):
        """
        gmsh.model.addPhysicalGroup(dim, tags, tag=-1, name="")

        Add a physical group of dimension `dim', grouping the model entities with
        tags `tags'. Return the tag of the physical group, equal to `tag' if `tag'
        is positive, or a new tag if `tag' < 0. Set the name of the physical group
        if `name' is not empty.

        Return an integer.

        Types:
        - `dim': integer
        - `tags': vector of integers
        - `tag': integer
        - `name': string
        """
        api_tags_, api_tags_n_ = _ivectorint(tags)
        ierr = c_int()
        api_result_ = lib.gmshModelAddPhysicalGroup(
            c_int(dim),
            api_tags_, api_tags_n_,
            c_int(tag),
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    add_physical_group = addPhysicalGroup

    @staticmethod
    def removePhysicalGroups(dimTags=[]):
        """
        gmsh.model.removePhysicalGroups(dimTags=[])

        Remove the physical groups `dimTags' (given as a vector of (dim, tag)
        pairs) from the current model. If `dimTags' is empty, remove all groups.

        Types:
        - `dimTags': vector of pairs of integers
        """
        api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
        ierr = c_int()
        lib.gmshModelRemovePhysicalGroups(
            api_dimTags_, api_dimTags_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    remove_physical_groups = removePhysicalGroups

    @staticmethod
    def setPhysicalName(dim, tag, name):
        """
        gmsh.model.setPhysicalName(dim, tag, name)

        Set the name of the physical group of dimension `dim' and tag `tag'.

        Types:
        - `dim': integer
        - `tag': integer
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelSetPhysicalName(
            c_int(dim),
            c_int(tag),
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_physical_name = setPhysicalName

    @staticmethod
    def getPhysicalName(dim, tag):
        """
        gmsh.model.getPhysicalName(dim, tag)

        Get the name of the physical group of dimension `dim' and tag `tag'.

        Return `name'.

        Types:
        - `dim': integer
        - `tag': integer
        - `name': string
        """
        api_name_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetPhysicalName(
            c_int(dim),
            c_int(tag),
            byref(api_name_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_name_)
    get_physical_name = getPhysicalName

    @staticmethod
    def removePhysicalName(name):
        """
        gmsh.model.removePhysicalName(name)

        Remove the physical name `name' from the current model.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelRemovePhysicalName(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    remove_physical_name = removePhysicalName

    @staticmethod
    def setTag(dim, tag, newTag):
        """
        gmsh.model.setTag(dim, tag, newTag)

        Set the tag of the entity of dimension `dim' and tag `tag' to the new value
        `newTag'.

        Types:
        - `dim': integer
        - `tag': integer
        - `newTag': integer
        """
        ierr = c_int()
        lib.gmshModelSetTag(
            c_int(dim),
            c_int(tag),
            c_int(newTag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_tag = setTag

    @staticmethod
    def getBoundary(dimTags, combined=True, oriented=True, recursive=False):
        """
        gmsh.model.getBoundary(dimTags, combined=True, oriented=True, recursive=False)

        Get the boundary of the model entities `dimTags', given as a vector of
        (dim, tag) pairs. Return in `outDimTags' the boundary of the individual
        entities (if `combined' is false) or the boundary of the combined
        geometrical shape formed by all input entities (if `combined' is true).
        Return tags multiplied by the sign of the boundary entity if `oriented' is
        true. Apply the boundary operator recursively down to dimension 0 (i.e. to
        points) if `recursive' is true.

        Return `outDimTags'.

        Types:
        - `dimTags': vector of pairs of integers
        - `outDimTags': vector of pairs of integers
        - `combined': boolean
        - `oriented': boolean
        - `recursive': boolean
        """
        api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
        api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetBoundary(
            api_dimTags_, api_dimTags_n_,
            byref(api_outDimTags_), byref(api_outDimTags_n_),
            c_int(bool(combined)),
            c_int(bool(oriented)),
            c_int(bool(recursive)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
    get_boundary = getBoundary

    @staticmethod
    def getAdjacencies(dim, tag):
        """
        gmsh.model.getAdjacencies(dim, tag)

        Get the upward and downward adjacencies of the model entity of dimension
        `dim' and tag `tag'. The `upward' vector returns the tags of adjacent
        entities of dimension `dim' + 1; the `downward' vector returns the tags of
        adjacent entities of dimension `dim' - 1.

        Return `upward', `downward'.

        Types:
        - `dim': integer
        - `tag': integer
        - `upward': vector of integers
        - `downward': vector of integers
        """
        api_upward_, api_upward_n_ = POINTER(c_int)(), c_size_t()
        api_downward_, api_downward_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetAdjacencies(
            c_int(dim),
            c_int(tag),
            byref(api_upward_), byref(api_upward_n_),
            byref(api_downward_), byref(api_downward_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectorint(api_upward_, api_upward_n_.value),
            _ovectorint(api_downward_, api_downward_n_.value))
    get_adjacencies = getAdjacencies

    @staticmethod
    def getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim=-1):
        """
        gmsh.model.getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim=-1)

        Get the model entities in the bounding box defined by the two points
        (`xmin', `ymin', `zmin') and (`xmax', `ymax', `zmax'). If `dim' is >= 0,
        return only the entities of the specified dimension (e.g. points if `dim'
        == 0).

        Return `dimTags'.

        Types:
        - `xmin': double
        - `ymin': double
        - `zmin': double
        - `xmax': double
        - `ymax': double
        - `zmax': double
        - `dimTags': vector of pairs of integers
        - `dim': integer
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetEntitiesInBoundingBox(
            c_double(xmin),
            c_double(ymin),
            c_double(zmin),
            c_double(xmax),
            c_double(ymax),
            c_double(zmax),
            byref(api_dimTags_), byref(api_dimTags_n_),
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
    get_entities_in_bounding_box = getEntitiesInBoundingBox

    @staticmethod
    def getBoundingBox(dim, tag):
        """
        gmsh.model.getBoundingBox(dim, tag)

        Get the bounding box (`xmin', `ymin', `zmin'), (`xmax', `ymax', `zmax') of
        the model entity of dimension `dim' and tag `tag'. If `dim' and `tag' are
        negative, get the bounding box of the whole model.

        Return `xmin', `ymin', `zmin', `xmax', `ymax', `zmax'.

        Types:
        - `dim': integer
        - `tag': integer
        - `xmin': double
        - `ymin': double
        - `zmin': double
        - `xmax': double
        - `ymax': double
        - `zmax': double
        """
        api_xmin_ = c_double()
        api_ymin_ = c_double()
        api_zmin_ = c_double()
        api_xmax_ = c_double()
        api_ymax_ = c_double()
        api_zmax_ = c_double()
        ierr = c_int()
        lib.gmshModelGetBoundingBox(
            c_int(dim),
            c_int(tag),
            byref(api_xmin_),
            byref(api_ymin_),
            byref(api_zmin_),
            byref(api_xmax_),
            byref(api_ymax_),
            byref(api_zmax_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_xmin_.value,
            api_ymin_.value,
            api_zmin_.value,
            api_xmax_.value,
            api_ymax_.value,
            api_zmax_.value)
    get_bounding_box = getBoundingBox

    @staticmethod
    def getDimension():
        """
        gmsh.model.getDimension()

        Return the geometrical dimension of the current model.

        Return an integer.
        """
        ierr = c_int()
        api_result_ = lib.gmshModelGetDimension(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_dimension = getDimension

    @staticmethod
    def addDiscreteEntity(dim, tag=-1, boundary=[]):
        """
        gmsh.model.addDiscreteEntity(dim, tag=-1, boundary=[])

        Add a discrete model entity (defined by a mesh) of dimension `dim' in the
        current model. Return the tag of the new discrete entity, equal to `tag' if
        `tag' is positive, or a new tag if `tag' < 0. `boundary' specifies the tags
        of the entities on the boundary of the discrete entity, if any. Specifying
        `boundary' allows Gmsh to construct the topology of the overall model.

        Return an integer.

        Types:
        - `dim': integer
        - `tag': integer
        - `boundary': vector of integers
        """
        api_boundary_, api_boundary_n_ = _ivectorint(boundary)
        ierr = c_int()
        api_result_ = lib.gmshModelAddDiscreteEntity(
            c_int(dim),
            c_int(tag),
            api_boundary_, api_boundary_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    add_discrete_entity = addDiscreteEntity

    @staticmethod
    def removeEntities(dimTags, recursive=False):
        """
        gmsh.model.removeEntities(dimTags, recursive=False)

        Remove the entities `dimTags' (given as a vector of (dim, tag) pairs) of
        the current model, provided that they are not on the boundary of (or
        embedded in) higher-dimensional entities. If `recursive' is true, remove
        all the entities on their boundaries, down to dimension 0.

        Types:
        - `dimTags': vector of pairs of integers
        - `recursive': boolean
        """
        api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
        ierr = c_int()
        lib.gmshModelRemoveEntities(
            api_dimTags_, api_dimTags_n_,
            c_int(bool(recursive)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    remove_entities = removeEntities

    @staticmethod
    def getEntityType(dim, tag):
        """
        gmsh.model.getEntityType(dim, tag)

        Get the type of the entity of dimension `dim' and tag `tag'.

        Return `entityType'.

        Types:
        - `dim': integer
        - `tag': integer
        - `entityType': string
        """
        api_entityType_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetEntityType(
            c_int(dim),
            c_int(tag),
            byref(api_entityType_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_entityType_)
    get_entity_type = getEntityType

    @staticmethod
    def getType(dim, tag):
        """
        gmsh.model.getType(dim, tag)

        Get the type of the entity of dimension `dim' and tag `tag'. (This is a
        deprecated synonym for `getType'.)

        Return `entityType'.

        Types:
        - `dim': integer
        - `tag': integer
        - `entityType': string
        """
        api_entityType_ = c_char_p()
        ierr = c_int()
        lib.gmshModelGetType(
            c_int(dim),
            c_int(tag),
            byref(api_entityType_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_entityType_)
    get_type = getType

    @staticmethod
    def getEntityProperties(dim, tag):
        """
        gmsh.model.getEntityProperties(dim, tag)

        Get the properties of the entity of dimension `dim' and tag `tag'. The
        `reals' vector contains the 4 coefficients of the cartesian equation for a
        plane surface; the center coordinates, axis direction, major radius and
        minor radius for a torus; the center coordinates, axis direction and radius
        for a cylinder; the center coordinates, axis direction, radius and semi-
        angle for surfaces of revolution; the center coordinates and the radius for
        a sphere.

        Return `integers', `reals'.

        Types:
        - `dim': integer
        - `tag': integer
        - `integers': vector of integers
        - `reals': vector of doubles
        """
        api_integers_, api_integers_n_ = POINTER(c_int)(), c_size_t()
        api_reals_, api_reals_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetEntityProperties(
            c_int(dim),
            c_int(tag),
            byref(api_integers_), byref(api_integers_n_),
            byref(api_reals_), byref(api_reals_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectorint(api_integers_, api_integers_n_.value),
            _ovectordouble(api_reals_, api_reals_n_.value))
    get_entity_properties = getEntityProperties

    @staticmethod
    def getParent(dim, tag):
        """
        gmsh.model.getParent(dim, tag)

        In a partitioned model, get the parent of the entity of dimension `dim' and
        tag `tag', i.e. from which the entity is a part of, if any. `parentDim' and
        `parentTag' are set to -1 if the entity has no parent.

        Return `parentDim', `parentTag'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parentDim': integer
        - `parentTag': integer
        """
        api_parentDim_ = c_int()
        api_parentTag_ = c_int()
        ierr = c_int()
        lib.gmshModelGetParent(
            c_int(dim),
            c_int(tag),
            byref(api_parentDim_),
            byref(api_parentTag_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_parentDim_.value,
            api_parentTag_.value)
    get_parent = getParent

    @staticmethod
    def getNumberOfPartitions():
        """
        gmsh.model.getNumberOfPartitions()

        Return the number of partitions in the model.

        Return an integer.
        """
        ierr = c_int()
        api_result_ = lib.gmshModelGetNumberOfPartitions(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_number_of_partitions = getNumberOfPartitions

    @staticmethod
    def getPartitions(dim, tag):
        """
        gmsh.model.getPartitions(dim, tag)

        In a partitioned model, return the tags of the partition(s) to which the
        entity belongs.

        Return `partitions'.

        Types:
        - `dim': integer
        - `tag': integer
        - `partitions': vector of integers
        """
        api_partitions_, api_partitions_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetPartitions(
            c_int(dim),
            c_int(tag),
            byref(api_partitions_), byref(api_partitions_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorint(api_partitions_, api_partitions_n_.value)
    get_partitions = getPartitions

    @staticmethod
    def getValue(dim, tag, parametricCoord):
        """
        gmsh.model.getValue(dim, tag, parametricCoord)

        Evaluate the parametrization of the entity of dimension `dim' and tag `tag'
        at the parametric coordinates `parametricCoord'. Only valid for `dim' equal
        to 0 (with empty `parametricCoord'), 1 (with `parametricCoord' containing
        parametric coordinates on the curve) or 2 (with `parametricCoord'
        containing u, v parametric coordinates on the surface, concatenated: [p1u,
        p1v, p2u, ...]). Return x, y, z coordinates in `coord', concatenated: [p1x,
        p1y, p1z, p2x, ...].

        Return `coord'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `coord': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetValue(
            c_int(dim),
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_coord_), byref(api_coord_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_coord_, api_coord_n_.value)
    get_value = getValue

    @staticmethod
    def getDerivative(dim, tag, parametricCoord):
        """
        gmsh.model.getDerivative(dim, tag, parametricCoord)

        Evaluate the derivative of the parametrization of the entity of dimension
        `dim' and tag `tag' at the parametric coordinates `parametricCoord'. Only
        valid for `dim' equal to 1 (with `parametricCoord' containing parametric
        coordinates on the curve) or 2 (with `parametricCoord' containing u, v
        parametric coordinates on the surface, concatenated: [p1u, p1v, p2u, ...]).
        For `dim' equal to 1 return the x, y, z components of the derivative with
        respect to u [d1ux, d1uy, d1uz, d2ux, ...]; for `dim' equal to 2 return the
        x, y, z components of the derivative with respect to u and v: [d1ux, d1uy,
        d1uz, d1vx, d1vy, d1vz, d2ux, ...].

        Return `derivatives'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `derivatives': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_derivatives_, api_derivatives_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetDerivative(
            c_int(dim),
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_derivatives_), byref(api_derivatives_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_derivatives_, api_derivatives_n_.value)
    get_derivative = getDerivative

    @staticmethod
    def getSecondDerivative(dim, tag, parametricCoord):
        """
        gmsh.model.getSecondDerivative(dim, tag, parametricCoord)

        Evaluate the second derivative of the parametrization of the entity of
        dimension `dim' and tag `tag' at the parametric coordinates
        `parametricCoord'. Only valid for `dim' equal to 1 (with `parametricCoord'
        containing parametric coordinates on the curve) or 2 (with
        `parametricCoord' containing u, v parametric coordinates on the surface,
        concatenated: [p1u, p1v, p2u, ...]). For `dim' equal to 1 return the x, y,
        z components of the second derivative with respect to u [d1uux, d1uuy,
        d1uuz, d2uux, ...]; for `dim' equal to 2 return the x, y, z components of
        the second derivative with respect to u and v, and the mixed derivative
        with respect to u and v: [d1uux, d1uuy, d1uuz, d1vvx, d1vvy, d1vvz, d1uvx,
        d1uvy, d1uvz, d2uux, ...].

        Return `derivatives'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `derivatives': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_derivatives_, api_derivatives_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetSecondDerivative(
            c_int(dim),
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_derivatives_), byref(api_derivatives_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_derivatives_, api_derivatives_n_.value)
    get_second_derivative = getSecondDerivative

    @staticmethod
    def getCurvature(dim, tag, parametricCoord):
        """
        gmsh.model.getCurvature(dim, tag, parametricCoord)

        Evaluate the (maximum) curvature of the entity of dimension `dim' and tag
        `tag' at the parametric coordinates `parametricCoord'. Only valid for `dim'
        equal to 1 (with `parametricCoord' containing parametric coordinates on the
        curve) or 2 (with `parametricCoord' containing u, v parametric coordinates
        on the surface, concatenated: [p1u, p1v, p2u, ...]).

        Return `curvatures'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `curvatures': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_curvatures_, api_curvatures_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetCurvature(
            c_int(dim),
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_curvatures_), byref(api_curvatures_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_curvatures_, api_curvatures_n_.value)
    get_curvature = getCurvature

    @staticmethod
    def getPrincipalCurvatures(tag, parametricCoord):
        """
        gmsh.model.getPrincipalCurvatures(tag, parametricCoord)

        Evaluate the principal curvatures of the surface with tag `tag' at the
        parametric coordinates `parametricCoord', as well as their respective
        directions. `parametricCoord' are given by pair of u and v coordinates,
        concatenated: [p1u, p1v, p2u, ...].

        Return `curvatureMax', `curvatureMin', `directionMax', `directionMin'.

        Types:
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `curvatureMax': vector of doubles
        - `curvatureMin': vector of doubles
        - `directionMax': vector of doubles
        - `directionMin': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_curvatureMax_, api_curvatureMax_n_ = POINTER(c_double)(), c_size_t()
        api_curvatureMin_, api_curvatureMin_n_ = POINTER(c_double)(), c_size_t()
        api_directionMax_, api_directionMax_n_ = POINTER(c_double)(), c_size_t()
        api_directionMin_, api_directionMin_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetPrincipalCurvatures(
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_curvatureMax_), byref(api_curvatureMax_n_),
            byref(api_curvatureMin_), byref(api_curvatureMin_n_),
            byref(api_directionMax_), byref(api_directionMax_n_),
            byref(api_directionMin_), byref(api_directionMin_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectordouble(api_curvatureMax_, api_curvatureMax_n_.value),
            _ovectordouble(api_curvatureMin_, api_curvatureMin_n_.value),
            _ovectordouble(api_directionMax_, api_directionMax_n_.value),
            _ovectordouble(api_directionMin_, api_directionMin_n_.value))
    get_principal_curvatures = getPrincipalCurvatures

    @staticmethod
    def getNormal(tag, parametricCoord):
        """
        gmsh.model.getNormal(tag, parametricCoord)

        Get the normal to the surface with tag `tag' at the parametric coordinates
        `parametricCoord'. The `parametricCoord' vector should contain u and v
        coordinates, concatenated: [p1u, p1v, p2u, ...]. `normals' are returned as
        a vector of x, y, z components, concatenated: [n1x, n1y, n1z, n2x, ...].

        Return `normals'.

        Types:
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `normals': vector of doubles
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_normals_, api_normals_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetNormal(
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            byref(api_normals_), byref(api_normals_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_normals_, api_normals_n_.value)
    get_normal = getNormal

    @staticmethod
    def getParametrization(dim, tag, coord):
        """
        gmsh.model.getParametrization(dim, tag, coord)

        Get the parametric coordinates `parametricCoord' for the points `coord' on
        the entity of dimension `dim' and tag `tag'. `coord' are given as x, y, z
        coordinates, concatenated: [p1x, p1y, p1z, p2x, ...]. `parametricCoord'
        returns the parametric coordinates t on the curve (if `dim' = 1) or u and v
        coordinates concatenated on the surface (if `dim' == 2), i.e. [p1t, p2t,
        ...] or [p1u, p1v, p2u, ...].

        Return `parametricCoord'.

        Types:
        - `dim': integer
        - `tag': integer
        - `coord': vector of doubles
        - `parametricCoord': vector of doubles
        """
        api_coord_, api_coord_n_ = _ivectordouble(coord)
        api_parametricCoord_, api_parametricCoord_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetParametrization(
            c_int(dim),
            c_int(tag),
            api_coord_, api_coord_n_,
            byref(api_parametricCoord_), byref(api_parametricCoord_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_parametricCoord_, api_parametricCoord_n_.value)
    get_parametrization = getParametrization

    @staticmethod
    def getParametrizationBounds(dim, tag):
        """
        gmsh.model.getParametrizationBounds(dim, tag)

        Get the `min' and `max' bounds of the parametric coordinates for the entity
        of dimension `dim' and tag `tag'.

        Return `min', `max'.

        Types:
        - `dim': integer
        - `tag': integer
        - `min': vector of doubles
        - `max': vector of doubles
        """
        api_min_, api_min_n_ = POINTER(c_double)(), c_size_t()
        api_max_, api_max_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetParametrizationBounds(
            c_int(dim),
            c_int(tag),
            byref(api_min_), byref(api_min_n_),
            byref(api_max_), byref(api_max_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectordouble(api_min_, api_min_n_.value),
            _ovectordouble(api_max_, api_max_n_.value))
    get_parametrization_bounds = getParametrizationBounds

    @staticmethod
    def isInside(dim, tag, coord, parametric=False):
        """
        gmsh.model.isInside(dim, tag, coord, parametric=False)

        Check if the coordinates (or the parametric coordinates if `parametric' is
        set) provided in `coord' correspond to points inside the entity of
        dimension `dim' and tag `tag', and return the number of points inside. This
        feature is only available for a subset of entities, depending on the
        underlying geometrical representation.

        Return an integer.

        Types:
        - `dim': integer
        - `tag': integer
        - `coord': vector of doubles
        - `parametric': boolean
        """
        api_coord_, api_coord_n_ = _ivectordouble(coord)
        ierr = c_int()
        api_result_ = lib.gmshModelIsInside(
            c_int(dim),
            c_int(tag),
            api_coord_, api_coord_n_,
            c_int(bool(parametric)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    is_inside = isInside

    @staticmethod
    def getClosestPoint(dim, tag, coord):
        """
        gmsh.model.getClosestPoint(dim, tag, coord)

        Get the points `closestCoord' on the entity of dimension `dim' and tag
        `tag' to the points `coord', by orthogonal projection. `coord' and
        `closestCoord' are given as x, y, z coordinates, concatenated: [p1x, p1y,
        p1z, p2x, ...]. `parametricCoord' returns the parametric coordinates t on
        the curve (if `dim' == 1) or u and v coordinates concatenated on the
        surface (if `dim' = 2), i.e. [p1t, p2t, ...] or [p1u, p1v, p2u, ...]. The
        closest points can lie outside the (trimmed) entities: use `isInside()' to
        check.

        Return `closestCoord', `parametricCoord'.

        Types:
        - `dim': integer
        - `tag': integer
        - `coord': vector of doubles
        - `closestCoord': vector of doubles
        - `parametricCoord': vector of doubles
        """
        api_coord_, api_coord_n_ = _ivectordouble(coord)
        api_closestCoord_, api_closestCoord_n_ = POINTER(c_double)(), c_size_t()
        api_parametricCoord_, api_parametricCoord_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetClosestPoint(
            c_int(dim),
            c_int(tag),
            api_coord_, api_coord_n_,
            byref(api_closestCoord_), byref(api_closestCoord_n_),
            byref(api_parametricCoord_), byref(api_parametricCoord_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectordouble(api_closestCoord_, api_closestCoord_n_.value),
            _ovectordouble(api_parametricCoord_, api_parametricCoord_n_.value))
    get_closest_point = getClosestPoint

    @staticmethod
    def reparametrizeOnSurface(dim, tag, parametricCoord, surfaceTag, which=0):
        """
        gmsh.model.reparametrizeOnSurface(dim, tag, parametricCoord, surfaceTag, which=0)

        Reparametrize the boundary entity (point or curve, i.e. with `dim' == 0 or
        `dim' == 1) of tag `tag' on the surface `surfaceTag'. If `dim' == 1,
        reparametrize all the points corresponding to the parametric coordinates
        `parametricCoord'. Multiple matches in case of periodic surfaces can be
        selected with `which'. This feature is only available for a subset of
        entities, depending on the underlying geometrical representation.

        Return `surfaceParametricCoord'.

        Types:
        - `dim': integer
        - `tag': integer
        - `parametricCoord': vector of doubles
        - `surfaceTag': integer
        - `surfaceParametricCoord': vector of doubles
        - `which': integer
        """
        api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
        api_surfaceParametricCoord_, api_surfaceParametricCoord_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshModelReparametrizeOnSurface(
            c_int(dim),
            c_int(tag),
            api_parametricCoord_, api_parametricCoord_n_,
            c_int(surfaceTag),
            byref(api_surfaceParametricCoord_), byref(api_surfaceParametricCoord_n_),
            c_int(which),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_surfaceParametricCoord_, api_surfaceParametricCoord_n_.value)
    reparametrize_on_surface = reparametrizeOnSurface

    @staticmethod
    def setVisibility(dimTags, value, recursive=False):
        """
        gmsh.model.setVisibility(dimTags, value, recursive=False)

        Set the visibility of the model entities `dimTags' (given as a vector of
        (dim, tag) pairs) to `value'. Apply the visibility setting recursively if
        `recursive' is true.

        Types:
        - `dimTags': vector of pairs of integers
        - `value': integer
        - `recursive': boolean
        """
        api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
        ierr = c_int()
        lib.gmshModelSetVisibility(
            api_dimTags_, api_dimTags_n_,
            c_int(value),
            c_int(bool(recursive)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_visibility = setVisibility

    @staticmethod
    def getVisibility(dim, tag):
        """
        gmsh.model.getVisibility(dim, tag)

        Get the visibility of the model entity of dimension `dim' and tag `tag'.

        Return `value'.

        Types:
        - `dim': integer
        - `tag': integer
        - `value': integer
        """
        api_value_ = c_int()
        ierr = c_int()
        lib.gmshModelGetVisibility(
            c_int(dim),
            c_int(tag),
            byref(api_value_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_value_.value
    get_visibility = getVisibility

    @staticmethod
    def setVisibilityPerWindow(value, windowIndex=0):
        """
        gmsh.model.setVisibilityPerWindow(value, windowIndex=0)

        Set the global visibility of the model per window to `value', where
        `windowIndex' identifies the window in the window list.

        Types:
        - `value': integer
        - `windowIndex': integer
        """
        ierr = c_int()
        lib.gmshModelSetVisibilityPerWindow(
            c_int(value),
            c_int(windowIndex),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_visibility_per_window = setVisibilityPerWindow

    @staticmethod
    def setColor(dimTags, r, g, b, a=255, recursive=False):
        """
        gmsh.model.setColor(dimTags, r, g, b, a=255, recursive=False)

        Set the color of the model entities `dimTags' (given as a vector of (dim,
        tag) pairs) to the RGBA value (`r', `g', `b', `a'), where `r', `g', `b' and
        `a' should be integers between 0 and 255. Apply the color setting
        recursively if `recursive' is true.

        Types:
        - `dimTags': vector of pairs of integers
        - `r': integer
        - `g': integer
        - `b': integer
        - `a': integer
        - `recursive': boolean
        """
        api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
        ierr = c_int()
        lib.gmshModelSetColor(
            api_dimTags_, api_dimTags_n_,
            c_int(r),
            c_int(g),
            c_int(b),
            c_int(a),
            c_int(bool(recursive)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_color = setColor

    @staticmethod
    def getColor(dim, tag):
        """
        gmsh.model.getColor(dim, tag)

        Get the color of the model entity of dimension `dim' and tag `tag'. If no
        color is specified for the entity, return fully transparent blue, i.e. (0,
        0, 255, 0).

        Return `r', `g', `b', `a'.

        Types:
        - `dim': integer
        - `tag': integer
        - `r': integer
        - `g': integer
        - `b': integer
        - `a': integer
        """
        api_r_ = c_int()
        api_g_ = c_int()
        api_b_ = c_int()
        api_a_ = c_int()
        ierr = c_int()
        lib.gmshModelGetColor(
            c_int(dim),
            c_int(tag),
            byref(api_r_),
            byref(api_g_),
            byref(api_b_),
            byref(api_a_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_r_.value,
            api_g_.value,
            api_b_.value,
            api_a_.value)
    get_color = getColor

    @staticmethod
    def setCoordinates(tag, x, y, z):
        """
        gmsh.model.setCoordinates(tag, x, y, z)

        Set the `x', `y', `z' coordinates of a geometrical point.

        Types:
        - `tag': integer
        - `x': double
        - `y': double
        - `z': double
        """
        ierr = c_int()
        lib.gmshModelSetCoordinates(
            c_int(tag),
            c_double(x),
            c_double(y),
            c_double(z),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_coordinates = setCoordinates

    @staticmethod
    def setAttribute(name, values):
        """
        gmsh.model.setAttribute(name, values)

        Set the values of the attribute with name `name'.

        Types:
        - `name': string
        - `values': vector of strings
        """
        api_values_, api_values_n_ = _ivectorstring(values)
        ierr = c_int()
        lib.gmshModelSetAttribute(
            c_char_p(name.encode()),
            api_values_, api_values_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_attribute = setAttribute

    @staticmethod
    def getAttribute(name):
        """
        gmsh.model.getAttribute(name)

        Get the values of the attribute with name `name'.

        Return `values'.

        Types:
        - `name': string
        - `values': vector of strings
        """
        api_values_, api_values_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetAttribute(
            c_char_p(name.encode()),
            byref(api_values_), byref(api_values_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_values_, api_values_n_.value)
    get_attribute = getAttribute

    @staticmethod
    def getAttributeNames():
        """
        gmsh.model.getAttributeNames()

        Get the names of any optional attributes stored in the model.

        Return `names'.

        Types:
        - `names': vector of strings
        """
        api_names_, api_names_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshModelGetAttributeNames(
            byref(api_names_), byref(api_names_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_names_, api_names_n_.value)
    get_attribute_names = getAttributeNames

    @staticmethod
    def removeAttribute(name):
        """
        gmsh.model.removeAttribute(name)

        Remove the attribute with name `name'.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshModelRemoveAttribute(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    remove_attribute = removeAttribute


    class mesh:
        """
        Mesh functions
        """

        @staticmethod
        def generate(dim=3):
            """
            gmsh.model.mesh.generate(dim=3)

            Generate a mesh of the current model, up to dimension `dim' (0, 1, 2 or 3).

            Types:
            - `dim': integer
            """
            ierr = c_int()
            lib.gmshModelMeshGenerate(
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def partition(numPart, elementTags=[], partitions=[]):
            """
            gmsh.model.mesh.partition(numPart, elementTags=[], partitions=[])

            Partition the mesh of the current model into `numPart' partitions.
            Optionally, `elementTags' and `partitions' can be provided to specify the
            partition of each element explicitly.

            Types:
            - `numPart': integer
            - `elementTags': vector of sizes
            - `partitions': vector of integers
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            api_partitions_, api_partitions_n_ = _ivectorint(partitions)
            ierr = c_int()
            lib.gmshModelMeshPartition(
                c_int(numPart),
                api_elementTags_, api_elementTags_n_,
                api_partitions_, api_partitions_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def unpartition():
            """
            gmsh.model.mesh.unpartition()

            Unpartition the mesh of the current model.
            """
            ierr = c_int()
            lib.gmshModelMeshUnpartition(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def optimize(method="", force=False, niter=1, dimTags=[]):
            """
            gmsh.model.mesh.optimize(method="", force=False, niter=1, dimTags=[])

            Optimize the mesh of the current model using `method' (empty for default
            tetrahedral mesh optimizer, "Netgen" for Netgen optimizer, "HighOrder" for
            direct high-order mesh optimizer, "HighOrderElastic" for high-order elastic
            smoother, "HighOrderFastCurving" for fast curving algorithm, "Laplace2D"
            for Laplace smoothing, "Relocate2D" and "Relocate3D" for node relocation,
            "QuadQuasiStructured" for quad mesh optimization, "UntangleMeshGeometry"
            for untangling). If `force' is set apply the optimization also to discrete
            entities. If `dimTags' (given as a vector of (dim, tag) pairs) is given,
            only apply the optimizer to the given entities.

            Types:
            - `method': string
            - `force': boolean
            - `niter': integer
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshOptimize(
                c_char_p(method.encode()),
                c_int(bool(force)),
                c_int(niter),
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def recombine():
            """
            gmsh.model.mesh.recombine()

            Recombine the mesh of the current model.
            """
            ierr = c_int()
            lib.gmshModelMeshRecombine(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def refine():
            """
            gmsh.model.mesh.refine()

            Refine the mesh of the current model by uniformly splitting the elements.
            """
            ierr = c_int()
            lib.gmshModelMeshRefine(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def setOrder(order):
            """
            gmsh.model.mesh.setOrder(order)

            Change the order of the elements in the mesh of the current model to
            `order'.

            Types:
            - `order': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSetOrder(
                c_int(order),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_order = setOrder

        @staticmethod
        def getLastEntityError():
            """
            gmsh.model.mesh.getLastEntityError()

            Get the last entities `dimTags' (as a vector of (dim, tag) pairs) where a
            meshing error occurred. Currently only populated by the new 3D meshing
            algorithms.

            Return `dimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetLastEntityError(
                byref(api_dimTags_), byref(api_dimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
        get_last_entity_error = getLastEntityError

        @staticmethod
        def getLastNodeError():
            """
            gmsh.model.mesh.getLastNodeError()

            Get the last node tags `nodeTags' where a meshing error occurred. Currently
            only populated by the new 3D meshing algorithms.

            Return `nodeTags'.

            Types:
            - `nodeTags': vector of sizes
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetLastNodeError(
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorsize(api_nodeTags_, api_nodeTags_n_.value)
        get_last_node_error = getLastNodeError

        @staticmethod
        def clear(dimTags=[]):
            """
            gmsh.model.mesh.clear(dimTags=[])

            Clear the mesh, i.e. delete all the nodes and elements, for the entities
            `dimTags', given as a vector of (dim, tag) pairs. If `dimTags' is empty,
            clear the whole mesh. Note that the mesh of an entity can only be cleared
            if this entity is not on the boundary of another entity with a non-empty
            mesh.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshClear(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def removeElements(dim, tag, elementTags=[]):
            """
            gmsh.model.mesh.removeElements(dim, tag, elementTags=[])

            Remove the elements with tags `elementTags' from the entity of dimension
            `dim' and tag `tag'. If `elementTags' is empty, remove all the elements
            classified on the entity. To get consistent node classification on model
            entities, `reclassifyNodes()' should be called afterwards.

            Types:
            - `dim': integer
            - `tag': integer
            - `elementTags': vector of sizes
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            ierr = c_int()
            lib.gmshModelMeshRemoveElements(
                c_int(dim),
                c_int(tag),
                api_elementTags_, api_elementTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_elements = removeElements

        @staticmethod
        def reverse(dimTags=[]):
            """
            gmsh.model.mesh.reverse(dimTags=[])

            Reverse the orientation of the elements in the entities `dimTags', given as
            a vector of (dim, tag) pairs. If `dimTags' is empty, reverse the
            orientation of the elements in the whole mesh.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshReverse(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def reverseElements(elementTags):
            """
            gmsh.model.mesh.reverseElements(elementTags)

            Reverse the orientation of the elements with tags `elementTags'.

            Types:
            - `elementTags': vector of sizes
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            ierr = c_int()
            lib.gmshModelMeshReverseElements(
                api_elementTags_, api_elementTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        reverse_elements = reverseElements

        @staticmethod
        def affineTransform(affineTransform, dimTags=[]):
            """
            gmsh.model.mesh.affineTransform(affineTransform, dimTags=[])

            Apply the affine transformation `affineTransform' (16 entries of a 4x4
            matrix, by row; only the 12 first can be provided for convenience) to the
            coordinates of the nodes classified on the entities `dimTags', given as a
            vector of (dim, tag) pairs. If `dimTags' is empty, transform all the nodes
            in the mesh.

            Types:
            - `affineTransform': vector of doubles
            - `dimTags': vector of pairs of integers
            """
            api_affineTransform_, api_affineTransform_n_ = _ivectordouble(affineTransform)
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshAffineTransform(
                api_affineTransform_, api_affineTransform_n_,
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        affine_transform = affineTransform

        @staticmethod
        def getNodes(dim=-1, tag=-1, includeBoundary=False, returnParametricCoord=True):
            """
            gmsh.model.mesh.getNodes(dim=-1, tag=-1, includeBoundary=False, returnParametricCoord=True)

            Get the nodes classified on the entity of dimension `dim' and tag `tag'. If
            `tag' < 0, get the nodes for all entities of dimension `dim'. If `dim' and
            `tag' are negative, get all the nodes in the mesh. `nodeTags' contains the
            node tags (their unique, strictly positive identification numbers). `coord'
            is a vector of length 3 times the length of `nodeTags' that contains the x,
            y, z coordinates of the nodes, concatenated: [n1x, n1y, n1z, n2x, ...]. If
            `dim' >= 0 and `returnParamtricCoord' is set, `parametricCoord' contains
            the parametric coordinates ([u1, u2, ...] or [u1, v1, u2, ...]) of the
            nodes, if available. The length of `parametricCoord' can be 0 or `dim'
            times the length of `nodeTags'. If `includeBoundary' is set, also return
            the nodes classified on the boundary of the entity (which will be
            reparametrized on the entity if `dim' >= 0 in order to compute their
            parametric coordinates).

            Return `nodeTags', `coord', `parametricCoord'.

            Types:
            - `nodeTags': vector of sizes
            - `coord': vector of doubles
            - `parametricCoord': vector of doubles
            - `dim': integer
            - `tag': integer
            - `includeBoundary': boolean
            - `returnParametricCoord': boolean
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            api_parametricCoord_, api_parametricCoord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetNodes(
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_coord_), byref(api_coord_n_),
                byref(api_parametricCoord_), byref(api_parametricCoord_n_),
                c_int(dim),
                c_int(tag),
                c_int(bool(includeBoundary)),
                c_int(bool(returnParametricCoord)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value),
                _ovectordouble(api_parametricCoord_, api_parametricCoord_n_.value))
        get_nodes = getNodes

        @staticmethod
        def getNodesByElementType(elementType, tag=-1, returnParametricCoord=True):
            """
            gmsh.model.mesh.getNodesByElementType(elementType, tag=-1, returnParametricCoord=True)

            Get the nodes classified on the entity of tag `tag', for all the elements
            of type `elementType'. The other arguments are treated as in `getNodes'.

            Return `nodeTags', `coord', `parametricCoord'.

            Types:
            - `elementType': integer
            - `nodeTags': vector of sizes
            - `coord': vector of doubles
            - `parametricCoord': vector of doubles
            - `tag': integer
            - `returnParametricCoord': boolean
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            api_parametricCoord_, api_parametricCoord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetNodesByElementType(
                c_int(elementType),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_coord_), byref(api_coord_n_),
                byref(api_parametricCoord_), byref(api_parametricCoord_n_),
                c_int(tag),
                c_int(bool(returnParametricCoord)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value),
                _ovectordouble(api_parametricCoord_, api_parametricCoord_n_.value))
        get_nodes_by_element_type = getNodesByElementType

        @staticmethod
        def getNode(nodeTag):
            """
            gmsh.model.mesh.getNode(nodeTag)

            Get the coordinates and the parametric coordinates (if any) of the node
            with tag `tag', as well as the dimension `dim' and tag `tag' of the entity
            on which the node is classified. This function relies on an internal cache
            (a vector in case of dense node numbering, a map otherwise); for large
            meshes accessing nodes in bulk is often preferable.

            Return `coord', `parametricCoord', `dim', `tag'.

            Types:
            - `nodeTag': size
            - `coord': vector of doubles
            - `parametricCoord': vector of doubles
            - `dim': integer
            - `tag': integer
            """
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            api_parametricCoord_, api_parametricCoord_n_ = POINTER(c_double)(), c_size_t()
            api_dim_ = c_int()
            api_tag_ = c_int()
            ierr = c_int()
            lib.gmshModelMeshGetNode(
                c_size_t(nodeTag),
                byref(api_coord_), byref(api_coord_n_),
                byref(api_parametricCoord_), byref(api_parametricCoord_n_),
                byref(api_dim_),
                byref(api_tag_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectordouble(api_coord_, api_coord_n_.value),
                _ovectordouble(api_parametricCoord_, api_parametricCoord_n_.value),
                api_dim_.value,
                api_tag_.value)
        get_node = getNode

        @staticmethod
        def setNode(nodeTag, coord, parametricCoord):
            """
            gmsh.model.mesh.setNode(nodeTag, coord, parametricCoord)

            Set the coordinates and the parametric coordinates (if any) of the node
            with tag `tag'. This function relies on an internal cache (a vector in case
            of dense node numbering, a map otherwise); for large meshes accessing nodes
            in bulk is often preferable.

            Types:
            - `nodeTag': size
            - `coord': vector of doubles
            - `parametricCoord': vector of doubles
            """
            api_coord_, api_coord_n_ = _ivectordouble(coord)
            api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
            ierr = c_int()
            lib.gmshModelMeshSetNode(
                c_size_t(nodeTag),
                api_coord_, api_coord_n_,
                api_parametricCoord_, api_parametricCoord_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_node = setNode

        @staticmethod
        def rebuildNodeCache(onlyIfNecessary=True):
            """
            gmsh.model.mesh.rebuildNodeCache(onlyIfNecessary=True)

            Rebuild the node cache.

            Types:
            - `onlyIfNecessary': boolean
            """
            ierr = c_int()
            lib.gmshModelMeshRebuildNodeCache(
                c_int(bool(onlyIfNecessary)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        rebuild_node_cache = rebuildNodeCache

        @staticmethod
        def rebuildElementCache(onlyIfNecessary=True):
            """
            gmsh.model.mesh.rebuildElementCache(onlyIfNecessary=True)

            Rebuild the element cache.

            Types:
            - `onlyIfNecessary': boolean
            """
            ierr = c_int()
            lib.gmshModelMeshRebuildElementCache(
                c_int(bool(onlyIfNecessary)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        rebuild_element_cache = rebuildElementCache

        @staticmethod
        def getNodesForPhysicalGroup(dim, tag):
            """
            gmsh.model.mesh.getNodesForPhysicalGroup(dim, tag)

            Get the nodes from all the elements belonging to the physical group of
            dimension `dim' and tag `tag'. `nodeTags' contains the node tags; `coord'
            is a vector of length 3 times the length of `nodeTags' that contains the x,
            y, z coordinates of the nodes, concatenated: [n1x, n1y, n1z, n2x, ...].

            Return `nodeTags', `coord'.

            Types:
            - `dim': integer
            - `tag': integer
            - `nodeTags': vector of sizes
            - `coord': vector of doubles
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetNodesForPhysicalGroup(
                c_int(dim),
                c_int(tag),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_coord_), byref(api_coord_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value))
        get_nodes_for_physical_group = getNodesForPhysicalGroup

        @staticmethod
        def getMaxNodeTag():
            """
            gmsh.model.mesh.getMaxNodeTag()

            Get the maximum tag `maxTag' of a node in the mesh.

            Return `maxTag'.

            Types:
            - `maxTag': size
            """
            api_maxTag_ = c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetMaxNodeTag(
                byref(api_maxTag_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_maxTag_.value
        get_max_node_tag = getMaxNodeTag

        @staticmethod
        def addNodes(dim, tag, nodeTags, coord, parametricCoord=[]):
            """
            gmsh.model.mesh.addNodes(dim, tag, nodeTags, coord, parametricCoord=[])

            Add nodes classified on the model entity of dimension `dim' and tag `tag'.
            `nodeTags' contains the node tags (their unique, strictly positive
            identification numbers). `coord' is a vector of length 3 times the length
            of `nodeTags' that contains the x, y, z coordinates of the nodes,
            concatenated: [n1x, n1y, n1z, n2x, ...]. The optional `parametricCoord'
            vector contains the parametric coordinates of the nodes, if any. The length
            of `parametricCoord' can be 0 or `dim' times the length of `nodeTags'. If
            the `nodeTags' vector is empty, new tags are automatically assigned to the
            nodes.

            Types:
            - `dim': integer
            - `tag': integer
            - `nodeTags': vector of sizes
            - `coord': vector of doubles
            - `parametricCoord': vector of doubles
            """
            api_nodeTags_, api_nodeTags_n_ = _ivectorsize(nodeTags)
            api_coord_, api_coord_n_ = _ivectordouble(coord)
            api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
            ierr = c_int()
            lib.gmshModelMeshAddNodes(
                c_int(dim),
                c_int(tag),
                api_nodeTags_, api_nodeTags_n_,
                api_coord_, api_coord_n_,
                api_parametricCoord_, api_parametricCoord_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_nodes = addNodes

        @staticmethod
        def reclassifyNodes():
            """
            gmsh.model.mesh.reclassifyNodes()

            Reclassify all nodes on their associated model entity, based on the
            elements. Can be used when importing nodes in bulk (e.g. by associating
            them all to a single volume), to reclassify them correctly on model
            surfaces, curves, etc. after the elements have been set.
            """
            ierr = c_int()
            lib.gmshModelMeshReclassifyNodes(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        reclassify_nodes = reclassifyNodes

        @staticmethod
        def relocateNodes(dim=-1, tag=-1):
            """
            gmsh.model.mesh.relocateNodes(dim=-1, tag=-1)

            Relocate the nodes classified on the entity of dimension `dim' and tag
            `tag' using their parametric coordinates. If `tag' < 0, relocate the nodes
            for all entities of dimension `dim'. If `dim' and `tag' are negative,
            relocate all the nodes in the mesh.

            Types:
            - `dim': integer
            - `tag': integer
            """
            ierr = c_int()
            lib.gmshModelMeshRelocateNodes(
                c_int(dim),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        relocate_nodes = relocateNodes

        @staticmethod
        def getElements(dim=-1, tag=-1):
            """
            gmsh.model.mesh.getElements(dim=-1, tag=-1)

            Get the elements classified on the entity of dimension `dim' and tag `tag'.
            If `tag' < 0, get the elements for all entities of dimension `dim'. If
            `dim' and `tag' are negative, get all the elements in the mesh.
            `elementTypes' contains the MSH types of the elements (e.g. `2' for 3-node
            triangles: see `getElementProperties' to obtain the properties for a given
            element type). `elementTags' is a vector of the same length as
            `elementTypes'; each entry is a vector containing the tags (unique,
            strictly positive identifiers) of the elements of the corresponding type.
            `nodeTags' is also a vector of the same length as `elementTypes'; each
            entry is a vector of length equal to the number of elements of the given
            type times the number N of nodes for this type of element, that contains
            the node tags of all the elements of the given type, concatenated: [e1n1,
            e1n2, ..., e1nN, e2n1, ...].

            Return `elementTypes', `elementTags', `nodeTags'.

            Types:
            - `elementTypes': vector of integers
            - `elementTags': vector of vectors of sizes
            - `nodeTags': vector of vectors of sizes
            - `dim': integer
            - `tag': integer
            """
            api_elementTypes_, api_elementTypes_n_ = POINTER(c_int)(), c_size_t()
            api_elementTags_, api_elementTags_n_, api_elementTags_nn_ = POINTER(POINTER(c_size_t))(), POINTER(c_size_t)(), c_size_t()
            api_nodeTags_, api_nodeTags_n_, api_nodeTags_nn_ = POINTER(POINTER(c_size_t))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElements(
                byref(api_elementTypes_), byref(api_elementTypes_n_),
                byref(api_elementTags_), byref(api_elementTags_n_), byref(api_elementTags_nn_),
                byref(api_nodeTags_), byref(api_nodeTags_n_), byref(api_nodeTags_nn_),
                c_int(dim),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorint(api_elementTypes_, api_elementTypes_n_.value),
                _ovectorvectorsize(api_elementTags_, api_elementTags_n_, api_elementTags_nn_),
                _ovectorvectorsize(api_nodeTags_, api_nodeTags_n_, api_nodeTags_nn_))
        get_elements = getElements

        @staticmethod
        def getElement(elementTag):
            """
            gmsh.model.mesh.getElement(elementTag)

            Get the type and node tags of the element with tag `tag', as well as the
            dimension `dim' and tag `tag' of the entity on which the element is
            classified. This function relies on an internal cache (a vector in case of
            dense element numbering, a map otherwise); for large meshes accessing
            elements in bulk is often preferable.

            Return `elementType', `nodeTags', `dim', `tag'.

            Types:
            - `elementTag': size
            - `elementType': integer
            - `nodeTags': vector of sizes
            - `dim': integer
            - `tag': integer
            """
            api_elementType_ = c_int()
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_dim_ = c_int()
            api_tag_ = c_int()
            ierr = c_int()
            lib.gmshModelMeshGetElement(
                c_size_t(elementTag),
                byref(api_elementType_),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_dim_),
                byref(api_tag_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_elementType_.value,
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                api_dim_.value,
                api_tag_.value)
        get_element = getElement

        @staticmethod
        def getElementByCoordinates(x, y, z, dim=-1, strict=False):
            """
            gmsh.model.mesh.getElementByCoordinates(x, y, z, dim=-1, strict=False)

            Search the mesh for an element located at coordinates (`x', `y', `z'). This
            function performs a search in a spatial octree. If an element is found,
            return its tag, type and node tags, as well as the local coordinates (`u',
            `v', `w') within the reference element corresponding to search location. If
            `dim' is >= 0, only search for elements of the given dimension. If `strict'
            is not set, use a tolerance to find elements near the search location.

            Return `elementTag', `elementType', `nodeTags', `u', `v', `w'.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `elementTag': size
            - `elementType': integer
            - `nodeTags': vector of sizes
            - `u': double
            - `v': double
            - `w': double
            - `dim': integer
            - `strict': boolean
            """
            api_elementTag_ = c_size_t()
            api_elementType_ = c_int()
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_u_ = c_double()
            api_v_ = c_double()
            api_w_ = c_double()
            ierr = c_int()
            lib.gmshModelMeshGetElementByCoordinates(
                c_double(x),
                c_double(y),
                c_double(z),
                byref(api_elementTag_),
                byref(api_elementType_),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_u_),
                byref(api_v_),
                byref(api_w_),
                c_int(dim),
                c_int(bool(strict)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_elementTag_.value,
                api_elementType_.value,
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                api_u_.value,
                api_v_.value,
                api_w_.value)
        get_element_by_coordinates = getElementByCoordinates

        @staticmethod
        def getElementsByCoordinates(x, y, z, dim=-1, strict=False):
            """
            gmsh.model.mesh.getElementsByCoordinates(x, y, z, dim=-1, strict=False)

            Search the mesh for element(s) located at coordinates (`x', `y', `z'). This
            function performs a search in a spatial octree. Return the tags of all
            found elements in `elementTags'. Additional information about the elements
            can be accessed through `getElement' and `getLocalCoordinatesInElement'. If
            `dim' is >= 0, only search for elements of the given dimension. If `strict'
            is not set, use a tolerance to find elements near the search location.

            Return `elementTags'.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `elementTags': vector of sizes
            - `dim': integer
            - `strict': boolean
            """
            api_elementTags_, api_elementTags_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementsByCoordinates(
                c_double(x),
                c_double(y),
                c_double(z),
                byref(api_elementTags_), byref(api_elementTags_n_),
                c_int(dim),
                c_int(bool(strict)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorsize(api_elementTags_, api_elementTags_n_.value)
        get_elements_by_coordinates = getElementsByCoordinates

        @staticmethod
        def getLocalCoordinatesInElement(elementTag, x, y, z):
            """
            gmsh.model.mesh.getLocalCoordinatesInElement(elementTag, x, y, z)

            Return the local coordinates (`u', `v', `w') within the element
            `elementTag' corresponding to the model coordinates (`x', `y', `z'). This
            function relies on an internal cache (a vector in case of dense element
            numbering, a map otherwise); for large meshes accessing elements in bulk is
            often preferable.

            Return `u', `v', `w'.

            Types:
            - `elementTag': size
            - `x': double
            - `y': double
            - `z': double
            - `u': double
            - `v': double
            - `w': double
            """
            api_u_ = c_double()
            api_v_ = c_double()
            api_w_ = c_double()
            ierr = c_int()
            lib.gmshModelMeshGetLocalCoordinatesInElement(
                c_size_t(elementTag),
                c_double(x),
                c_double(y),
                c_double(z),
                byref(api_u_),
                byref(api_v_),
                byref(api_w_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_u_.value,
                api_v_.value,
                api_w_.value)
        get_local_coordinates_in_element = getLocalCoordinatesInElement

        @staticmethod
        def getElementTypes(dim=-1, tag=-1):
            """
            gmsh.model.mesh.getElementTypes(dim=-1, tag=-1)

            Get the types of elements in the entity of dimension `dim' and tag `tag'.
            If `tag' < 0, get the types for all entities of dimension `dim'. If `dim'
            and `tag' are negative, get all the types in the mesh.

            Return `elementTypes'.

            Types:
            - `elementTypes': vector of integers
            - `dim': integer
            - `tag': integer
            """
            api_elementTypes_, api_elementTypes_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementTypes(
                byref(api_elementTypes_), byref(api_elementTypes_n_),
                c_int(dim),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_elementTypes_, api_elementTypes_n_.value)
        get_element_types = getElementTypes

        @staticmethod
        def getElementType(familyName, order, serendip=False):
            """
            gmsh.model.mesh.getElementType(familyName, order, serendip=False)

            Return an element type given its family name `familyName' ("Point", "Line",
            "Triangle", "Quadrangle", "Tetrahedron", "Pyramid", "Prism", "Hexahedron")
            and polynomial order `order'. If `serendip' is true, return the
            corresponding serendip element type (element without interior nodes).

            Return an integer.

            Types:
            - `familyName': string
            - `order': integer
            - `serendip': boolean
            """
            ierr = c_int()
            api_result_ = lib.gmshModelMeshGetElementType(
                c_char_p(familyName.encode()),
                c_int(order),
                c_int(bool(serendip)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        get_element_type = getElementType

        @staticmethod
        def getElementProperties(elementType):
            """
            gmsh.model.mesh.getElementProperties(elementType)

            Get the properties of an element of type `elementType': its name
            (`elementName'), dimension (`dim'), order (`order'), number of nodes
            (`numNodes'), local coordinates of the nodes in the reference element
            (`localNodeCoord' vector, of length `dim' times `numNodes') and number of
            primary (first order) nodes (`numPrimaryNodes').

            Return `elementName', `dim', `order', `numNodes', `localNodeCoord', `numPrimaryNodes'.

            Types:
            - `elementType': integer
            - `elementName': string
            - `dim': integer
            - `order': integer
            - `numNodes': integer
            - `localNodeCoord': vector of doubles
            - `numPrimaryNodes': integer
            """
            api_elementName_ = c_char_p()
            api_dim_ = c_int()
            api_order_ = c_int()
            api_numNodes_ = c_int()
            api_localNodeCoord_, api_localNodeCoord_n_ = POINTER(c_double)(), c_size_t()
            api_numPrimaryNodes_ = c_int()
            ierr = c_int()
            lib.gmshModelMeshGetElementProperties(
                c_int(elementType),
                byref(api_elementName_),
                byref(api_dim_),
                byref(api_order_),
                byref(api_numNodes_),
                byref(api_localNodeCoord_), byref(api_localNodeCoord_n_),
                byref(api_numPrimaryNodes_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ostring(api_elementName_),
                api_dim_.value,
                api_order_.value,
                api_numNodes_.value,
                _ovectordouble(api_localNodeCoord_, api_localNodeCoord_n_.value),
                api_numPrimaryNodes_.value)
        get_element_properties = getElementProperties

        @staticmethod
        def getElementsByType(elementType, tag=-1, task=0, numTasks=1):
            """
            gmsh.model.mesh.getElementsByType(elementType, tag=-1, task=0, numTasks=1)

            Get the elements of type `elementType' classified on the entity of tag
            `tag'. If `tag' < 0, get the elements for all entities. `elementTags' is a
            vector containing the tags (unique, strictly positive identifiers) of the
            elements of the corresponding type. `nodeTags' is a vector of length equal
            to the number of elements of the given type times the number N of nodes for
            this type of element, that contains the node tags of all the elements of
            the given type, concatenated: [e1n1, e1n2, ..., e1nN, e2n1, ...]. If
            `numTasks' > 1, only compute and return the part of the data indexed by
            `task' (for C++ only; output vectors must be preallocated).

            Return `elementTags', `nodeTags'.

            Types:
            - `elementType': integer
            - `elementTags': vector of sizes
            - `nodeTags': vector of sizes
            - `tag': integer
            - `task': size
            - `numTasks': size
            """
            api_elementTags_, api_elementTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementsByType(
                c_int(elementType),
                byref(api_elementTags_), byref(api_elementTags_n_),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                c_int(tag),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_elementTags_, api_elementTags_n_.value),
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value))
        get_elements_by_type = getElementsByType

        @staticmethod
        def getMaxElementTag():
            """
            gmsh.model.mesh.getMaxElementTag()

            Get the maximum tag `maxTag' of an element in the mesh.

            Return `maxTag'.

            Types:
            - `maxTag': size
            """
            api_maxTag_ = c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetMaxElementTag(
                byref(api_maxTag_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_maxTag_.value
        get_max_element_tag = getMaxElementTag

        @staticmethod
        def getElementQualities(elementTags, qualityName="minSICN", task=0, numTasks=1):
            """
            gmsh.model.mesh.getElementQualities(elementTags, qualityName="minSICN", task=0, numTasks=1)

            Get the quality `elementQualities' of the elements with tags `elementTags'.
            `qualityType' is the requested quality measure: "minDetJac" and "maxDetJac"
            for the adaptively computed minimal and maximal Jacobian determinant,
            "minSJ" for the sampled minimal scaled jacobien, "minSICN" for the sampled
            minimal signed inverted condition number, "minSIGE" for the sampled signed
            inverted gradient error, "gamma" for the ratio of the inscribed to
            circumcribed sphere radius, "innerRadius" for the inner radius,
            "outerRadius" for the outerRadius, "minIsotropy" for the minimum isotropy
            measure, "angleShape" for the angle shape measure, "minEdge" for the
            minimum straight edge length, "maxEdge" for the maximum straight edge
            length, "volume" for the volume. If `numTasks' > 1, only compute and return
            the part of the data indexed by `task' (for C++ only; output vector must be
            preallocated).

            Return `elementsQuality'.

            Types:
            - `elementTags': vector of sizes
            - `elementsQuality': vector of doubles
            - `qualityName': string
            - `task': size
            - `numTasks': size
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            api_elementsQuality_, api_elementsQuality_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementQualities(
                api_elementTags_, api_elementTags_n_,
                byref(api_elementsQuality_), byref(api_elementsQuality_n_),
                c_char_p(qualityName.encode()),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectordouble(api_elementsQuality_, api_elementsQuality_n_.value)
        get_element_qualities = getElementQualities

        @staticmethod
        def addElements(dim, tag, elementTypes, elementTags, nodeTags):
            """
            gmsh.model.mesh.addElements(dim, tag, elementTypes, elementTags, nodeTags)

            Add elements classified on the entity of dimension `dim' and tag `tag'.
            `types' contains the MSH types of the elements (e.g. `2' for 3-node
            triangles: see the Gmsh reference manual). `elementTags' is a vector of the
            same length as `types'; each entry is a vector containing the tags (unique,
            strictly positive identifiers) of the elements of the corresponding type.
            `nodeTags' is also a vector of the same length as `types'; each entry is a
            vector of length equal to the number of elements of the given type times
            the number N of nodes per element, that contains the node tags of all the
            elements of the given type, concatenated: [e1n1, e1n2, ..., e1nN, e2n1,
            ...].

            Types:
            - `dim': integer
            - `tag': integer
            - `elementTypes': vector of integers
            - `elementTags': vector of vectors of integers (size)
            - `nodeTags': vector of vectors of integers (size)
            """
            api_elementTypes_, api_elementTypes_n_ = _ivectorint(elementTypes)
            api_elementTags_, api_elementTags_n_, api_elementTags_nn_ = _ivectorvectorsize(elementTags)
            api_nodeTags_, api_nodeTags_n_, api_nodeTags_nn_ = _ivectorvectorsize(nodeTags)
            ierr = c_int()
            lib.gmshModelMeshAddElements(
                c_int(dim),
                c_int(tag),
                api_elementTypes_, api_elementTypes_n_,
                api_elementTags_, api_elementTags_n_, api_elementTags_nn_,
                api_nodeTags_, api_nodeTags_n_, api_nodeTags_nn_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_elements = addElements

        @staticmethod
        def addElementsByType(tag, elementType, elementTags, nodeTags):
            """
            gmsh.model.mesh.addElementsByType(tag, elementType, elementTags, nodeTags)

            Add elements of type `elementType' classified on the entity of tag `tag'.
            `elementTags' contains the tags (unique, strictly positive identifiers) of
            the elements of the corresponding type. `nodeTags' is a vector of length
            equal to the number of elements times the number N of nodes per element,
            that contains the node tags of all the elements, concatenated: [e1n1, e1n2,
            ..., e1nN, e2n1, ...]. If the `elementTag' vector is empty, new tags are
            automatically assigned to the elements.

            Types:
            - `tag': integer
            - `elementType': integer
            - `elementTags': vector of sizes
            - `nodeTags': vector of sizes
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            api_nodeTags_, api_nodeTags_n_ = _ivectorsize(nodeTags)
            ierr = c_int()
            lib.gmshModelMeshAddElementsByType(
                c_int(tag),
                c_int(elementType),
                api_elementTags_, api_elementTags_n_,
                api_nodeTags_, api_nodeTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_elements_by_type = addElementsByType

        @staticmethod
        def getIntegrationPoints(elementType, integrationType):
            """
            gmsh.model.mesh.getIntegrationPoints(elementType, integrationType)

            Get the numerical quadrature information for the given element type
            `elementType' and integration rule `integrationType', where
            `integrationType' concatenates the integration rule family name with the
            desired order (e.g. "Gauss4" for a quadrature suited for integrating 4th
            order polynomials). The "CompositeGauss" family uses tensor-product rules
            based the 1D Gauss-Legendre rule; the "Gauss" family uses an economic
            scheme when available (i.e. with a minimal number of points), and falls
            back to "CompositeGauss" otherwise. Note that integration points for the
            "Gauss" family can fall outside of the reference element for high-order
            rules. `localCoord' contains the u, v, w coordinates of the G integration
            points in the reference element: [g1u, g1v, g1w, ..., gGu, gGv, gGw].
            `weights' contains the associated weights: [g1q, ..., gGq].

            Return `localCoord', `weights'.

            Types:
            - `elementType': integer
            - `integrationType': string
            - `localCoord': vector of doubles
            - `weights': vector of doubles
            """
            api_localCoord_, api_localCoord_n_ = POINTER(c_double)(), c_size_t()
            api_weights_, api_weights_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetIntegrationPoints(
                c_int(elementType),
                c_char_p(integrationType.encode()),
                byref(api_localCoord_), byref(api_localCoord_n_),
                byref(api_weights_), byref(api_weights_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectordouble(api_localCoord_, api_localCoord_n_.value),
                _ovectordouble(api_weights_, api_weights_n_.value))
        get_integration_points = getIntegrationPoints

        @staticmethod
        def getJacobians(elementType, localCoord, tag=-1, task=0, numTasks=1):
            """
            gmsh.model.mesh.getJacobians(elementType, localCoord, tag=-1, task=0, numTasks=1)

            Get the Jacobians of all the elements of type `elementType' classified on
            the entity of tag `tag', at the G evaluation points `localCoord' given as
            concatenated u, v, w coordinates in the reference element [g1u, g1v, g1w,
            ..., gGu, gGv, gGw]. Data is returned by element, with elements in the same
            order as in `getElements' and `getElementsByType'. `jacobians' contains for
            each element the 9 entries of the 3x3 Jacobian matrix at each evaluation
            point. The matrix is returned by column: [e1g1Jxu, e1g1Jyu, e1g1Jzu,
            e1g1Jxv, ..., e1g1Jzw, e1g2Jxu, ..., e1gGJzw, e2g1Jxu, ...], with Jxu =
            dx/du, Jyu = dy/du, etc. `determinants' contains for each element the
            determinant of the Jacobian matrix at each evaluation point: [e1g1, e1g2,
            ... e1gG, e2g1, ...]. `coord' contains for each element the x, y, z
            coordinates of the evaluation points. If `tag' < 0, get the Jacobian data
            for all entities. If `numTasks' > 1, only compute and return the part of
            the data indexed by `task' (for C++ only; output vectors must be
            preallocated).

            Return `jacobians', `determinants', `coord'.

            Types:
            - `elementType': integer
            - `localCoord': vector of doubles
            - `jacobians': vector of doubles
            - `determinants': vector of doubles
            - `coord': vector of doubles
            - `tag': integer
            - `task': size
            - `numTasks': size
            """
            api_localCoord_, api_localCoord_n_ = _ivectordouble(localCoord)
            api_jacobians_, api_jacobians_n_ = POINTER(c_double)(), c_size_t()
            api_determinants_, api_determinants_n_ = POINTER(c_double)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetJacobians(
                c_int(elementType),
                api_localCoord_, api_localCoord_n_,
                byref(api_jacobians_), byref(api_jacobians_n_),
                byref(api_determinants_), byref(api_determinants_n_),
                byref(api_coord_), byref(api_coord_n_),
                c_int(tag),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectordouble(api_jacobians_, api_jacobians_n_.value),
                _ovectordouble(api_determinants_, api_determinants_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value))
        get_jacobians = getJacobians

        @staticmethod
        def getJacobian(elementTag, localCoord):
            """
            gmsh.model.mesh.getJacobian(elementTag, localCoord)

            Get the Jacobian for a single element `elementTag', at the G evaluation
            points `localCoord' given as concatenated u, v, w coordinates in the
            reference element [g1u, g1v, g1w, ..., gGu, gGv, gGw]. `jacobians' contains
            the 9 entries of the 3x3 Jacobian matrix at each evaluation point. The
            matrix is returned by column: [e1g1Jxu, e1g1Jyu, e1g1Jzu, e1g1Jxv, ...,
            e1g1Jzw, e1g2Jxu, ..., e1gGJzw, e2g1Jxu, ...], with Jxu = dx/du, Jyu =
            dy/du, etc. `determinants' contains the determinant of the Jacobian matrix
            at each evaluation point. `coord' contains the x, y, z coordinates of the
            evaluation points. This function relies on an internal cache (a vector in
            case of dense element numbering, a map otherwise); for large meshes
            accessing Jacobians in bulk is often preferable.

            Return `jacobians', `determinants', `coord'.

            Types:
            - `elementTag': size
            - `localCoord': vector of doubles
            - `jacobians': vector of doubles
            - `determinants': vector of doubles
            - `coord': vector of doubles
            """
            api_localCoord_, api_localCoord_n_ = _ivectordouble(localCoord)
            api_jacobians_, api_jacobians_n_ = POINTER(c_double)(), c_size_t()
            api_determinants_, api_determinants_n_ = POINTER(c_double)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetJacobian(
                c_size_t(elementTag),
                api_localCoord_, api_localCoord_n_,
                byref(api_jacobians_), byref(api_jacobians_n_),
                byref(api_determinants_), byref(api_determinants_n_),
                byref(api_coord_), byref(api_coord_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectordouble(api_jacobians_, api_jacobians_n_.value),
                _ovectordouble(api_determinants_, api_determinants_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value))
        get_jacobian = getJacobian

        @staticmethod
        def getBasisFunctions(elementType, localCoord, functionSpaceType, wantedOrientations=[]):
            """
            gmsh.model.mesh.getBasisFunctions(elementType, localCoord, functionSpaceType, wantedOrientations=[])

            Get the basis functions of the element of type `elementType' at the
            evaluation points `localCoord' (given as concatenated u, v, w coordinates
            in the reference element [g1u, g1v, g1w, ..., gGu, gGv, gGw]), for the
            function space `functionSpaceType'. Currently supported function spaces
            include "Lagrange" and "GradLagrange" for isoparametric Lagrange basis
            functions and their gradient in the u, v, w coordinates of the reference
            element; "LagrangeN" and "GradLagrangeN", with N = 1, 2, ..., for N-th
            order Lagrange basis functions; "H1LegendreN" and "GradH1LegendreN", with N
            = 1, 2, ..., for N-th order hierarchical H1 Legendre functions;
            "HcurlLegendreN" and "CurlHcurlLegendreN", with N = 1, 2, ..., for N-th
            order curl-conforming basis functions. `numComponents' returns the number C
            of components of a basis function (e.g. 1 for scalar functions and 3 for
            vector functions). `basisFunctions' returns the value of the N basis
            functions at the evaluation points, i.e. [g1f1, g1f2, ..., g1fN, g2f1, ...]
            when C == 1 or [g1f1u, g1f1v, g1f1w, g1f2u, ..., g1fNw, g2f1u, ...] when C
            == 3. For basis functions that depend on the orientation of the elements,
            all values for the first orientation are returned first, followed by values
            for the second, etc. `numOrientations' returns the overall number of
            orientations. If the `wantedOrientations' vector is not empty, only return
            the values for the desired orientation indices.

            Return `numComponents', `basisFunctions', `numOrientations'.

            Types:
            - `elementType': integer
            - `localCoord': vector of doubles
            - `functionSpaceType': string
            - `numComponents': integer
            - `basisFunctions': vector of doubles
            - `numOrientations': integer
            - `wantedOrientations': vector of integers
            """
            api_localCoord_, api_localCoord_n_ = _ivectordouble(localCoord)
            api_numComponents_ = c_int()
            api_basisFunctions_, api_basisFunctions_n_ = POINTER(c_double)(), c_size_t()
            api_numOrientations_ = c_int()
            api_wantedOrientations_, api_wantedOrientations_n_ = _ivectorint(wantedOrientations)
            ierr = c_int()
            lib.gmshModelMeshGetBasisFunctions(
                c_int(elementType),
                api_localCoord_, api_localCoord_n_,
                c_char_p(functionSpaceType.encode()),
                byref(api_numComponents_),
                byref(api_basisFunctions_), byref(api_basisFunctions_n_),
                byref(api_numOrientations_),
                api_wantedOrientations_, api_wantedOrientations_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_numComponents_.value,
                _ovectordouble(api_basisFunctions_, api_basisFunctions_n_.value),
                api_numOrientations_.value)
        get_basis_functions = getBasisFunctions

        @staticmethod
        def getBasisFunctionsOrientation(elementType, functionSpaceType, tag=-1, task=0, numTasks=1):
            """
            gmsh.model.mesh.getBasisFunctionsOrientation(elementType, functionSpaceType, tag=-1, task=0, numTasks=1)

            Get the orientation index of the elements of type `elementType' in the
            entity of tag `tag'. The arguments have the same meaning as in
            `getBasisFunctions'. `basisFunctionsOrientation' is a vector giving for
            each element the orientation index in the values returned by
            `getBasisFunctions'. For Lagrange basis functions the call is superfluous
            as it will return a vector of zeros. If `numTasks' > 1, only compute and
            return the part of the data indexed by `task' (for C++ only; output vector
            must be preallocated).

            Return `basisFunctionsOrientation'.

            Types:
            - `elementType': integer
            - `functionSpaceType': string
            - `basisFunctionsOrientation': vector of integers
            - `tag': integer
            - `task': size
            - `numTasks': size
            """
            api_basisFunctionsOrientation_, api_basisFunctionsOrientation_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetBasisFunctionsOrientation(
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                byref(api_basisFunctionsOrientation_), byref(api_basisFunctionsOrientation_n_),
                c_int(tag),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_basisFunctionsOrientation_, api_basisFunctionsOrientation_n_.value)
        get_basis_functions_orientation = getBasisFunctionsOrientation

        @staticmethod
        def getBasisFunctionsOrientationForElement(elementTag, functionSpaceType):
            """
            gmsh.model.mesh.getBasisFunctionsOrientationForElement(elementTag, functionSpaceType)

            Get the orientation of a single element `elementTag'.

            Return `basisFunctionsOrientation'.

            Types:
            - `elementTag': size
            - `functionSpaceType': string
            - `basisFunctionsOrientation': integer
            """
            api_basisFunctionsOrientation_ = c_int()
            ierr = c_int()
            lib.gmshModelMeshGetBasisFunctionsOrientationForElement(
                c_size_t(elementTag),
                c_char_p(functionSpaceType.encode()),
                byref(api_basisFunctionsOrientation_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_basisFunctionsOrientation_.value
        get_basis_functions_orientation_for_element = getBasisFunctionsOrientationForElement

        @staticmethod
        def getNumberOfOrientations(elementType, functionSpaceType):
            """
            gmsh.model.mesh.getNumberOfOrientations(elementType, functionSpaceType)

            Get the number of possible orientations for elements of type `elementType'
            and function space named `functionSpaceType'.

            Return an integer.

            Types:
            - `elementType': integer
            - `functionSpaceType': string
            """
            ierr = c_int()
            api_result_ = lib.gmshModelMeshGetNumberOfOrientations(
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        get_number_of_orientations = getNumberOfOrientations

        @staticmethod
        def getEdges(nodeTags):
            """
            gmsh.model.mesh.getEdges(nodeTags)

            Get the global unique mesh edge identifiers `edgeTags' and orientations
            `edgeOrientation' for an input list of node tag pairs defining these edges,
            concatenated in the vector `nodeTags'. Mesh edges are created e.g. by
            `createEdges()', `getKeys()' or `addEdges()'. The reference positive
            orientation is n1 < n2, where n1 and n2 are the tags of the two edge nodes,
            which corresponds to the local orientation of edge-based basis functions as
            well.

            Return `edgeTags', `edgeOrientations'.

            Types:
            - `nodeTags': vector of sizes
            - `edgeTags': vector of sizes
            - `edgeOrientations': vector of integers
            """
            api_nodeTags_, api_nodeTags_n_ = _ivectorsize(nodeTags)
            api_edgeTags_, api_edgeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_edgeOrientations_, api_edgeOrientations_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetEdges(
                api_nodeTags_, api_nodeTags_n_,
                byref(api_edgeTags_), byref(api_edgeTags_n_),
                byref(api_edgeOrientations_), byref(api_edgeOrientations_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_edgeTags_, api_edgeTags_n_.value),
                _ovectorint(api_edgeOrientations_, api_edgeOrientations_n_.value))
        get_edges = getEdges

        @staticmethod
        def getFaces(faceType, nodeTags):
            """
            gmsh.model.mesh.getFaces(faceType, nodeTags)

            Get the global unique mesh face identifiers `faceTags' and orientations
            `faceOrientations' for an input list of a multiple of three (if `faceType'
            == 3) or four (if `faceType' == 4) node tags defining these faces,
            concatenated in the vector `nodeTags'. Mesh faces are created e.g. by
            `createFaces()', `getKeys()' or `addFaces()'.

            Return `faceTags', `faceOrientations'.

            Types:
            - `faceType': integer
            - `nodeTags': vector of sizes
            - `faceTags': vector of sizes
            - `faceOrientations': vector of integers
            """
            api_nodeTags_, api_nodeTags_n_ = _ivectorsize(nodeTags)
            api_faceTags_, api_faceTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_faceOrientations_, api_faceOrientations_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetFaces(
                c_int(faceType),
                api_nodeTags_, api_nodeTags_n_,
                byref(api_faceTags_), byref(api_faceTags_n_),
                byref(api_faceOrientations_), byref(api_faceOrientations_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_faceTags_, api_faceTags_n_.value),
                _ovectorint(api_faceOrientations_, api_faceOrientations_n_.value))
        get_faces = getFaces

        @staticmethod
        def createEdges(dimTags=[]):
            """
            gmsh.model.mesh.createEdges(dimTags=[])

            Create unique mesh edges for the entities `dimTags', given as a vector of
            (dim, tag) pairs.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshCreateEdges(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        create_edges = createEdges

        @staticmethod
        def createFaces(dimTags=[]):
            """
            gmsh.model.mesh.createFaces(dimTags=[])

            Create unique mesh faces for the entities `dimTags', given as a vector of
            (dim, tag) pairs.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshCreateFaces(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        create_faces = createFaces

        @staticmethod
        def getAllEdges():
            """
            gmsh.model.mesh.getAllEdges()

            Get the global unique identifiers `edgeTags' and the nodes `edgeNodes' of
            the edges in the mesh. Mesh edges are created e.g. by `createEdges()',
            `getKeys()' or addEdges().

            Return `edgeTags', `edgeNodes'.

            Types:
            - `edgeTags': vector of sizes
            - `edgeNodes': vector of sizes
            """
            api_edgeTags_, api_edgeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_edgeNodes_, api_edgeNodes_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetAllEdges(
                byref(api_edgeTags_), byref(api_edgeTags_n_),
                byref(api_edgeNodes_), byref(api_edgeNodes_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_edgeTags_, api_edgeTags_n_.value),
                _ovectorsize(api_edgeNodes_, api_edgeNodes_n_.value))
        get_all_edges = getAllEdges

        @staticmethod
        def getAllFaces(faceType):
            """
            gmsh.model.mesh.getAllFaces(faceType)

            Get the global unique identifiers `faceTags' and the nodes `faceNodes' of
            the faces of type `faceType' in the mesh. Mesh faces are created e.g. by
            `createFaces()', `getKeys()' or addFaces().

            Return `faceTags', `faceNodes'.

            Types:
            - `faceType': integer
            - `faceTags': vector of sizes
            - `faceNodes': vector of sizes
            """
            api_faceTags_, api_faceTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_faceNodes_, api_faceNodes_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetAllFaces(
                c_int(faceType),
                byref(api_faceTags_), byref(api_faceTags_n_),
                byref(api_faceNodes_), byref(api_faceNodes_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_faceTags_, api_faceTags_n_.value),
                _ovectorsize(api_faceNodes_, api_faceNodes_n_.value))
        get_all_faces = getAllFaces

        @staticmethod
        def addEdges(edgeTags, edgeNodes):
            """
            gmsh.model.mesh.addEdges(edgeTags, edgeNodes)

            Add mesh edges defined by their global unique identifiers `edgeTags' and
            their nodes `edgeNodes'.

            Types:
            - `edgeTags': vector of sizes
            - `edgeNodes': vector of sizes
            """
            api_edgeTags_, api_edgeTags_n_ = _ivectorsize(edgeTags)
            api_edgeNodes_, api_edgeNodes_n_ = _ivectorsize(edgeNodes)
            ierr = c_int()
            lib.gmshModelMeshAddEdges(
                api_edgeTags_, api_edgeTags_n_,
                api_edgeNodes_, api_edgeNodes_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_edges = addEdges

        @staticmethod
        def addFaces(faceType, faceTags, faceNodes):
            """
            gmsh.model.mesh.addFaces(faceType, faceTags, faceNodes)

            Add mesh faces of type `faceType' defined by their global unique
            identifiers `faceTags' and their nodes `faceNodes'.

            Types:
            - `faceType': integer
            - `faceTags': vector of sizes
            - `faceNodes': vector of sizes
            """
            api_faceTags_, api_faceTags_n_ = _ivectorsize(faceTags)
            api_faceNodes_, api_faceNodes_n_ = _ivectorsize(faceNodes)
            ierr = c_int()
            lib.gmshModelMeshAddFaces(
                c_int(faceType),
                api_faceTags_, api_faceTags_n_,
                api_faceNodes_, api_faceNodes_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_faces = addFaces

        @staticmethod
        def getKeys(elementType, functionSpaceType, tag=-1, returnCoord=True):
            """
            gmsh.model.mesh.getKeys(elementType, functionSpaceType, tag=-1, returnCoord=True)

            Generate the pair of keys for the elements of type `elementType' in the
            entity of tag `tag', for the `functionSpaceType' function space. Each pair
            (`typeKey', `entityKey') uniquely identifies a basis function in the
            function space. If `returnCoord' is set, the `coord' vector contains the x,
            y, z coordinates locating basis functions for sorting purposes. Warning:
            this is an experimental feature and will probably change in a future
            release.

            Return `typeKeys', `entityKeys', `coord'.

            Types:
            - `elementType': integer
            - `functionSpaceType': string
            - `typeKeys': vector of integers
            - `entityKeys': vector of sizes
            - `coord': vector of doubles
            - `tag': integer
            - `returnCoord': boolean
            """
            api_typeKeys_, api_typeKeys_n_ = POINTER(c_int)(), c_size_t()
            api_entityKeys_, api_entityKeys_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetKeys(
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                byref(api_typeKeys_), byref(api_typeKeys_n_),
                byref(api_entityKeys_), byref(api_entityKeys_n_),
                byref(api_coord_), byref(api_coord_n_),
                c_int(tag),
                c_int(bool(returnCoord)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorint(api_typeKeys_, api_typeKeys_n_.value),
                _ovectorsize(api_entityKeys_, api_entityKeys_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value))
        get_keys = getKeys

        @staticmethod
        def getKeysForElement(elementTag, functionSpaceType, returnCoord=True):
            """
            gmsh.model.mesh.getKeysForElement(elementTag, functionSpaceType, returnCoord=True)

            Get the pair of keys for a single element `elementTag'.

            Return `typeKeys', `entityKeys', `coord'.

            Types:
            - `elementTag': size
            - `functionSpaceType': string
            - `typeKeys': vector of integers
            - `entityKeys': vector of sizes
            - `coord': vector of doubles
            - `returnCoord': boolean
            """
            api_typeKeys_, api_typeKeys_n_ = POINTER(c_int)(), c_size_t()
            api_entityKeys_, api_entityKeys_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetKeysForElement(
                c_size_t(elementTag),
                c_char_p(functionSpaceType.encode()),
                byref(api_typeKeys_), byref(api_typeKeys_n_),
                byref(api_entityKeys_), byref(api_entityKeys_n_),
                byref(api_coord_), byref(api_coord_n_),
                c_int(bool(returnCoord)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorint(api_typeKeys_, api_typeKeys_n_.value),
                _ovectorsize(api_entityKeys_, api_entityKeys_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value))
        get_keys_for_element = getKeysForElement

        @staticmethod
        def getNumberOfKeys(elementType, functionSpaceType):
            """
            gmsh.model.mesh.getNumberOfKeys(elementType, functionSpaceType)

            Get the number of keys by elements of type `elementType' for function space
            named `functionSpaceType'.

            Return an integer.

            Types:
            - `elementType': integer
            - `functionSpaceType': string
            """
            ierr = c_int()
            api_result_ = lib.gmshModelMeshGetNumberOfKeys(
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        get_number_of_keys = getNumberOfKeys

        @staticmethod
        def getKeysInformation(typeKeys, entityKeys, elementType, functionSpaceType):
            """
            gmsh.model.mesh.getKeysInformation(typeKeys, entityKeys, elementType, functionSpaceType)

            Get information about the pair of `keys'. `infoKeys' returns information
            about the functions associated with the pairs (`typeKeys', `entityKey').
            `infoKeys[0].first' describes the type of function (0 for  vertex function,
            1 for edge function, 2 for face function and 3 for bubble function).
            `infoKeys[0].second' gives the order of the function associated with the
            key. Warning: this is an experimental feature and will probably change in a
            future release.

            Return `infoKeys'.

            Types:
            - `typeKeys': vector of integers
            - `entityKeys': vector of sizes
            - `elementType': integer
            - `functionSpaceType': string
            - `infoKeys': vector of pairs of integers
            """
            api_typeKeys_, api_typeKeys_n_ = _ivectorint(typeKeys)
            api_entityKeys_, api_entityKeys_n_ = _ivectorsize(entityKeys)
            api_infoKeys_, api_infoKeys_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetKeysInformation(
                api_typeKeys_, api_typeKeys_n_,
                api_entityKeys_, api_entityKeys_n_,
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                byref(api_infoKeys_), byref(api_infoKeys_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_infoKeys_, api_infoKeys_n_.value)
        get_keys_information = getKeysInformation

        @staticmethod
        def getBarycenters(elementType, tag, fast, primary, task=0, numTasks=1):
            """
            gmsh.model.mesh.getBarycenters(elementType, tag, fast, primary, task=0, numTasks=1)

            Get the barycenters of all elements of type `elementType' classified on the
            entity of tag `tag'. If `primary' is set, only the primary nodes of the
            elements are taken into account for the barycenter calculation. If `fast'
            is set, the function returns the sum of the primary node coordinates
            (without normalizing by the number of nodes). If `tag' < 0, get the
            barycenters for all entities. If `numTasks' > 1, only compute and return
            the part of the data indexed by `task' (for C++ only; output vector must be
            preallocated).

            Return `barycenters'.

            Types:
            - `elementType': integer
            - `tag': integer
            - `fast': boolean
            - `primary': boolean
            - `barycenters': vector of doubles
            - `task': size
            - `numTasks': size
            """
            api_barycenters_, api_barycenters_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetBarycenters(
                c_int(elementType),
                c_int(tag),
                c_int(bool(fast)),
                c_int(bool(primary)),
                byref(api_barycenters_), byref(api_barycenters_n_),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectordouble(api_barycenters_, api_barycenters_n_.value)
        get_barycenters = getBarycenters

        @staticmethod
        def getElementEdgeNodes(elementType, tag=-1, primary=False, task=0, numTasks=1):
            """
            gmsh.model.mesh.getElementEdgeNodes(elementType, tag=-1, primary=False, task=0, numTasks=1)

            Get the nodes on the edges of all elements of type `elementType' classified
            on the entity of tag `tag'. `nodeTags' contains the node tags of the edges
            for all the elements: [e1a1n1, e1a1n2, e1a2n1, ...]. Data is returned by
            element, with elements in the same order as in `getElements' and
            `getElementsByType'. If `primary' is set, only the primary (begin/end)
            nodes of the edges are returned. If `tag' < 0, get the edge nodes for all
            entities. If `numTasks' > 1, only compute and return the part of the data
            indexed by `task' (for C++ only; output vector must be preallocated).

            Return `nodeTags'.

            Types:
            - `elementType': integer
            - `nodeTags': vector of sizes
            - `tag': integer
            - `primary': boolean
            - `task': size
            - `numTasks': size
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementEdgeNodes(
                c_int(elementType),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                c_int(tag),
                c_int(bool(primary)),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorsize(api_nodeTags_, api_nodeTags_n_.value)
        get_element_edge_nodes = getElementEdgeNodes

        @staticmethod
        def getElementFaceNodes(elementType, faceType, tag=-1, primary=False, task=0, numTasks=1):
            """
            gmsh.model.mesh.getElementFaceNodes(elementType, faceType, tag=-1, primary=False, task=0, numTasks=1)

            Get the nodes on the faces of type `faceType' (3 for triangular faces, 4
            for quadrangular faces) of all elements of type `elementType' classified on
            the entity of tag `tag'. `nodeTags' contains the node tags of the faces for
            all elements: [e1f1n1, ..., e1f1nFaceType, e1f2n1, ...]. Data is returned
            by element, with elements in the same order as in `getElements' and
            `getElementsByType'. If `primary' is set, only the primary (corner) nodes
            of the faces are returned. If `tag' < 0, get the face nodes for all
            entities. If `numTasks' > 1, only compute and return the part of the data
            indexed by `task' (for C++ only; output vector must be preallocated).

            Return `nodeTags'.

            Types:
            - `elementType': integer
            - `faceType': integer
            - `nodeTags': vector of sizes
            - `tag': integer
            - `primary': boolean
            - `task': size
            - `numTasks': size
            """
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetElementFaceNodes(
                c_int(elementType),
                c_int(faceType),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                c_int(tag),
                c_int(bool(primary)),
                c_size_t(task),
                c_size_t(numTasks),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorsize(api_nodeTags_, api_nodeTags_n_.value)
        get_element_face_nodes = getElementFaceNodes

        @staticmethod
        def getGhostElements(dim, tag):
            """
            gmsh.model.mesh.getGhostElements(dim, tag)

            Get the ghost elements `elementTags' and their associated `partitions'
            stored in the ghost entity of dimension `dim' and tag `tag'.

            Return `elementTags', `partitions'.

            Types:
            - `dim': integer
            - `tag': integer
            - `elementTags': vector of sizes
            - `partitions': vector of integers
            """
            api_elementTags_, api_elementTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_partitions_, api_partitions_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetGhostElements(
                c_int(dim),
                c_int(tag),
                byref(api_elementTags_), byref(api_elementTags_n_),
                byref(api_partitions_), byref(api_partitions_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_elementTags_, api_elementTags_n_.value),
                _ovectorint(api_partitions_, api_partitions_n_.value))
        get_ghost_elements = getGhostElements

        @staticmethod
        def setSize(dimTags, size):
            """
            gmsh.model.mesh.setSize(dimTags, size)

            Set a mesh size constraint on the model entities `dimTags', given as a
            vector of (dim, tag) pairs. Currently only entities of dimension 0 (points)
            are handled.

            Types:
            - `dimTags': vector of pairs of integers
            - `size': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshSetSize(
                api_dimTags_, api_dimTags_n_,
                c_double(size),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_size = setSize

        @staticmethod
        def getSizes(dimTags):
            """
            gmsh.model.mesh.getSizes(dimTags)

            Get the mesh size constraints (if any) associated with the model entities
            `dimTags', given as a vector of (dim, tag) pairs. A zero entry in the
            output `sizes' vector indicates that no size constraint is specified on the
            corresponding entity.

            Return `sizes'.

            Types:
            - `dimTags': vector of pairs of integers
            - `sizes': vector of doubles
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_sizes_, api_sizes_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetSizes(
                api_dimTags_, api_dimTags_n_,
                byref(api_sizes_), byref(api_sizes_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectordouble(api_sizes_, api_sizes_n_.value)
        get_sizes = getSizes

        @staticmethod
        def setSizeAtParametricPoints(dim, tag, parametricCoord, sizes):
            """
            gmsh.model.mesh.setSizeAtParametricPoints(dim, tag, parametricCoord, sizes)

            Set mesh size constraints at the given parametric points `parametricCoord'
            on the model entity of dimension `dim' and tag `tag'. Currently only
            entities of dimension 1 (lines) are handled.

            Types:
            - `dim': integer
            - `tag': integer
            - `parametricCoord': vector of doubles
            - `sizes': vector of doubles
            """
            api_parametricCoord_, api_parametricCoord_n_ = _ivectordouble(parametricCoord)
            api_sizes_, api_sizes_n_ = _ivectordouble(sizes)
            ierr = c_int()
            lib.gmshModelMeshSetSizeAtParametricPoints(
                c_int(dim),
                c_int(tag),
                api_parametricCoord_, api_parametricCoord_n_,
                api_sizes_, api_sizes_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_size_at_parametric_points = setSizeAtParametricPoints

        @staticmethod
        def setSizeCallback(callback):
            """
            gmsh.model.mesh.setSizeCallback(callback)

            Set a mesh size callback for the current model. The callback function
            should take six arguments as input (`dim', `tag', `x', `y', `z' and `lc').
            The first two integer arguments correspond to the dimension `dim' and tag
            `tag' of the entity being meshed. The next four double precision arguments
            correspond to the coordinates `x', `y' and `z' around which to prescribe
            the mesh size and to the mesh size `lc' that would be prescribed if the
            callback had not been called. The callback function should return a double
            precision number specifying the desired mesh size; returning `lc' is
            equivalent to a no-op.

            Types:
            - `callback': 
            """
            global api_callback_type_
            api_callback_type_ = CFUNCTYPE(c_double, c_int, c_int, c_double, c_double, c_double, c_double, c_void_p)
            global api_callback_
            api_callback_ = api_callback_type_(lambda dim, tag, x, y, z, lc, _ : callback(dim, tag, x, y, z, lc))
            ierr = c_int()
            lib.gmshModelMeshSetSizeCallback(
                api_callback_, None,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_size_callback = setSizeCallback

        @staticmethod
        def removeSizeCallback():
            """
            gmsh.model.mesh.removeSizeCallback()

            Remove the mesh size callback from the current model.
            """
            ierr = c_int()
            lib.gmshModelMeshRemoveSizeCallback(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_size_callback = removeSizeCallback

        @staticmethod
        def setTransfiniteCurve(tag, numNodes, meshType="Progression", coef=1.):
            """
            gmsh.model.mesh.setTransfiniteCurve(tag, numNodes, meshType="Progression", coef=1.)

            Set a transfinite meshing constraint on the curve `tag', with `numNodes'
            nodes distributed according to `meshType' and `coef'. Currently supported
            types are "Progression" (geometrical progression with power `coef'), "Bump"
            (refinement toward both extremities of the curve) and "Beta" (beta law).

            Types:
            - `tag': integer
            - `numNodes': integer
            - `meshType': string
            - `coef': double
            """
            ierr = c_int()
            lib.gmshModelMeshSetTransfiniteCurve(
                c_int(tag),
                c_int(numNodes),
                c_char_p(meshType.encode()),
                c_double(coef),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_transfinite_curve = setTransfiniteCurve

        @staticmethod
        def setTransfiniteSurface(tag, arrangement="Left", cornerTags=[]):
            """
            gmsh.model.mesh.setTransfiniteSurface(tag, arrangement="Left", cornerTags=[])

            Set a transfinite meshing constraint on the surface `tag'. `arrangement'
            describes the arrangement of the triangles when the surface is not flagged
            as recombined: currently supported values are "Left", "Right",
            "AlternateLeft" and "AlternateRight". `cornerTags' can be used to specify
            the (3 or 4) corners of the transfinite interpolation explicitly;
            specifying the corners explicitly is mandatory if the surface has more that
            3 or 4 points on its boundary.

            Types:
            - `tag': integer
            - `arrangement': string
            - `cornerTags': vector of integers
            """
            api_cornerTags_, api_cornerTags_n_ = _ivectorint(cornerTags)
            ierr = c_int()
            lib.gmshModelMeshSetTransfiniteSurface(
                c_int(tag),
                c_char_p(arrangement.encode()),
                api_cornerTags_, api_cornerTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_transfinite_surface = setTransfiniteSurface

        @staticmethod
        def setTransfiniteVolume(tag, cornerTags=[]):
            """
            gmsh.model.mesh.setTransfiniteVolume(tag, cornerTags=[])

            Set a transfinite meshing constraint on the surface `tag'. `cornerTags' can
            be used to specify the (6 or 8) corners of the transfinite interpolation
            explicitly.

            Types:
            - `tag': integer
            - `cornerTags': vector of integers
            """
            api_cornerTags_, api_cornerTags_n_ = _ivectorint(cornerTags)
            ierr = c_int()
            lib.gmshModelMeshSetTransfiniteVolume(
                c_int(tag),
                api_cornerTags_, api_cornerTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_transfinite_volume = setTransfiniteVolume

        @staticmethod
        def setTransfiniteAutomatic(dimTags=[], cornerAngle=2.35, recombine=True):
            """
            gmsh.model.mesh.setTransfiniteAutomatic(dimTags=[], cornerAngle=2.35, recombine=True)

            Set transfinite meshing constraints on the model entities in `dimTags',
            given as a vector of (dim, tag) pairs. Transfinite meshing constraints are
            added to the curves of the quadrangular surfaces and to the faces of
            6-sided volumes. Quadragular faces with a corner angle superior to
            `cornerAngle' (in radians) are ignored. The number of points is
            automatically determined from the sizing constraints. If `dimTag' is empty,
            the constraints are applied to all entities in the model. If `recombine' is
            true, the recombine flag is automatically set on the transfinite surfaces.

            Types:
            - `dimTags': vector of pairs of integers
            - `cornerAngle': double
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshSetTransfiniteAutomatic(
                api_dimTags_, api_dimTags_n_,
                c_double(cornerAngle),
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_transfinite_automatic = setTransfiniteAutomatic

        @staticmethod
        def setRecombine(dim, tag, angle=45.):
            """
            gmsh.model.mesh.setRecombine(dim, tag, angle=45.)

            Set a recombination meshing constraint on the model entity of dimension
            `dim' and tag `tag'. Currently only entities of dimension 2 (to recombine
            triangles into quadrangles) are supported; `angle' specifies the threshold
            angle for the simple recombination algorithm..

            Types:
            - `dim': integer
            - `tag': integer
            - `angle': double
            """
            ierr = c_int()
            lib.gmshModelMeshSetRecombine(
                c_int(dim),
                c_int(tag),
                c_double(angle),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_recombine = setRecombine

        @staticmethod
        def setSmoothing(dim, tag, val):
            """
            gmsh.model.mesh.setSmoothing(dim, tag, val)

            Set a smoothing meshing constraint on the model entity of dimension `dim'
            and tag `tag'. `val' iterations of a Laplace smoother are applied.

            Types:
            - `dim': integer
            - `tag': integer
            - `val': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSetSmoothing(
                c_int(dim),
                c_int(tag),
                c_int(val),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_smoothing = setSmoothing

        @staticmethod
        def setReverse(dim, tag, val=True):
            """
            gmsh.model.mesh.setReverse(dim, tag, val=True)

            Set a reverse meshing constraint on the model entity of dimension `dim' and
            tag `tag'. If `val' is true, the mesh orientation will be reversed with
            respect to the natural mesh orientation (i.e. the orientation consistent
            with the orientation of the geometry). If `val' is false, the mesh is left
            as-is.

            Types:
            - `dim': integer
            - `tag': integer
            - `val': boolean
            """
            ierr = c_int()
            lib.gmshModelMeshSetReverse(
                c_int(dim),
                c_int(tag),
                c_int(bool(val)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_reverse = setReverse

        @staticmethod
        def setAlgorithm(dim, tag, val):
            """
            gmsh.model.mesh.setAlgorithm(dim, tag, val)

            Set the meshing algorithm on the model entity of dimension `dim' and tag
            `tag'. Supported values are those of the `Mesh.Algorithm' option, as listed
            in the Gmsh reference manual. Currently only supported for `dim' == 2.

            Types:
            - `dim': integer
            - `tag': integer
            - `val': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSetAlgorithm(
                c_int(dim),
                c_int(tag),
                c_int(val),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_algorithm = setAlgorithm

        @staticmethod
        def setSizeFromBoundary(dim, tag, val):
            """
            gmsh.model.mesh.setSizeFromBoundary(dim, tag, val)

            Force the mesh size to be extended from the boundary, or not, for the model
            entity of dimension `dim' and tag `tag'. Currently only supported for `dim'
            == 2.

            Types:
            - `dim': integer
            - `tag': integer
            - `val': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSetSizeFromBoundary(
                c_int(dim),
                c_int(tag),
                c_int(val),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_size_from_boundary = setSizeFromBoundary

        @staticmethod
        def setCompound(dim, tags):
            """
            gmsh.model.mesh.setCompound(dim, tags)

            Set a compound meshing constraint on the model entities of dimension `dim'
            and tags `tags'. During meshing, compound entities are treated as a single
            discrete entity, which is automatically reparametrized.

            Types:
            - `dim': integer
            - `tags': vector of integers
            """
            api_tags_, api_tags_n_ = _ivectorint(tags)
            ierr = c_int()
            lib.gmshModelMeshSetCompound(
                c_int(dim),
                api_tags_, api_tags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_compound = setCompound

        @staticmethod
        def setOutwardOrientation(tag):
            """
            gmsh.model.mesh.setOutwardOrientation(tag)

            Set meshing constraints on the bounding surfaces of the volume of tag `tag'
            so that all surfaces are oriented with outward pointing normals; and if a
            mesh already exists, reorient it. Currently only available with the
            OpenCASCADE kernel, as it relies on the STL triangulation.

            Types:
            - `tag': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSetOutwardOrientation(
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_outward_orientation = setOutwardOrientation

        @staticmethod
        def removeConstraints(dimTags=[]):
            """
            gmsh.model.mesh.removeConstraints(dimTags=[])

            Remove all meshing constraints from the model entities `dimTags', given as
            a vector of (dim, tag) pairs. If `dimTags' is empty, remove all
            constraings.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshRemoveConstraints(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_constraints = removeConstraints

        @staticmethod
        def embed(dim, tags, inDim, inTag):
            """
            gmsh.model.mesh.embed(dim, tags, inDim, inTag)

            Embed the model entities of dimension `dim' and tags `tags' in the
            (`inDim', `inTag') model entity. The dimension `dim' can 0, 1 or 2 and must
            be strictly smaller than `inDim', which must be either 2 or 3. The embedded
            entities should not intersect each other or be part of the boundary of the
            entity `inTag', whose mesh will conform to the mesh of the embedded
            entities. With the OpenCASCADE kernel, if the `fragment' operation is
            applied to entities of different dimensions, the lower dimensional entities
            will be automatically embedded in the higher dimensional entities if they
            are not on their boundary.

            Types:
            - `dim': integer
            - `tags': vector of integers
            - `inDim': integer
            - `inTag': integer
            """
            api_tags_, api_tags_n_ = _ivectorint(tags)
            ierr = c_int()
            lib.gmshModelMeshEmbed(
                c_int(dim),
                api_tags_, api_tags_n_,
                c_int(inDim),
                c_int(inTag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def removeEmbedded(dimTags, dim=-1):
            """
            gmsh.model.mesh.removeEmbedded(dimTags, dim=-1)

            Remove embedded entities from the model entities `dimTags', given as a
            vector of (dim, tag) pairs. if `dim' is >= 0, only remove embedded entities
            of the given dimension (e.g. embedded points if `dim' == 0).

            Types:
            - `dimTags': vector of pairs of integers
            - `dim': integer
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshRemoveEmbedded(
                api_dimTags_, api_dimTags_n_,
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_embedded = removeEmbedded

        @staticmethod
        def getEmbedded(dim, tag):
            """
            gmsh.model.mesh.getEmbedded(dim, tag)

            Get the entities (if any) embedded in the model entity of dimension `dim'
            and tag `tag'.

            Return `dimTags'.

            Types:
            - `dim': integer
            - `tag': integer
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetEmbedded(
                c_int(dim),
                c_int(tag),
                byref(api_dimTags_), byref(api_dimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
        get_embedded = getEmbedded

        @staticmethod
        def reorderElements(elementType, tag, ordering):
            """
            gmsh.model.mesh.reorderElements(elementType, tag, ordering)

            Reorder the elements of type `elementType' classified on the entity of tag
            `tag' according to the `ordering' vector.

            Types:
            - `elementType': integer
            - `tag': integer
            - `ordering': vector of sizes
            """
            api_ordering_, api_ordering_n_ = _ivectorsize(ordering)
            ierr = c_int()
            lib.gmshModelMeshReorderElements(
                c_int(elementType),
                c_int(tag),
                api_ordering_, api_ordering_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        reorder_elements = reorderElements

        @staticmethod
        def computeRenumbering(method="RCMK", elementTags=[]):
            """
            gmsh.model.mesh.computeRenumbering(method="RCMK", elementTags=[])

            Compute a renumbering vector `newTags' corresponding to the input tags
            `oldTags' for a given list of element tags `elementTags'. If `elementTags'
            is empty, compute the renumbering on the full mesh. If `method' is equal to
            "RCMK", compute a node renumering with Reverse Cuthill McKee. If `method'
            is equal to "Hilbert", compute a node renumering along a Hilbert curve. If
            `method' is equal to "Metis", compute a node renumering using Metis.
            Element renumbering is not available yet.

            Return `oldTags', `newTags'.

            Types:
            - `oldTags': vector of sizes
            - `newTags': vector of sizes
            - `method': string
            - `elementTags': vector of sizes
            """
            api_oldTags_, api_oldTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_newTags_, api_newTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            ierr = c_int()
            lib.gmshModelMeshComputeRenumbering(
                byref(api_oldTags_), byref(api_oldTags_n_),
                byref(api_newTags_), byref(api_newTags_n_),
                c_char_p(method.encode()),
                api_elementTags_, api_elementTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorsize(api_oldTags_, api_oldTags_n_.value),
                _ovectorsize(api_newTags_, api_newTags_n_.value))
        compute_renumbering = computeRenumbering

        @staticmethod
        def renumberNodes(oldTags=[], newTags=[]):
            """
            gmsh.model.mesh.renumberNodes(oldTags=[], newTags=[])

            Renumber the node tags. If no explicit renumbering is provided through the
            `oldTags' and `newTags' vectors, renumber the nodes in a continuous
            sequence, taking into account the subset of elements to be saved later on
            if the option "Mesh.SaveAll" is not set.

            Types:
            - `oldTags': vector of sizes
            - `newTags': vector of sizes
            """
            api_oldTags_, api_oldTags_n_ = _ivectorsize(oldTags)
            api_newTags_, api_newTags_n_ = _ivectorsize(newTags)
            ierr = c_int()
            lib.gmshModelMeshRenumberNodes(
                api_oldTags_, api_oldTags_n_,
                api_newTags_, api_newTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        renumber_nodes = renumberNodes

        @staticmethod
        def renumberElements(oldTags=[], newTags=[]):
            """
            gmsh.model.mesh.renumberElements(oldTags=[], newTags=[])

            Renumber the element tags in a continuous sequence. If no explicit
            renumbering is provided through the `oldTags' and `newTags' vectors,
            renumber the elements in a continuous sequence, taking into account the
            subset of elements to be saved later on if the option "Mesh.SaveAll" is not
            set.

            Types:
            - `oldTags': vector of sizes
            - `newTags': vector of sizes
            """
            api_oldTags_, api_oldTags_n_ = _ivectorsize(oldTags)
            api_newTags_, api_newTags_n_ = _ivectorsize(newTags)
            ierr = c_int()
            lib.gmshModelMeshRenumberElements(
                api_oldTags_, api_oldTags_n_,
                api_newTags_, api_newTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        renumber_elements = renumberElements

        @staticmethod
        def setPeriodic(dim, tags, tagsMaster, affineTransform):
            """
            gmsh.model.mesh.setPeriodic(dim, tags, tagsMaster, affineTransform)

            Set the meshes of the entities of dimension `dim' and tag `tags' as
            periodic copies of the meshes of entities `tagsMaster', using the affine
            transformation specified in `affineTransformation' (16 entries of a 4x4
            matrix, by row). If used after meshing, generate the periodic node
            correspondence information assuming the meshes of entities `tags'
            effectively match the meshes of entities `tagsMaster' (useful for
            structured and extruded meshes). Currently only available for @code{dim} ==
            1 and @code{dim} == 2.

            Types:
            - `dim': integer
            - `tags': vector of integers
            - `tagsMaster': vector of integers
            - `affineTransform': vector of doubles
            """
            api_tags_, api_tags_n_ = _ivectorint(tags)
            api_tagsMaster_, api_tagsMaster_n_ = _ivectorint(tagsMaster)
            api_affineTransform_, api_affineTransform_n_ = _ivectordouble(affineTransform)
            ierr = c_int()
            lib.gmshModelMeshSetPeriodic(
                c_int(dim),
                api_tags_, api_tags_n_,
                api_tagsMaster_, api_tagsMaster_n_,
                api_affineTransform_, api_affineTransform_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_periodic = setPeriodic

        @staticmethod
        def getPeriodic(dim, tags):
            """
            gmsh.model.mesh.getPeriodic(dim, tags)

            Get master entities `tagsMaster' for the entities of dimension `dim' and
            tags `tags'.

            Return `tagMaster'.

            Types:
            - `dim': integer
            - `tags': vector of integers
            - `tagMaster': vector of integers
            """
            api_tags_, api_tags_n_ = _ivectorint(tags)
            api_tagMaster_, api_tagMaster_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetPeriodic(
                c_int(dim),
                api_tags_, api_tags_n_,
                byref(api_tagMaster_), byref(api_tagMaster_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_tagMaster_, api_tagMaster_n_.value)
        get_periodic = getPeriodic

        @staticmethod
        def getPeriodicNodes(dim, tag, includeHighOrderNodes=False):
            """
            gmsh.model.mesh.getPeriodicNodes(dim, tag, includeHighOrderNodes=False)

            Get the master entity `tagMaster', the node tags `nodeTags' and their
            corresponding master node tags `nodeTagsMaster', and the affine transform
            `affineTransform' for the entity of dimension `dim' and tag `tag'. If
            `includeHighOrderNodes' is set, include high-order nodes in the returned
            data.

            Return `tagMaster', `nodeTags', `nodeTagsMaster', `affineTransform'.

            Types:
            - `dim': integer
            - `tag': integer
            - `tagMaster': integer
            - `nodeTags': vector of sizes
            - `nodeTagsMaster': vector of sizes
            - `affineTransform': vector of doubles
            - `includeHighOrderNodes': boolean
            """
            api_tagMaster_ = c_int()
            api_nodeTags_, api_nodeTags_n_ = POINTER(c_size_t)(), c_size_t()
            api_nodeTagsMaster_, api_nodeTagsMaster_n_ = POINTER(c_size_t)(), c_size_t()
            api_affineTransform_, api_affineTransform_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetPeriodicNodes(
                c_int(dim),
                c_int(tag),
                byref(api_tagMaster_),
                byref(api_nodeTags_), byref(api_nodeTags_n_),
                byref(api_nodeTagsMaster_), byref(api_nodeTagsMaster_n_),
                byref(api_affineTransform_), byref(api_affineTransform_n_),
                c_int(bool(includeHighOrderNodes)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_tagMaster_.value,
                _ovectorsize(api_nodeTags_, api_nodeTags_n_.value),
                _ovectorsize(api_nodeTagsMaster_, api_nodeTagsMaster_n_.value),
                _ovectordouble(api_affineTransform_, api_affineTransform_n_.value))
        get_periodic_nodes = getPeriodicNodes

        @staticmethod
        def getPeriodicKeys(elementType, functionSpaceType, tag, returnCoord=True):
            """
            gmsh.model.mesh.getPeriodicKeys(elementType, functionSpaceType, tag, returnCoord=True)

            Get the master entity `tagMaster' and the key pairs (`typeKeyMaster',
            `entityKeyMaster') corresponding to the entity `tag' and the key pairs
            (`typeKey', `entityKey') for the elements of type `elementType' and
            function space type `functionSpaceType'. If `returnCoord' is set, the
            `coord' and `coordMaster' vectors contain the x, y, z coordinates locating
            basis functions for sorting purposes.

            Return `tagMaster', `typeKeys', `typeKeysMaster', `entityKeys', `entityKeysMaster', `coord', `coordMaster'.

            Types:
            - `elementType': integer
            - `functionSpaceType': string
            - `tag': integer
            - `tagMaster': integer
            - `typeKeys': vector of integers
            - `typeKeysMaster': vector of integers
            - `entityKeys': vector of sizes
            - `entityKeysMaster': vector of sizes
            - `coord': vector of doubles
            - `coordMaster': vector of doubles
            - `returnCoord': boolean
            """
            api_tagMaster_ = c_int()
            api_typeKeys_, api_typeKeys_n_ = POINTER(c_int)(), c_size_t()
            api_typeKeysMaster_, api_typeKeysMaster_n_ = POINTER(c_int)(), c_size_t()
            api_entityKeys_, api_entityKeys_n_ = POINTER(c_size_t)(), c_size_t()
            api_entityKeysMaster_, api_entityKeysMaster_n_ = POINTER(c_size_t)(), c_size_t()
            api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
            api_coordMaster_, api_coordMaster_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetPeriodicKeys(
                c_int(elementType),
                c_char_p(functionSpaceType.encode()),
                c_int(tag),
                byref(api_tagMaster_),
                byref(api_typeKeys_), byref(api_typeKeys_n_),
                byref(api_typeKeysMaster_), byref(api_typeKeysMaster_n_),
                byref(api_entityKeys_), byref(api_entityKeys_n_),
                byref(api_entityKeysMaster_), byref(api_entityKeysMaster_n_),
                byref(api_coord_), byref(api_coord_n_),
                byref(api_coordMaster_), byref(api_coordMaster_n_),
                c_int(bool(returnCoord)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_tagMaster_.value,
                _ovectorint(api_typeKeys_, api_typeKeys_n_.value),
                _ovectorint(api_typeKeysMaster_, api_typeKeysMaster_n_.value),
                _ovectorsize(api_entityKeys_, api_entityKeys_n_.value),
                _ovectorsize(api_entityKeysMaster_, api_entityKeysMaster_n_.value),
                _ovectordouble(api_coord_, api_coord_n_.value),
                _ovectordouble(api_coordMaster_, api_coordMaster_n_.value))
        get_periodic_keys = getPeriodicKeys

        @staticmethod
        def importStl():
            """
            gmsh.model.mesh.importStl()

            Import the model STL representation (if available) as the current mesh.
            """
            ierr = c_int()
            lib.gmshModelMeshImportStl(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        import_stl = importStl

        @staticmethod
        def getDuplicateNodes(dimTags=[]):
            """
            gmsh.model.mesh.getDuplicateNodes(dimTags=[])

            Get the `tags' of any duplicate nodes in the mesh of the entities
            `dimTags', given as a vector of (dim, tag) pairs. If `dimTags' is empty,
            consider the whole mesh.

            Return `tags'.

            Types:
            - `tags': vector of sizes
            - `dimTags': vector of pairs of integers
            """
            api_tags_, api_tags_n_ = POINTER(c_size_t)(), c_size_t()
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshGetDuplicateNodes(
                byref(api_tags_), byref(api_tags_n_),
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorsize(api_tags_, api_tags_n_.value)
        get_duplicate_nodes = getDuplicateNodes

        @staticmethod
        def removeDuplicateNodes(dimTags=[]):
            """
            gmsh.model.mesh.removeDuplicateNodes(dimTags=[])

            Remove duplicate nodes in the mesh of the entities `dimTags', given as a
            vector of (dim, tag) pairs. If `dimTags' is empty, consider the whole mesh.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshRemoveDuplicateNodes(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_duplicate_nodes = removeDuplicateNodes

        @staticmethod
        def removeDuplicateElements(dimTags=[]):
            """
            gmsh.model.mesh.removeDuplicateElements(dimTags=[])

            Remove duplicate elements (defined by the same nodes, in the same entity)
            in the mesh of the entities `dimTags', given as a vector of (dim, tag)
            pairs. If `dimTags' is empty, consider the whole mesh.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshRemoveDuplicateElements(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_duplicate_elements = removeDuplicateElements

        @staticmethod
        def splitQuadrangles(quality=1., tag=-1):
            """
            gmsh.model.mesh.splitQuadrangles(quality=1., tag=-1)

            Split (into two triangles) all quadrangles in surface `tag' whose quality
            is lower than `quality'. If `tag' < 0, split quadrangles in all surfaces.

            Types:
            - `quality': double
            - `tag': integer
            """
            ierr = c_int()
            lib.gmshModelMeshSplitQuadrangles(
                c_double(quality),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        split_quadrangles = splitQuadrangles

        @staticmethod
        def setVisibility(elementTags, value):
            """
            gmsh.model.mesh.setVisibility(elementTags, value)

            Set the visibility of the elements of tags `elementTags' to `value'.

            Types:
            - `elementTags': vector of sizes
            - `value': integer
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            ierr = c_int()
            lib.gmshModelMeshSetVisibility(
                api_elementTags_, api_elementTags_n_,
                c_int(value),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_visibility = setVisibility

        @staticmethod
        def getVisibility(elementTags):
            """
            gmsh.model.mesh.getVisibility(elementTags)

            Get the visibility of the elements of tags `elementTags'.

            Return `values'.

            Types:
            - `elementTags': vector of sizes
            - `values': vector of integers
            """
            api_elementTags_, api_elementTags_n_ = _ivectorsize(elementTags)
            api_values_, api_values_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshGetVisibility(
                api_elementTags_, api_elementTags_n_,
                byref(api_values_), byref(api_values_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_values_, api_values_n_.value)
        get_visibility = getVisibility

        @staticmethod
        def classifySurfaces(angle, boundary=True, forReparametrization=False, curveAngle=pi, exportDiscrete=True):
            """
            gmsh.model.mesh.classifySurfaces(angle, boundary=True, forReparametrization=False, curveAngle=pi, exportDiscrete=True)

            Classify ("color") the surface mesh based on the angle threshold `angle'
            (in radians), and create new discrete surfaces, curves and points
            accordingly. If `boundary' is set, also create discrete curves on the
            boundary if the surface is open. If `forReparametrization' is set, create
            curves and surfaces that can be reparametrized using a single map. If
            `curveAngle' is less than Pi, also force curves to be split according to
            `curveAngle'. If `exportDiscrete' is set, clear any built-in CAD kernel
            entities and export the discrete entities in the built-in CAD kernel.

            Types:
            - `angle': double
            - `boundary': boolean
            - `forReparametrization': boolean
            - `curveAngle': double
            - `exportDiscrete': boolean
            """
            ierr = c_int()
            lib.gmshModelMeshClassifySurfaces(
                c_double(angle),
                c_int(bool(boundary)),
                c_int(bool(forReparametrization)),
                c_double(curveAngle),
                c_int(bool(exportDiscrete)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        classify_surfaces = classifySurfaces

        @staticmethod
        def createGeometry(dimTags=[]):
            """
            gmsh.model.mesh.createGeometry(dimTags=[])

            Create a geometry for the discrete entities `dimTags' (given as a vector of
            (dim, tag) pairs) represented solely by a mesh (without an underlying CAD
            description), i.e. create a parametrization for discrete curves and
            surfaces, assuming that each can be parametrized with a single map. If
            `dimTags' is empty, create a geometry for all the discrete entities.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelMeshCreateGeometry(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        create_geometry = createGeometry

        @staticmethod
        def createTopology(makeSimplyConnected=True, exportDiscrete=True):
            """
            gmsh.model.mesh.createTopology(makeSimplyConnected=True, exportDiscrete=True)

            Create a boundary representation from the mesh if the model does not have
            one (e.g. when imported from mesh file formats with no BRep representation
            of the underlying model). If `makeSimplyConnected' is set, enforce simply
            connected discrete surfaces and volumes. If `exportDiscrete' is set, clear
            any built-in CAD kernel entities and export the discrete entities in the
            built-in CAD kernel.

            Types:
            - `makeSimplyConnected': boolean
            - `exportDiscrete': boolean
            """
            ierr = c_int()
            lib.gmshModelMeshCreateTopology(
                c_int(bool(makeSimplyConnected)),
                c_int(bool(exportDiscrete)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        create_topology = createTopology

        @staticmethod
        def addHomologyRequest(type="Homology", domainTags=[], subdomainTags=[], dims=[]):
            """
            gmsh.model.mesh.addHomologyRequest(type="Homology", domainTags=[], subdomainTags=[], dims=[])

            Add a request to compute a basis representation for homology spaces (if
            `type' == "Homology") or cohomology spaces (if `type' == "Cohomology"). The
            computation domain is given in a list of physical group tags `domainTags';
            if empty, the whole mesh is the domain. The computation subdomain for
            relative (co)homology computation is given in a list of physical group tags
            `subdomainTags'; if empty, absolute (co)homology is computed. The
            dimensions of the (co)homology bases to be computed are given in the list
            `dim'; if empty, all bases are computed. Resulting basis representation
            (co)chains are stored as physical groups in the mesh. If the request is
            added before mesh generation, the computation will be performed at the end
            of the meshing pipeline.

            Types:
            - `type': string
            - `domainTags': vector of integers
            - `subdomainTags': vector of integers
            - `dims': vector of integers
            """
            api_domainTags_, api_domainTags_n_ = _ivectorint(domainTags)
            api_subdomainTags_, api_subdomainTags_n_ = _ivectorint(subdomainTags)
            api_dims_, api_dims_n_ = _ivectorint(dims)
            ierr = c_int()
            lib.gmshModelMeshAddHomologyRequest(
                c_char_p(type.encode()),
                api_domainTags_, api_domainTags_n_,
                api_subdomainTags_, api_subdomainTags_n_,
                api_dims_, api_dims_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        add_homology_request = addHomologyRequest

        @staticmethod
        def clearHomologyRequests():
            """
            gmsh.model.mesh.clearHomologyRequests()

            Clear all (co)homology computation requests.
            """
            ierr = c_int()
            lib.gmshModelMeshClearHomologyRequests(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        clear_homology_requests = clearHomologyRequests

        @staticmethod
        def computeHomology():
            """
            gmsh.model.mesh.computeHomology()

            Perform the (co)homology computations requested by addHomologyRequest().
            The newly created physical groups are returned in `dimTags' as a vector of
            (dim, tag) pairs.

            Return `dimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshComputeHomology(
                byref(api_dimTags_), byref(api_dimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
        compute_homology = computeHomology

        @staticmethod
        def computeCrossField():
            """
            gmsh.model.mesh.computeCrossField()

            Compute a cross field for the current mesh. The function creates 3 views:
            the H function, the Theta function and cross directions. Return the tags of
            the views.

            Return `viewTags'.

            Types:
            - `viewTags': vector of integers
            """
            api_viewTags_, api_viewTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelMeshComputeCrossField(
                byref(api_viewTags_), byref(api_viewTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_viewTags_, api_viewTags_n_.value)
        compute_cross_field = computeCrossField


        class field:
            """
            Mesh size field functions
            """

            @staticmethod
            def add(fieldType, tag=-1):
                """
                gmsh.model.mesh.field.add(fieldType, tag=-1)

                Add a new mesh size field of type `fieldType'. If `tag' is positive, assign
                the tag explicitly; otherwise a new tag is assigned automatically. Return
                the field tag. Available field types are listed in the "Gmsh mesh size
                fields" chapter of the Gmsh reference manual
                (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-mesh-size-fields).

                Return an integer.

                Types:
                - `fieldType': string
                - `tag': integer
                """
                ierr = c_int()
                api_result_ = lib.gmshModelMeshFieldAdd(
                    c_char_p(fieldType.encode()),
                    c_int(tag),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return api_result_

            @staticmethod
            def remove(tag):
                """
                gmsh.model.mesh.field.remove(tag)

                Remove the field with tag `tag'.

                Types:
                - `tag': integer
                """
                ierr = c_int()
                lib.gmshModelMeshFieldRemove(
                    c_int(tag),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())

            @staticmethod
            def list():
                """
                gmsh.model.mesh.field.list()

                Get the list of all fields.

                Return `tags'.

                Types:
                - `tags': vector of integers
                """
                api_tags_, api_tags_n_ = POINTER(c_int)(), c_size_t()
                ierr = c_int()
                lib.gmshModelMeshFieldList(
                    byref(api_tags_), byref(api_tags_n_),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return _ovectorint(api_tags_, api_tags_n_.value)

            @staticmethod
            def getType(tag):
                """
                gmsh.model.mesh.field.getType(tag)

                Get the type `fieldType' of the field with tag `tag'.

                Return `fileType'.

                Types:
                - `tag': integer
                - `fileType': string
                """
                api_fileType_ = c_char_p()
                ierr = c_int()
                lib.gmshModelMeshFieldGetType(
                    c_int(tag),
                    byref(api_fileType_),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return _ostring(api_fileType_)
            get_type = getType

            @staticmethod
            def setNumber(tag, option, value):
                """
                gmsh.model.mesh.field.setNumber(tag, option, value)

                Set the numerical option `option' to value `value' for field `tag'.

                Types:
                - `tag': integer
                - `option': string
                - `value': double
                """
                ierr = c_int()
                lib.gmshModelMeshFieldSetNumber(
                    c_int(tag),
                    c_char_p(option.encode()),
                    c_double(value),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_number = setNumber

            @staticmethod
            def getNumber(tag, option):
                """
                gmsh.model.mesh.field.getNumber(tag, option)

                Get the value of the numerical option `option' for field `tag'.

                Return `value'.

                Types:
                - `tag': integer
                - `option': string
                - `value': double
                """
                api_value_ = c_double()
                ierr = c_int()
                lib.gmshModelMeshFieldGetNumber(
                    c_int(tag),
                    c_char_p(option.encode()),
                    byref(api_value_),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return api_value_.value
            get_number = getNumber

            @staticmethod
            def setString(tag, option, value):
                """
                gmsh.model.mesh.field.setString(tag, option, value)

                Set the string option `option' to value `value' for field `tag'.

                Types:
                - `tag': integer
                - `option': string
                - `value': string
                """
                ierr = c_int()
                lib.gmshModelMeshFieldSetString(
                    c_int(tag),
                    c_char_p(option.encode()),
                    c_char_p(value.encode()),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_string = setString

            @staticmethod
            def getString(tag, option):
                """
                gmsh.model.mesh.field.getString(tag, option)

                Get the value of the string option `option' for field `tag'.

                Return `value'.

                Types:
                - `tag': integer
                - `option': string
                - `value': string
                """
                api_value_ = c_char_p()
                ierr = c_int()
                lib.gmshModelMeshFieldGetString(
                    c_int(tag),
                    c_char_p(option.encode()),
                    byref(api_value_),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return _ostring(api_value_)
            get_string = getString

            @staticmethod
            def setNumbers(tag, option, values):
                """
                gmsh.model.mesh.field.setNumbers(tag, option, values)

                Set the numerical list option `option' to value `values' for field `tag'.

                Types:
                - `tag': integer
                - `option': string
                - `values': vector of doubles
                """
                api_values_, api_values_n_ = _ivectordouble(values)
                ierr = c_int()
                lib.gmshModelMeshFieldSetNumbers(
                    c_int(tag),
                    c_char_p(option.encode()),
                    api_values_, api_values_n_,
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_numbers = setNumbers

            @staticmethod
            def getNumbers(tag, option):
                """
                gmsh.model.mesh.field.getNumbers(tag, option)

                Get the value of the numerical list option `option' for field `tag'.

                Return `values'.

                Types:
                - `tag': integer
                - `option': string
                - `values': vector of doubles
                """
                api_values_, api_values_n_ = POINTER(c_double)(), c_size_t()
                ierr = c_int()
                lib.gmshModelMeshFieldGetNumbers(
                    c_int(tag),
                    c_char_p(option.encode()),
                    byref(api_values_), byref(api_values_n_),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
                return _ovectordouble(api_values_, api_values_n_.value)
            get_numbers = getNumbers

            @staticmethod
            def setAsBackgroundMesh(tag):
                """
                gmsh.model.mesh.field.setAsBackgroundMesh(tag)

                Set the field `tag' as the background mesh size field.

                Types:
                - `tag': integer
                """
                ierr = c_int()
                lib.gmshModelMeshFieldSetAsBackgroundMesh(
                    c_int(tag),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_as_background_mesh = setAsBackgroundMesh

            @staticmethod
            def setAsBoundaryLayer(tag):
                """
                gmsh.model.mesh.field.setAsBoundaryLayer(tag)

                Set the field `tag' as a boundary layer size field.

                Types:
                - `tag': integer
                """
                ierr = c_int()
                lib.gmshModelMeshFieldSetAsBoundaryLayer(
                    c_int(tag),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_as_boundary_layer = setAsBoundaryLayer


    class geo:
        """
        Built-in CAD kernel functions
        """

        @staticmethod
        def addPoint(x, y, z, meshSize=0., tag=-1):
            """
            gmsh.model.geo.addPoint(x, y, z, meshSize=0., tag=-1)

            Add a geometrical point in the built-in CAD representation, at coordinates
            (`x', `y', `z'). If `meshSize' is > 0, add a meshing constraint at that
            point. If `tag' is positive, set the tag explicitly; otherwise a new tag is
            selected automatically. Return the tag of the point. (Note that the point
            will be added in the current model only after `synchronize' is called. This
            behavior holds for all the entities added in the geo module.)

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `meshSize': double
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddPoint(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(meshSize),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_point = addPoint

        @staticmethod
        def addLine(startTag, endTag, tag=-1):
            """
            gmsh.model.geo.addLine(startTag, endTag, tag=-1)

            Add a straight line segment in the built-in CAD representation, between the
            two points with tags `startTag' and `endTag'. If `tag' is positive, set the
            tag explicitly; otherwise a new tag is selected automatically. Return the
            tag of the line.

            Return an integer.

            Types:
            - `startTag': integer
            - `endTag': integer
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddLine(
                c_int(startTag),
                c_int(endTag),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_line = addLine

        @staticmethod
        def addCircleArc(startTag, centerTag, endTag, tag=-1, nx=0., ny=0., nz=0.):
            """
            gmsh.model.geo.addCircleArc(startTag, centerTag, endTag, tag=-1, nx=0., ny=0., nz=0.)

            Add a circle arc (strictly smaller than Pi) in the built-in CAD
            representation, between the two points with tags `startTag' and `endTag',
            and with center `centerTag'. If `tag' is positive, set the tag explicitly;
            otherwise a new tag is selected automatically. If (`nx', `ny', `nz') != (0,
            0, 0), explicitly set the plane of the circle arc. Return the tag of the
            circle arc.

            Return an integer.

            Types:
            - `startTag': integer
            - `centerTag': integer
            - `endTag': integer
            - `tag': integer
            - `nx': double
            - `ny': double
            - `nz': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddCircleArc(
                c_int(startTag),
                c_int(centerTag),
                c_int(endTag),
                c_int(tag),
                c_double(nx),
                c_double(ny),
                c_double(nz),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_circle_arc = addCircleArc

        @staticmethod
        def addEllipseArc(startTag, centerTag, majorTag, endTag, tag=-1, nx=0., ny=0., nz=0.):
            """
            gmsh.model.geo.addEllipseArc(startTag, centerTag, majorTag, endTag, tag=-1, nx=0., ny=0., nz=0.)

            Add an ellipse arc (strictly smaller than Pi) in the built-in CAD
            representation, between the two points `startTag' and `endTag', and with
            center `centerTag' and major axis point `majorTag'. If `tag' is positive,
            set the tag explicitly; otherwise a new tag is selected automatically. If
            (`nx', `ny', `nz') != (0, 0, 0), explicitly set the plane of the circle
            arc. Return the tag of the ellipse arc.

            Return an integer.

            Types:
            - `startTag': integer
            - `centerTag': integer
            - `majorTag': integer
            - `endTag': integer
            - `tag': integer
            - `nx': double
            - `ny': double
            - `nz': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddEllipseArc(
                c_int(startTag),
                c_int(centerTag),
                c_int(majorTag),
                c_int(endTag),
                c_int(tag),
                c_double(nx),
                c_double(ny),
                c_double(nz),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_ellipse_arc = addEllipseArc

        @staticmethod
        def addSpline(pointTags, tag=-1):
            """
            gmsh.model.geo.addSpline(pointTags, tag=-1)

            Add a spline (Catmull-Rom) curve in the built-in CAD representation, going
            through the points `pointTags'. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. Create a
            periodic curve if the first and last points are the same. Return the tag of
            the spline curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddSpline(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_spline = addSpline

        @staticmethod
        def addBSpline(pointTags, tag=-1):
            """
            gmsh.model.geo.addBSpline(pointTags, tag=-1)

            Add a cubic b-spline curve in the built-in CAD representation, with
            `pointTags' control points. If `tag' is positive, set the tag explicitly;
            otherwise a new tag is selected automatically. Creates a periodic curve if
            the first and last points are the same. Return the tag of the b-spline
            curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddBSpline(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bspline = addBSpline

        @staticmethod
        def addBezier(pointTags, tag=-1):
            """
            gmsh.model.geo.addBezier(pointTags, tag=-1)

            Add a Bezier curve in the built-in CAD representation, with `pointTags'
            control points. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically.  Return the tag of the Bezier curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddBezier(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bezier = addBezier

        @staticmethod
        def addPolyline(pointTags, tag=-1):
            """
            gmsh.model.geo.addPolyline(pointTags, tag=-1)

            Add a polyline curve in the built-in CAD representation, going through the
            points `pointTags'. If `tag' is positive, set the tag explicitly; otherwise
            a new tag is selected automatically. Create a periodic curve if the first
            and last points are the same. Return the tag of the polyline curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddPolyline(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_polyline = addPolyline

        @staticmethod
        def addCompoundSpline(curveTags, numIntervals=5, tag=-1):
            """
            gmsh.model.geo.addCompoundSpline(curveTags, numIntervals=5, tag=-1)

            Add a spline (Catmull-Rom) curve in the built-in CAD representation, going
            through points sampling the curves in `curveTags'. The density of sampling
            points on each curve is governed by `numIntervals'. If `tag' is positive,
            set the tag explicitly; otherwise a new tag is selected automatically.
            Return the tag of the spline.

            Return an integer.

            Types:
            - `curveTags': vector of integers
            - `numIntervals': integer
            - `tag': integer
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddCompoundSpline(
                api_curveTags_, api_curveTags_n_,
                c_int(numIntervals),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_compound_spline = addCompoundSpline

        @staticmethod
        def addCompoundBSpline(curveTags, numIntervals=20, tag=-1):
            """
            gmsh.model.geo.addCompoundBSpline(curveTags, numIntervals=20, tag=-1)

            Add a b-spline curve in the built-in CAD representation, with control
            points sampling the curves in `curveTags'. The density of sampling points
            on each curve is governed by `numIntervals'. If `tag' is positive, set the
            tag explicitly; otherwise a new tag is selected automatically. Return the
            tag of the b-spline.

            Return an integer.

            Types:
            - `curveTags': vector of integers
            - `numIntervals': integer
            - `tag': integer
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddCompoundBSpline(
                api_curveTags_, api_curveTags_n_,
                c_int(numIntervals),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_compound_bspline = addCompoundBSpline

        @staticmethod
        def addCurveLoop(curveTags, tag=-1, reorient=False):
            """
            gmsh.model.geo.addCurveLoop(curveTags, tag=-1, reorient=False)

            Add a curve loop (a closed wire) in the built-in CAD representation, formed
            by the curves `curveTags'. `curveTags' should contain (signed) tags of
            model entities of dimension 1 forming a closed loop: a negative tag
            signifies that the underlying curve is considered with reversed
            orientation. If `tag' is positive, set the tag explicitly; otherwise a new
            tag is selected automatically. If `reorient' is set, automatically reorient
            the curves if necessary. Return the tag of the curve loop.

            Return an integer.

            Types:
            - `curveTags': vector of integers
            - `tag': integer
            - `reorient': boolean
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddCurveLoop(
                api_curveTags_, api_curveTags_n_,
                c_int(tag),
                c_int(bool(reorient)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_curve_loop = addCurveLoop

        @staticmethod
        def addCurveLoops(curveTags):
            """
            gmsh.model.geo.addCurveLoops(curveTags)

            Add curve loops in the built-in CAD representation based on the curves
            `curveTags'. Return the `tags' of found curve loops, if any.

            Return `tags'.

            Types:
            - `curveTags': vector of integers
            - `tags': vector of integers
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            api_tags_, api_tags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelGeoAddCurveLoops(
                api_curveTags_, api_curveTags_n_,
                byref(api_tags_), byref(api_tags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_tags_, api_tags_n_.value)
        add_curve_loops = addCurveLoops

        @staticmethod
        def addPlaneSurface(wireTags, tag=-1):
            """
            gmsh.model.geo.addPlaneSurface(wireTags, tag=-1)

            Add a plane surface in the built-in CAD representation, defined by one or
            more curve loops `wireTags'. The first curve loop defines the exterior
            contour; additional curve loop define holes. If `tag' is positive, set the
            tag explicitly; otherwise a new tag is selected automatically. Return the
            tag of the surface.

            Return an integer.

            Types:
            - `wireTags': vector of integers
            - `tag': integer
            """
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddPlaneSurface(
                api_wireTags_, api_wireTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_plane_surface = addPlaneSurface

        @staticmethod
        def addSurfaceFilling(wireTags, tag=-1, sphereCenterTag=-1):
            """
            gmsh.model.geo.addSurfaceFilling(wireTags, tag=-1, sphereCenterTag=-1)

            Add a surface in the built-in CAD representation, filling the curve loops
            in `wireTags' using transfinite interpolation. Currently only a single
            curve loop is supported; this curve loop should be composed by 3 or 4
            curves only. If `tag' is positive, set the tag explicitly; otherwise a new
            tag is selected automatically. Return the tag of the surface.

            Return an integer.

            Types:
            - `wireTags': vector of integers
            - `tag': integer
            - `sphereCenterTag': integer
            """
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddSurfaceFilling(
                api_wireTags_, api_wireTags_n_,
                c_int(tag),
                c_int(sphereCenterTag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_surface_filling = addSurfaceFilling

        @staticmethod
        def addSurfaceLoop(surfaceTags, tag=-1):
            """
            gmsh.model.geo.addSurfaceLoop(surfaceTags, tag=-1)

            Add a surface loop (a closed shell) formed by `surfaceTags' in the built-in
            CAD representation.  If `tag' is positive, set the tag explicitly;
            otherwise a new tag is selected automatically. Return the tag of the shell.

            Return an integer.

            Types:
            - `surfaceTags': vector of integers
            - `tag': integer
            """
            api_surfaceTags_, api_surfaceTags_n_ = _ivectorint(surfaceTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddSurfaceLoop(
                api_surfaceTags_, api_surfaceTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_surface_loop = addSurfaceLoop

        @staticmethod
        def addVolume(shellTags, tag=-1):
            """
            gmsh.model.geo.addVolume(shellTags, tag=-1)

            Add a volume (a region) in the built-in CAD representation, defined by one
            or more shells `shellTags'. The first surface loop defines the exterior
            boundary; additional surface loop define holes. If `tag' is positive, set
            the tag explicitly; otherwise a new tag is selected automatically. Return
            the tag of the volume.

            Return an integer.

            Types:
            - `shellTags': vector of integers
            - `tag': integer
            """
            api_shellTags_, api_shellTags_n_ = _ivectorint(shellTags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddVolume(
                api_shellTags_, api_shellTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_volume = addVolume

        @staticmethod
        def addGeometry(geometry, numbers=[], strings=[], tag=-1):
            """
            gmsh.model.geo.addGeometry(geometry, numbers=[], strings=[], tag=-1)

            Add a `geometry' in the built-in CAD representation. `geometry' can
            currently be one of "Sphere" or "PolarSphere" (where `numbers' should
            contain the x, y, z coordinates of the center, followed by the radius), or
            "ParametricSurface" (where `strings' should contains three expression
            evaluating to the x, y and z coordinates in terms of parametric coordinates
            u and v). If `tag' is positive, set the tag of the geometry explicitly;
            otherwise a new tag is selected automatically. Return the tag of the
            geometry.

            Return an integer.

            Types:
            - `geometry': string
            - `numbers': vector of doubles
            - `strings': vector of strings
            - `tag': integer
            """
            api_numbers_, api_numbers_n_ = _ivectordouble(numbers)
            api_strings_, api_strings_n_ = _ivectorstring(strings)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddGeometry(
                c_char_p(geometry.encode()),
                api_numbers_, api_numbers_n_,
                api_strings_, api_strings_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_geometry = addGeometry

        @staticmethod
        def addPointOnGeometry(geometryTag, x, y, z=0., meshSize=0., tag=-1):
            """
            gmsh.model.geo.addPointOnGeometry(geometryTag, x, y, z=0., meshSize=0., tag=-1)

            Add a point in the built-in CAD representation, at coordinates (`x', `y',
            `z') on the geometry `geometryTag'. If `meshSize' is > 0, add a meshing
            constraint at that point. If `tag' is positive, set the tag explicitly;
            otherwise a new tag is selected automatically. Return the tag of the point.
            For surface geometries, only the `x' and `y' coordinates are used.

            Return an integer.

            Types:
            - `geometryTag': integer
            - `x': double
            - `y': double
            - `z': double
            - `meshSize': double
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddPointOnGeometry(
                c_int(geometryTag),
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(meshSize),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_point_on_geometry = addPointOnGeometry

        @staticmethod
        def extrude(dimTags, dx, dy, dz, numElements=[], heights=[], recombine=False):
            """
            gmsh.model.geo.extrude(dimTags, dx, dy, dz, numElements=[], heights=[], recombine=False)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, using a translation along (`dx', `dy',
            `dz'). Return extruded entities in `outDimTags'. If the `numElements'
            vector is not empty, also extrude the mesh: the entries in `numElements'
            give the number of elements in each layer. If the `height' vector is not
            empty, it provides the (cumulative) height of the different layers,
            normalized to 1. If `recombine' is set, recombine the mesh in the layers.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `dx': double
            - `dy': double
            - `dz': double
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelGeoExtrude(
                api_dimTags_, api_dimTags_n_,
                c_double(dx),
                c_double(dy),
                c_double(dz),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def revolve(dimTags, x, y, z, ax, ay, az, angle, numElements=[], heights=[], recombine=False):
            """
            gmsh.model.geo.revolve(dimTags, x, y, z, ax, ay, az, angle, numElements=[], heights=[], recombine=False)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, using a rotation of `angle' radians around
            the axis of revolution defined by the point (`x', `y', `z') and the
            direction (`ax', `ay', `az'). The angle should be strictly smaller than Pi.
            Return extruded entities in `outDimTags'. If the `numElements' vector is
            not empty, also extrude the mesh: the entries in `numElements' give the
            number of elements in each layer. If the `height' vector is not empty, it
            provides the (cumulative) height of the different layers, normalized to 1.
            If `recombine' is set, recombine the mesh in the layers.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `ax': double
            - `ay': double
            - `az': double
            - `angle': double
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelGeoRevolve(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(ax),
                c_double(ay),
                c_double(az),
                c_double(angle),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def twist(dimTags, x, y, z, dx, dy, dz, ax, ay, az, angle, numElements=[], heights=[], recombine=False):
            """
            gmsh.model.geo.twist(dimTags, x, y, z, dx, dy, dz, ax, ay, az, angle, numElements=[], heights=[], recombine=False)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, using a combined translation and rotation
            of `angle' radians, along (`dx', `dy', `dz') and around the axis of
            revolution defined by the point (`x', `y', `z') and the direction (`ax',
            `ay', `az'). The angle should be strictly smaller than Pi. Return extruded
            entities in `outDimTags'. If the `numElements' vector is not empty, also
            extrude the mesh: the entries in `numElements' give the number of elements
            in each layer. If the `height' vector is not empty, it provides the
            (cumulative) height of the different layers, normalized to 1. If
            `recombine' is set, recombine the mesh in the layers.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `dz': double
            - `ax': double
            - `ay': double
            - `az': double
            - `angle': double
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelGeoTwist(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_double(dz),
                c_double(ax),
                c_double(ay),
                c_double(az),
                c_double(angle),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def extrudeBoundaryLayer(dimTags, numElements=[1], heights=[], recombine=False, second=False, viewIndex=-1):
            """
            gmsh.model.geo.extrudeBoundaryLayer(dimTags, numElements=[1], heights=[], recombine=False, second=False, viewIndex=-1)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation along the normals of the mesh, creating
            discrete boundary layer entities. Return extruded entities in `outDimTags'.
            The entries in `numElements' give the number of elements in each layer. If
            the `height' vector is not empty, it provides the (cumulative) height of
            the different layers. If `recombine' is set, recombine the mesh in the
            layers. A second boundary layer can be created from the same entities if
            `second' is set. If `viewIndex' is >= 0, use the corresponding view to
            either specify the normals (if the view contains a vector field) or scale
            the normals (if the view is scalar).

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            - `second': boolean
            - `viewIndex': integer
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelGeoExtrudeBoundaryLayer(
                api_dimTags_, api_dimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                c_int(bool(second)),
                c_int(viewIndex),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        extrude_boundary_layer = extrudeBoundaryLayer

        @staticmethod
        def translate(dimTags, dx, dy, dz):
            """
            gmsh.model.geo.translate(dimTags, dx, dy, dz)

            Translate the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation along (`dx', `dy', `dz').

            Types:
            - `dimTags': vector of pairs of integers
            - `dx': double
            - `dy': double
            - `dz': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoTranslate(
                api_dimTags_, api_dimTags_n_,
                c_double(dx),
                c_double(dy),
                c_double(dz),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def rotate(dimTags, x, y, z, ax, ay, az, angle):
            """
            gmsh.model.geo.rotate(dimTags, x, y, z, ax, ay, az, angle)

            Rotate the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation by `angle' radians around the axis of
            revolution defined by the point (`x', `y', `z') and the direction (`ax',
            `ay', `az').

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `ax': double
            - `ay': double
            - `az': double
            - `angle': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoRotate(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(ax),
                c_double(ay),
                c_double(az),
                c_double(angle),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def dilate(dimTags, x, y, z, a, b, c):
            """
            gmsh.model.geo.dilate(dimTags, x, y, z, a, b, c)

            Scale the entities `dimTags' (given as a vector of (dim, tag) pairs) in the
            built-in CAD representation by factors `a', `b' and `c' along the three
            coordinate axes; use (`x', `y', `z') as the center of the homothetic
            transformation.

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `a': double
            - `b': double
            - `c': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoDilate(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(a),
                c_double(b),
                c_double(c),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def mirror(dimTags, a, b, c, d):
            """
            gmsh.model.geo.mirror(dimTags, a, b, c, d)

            Mirror the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, with respect to the plane of equation `a'
            * x + `b' * y + `c' * z + `d' = 0.

            Types:
            - `dimTags': vector of pairs of integers
            - `a': double
            - `b': double
            - `c': double
            - `d': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoMirror(
                api_dimTags_, api_dimTags_n_,
                c_double(a),
                c_double(b),
                c_double(c),
                c_double(d),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def symmetrize(dimTags, a, b, c, d):
            """
            gmsh.model.geo.symmetrize(dimTags, a, b, c, d)

            Mirror the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, with respect to the plane of equation `a'
            * x + `b' * y + `c' * z + `d' = 0. (This is a deprecated synonym for
            `mirror'.)

            Types:
            - `dimTags': vector of pairs of integers
            - `a': double
            - `b': double
            - `c': double
            - `d': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoSymmetrize(
                api_dimTags_, api_dimTags_n_,
                c_double(a),
                c_double(b),
                c_double(c),
                c_double(d),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def copy(dimTags):
            """
            gmsh.model.geo.copy(dimTags)

            Copy the entities `dimTags' (given as a vector of (dim, tag) pairs) in the
            built-in CAD representation; the new entities are returned in `outDimTags'.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelGeoCopy(
                api_dimTags_, api_dimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def remove(dimTags, recursive=False):
            """
            gmsh.model.geo.remove(dimTags, recursive=False)

            Remove the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the built-in CAD representation, provided that they are not on the boundary
            of higher-dimensional entities. If `recursive' is true, remove all the
            entities on their boundaries, down to dimension 0.

            Types:
            - `dimTags': vector of pairs of integers
            - `recursive': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoRemove(
                api_dimTags_, api_dimTags_n_,
                c_int(bool(recursive)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def removeAllDuplicates():
            """
            gmsh.model.geo.removeAllDuplicates()

            Remove all duplicate entities in the built-in CAD representation (different
            entities at the same geometrical location).
            """
            ierr = c_int()
            lib.gmshModelGeoRemoveAllDuplicates(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_all_duplicates = removeAllDuplicates

        @staticmethod
        def splitCurve(tag, pointTags):
            """
            gmsh.model.geo.splitCurve(tag, pointTags)

            Split the curve of tag `tag' in the built-in CAD representation, on the
            specified control points `pointTags'. This feature is only available for
            splines and b-splines. Return the tag(s) `curveTags' of the newly created
            curve(s).

            Return `curveTags'.

            Types:
            - `tag': integer
            - `pointTags': vector of integers
            - `curveTags': vector of integers
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            api_curveTags_, api_curveTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelGeoSplitCurve(
                c_int(tag),
                api_pointTags_, api_pointTags_n_,
                byref(api_curveTags_), byref(api_curveTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorint(api_curveTags_, api_curveTags_n_.value)
        split_curve = splitCurve

        @staticmethod
        def getMaxTag(dim):
            """
            gmsh.model.geo.getMaxTag(dim)

            Get the maximum tag of entities of dimension `dim' in the built-in CAD
            representation.

            Return an integer.

            Types:
            - `dim': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelGeoGetMaxTag(
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        get_max_tag = getMaxTag

        @staticmethod
        def setMaxTag(dim, maxTag):
            """
            gmsh.model.geo.setMaxTag(dim, maxTag)

            Set the maximum tag `maxTag' for entities of dimension `dim' in the built-
            in CAD representation.

            Types:
            - `dim': integer
            - `maxTag': integer
            """
            ierr = c_int()
            lib.gmshModelGeoSetMaxTag(
                c_int(dim),
                c_int(maxTag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_max_tag = setMaxTag

        @staticmethod
        def addPhysicalGroup(dim, tags, tag=-1, name=""):
            """
            gmsh.model.geo.addPhysicalGroup(dim, tags, tag=-1, name="")

            Add a physical group of dimension `dim', grouping the entities with tags
            `tags' in the built-in CAD representation. Return the tag of the physical
            group, equal to `tag' if `tag' is positive, or a new tag if `tag' < 0. Set
            the name of the physical group if `name' is not empty.

            Return an integer.

            Types:
            - `dim': integer
            - `tags': vector of integers
            - `tag': integer
            - `name': string
            """
            api_tags_, api_tags_n_ = _ivectorint(tags)
            ierr = c_int()
            api_result_ = lib.gmshModelGeoAddPhysicalGroup(
                c_int(dim),
                api_tags_, api_tags_n_,
                c_int(tag),
                c_char_p(name.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_physical_group = addPhysicalGroup

        @staticmethod
        def removePhysicalGroups(dimTags=[]):
            """
            gmsh.model.geo.removePhysicalGroups(dimTags=[])

            Remove the physical groups `dimTags' (given as a vector of (dim, tag)
            pairs) from the built-in CAD representation. If `dimTags' is empty, remove
            all groups.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelGeoRemovePhysicalGroups(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_physical_groups = removePhysicalGroups

        @staticmethod
        def synchronize():
            """
            gmsh.model.geo.synchronize()

            Synchronize the built-in CAD representation with the current Gmsh model.
            This can be called at any time, but since it involves a non trivial amount
            of processing, the number of synchronization points should normally be
            minimized. Without synchronization the entities in the built-in CAD
            representation are not available to any function outside of the built-in
            CAD kernel functions.
            """
            ierr = c_int()
            lib.gmshModelGeoSynchronize(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())


        class mesh:
            """
            Built-in CAD kernel meshing constraints
            """

            @staticmethod
            def setSize(dimTags, size):
                """
                gmsh.model.geo.mesh.setSize(dimTags, size)

                Set a mesh size constraint on the entities `dimTags' (given as a vector of
                (dim, tag) pairs) in the built-in CAD kernel representation. Currently only
                entities of dimension 0 (points) are handled.

                Types:
                - `dimTags': vector of pairs of integers
                - `size': double
                """
                api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
                ierr = c_int()
                lib.gmshModelGeoMeshSetSize(
                    api_dimTags_, api_dimTags_n_,
                    c_double(size),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_size = setSize

            @staticmethod
            def setTransfiniteCurve(tag, nPoints, meshType="Progression", coef=1.):
                """
                gmsh.model.geo.mesh.setTransfiniteCurve(tag, nPoints, meshType="Progression", coef=1.)

                Set a transfinite meshing constraint on the curve `tag' in the built-in CAD
                kernel representation, with `numNodes' nodes distributed according to
                `meshType' and `coef'. Currently supported types are "Progression"
                (geometrical progression with power `coef') and "Bump" (refinement toward
                both extremities of the curve).

                Types:
                - `tag': integer
                - `nPoints': integer
                - `meshType': string
                - `coef': double
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetTransfiniteCurve(
                    c_int(tag),
                    c_int(nPoints),
                    c_char_p(meshType.encode()),
                    c_double(coef),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_transfinite_curve = setTransfiniteCurve

            @staticmethod
            def setTransfiniteSurface(tag, arrangement="Left", cornerTags=[]):
                """
                gmsh.model.geo.mesh.setTransfiniteSurface(tag, arrangement="Left", cornerTags=[])

                Set a transfinite meshing constraint on the surface `tag' in the built-in
                CAD kernel representation. `arrangement' describes the arrangement of the
                triangles when the surface is not flagged as recombined: currently
                supported values are "Left", "Right", "AlternateLeft" and "AlternateRight".
                `cornerTags' can be used to specify the (3 or 4) corners of the transfinite
                interpolation explicitly; specifying the corners explicitly is mandatory if
                the surface has more that 3 or 4 points on its boundary.

                Types:
                - `tag': integer
                - `arrangement': string
                - `cornerTags': vector of integers
                """
                api_cornerTags_, api_cornerTags_n_ = _ivectorint(cornerTags)
                ierr = c_int()
                lib.gmshModelGeoMeshSetTransfiniteSurface(
                    c_int(tag),
                    c_char_p(arrangement.encode()),
                    api_cornerTags_, api_cornerTags_n_,
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_transfinite_surface = setTransfiniteSurface

            @staticmethod
            def setTransfiniteVolume(tag, cornerTags=[]):
                """
                gmsh.model.geo.mesh.setTransfiniteVolume(tag, cornerTags=[])

                Set a transfinite meshing constraint on the surface `tag' in the built-in
                CAD kernel representation. `cornerTags' can be used to specify the (6 or 8)
                corners of the transfinite interpolation explicitly.

                Types:
                - `tag': integer
                - `cornerTags': vector of integers
                """
                api_cornerTags_, api_cornerTags_n_ = _ivectorint(cornerTags)
                ierr = c_int()
                lib.gmshModelGeoMeshSetTransfiniteVolume(
                    c_int(tag),
                    api_cornerTags_, api_cornerTags_n_,
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_transfinite_volume = setTransfiniteVolume

            @staticmethod
            def setRecombine(dim, tag, angle=45.):
                """
                gmsh.model.geo.mesh.setRecombine(dim, tag, angle=45.)

                Set a recombination meshing constraint on the entity of dimension `dim' and
                tag `tag' in the built-in CAD kernel representation. Currently only
                entities of dimension 2 (to recombine triangles into quadrangles) are
                supported; `angle' specifies the threshold angle for the simple
                recombination algorithm.

                Types:
                - `dim': integer
                - `tag': integer
                - `angle': double
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetRecombine(
                    c_int(dim),
                    c_int(tag),
                    c_double(angle),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_recombine = setRecombine

            @staticmethod
            def setSmoothing(dim, tag, val):
                """
                gmsh.model.geo.mesh.setSmoothing(dim, tag, val)

                Set a smoothing meshing constraint on the entity of dimension `dim' and tag
                `tag' in the built-in CAD kernel representation. `val' iterations of a
                Laplace smoother are applied.

                Types:
                - `dim': integer
                - `tag': integer
                - `val': integer
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetSmoothing(
                    c_int(dim),
                    c_int(tag),
                    c_int(val),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_smoothing = setSmoothing

            @staticmethod
            def setReverse(dim, tag, val=True):
                """
                gmsh.model.geo.mesh.setReverse(dim, tag, val=True)

                Set a reverse meshing constraint on the entity of dimension `dim' and tag
                `tag' in the built-in CAD kernel representation. If `val' is true, the mesh
                orientation will be reversed with respect to the natural mesh orientation
                (i.e. the orientation consistent with the orientation of the geometry). If
                `val' is false, the mesh is left as-is.

                Types:
                - `dim': integer
                - `tag': integer
                - `val': boolean
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetReverse(
                    c_int(dim),
                    c_int(tag),
                    c_int(bool(val)),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_reverse = setReverse

            @staticmethod
            def setAlgorithm(dim, tag, val):
                """
                gmsh.model.geo.mesh.setAlgorithm(dim, tag, val)

                Set the meshing algorithm on the entity of dimension `dim' and tag `tag' in
                the built-in CAD kernel representation. Currently only supported for `dim'
                == 2.

                Types:
                - `dim': integer
                - `tag': integer
                - `val': integer
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetAlgorithm(
                    c_int(dim),
                    c_int(tag),
                    c_int(val),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_algorithm = setAlgorithm

            @staticmethod
            def setSizeFromBoundary(dim, tag, val):
                """
                gmsh.model.geo.mesh.setSizeFromBoundary(dim, tag, val)

                Force the mesh size to be extended from the boundary, or not, for the
                entity of dimension `dim' and tag `tag' in the built-in CAD kernel
                representation. Currently only supported for `dim' == 2.

                Types:
                - `dim': integer
                - `tag': integer
                - `val': integer
                """
                ierr = c_int()
                lib.gmshModelGeoMeshSetSizeFromBoundary(
                    c_int(dim),
                    c_int(tag),
                    c_int(val),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_size_from_boundary = setSizeFromBoundary


    class occ:
        """
        OpenCASCADE CAD kernel functions
        """

        @staticmethod
        def addPoint(x, y, z, meshSize=0., tag=-1):
            """
            gmsh.model.occ.addPoint(x, y, z, meshSize=0., tag=-1)

            Add a geometrical point in the OpenCASCADE CAD representation, at
            coordinates (`x', `y', `z'). If `meshSize' is > 0, add a meshing constraint
            at that point. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. Return the tag of the point. (Note that
            the point will be added in the current model only after `synchronize' is
            called. This behavior holds for all the entities added in the occ module.)

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `meshSize': double
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddPoint(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(meshSize),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_point = addPoint

        @staticmethod
        def addLine(startTag, endTag, tag=-1):
            """
            gmsh.model.occ.addLine(startTag, endTag, tag=-1)

            Add a straight line segment in the OpenCASCADE CAD representation, between
            the two points with tags `startTag' and `endTag'. If `tag' is positive, set
            the tag explicitly; otherwise a new tag is selected automatically. Return
            the tag of the line.

            Return an integer.

            Types:
            - `startTag': integer
            - `endTag': integer
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddLine(
                c_int(startTag),
                c_int(endTag),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_line = addLine

        @staticmethod
        def addCircleArc(startTag, middleTag, endTag, tag=-1, center=True):
            """
            gmsh.model.occ.addCircleArc(startTag, middleTag, endTag, tag=-1, center=True)

            Add a circle arc in the OpenCASCADE CAD representation, between the two
            points with tags `startTag' and `endTag', with middle point `middleTag'. If
            `center' is true, the middle point is the center of the circle; otherwise
            the circle goes through the middle point. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. Return the tag
            of the circle arc.

            Return an integer.

            Types:
            - `startTag': integer
            - `middleTag': integer
            - `endTag': integer
            - `tag': integer
            - `center': boolean
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddCircleArc(
                c_int(startTag),
                c_int(middleTag),
                c_int(endTag),
                c_int(tag),
                c_int(bool(center)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_circle_arc = addCircleArc

        @staticmethod
        def addCircle(x, y, z, r, tag=-1, angle1=0., angle2=2*pi, zAxis=[], xAxis=[]):
            """
            gmsh.model.occ.addCircle(x, y, z, r, tag=-1, angle1=0., angle2=2*pi, zAxis=[], xAxis=[])

            Add a circle of center (`x', `y', `z') and radius `r' in the OpenCASCADE
            CAD representation. If `tag' is positive, set the tag explicitly; otherwise
            a new tag is selected automatically. If `angle1' and `angle2' are
            specified, create a circle arc between the two angles. If a vector `zAxis'
            of size 3 is provided, use it as the normal to the circle plane (z-axis).
            If a vector `xAxis' of size 3 is provided in addition to `zAxis', use it to
            define the x-axis. Return the tag of the circle.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `r': double
            - `tag': integer
            - `angle1': double
            - `angle2': double
            - `zAxis': vector of doubles
            - `xAxis': vector of doubles
            """
            api_zAxis_, api_zAxis_n_ = _ivectordouble(zAxis)
            api_xAxis_, api_xAxis_n_ = _ivectordouble(xAxis)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddCircle(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(r),
                c_int(tag),
                c_double(angle1),
                c_double(angle2),
                api_zAxis_, api_zAxis_n_,
                api_xAxis_, api_xAxis_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_circle = addCircle

        @staticmethod
        def addEllipseArc(startTag, centerTag, majorTag, endTag, tag=-1):
            """
            gmsh.model.occ.addEllipseArc(startTag, centerTag, majorTag, endTag, tag=-1)

            Add an ellipse arc in the OpenCASCADE CAD representation, between the two
            points `startTag' and `endTag', and with center `centerTag' and major axis
            point `majorTag'. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. Return the tag of the ellipse arc. Note
            that OpenCASCADE does not allow creating ellipse arcs with the major radius
            smaller than the minor radius.

            Return an integer.

            Types:
            - `startTag': integer
            - `centerTag': integer
            - `majorTag': integer
            - `endTag': integer
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddEllipseArc(
                c_int(startTag),
                c_int(centerTag),
                c_int(majorTag),
                c_int(endTag),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_ellipse_arc = addEllipseArc

        @staticmethod
        def addEllipse(x, y, z, r1, r2, tag=-1, angle1=0., angle2=2*pi, zAxis=[], xAxis=[]):
            """
            gmsh.model.occ.addEllipse(x, y, z, r1, r2, tag=-1, angle1=0., angle2=2*pi, zAxis=[], xAxis=[])

            Add an ellipse of center (`x', `y', `z') and radii `r1' and `r2' (with `r1'
            >= `r2') along the x- and y-axes, respectively, in the OpenCASCADE CAD
            representation. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. If `angle1' and `angle2' are specified,
            create an ellipse arc between the two angles. If a vector `zAxis' of size 3
            is provided, use it as the normal to the ellipse plane (z-axis). If a
            vector `xAxis' of size 3 is provided in addition to `zAxis', use it to
            define the x-axis. Return the tag of the ellipse.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `r1': double
            - `r2': double
            - `tag': integer
            - `angle1': double
            - `angle2': double
            - `zAxis': vector of doubles
            - `xAxis': vector of doubles
            """
            api_zAxis_, api_zAxis_n_ = _ivectordouble(zAxis)
            api_xAxis_, api_xAxis_n_ = _ivectordouble(xAxis)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddEllipse(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(r1),
                c_double(r2),
                c_int(tag),
                c_double(angle1),
                c_double(angle2),
                api_zAxis_, api_zAxis_n_,
                api_xAxis_, api_xAxis_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_ellipse = addEllipse

        @staticmethod
        def addSpline(pointTags, tag=-1, tangents=[]):
            """
            gmsh.model.occ.addSpline(pointTags, tag=-1, tangents=[])

            Add a spline (C2 b-spline) curve in the OpenCASCADE CAD representation,
            going through the points `pointTags'. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. Create a
            periodic curve if the first and last points are the same. Return the tag of
            the spline curve. If the `tangents' vector contains 6 entries, use them as
            concatenated x, y, z components of the initial and final tangents of the
            b-spline; if it contains 3 times as many entries as the number of points,
            use them as concatenated x, y, z components of the tangents at each point,
            unless the norm of the tangent is zero.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            - `tangents': vector of doubles
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            api_tangents_, api_tangents_n_ = _ivectordouble(tangents)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddSpline(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                api_tangents_, api_tangents_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_spline = addSpline

        @staticmethod
        def addBSpline(pointTags, tag=-1, degree=3, weights=[], knots=[], multiplicities=[]):
            """
            gmsh.model.occ.addBSpline(pointTags, tag=-1, degree=3, weights=[], knots=[], multiplicities=[])

            Add a b-spline curve of degree `degree' in the OpenCASCADE CAD
            representation, with `pointTags' control points. If `weights', `knots' or
            `multiplicities' are not provided, default parameters are computed
            automatically. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. Create a periodic curve if the first and
            last points are the same. Return the tag of the b-spline curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            - `degree': integer
            - `weights': vector of doubles
            - `knots': vector of doubles
            - `multiplicities': vector of integers
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            api_weights_, api_weights_n_ = _ivectordouble(weights)
            api_knots_, api_knots_n_ = _ivectordouble(knots)
            api_multiplicities_, api_multiplicities_n_ = _ivectorint(multiplicities)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBSpline(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                c_int(degree),
                api_weights_, api_weights_n_,
                api_knots_, api_knots_n_,
                api_multiplicities_, api_multiplicities_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bspline = addBSpline

        @staticmethod
        def addBezier(pointTags, tag=-1):
            """
            gmsh.model.occ.addBezier(pointTags, tag=-1)

            Add a Bezier curve in the OpenCASCADE CAD representation, with `pointTags'
            control points. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. Return the tag of the Bezier curve.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `tag': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBezier(
                api_pointTags_, api_pointTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bezier = addBezier

        @staticmethod
        def addWire(curveTags, tag=-1, checkClosed=False):
            """
            gmsh.model.occ.addWire(curveTags, tag=-1, checkClosed=False)

            Add a wire (open or closed) in the OpenCASCADE CAD representation, formed
            by the curves `curveTags'. Note that an OpenCASCADE wire can be made of
            curves that share geometrically identical (but topologically different)
            points. If `tag' is positive, set the tag explicitly; otherwise a new tag
            is selected automatically. Return the tag of the wire.

            Return an integer.

            Types:
            - `curveTags': vector of integers
            - `tag': integer
            - `checkClosed': boolean
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddWire(
                api_curveTags_, api_curveTags_n_,
                c_int(tag),
                c_int(bool(checkClosed)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_wire = addWire

        @staticmethod
        def addCurveLoop(curveTags, tag=-1):
            """
            gmsh.model.occ.addCurveLoop(curveTags, tag=-1)

            Add a curve loop (a closed wire) in the OpenCASCADE CAD representation,
            formed by the curves `curveTags'. `curveTags' should contain tags of curves
            forming a closed loop. Negative tags can be specified for compatibility
            with the built-in kernel, but are simply ignored: the wire is oriented
            according to the orientation of its first curve. Note that an OpenCASCADE
            curve loop can be made of curves that share geometrically identical (but
            topologically different) points. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. Return the tag
            of the curve loop.

            Return an integer.

            Types:
            - `curveTags': vector of integers
            - `tag': integer
            """
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddCurveLoop(
                api_curveTags_, api_curveTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_curve_loop = addCurveLoop

        @staticmethod
        def addRectangle(x, y, z, dx, dy, tag=-1, roundedRadius=0.):
            """
            gmsh.model.occ.addRectangle(x, y, z, dx, dy, tag=-1, roundedRadius=0.)

            Add a rectangle in the OpenCASCADE CAD representation, with lower left
            corner at (`x', `y', `z') and upper right corner at (`x' + `dx', `y' +
            `dy', `z'). If `tag' is positive, set the tag explicitly; otherwise a new
            tag is selected automatically. Round the corners if `roundedRadius' is
            nonzero. Return the tag of the rectangle.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `tag': integer
            - `roundedRadius': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddRectangle(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_int(tag),
                c_double(roundedRadius),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_rectangle = addRectangle

        @staticmethod
        def addDisk(xc, yc, zc, rx, ry, tag=-1, zAxis=[], xAxis=[]):
            """
            gmsh.model.occ.addDisk(xc, yc, zc, rx, ry, tag=-1, zAxis=[], xAxis=[])

            Add a disk in the OpenCASCADE CAD representation, with center (`xc', `yc',
            `zc') and radius `rx' along the x-axis and `ry' along the y-axis (`rx' >=
            `ry'). If `tag' is positive, set the tag explicitly; otherwise a new tag is
            selected automatically. If a vector `zAxis' of size 3 is provided, use it
            as the normal to the disk (z-axis). If a vector `xAxis' of size 3 is
            provided in addition to `zAxis', use it to define the x-axis. Return the
            tag of the disk.

            Return an integer.

            Types:
            - `xc': double
            - `yc': double
            - `zc': double
            - `rx': double
            - `ry': double
            - `tag': integer
            - `zAxis': vector of doubles
            - `xAxis': vector of doubles
            """
            api_zAxis_, api_zAxis_n_ = _ivectordouble(zAxis)
            api_xAxis_, api_xAxis_n_ = _ivectordouble(xAxis)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddDisk(
                c_double(xc),
                c_double(yc),
                c_double(zc),
                c_double(rx),
                c_double(ry),
                c_int(tag),
                api_zAxis_, api_zAxis_n_,
                api_xAxis_, api_xAxis_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_disk = addDisk

        @staticmethod
        def addPlaneSurface(wireTags, tag=-1):
            """
            gmsh.model.occ.addPlaneSurface(wireTags, tag=-1)

            Add a plane surface in the OpenCASCADE CAD representation, defined by one
            or more curve loops (or closed wires) `wireTags'. The first curve loop
            defines the exterior contour; additional curve loop define holes. If `tag'
            is positive, set the tag explicitly; otherwise a new tag is selected
            automatically. Return the tag of the surface.

            Return an integer.

            Types:
            - `wireTags': vector of integers
            - `tag': integer
            """
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddPlaneSurface(
                api_wireTags_, api_wireTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_plane_surface = addPlaneSurface

        @staticmethod
        def addSurfaceFilling(wireTag, tag=-1, pointTags=[], degree=2, numPointsOnCurves=15, numIter=2, anisotropic=False, tol2d=0.00001, tol3d=0.0001, tolAng=0.01, tolCurv=0.1, maxDegree=8, maxSegments=9):
            """
            gmsh.model.occ.addSurfaceFilling(wireTag, tag=-1, pointTags=[], degree=2, numPointsOnCurves=15, numIter=2, anisotropic=False, tol2d=0.00001, tol3d=0.0001, tolAng=0.01, tolCurv=0.1, maxDegree=8, maxSegments=9)

            Add a surface in the OpenCASCADE CAD representation, filling the curve loop
            `wireTag'. If `tag' is positive, set the tag explicitly; otherwise a new
            tag is selected automatically. Return the tag of the surface. If
            `pointTags' are provided, force the surface to pass through the given
            points. The other optional arguments are `degree' (the degree of the energy
            criterion to minimize for computing the deformation of the surface),
            `numPointsOnCurves' (the average number of points for discretisation of the
            bounding curves), `numIter' (the maximum number of iterations of the
            optimization process), `anisotropic' (improve performance when the ratio of
            the length along the two parametric coordinates of the surface is high),
            `tol2d' (tolerance to the constraints in the parametric plane of the
            surface), `tol3d' (the maximum distance allowed between the support surface
            and the constraints), `tolAng' (the maximum angle allowed between the
            normal of the surface and the constraints), `tolCurv' (the maximum
            difference of curvature allowed between the surface and the constraint),
            `maxDegree' (the highest degree which the polynomial defining the filling
            surface can have) and, `maxSegments' (the largest number of segments which
            the filling surface can have).

            Return an integer.

            Types:
            - `wireTag': integer
            - `tag': integer
            - `pointTags': vector of integers
            - `degree': integer
            - `numPointsOnCurves': integer
            - `numIter': integer
            - `anisotropic': boolean
            - `tol2d': double
            - `tol3d': double
            - `tolAng': double
            - `tolCurv': double
            - `maxDegree': integer
            - `maxSegments': integer
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddSurfaceFilling(
                c_int(wireTag),
                c_int(tag),
                api_pointTags_, api_pointTags_n_,
                c_int(degree),
                c_int(numPointsOnCurves),
                c_int(numIter),
                c_int(bool(anisotropic)),
                c_double(tol2d),
                c_double(tol3d),
                c_double(tolAng),
                c_double(tolCurv),
                c_int(maxDegree),
                c_int(maxSegments),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_surface_filling = addSurfaceFilling

        @staticmethod
        def addBSplineFilling(wireTag, tag=-1, type=""):
            """
            gmsh.model.occ.addBSplineFilling(wireTag, tag=-1, type="")

            Add a BSpline surface in the OpenCASCADE CAD representation, filling the
            curve loop `wireTag'. The curve loop should be made of 2, 3 or 4 curves.
            The optional `type' argument specifies the type of filling: "Stretch"
            creates the flattest patch, "Curved" (the default) creates the most rounded
            patch, and "Coons" creates a rounded patch with less depth than "Curved".
            If `tag' is positive, set the tag explicitly; otherwise a new tag is
            selected automatically. Return the tag of the surface.

            Return an integer.

            Types:
            - `wireTag': integer
            - `tag': integer
            - `type': string
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBSplineFilling(
                c_int(wireTag),
                c_int(tag),
                c_char_p(type.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bspline_filling = addBSplineFilling

        @staticmethod
        def addBezierFilling(wireTag, tag=-1, type=""):
            """
            gmsh.model.occ.addBezierFilling(wireTag, tag=-1, type="")

            Add a Bezier surface in the OpenCASCADE CAD representation, filling the
            curve loop `wireTag'. The curve loop should be made of 2, 3 or 4 Bezier
            curves. The optional `type' argument specifies the type of filling:
            "Stretch" creates the flattest patch, "Curved" (the default) creates the
            most rounded patch, and "Coons" creates a rounded patch with less depth
            than "Curved". If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. Return the tag of the surface.

            Return an integer.

            Types:
            - `wireTag': integer
            - `tag': integer
            - `type': string
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBezierFilling(
                c_int(wireTag),
                c_int(tag),
                c_char_p(type.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bezier_filling = addBezierFilling

        @staticmethod
        def addBSplineSurface(pointTags, numPointsU, tag=-1, degreeU=3, degreeV=3, weights=[], knotsU=[], knotsV=[], multiplicitiesU=[], multiplicitiesV=[], wireTags=[], wire3D=False):
            """
            gmsh.model.occ.addBSplineSurface(pointTags, numPointsU, tag=-1, degreeU=3, degreeV=3, weights=[], knotsU=[], knotsV=[], multiplicitiesU=[], multiplicitiesV=[], wireTags=[], wire3D=False)

            Add a b-spline surface of degree `degreeU' x `degreeV' in the OpenCASCADE
            CAD representation, with `pointTags' control points given as a single
            vector [Pu1v1, ... Pu`numPointsU'v1, Pu1v2, ...]. If `weights', `knotsU',
            `knotsV', `multiplicitiesU' or `multiplicitiesV' are not provided, default
            parameters are computed automatically. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. If `wireTags' is
            provided, trim the b-spline patch using the provided wires: the first wire
            defines the external contour, the others define holes. If `wire3D' is set,
            consider wire curves as 3D curves and project them on the b-spline surface;
            otherwise consider the wire curves as defined in the parametric space of
            the surface. Return the tag of the b-spline surface.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `numPointsU': integer
            - `tag': integer
            - `degreeU': integer
            - `degreeV': integer
            - `weights': vector of doubles
            - `knotsU': vector of doubles
            - `knotsV': vector of doubles
            - `multiplicitiesU': vector of integers
            - `multiplicitiesV': vector of integers
            - `wireTags': vector of integers
            - `wire3D': boolean
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            api_weights_, api_weights_n_ = _ivectordouble(weights)
            api_knotsU_, api_knotsU_n_ = _ivectordouble(knotsU)
            api_knotsV_, api_knotsV_n_ = _ivectordouble(knotsV)
            api_multiplicitiesU_, api_multiplicitiesU_n_ = _ivectorint(multiplicitiesU)
            api_multiplicitiesV_, api_multiplicitiesV_n_ = _ivectorint(multiplicitiesV)
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBSplineSurface(
                api_pointTags_, api_pointTags_n_,
                c_int(numPointsU),
                c_int(tag),
                c_int(degreeU),
                c_int(degreeV),
                api_weights_, api_weights_n_,
                api_knotsU_, api_knotsU_n_,
                api_knotsV_, api_knotsV_n_,
                api_multiplicitiesU_, api_multiplicitiesU_n_,
                api_multiplicitiesV_, api_multiplicitiesV_n_,
                api_wireTags_, api_wireTags_n_,
                c_int(bool(wire3D)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bspline_surface = addBSplineSurface

        @staticmethod
        def addBezierSurface(pointTags, numPointsU, tag=-1, wireTags=[], wire3D=False):
            """
            gmsh.model.occ.addBezierSurface(pointTags, numPointsU, tag=-1, wireTags=[], wire3D=False)

            Add a Bezier surface in the OpenCASCADE CAD representation, with
            `pointTags' control points given as a single vector [Pu1v1, ...
            Pu`numPointsU'v1, Pu1v2, ...]. If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. If `wireTags' is
            provided, trim the Bezier patch using the provided wires: the first wire
            defines the external contour, the others define holes. If `wire3D' is set,
            consider wire curves as 3D curves and project them on the Bezier surface;
            otherwise consider the wire curves as defined in the parametric space of
            the surface. Return the tag of the Bezier surface.

            Return an integer.

            Types:
            - `pointTags': vector of integers
            - `numPointsU': integer
            - `tag': integer
            - `wireTags': vector of integers
            - `wire3D': boolean
            """
            api_pointTags_, api_pointTags_n_ = _ivectorint(pointTags)
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBezierSurface(
                api_pointTags_, api_pointTags_n_,
                c_int(numPointsU),
                c_int(tag),
                api_wireTags_, api_wireTags_n_,
                c_int(bool(wire3D)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_bezier_surface = addBezierSurface

        @staticmethod
        def addTrimmedSurface(surfaceTag, wireTags=[], wire3D=False, tag=-1):
            """
            gmsh.model.occ.addTrimmedSurface(surfaceTag, wireTags=[], wire3D=False, tag=-1)

            Trim the surface `surfaceTag' with the wires `wireTags', replacing any
            existing trimming curves. The first wire defines the external contour, the
            others define holes. If `wire3D' is set, consider wire curves as 3D curves
            and project them on the surface; otherwise consider the wire curves as
            defined in the parametric space of the surface. If `tag' is positive, set
            the tag explicitly; otherwise a new tag is selected automatically. Return
            the tag of the trimmed surface.

            Return an integer.

            Types:
            - `surfaceTag': integer
            - `wireTags': vector of integers
            - `wire3D': boolean
            - `tag': integer
            """
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddTrimmedSurface(
                c_int(surfaceTag),
                api_wireTags_, api_wireTags_n_,
                c_int(bool(wire3D)),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_trimmed_surface = addTrimmedSurface

        @staticmethod
        def addSurfaceLoop(surfaceTags, tag=-1, sewing=False):
            """
            gmsh.model.occ.addSurfaceLoop(surfaceTags, tag=-1, sewing=False)

            Add a surface loop (a closed shell) in the OpenCASCADE CAD representation,
            formed by `surfaceTags'.  If `tag' is positive, set the tag explicitly;
            otherwise a new tag is selected automatically. Return the tag of the
            surface loop. Setting `sewing' allows one to build a shell made of surfaces
            that share geometrically identical (but topologically different) curves.

            Return an integer.

            Types:
            - `surfaceTags': vector of integers
            - `tag': integer
            - `sewing': boolean
            """
            api_surfaceTags_, api_surfaceTags_n_ = _ivectorint(surfaceTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddSurfaceLoop(
                api_surfaceTags_, api_surfaceTags_n_,
                c_int(tag),
                c_int(bool(sewing)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_surface_loop = addSurfaceLoop

        @staticmethod
        def addVolume(shellTags, tag=-1):
            """
            gmsh.model.occ.addVolume(shellTags, tag=-1)

            Add a volume (a region) in the OpenCASCADE CAD representation, defined by
            one or more surface loops `shellTags'. The first surface loop defines the
            exterior boundary; additional surface loop define holes. If `tag' is
            positive, set the tag explicitly; otherwise a new tag is selected
            automatically. Return the tag of the volume.

            Return an integer.

            Types:
            - `shellTags': vector of integers
            - `tag': integer
            """
            api_shellTags_, api_shellTags_n_ = _ivectorint(shellTags)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddVolume(
                api_shellTags_, api_shellTags_n_,
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_volume = addVolume

        @staticmethod
        def addSphere(xc, yc, zc, radius, tag=-1, angle1=-pi/2, angle2=pi/2, angle3=2*pi):
            """
            gmsh.model.occ.addSphere(xc, yc, zc, radius, tag=-1, angle1=-pi/2, angle2=pi/2, angle3=2*pi)

            Add a sphere of center (`xc', `yc', `zc') and radius `r' in the OpenCASCADE
            CAD representation. The optional `angle1' and `angle2' arguments define the
            polar angle opening (from -Pi/2 to Pi/2). The optional `angle3' argument
            defines the azimuthal opening (from 0 to 2*Pi). If `tag' is positive, set
            the tag explicitly; otherwise a new tag is selected automatically. Return
            the tag of the sphere.

            Return an integer.

            Types:
            - `xc': double
            - `yc': double
            - `zc': double
            - `radius': double
            - `tag': integer
            - `angle1': double
            - `angle2': double
            - `angle3': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddSphere(
                c_double(xc),
                c_double(yc),
                c_double(zc),
                c_double(radius),
                c_int(tag),
                c_double(angle1),
                c_double(angle2),
                c_double(angle3),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_sphere = addSphere

        @staticmethod
        def addBox(x, y, z, dx, dy, dz, tag=-1):
            """
            gmsh.model.occ.addBox(x, y, z, dx, dy, dz, tag=-1)

            Add a parallelepipedic box in the OpenCASCADE CAD representation, defined
            by a point (`x', `y', `z') and the extents along the x-, y- and z-axes. If
            `tag' is positive, set the tag explicitly; otherwise a new tag is selected
            automatically. Return the tag of the box.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `dz': double
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddBox(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_double(dz),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_box = addBox

        @staticmethod
        def addCylinder(x, y, z, dx, dy, dz, r, tag=-1, angle=2*pi):
            """
            gmsh.model.occ.addCylinder(x, y, z, dx, dy, dz, r, tag=-1, angle=2*pi)

            Add a cylinder in the OpenCASCADE CAD representation, defined by the center
            (`x', `y', `z') of its first circular face, the 3 components (`dx', `dy',
            `dz') of the vector defining its axis and its radius `r'. The optional
            `angle' argument defines the angular opening (from 0 to 2*Pi). If `tag' is
            positive, set the tag explicitly; otherwise a new tag is selected
            automatically. Return the tag of the cylinder.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `dz': double
            - `r': double
            - `tag': integer
            - `angle': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddCylinder(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_double(dz),
                c_double(r),
                c_int(tag),
                c_double(angle),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_cylinder = addCylinder

        @staticmethod
        def addCone(x, y, z, dx, dy, dz, r1, r2, tag=-1, angle=2*pi):
            """
            gmsh.model.occ.addCone(x, y, z, dx, dy, dz, r1, r2, tag=-1, angle=2*pi)

            Add a cone in the OpenCASCADE CAD representation, defined by the center
            (`x', `y', `z') of its first circular face, the 3 components of the vector
            (`dx', `dy', `dz') defining its axis and the two radii `r1' and `r2' of the
            faces (these radii can be zero). If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. `angle' defines
            the optional angular opening (from 0 to 2*Pi). Return the tag of the cone.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `dz': double
            - `r1': double
            - `r2': double
            - `tag': integer
            - `angle': double
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddCone(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_double(dz),
                c_double(r1),
                c_double(r2),
                c_int(tag),
                c_double(angle),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_cone = addCone

        @staticmethod
        def addWedge(x, y, z, dx, dy, dz, tag=-1, ltx=0., zAxis=[]):
            """
            gmsh.model.occ.addWedge(x, y, z, dx, dy, dz, tag=-1, ltx=0., zAxis=[])

            Add a right angular wedge in the OpenCASCADE CAD representation, defined by
            the right-angle point (`x', `y', `z') and the 3 extends along the x-, y-
            and z-axes (`dx', `dy', `dz'). If `tag' is positive, set the tag
            explicitly; otherwise a new tag is selected automatically. The optional
            argument `ltx' defines the top extent along the x-axis. If a vector `zAxis'
            of size 3 is provided, use it to define the z-axis. Return the tag of the
            wedge.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `dx': double
            - `dy': double
            - `dz': double
            - `tag': integer
            - `ltx': double
            - `zAxis': vector of doubles
            """
            api_zAxis_, api_zAxis_n_ = _ivectordouble(zAxis)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddWedge(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(dx),
                c_double(dy),
                c_double(dz),
                c_int(tag),
                c_double(ltx),
                api_zAxis_, api_zAxis_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_wedge = addWedge

        @staticmethod
        def addTorus(x, y, z, r1, r2, tag=-1, angle=2*pi, zAxis=[]):
            """
            gmsh.model.occ.addTorus(x, y, z, r1, r2, tag=-1, angle=2*pi, zAxis=[])

            Add a torus in the OpenCASCADE CAD representation, defined by its center
            (`x', `y', `z') and its 2 radii `r' and `r2'. If `tag' is positive, set the
            tag explicitly; otherwise a new tag is selected automatically. The optional
            argument `angle' defines the angular opening (from 0 to 2*Pi). If a vector
            `zAxis' of size 3 is provided, use it to define the z-axis. Return the tag
            of the torus.

            Return an integer.

            Types:
            - `x': double
            - `y': double
            - `z': double
            - `r1': double
            - `r2': double
            - `tag': integer
            - `angle': double
            - `zAxis': vector of doubles
            """
            api_zAxis_, api_zAxis_n_ = _ivectordouble(zAxis)
            ierr = c_int()
            api_result_ = lib.gmshModelOccAddTorus(
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(r1),
                c_double(r2),
                c_int(tag),
                c_double(angle),
                api_zAxis_, api_zAxis_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        add_torus = addTorus

        @staticmethod
        def addThruSections(wireTags, tag=-1, makeSolid=True, makeRuled=False, maxDegree=-1, continuity="", parametrization="", smoothing=False):
            """
            gmsh.model.occ.addThruSections(wireTags, tag=-1, makeSolid=True, makeRuled=False, maxDegree=-1, continuity="", parametrization="", smoothing=False)

            Add a volume (if the optional argument `makeSolid' is set) or surfaces in
            the OpenCASCADE CAD representation, defined through the open or closed
            wires `wireTags'. If `tag' is positive, set the tag explicitly; otherwise a
            new tag is selected automatically. The new entities are returned in
            `outDimTags' as a vector of (dim, tag) pairs. If the optional argument
            `makeRuled' is set, the surfaces created on the boundary are forced to be
            ruled surfaces. If `maxDegree' is positive, set the maximal degree of
            resulting surface. The optional argument `continuity' allows to specify the
            continuity of the resulting shape ("C0", "G1", "C1", "G2", "C2", "C3",
            "CN"). The optional argument `parametrization' sets the parametrization
            type ("ChordLength", "Centripetal", "IsoParametric"). The optional argument
            `smoothing' determines if smoothing is applied.

            Return `outDimTags'.

            Types:
            - `wireTags': vector of integers
            - `outDimTags': vector of pairs of integers
            - `tag': integer
            - `makeSolid': boolean
            - `makeRuled': boolean
            - `maxDegree': integer
            - `continuity': string
            - `parametrization': string
            - `smoothing': boolean
            """
            api_wireTags_, api_wireTags_n_ = _ivectorint(wireTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccAddThruSections(
                api_wireTags_, api_wireTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(tag),
                c_int(bool(makeSolid)),
                c_int(bool(makeRuled)),
                c_int(maxDegree),
                c_char_p(continuity.encode()),
                c_char_p(parametrization.encode()),
                c_int(bool(smoothing)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        add_thru_sections = addThruSections

        @staticmethod
        def addThickSolid(volumeTag, excludeSurfaceTags, offset, tag=-1):
            """
            gmsh.model.occ.addThickSolid(volumeTag, excludeSurfaceTags, offset, tag=-1)

            Add a hollowed volume in the OpenCASCADE CAD representation, built from an
            initial volume `volumeTag' and a set of faces from this volume
            `excludeSurfaceTags', which are to be removed. The remaining faces of the
            volume become the walls of the hollowed solid, with thickness `offset'. If
            `tag' is positive, set the tag explicitly; otherwise a new tag is selected
            automatically.

            Return `outDimTags'.

            Types:
            - `volumeTag': integer
            - `excludeSurfaceTags': vector of integers
            - `offset': double
            - `outDimTags': vector of pairs of integers
            - `tag': integer
            """
            api_excludeSurfaceTags_, api_excludeSurfaceTags_n_ = _ivectorint(excludeSurfaceTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccAddThickSolid(
                c_int(volumeTag),
                api_excludeSurfaceTags_, api_excludeSurfaceTags_n_,
                c_double(offset),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        add_thick_solid = addThickSolid

        @staticmethod
        def extrude(dimTags, dx, dy, dz, numElements=[], heights=[], recombine=False):
            """
            gmsh.model.occ.extrude(dimTags, dx, dy, dz, numElements=[], heights=[], recombine=False)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation, using a translation along (`dx', `dy',
            `dz'). Return extruded entities in `outDimTags'. If the `numElements'
            vector is not empty, also extrude the mesh: the entries in `numElements'
            give the number of elements in each layer. If the `height' vector is not
            empty, it provides the (cumulative) height of the different layers,
            normalized to 1. If `recombine' is set, recombine the mesh in the layers.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `dx': double
            - `dy': double
            - `dz': double
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelOccExtrude(
                api_dimTags_, api_dimTags_n_,
                c_double(dx),
                c_double(dy),
                c_double(dz),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def revolve(dimTags, x, y, z, ax, ay, az, angle, numElements=[], heights=[], recombine=False):
            """
            gmsh.model.occ.revolve(dimTags, x, y, z, ax, ay, az, angle, numElements=[], heights=[], recombine=False)

            Extrude the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation, using a rotation of `angle' radians
            around the axis of revolution defined by the point (`x', `y', `z') and the
            direction (`ax', `ay', `az'). Return extruded entities in `outDimTags'. If
            the `numElements' vector is not empty, also extrude the mesh: the entries
            in `numElements' give the number of elements in each layer. If the `height'
            vector is not empty, it provides the (cumulative) height of the different
            layers, normalized to 1. When the mesh is extruded the angle should be
            strictly smaller than 2*Pi. If `recombine' is set, recombine the mesh in
            the layers.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `ax': double
            - `ay': double
            - `az': double
            - `angle': double
            - `outDimTags': vector of pairs of integers
            - `numElements': vector of integers
            - `heights': vector of doubles
            - `recombine': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_numElements_, api_numElements_n_ = _ivectorint(numElements)
            api_heights_, api_heights_n_ = _ivectordouble(heights)
            ierr = c_int()
            lib.gmshModelOccRevolve(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(ax),
                c_double(ay),
                c_double(az),
                c_double(angle),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_numElements_, api_numElements_n_,
                api_heights_, api_heights_n_,
                c_int(bool(recombine)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def addPipe(dimTags, wireTag, trihedron=""):
            """
            gmsh.model.occ.addPipe(dimTags, wireTag, trihedron="")

            Add a pipe in the OpenCASCADE CAD representation, by extruding the entities
            `dimTags' (given as a vector of (dim, tag) pairs) along the wire `wireTag'.
            The type of sweep can be specified with `trihedron' (possible values:
            "DiscreteTrihedron", "CorrectedFrenet", "Fixed", "Frenet",
            "ConstantNormal", "Darboux", "GuideAC", "GuidePlan", "GuideACWithContact",
            "GuidePlanWithContact"). If `trihedron' is not provided,
            "DiscreteTrihedron" is assumed. Return the pipe in `outDimTags'.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `wireTag': integer
            - `outDimTags': vector of pairs of integers
            - `trihedron': string
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccAddPipe(
                api_dimTags_, api_dimTags_n_,
                c_int(wireTag),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_char_p(trihedron.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        add_pipe = addPipe

        @staticmethod
        def fillet(volumeTags, curveTags, radii, removeVolume=True):
            """
            gmsh.model.occ.fillet(volumeTags, curveTags, radii, removeVolume=True)

            Fillet the volumes `volumeTags' on the curves `curveTags' with radii
            `radii'. The `radii' vector can either contain a single radius, as many
            radii as `curveTags', or twice as many as `curveTags' (in which case
            different radii are provided for the begin and end points of the curves).
            Return the filleted entities in `outDimTags' as a vector of (dim, tag)
            pairs. Remove the original volume if `removeVolume' is set.

            Return `outDimTags'.

            Types:
            - `volumeTags': vector of integers
            - `curveTags': vector of integers
            - `radii': vector of doubles
            - `outDimTags': vector of pairs of integers
            - `removeVolume': boolean
            """
            api_volumeTags_, api_volumeTags_n_ = _ivectorint(volumeTags)
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            api_radii_, api_radii_n_ = _ivectordouble(radii)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccFillet(
                api_volumeTags_, api_volumeTags_n_,
                api_curveTags_, api_curveTags_n_,
                api_radii_, api_radii_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(bool(removeVolume)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def chamfer(volumeTags, curveTags, surfaceTags, distances, removeVolume=True):
            """
            gmsh.model.occ.chamfer(volumeTags, curveTags, surfaceTags, distances, removeVolume=True)

            Chamfer the volumes `volumeTags' on the curves `curveTags' with distances
            `distances' measured on surfaces `surfaceTags'. The `distances' vector can
            either contain a single distance, as many distances as `curveTags' and
            `surfaceTags', or twice as many as `curveTags' and `surfaceTags' (in which
            case the first in each pair is measured on the corresponding surface in
            `surfaceTags', the other on the other adjacent surface). Return the
            chamfered entities in `outDimTags'. Remove the original volume if
            `removeVolume' is set.

            Return `outDimTags'.

            Types:
            - `volumeTags': vector of integers
            - `curveTags': vector of integers
            - `surfaceTags': vector of integers
            - `distances': vector of doubles
            - `outDimTags': vector of pairs of integers
            - `removeVolume': boolean
            """
            api_volumeTags_, api_volumeTags_n_ = _ivectorint(volumeTags)
            api_curveTags_, api_curveTags_n_ = _ivectorint(curveTags)
            api_surfaceTags_, api_surfaceTags_n_ = _ivectorint(surfaceTags)
            api_distances_, api_distances_n_ = _ivectordouble(distances)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccChamfer(
                api_volumeTags_, api_volumeTags_n_,
                api_curveTags_, api_curveTags_n_,
                api_surfaceTags_, api_surfaceTags_n_,
                api_distances_, api_distances_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(bool(removeVolume)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def defeature(volumeTags, surfaceTags, removeVolume=True):
            """
            gmsh.model.occ.defeature(volumeTags, surfaceTags, removeVolume=True)

            Defeature the volumes `volumeTags' by removing the surfaces `surfaceTags'.
            Return the defeatured entities in `outDimTags'. Remove the original volume
            if `removeVolume' is set.

            Return `outDimTags'.

            Types:
            - `volumeTags': vector of integers
            - `surfaceTags': vector of integers
            - `outDimTags': vector of pairs of integers
            - `removeVolume': boolean
            """
            api_volumeTags_, api_volumeTags_n_ = _ivectorint(volumeTags)
            api_surfaceTags_, api_surfaceTags_n_ = _ivectorint(surfaceTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccDefeature(
                api_volumeTags_, api_volumeTags_n_,
                api_surfaceTags_, api_surfaceTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(bool(removeVolume)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def fillet2D(edgeTag1, edgeTag2, radius, tag=-1, pointTag=-1, reverse=False):
            """
            gmsh.model.occ.fillet2D(edgeTag1, edgeTag2, radius, tag=-1, pointTag=-1, reverse=False)

            Create a fillet edge between edges `edgeTag1' and `edgeTag2' with radius
            `radius'. The modifed edges keep their tag. If `tag' is positive, set the
            tag explicitly; otherwise a new tag is selected automatically. If
            `pointTag' is positive, set the point on the edge at which the fillet is
            created. If `reverse' is set, the normal of the plane through the two
            planes is reversed before the fillet is created.

            Return an integer.

            Types:
            - `edgeTag1': integer
            - `edgeTag2': integer
            - `radius': double
            - `tag': integer
            - `pointTag': integer
            - `reverse': boolean
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccFillet2D(
                c_int(edgeTag1),
                c_int(edgeTag2),
                c_double(radius),
                c_int(tag),
                c_int(pointTag),
                c_int(bool(reverse)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        fillet2_d = fillet2D

        @staticmethod
        def chamfer2D(edgeTag1, edgeTag2, distance1, distance2, tag=-1):
            """
            gmsh.model.occ.chamfer2D(edgeTag1, edgeTag2, distance1, distance2, tag=-1)

            Create a chamfer edge between edges `edgeTag1' and `edgeTag2' with
            distance1 `distance1' and distance2 `distance2'. The modifed edges keep
            their tag. If `tag' is positive, set the tag explicitly; otherwise a new
            tag is selected automatically.

            Return an integer.

            Types:
            - `edgeTag1': integer
            - `edgeTag2': integer
            - `distance1': double
            - `distance2': double
            - `tag': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccChamfer2D(
                c_int(edgeTag1),
                c_int(edgeTag2),
                c_double(distance1),
                c_double(distance2),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        chamfer2_d = chamfer2D

        @staticmethod
        def offsetCurve(curveLoopTag, offset):
            """
            gmsh.model.occ.offsetCurve(curveLoopTag, offset)

            Create an offset curve based on the curve loop `curveLoopTag' with offset
            `offset'. Return the offset curves in `outDimTags' as a vector of (dim,
            tag) pairs.

            Return `outDimTags'.

            Types:
            - `curveLoopTag': integer
            - `offset': double
            - `outDimTags': vector of pairs of integers
            """
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccOffsetCurve(
                c_int(curveLoopTag),
                c_double(offset),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        offset_curve = offsetCurve

        @staticmethod
        def getDistance(dim1, tag1, dim2, tag2):
            """
            gmsh.model.occ.getDistance(dim1, tag1, dim2, tag2)

            Find the minimal distance between shape with `dim1' and `tag1' and shape
            with `dim2' and `tag2' and the according coordinates. Return the distance
            in `distance' and the coordinate of the points as `x1', `y1', `z1' and
            `x2', `y2', `z2'.

            Return `distance', `x1', `y1', `z1', `x2', `y2', `z2'.

            Types:
            - `dim1': integer
            - `tag1': integer
            - `dim2': integer
            - `tag2': integer
            - `distance': double
            - `x1': double
            - `y1': double
            - `z1': double
            - `x2': double
            - `y2': double
            - `z2': double
            """
            api_distance_ = c_double()
            api_x1_ = c_double()
            api_y1_ = c_double()
            api_z1_ = c_double()
            api_x2_ = c_double()
            api_y2_ = c_double()
            api_z2_ = c_double()
            ierr = c_int()
            lib.gmshModelOccGetDistance(
                c_int(dim1),
                c_int(tag1),
                c_int(dim2),
                c_int(tag2),
                byref(api_distance_),
                byref(api_x1_),
                byref(api_y1_),
                byref(api_z1_),
                byref(api_x2_),
                byref(api_y2_),
                byref(api_z2_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_distance_.value,
                api_x1_.value,
                api_y1_.value,
                api_z1_.value,
                api_x2_.value,
                api_y2_.value,
                api_z2_.value)
        get_distance = getDistance

        @staticmethod
        def fuse(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True):
            """
            gmsh.model.occ.fuse(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True)

            Compute the boolean union (the fusion) of the entities `objectDimTags' and
            `toolDimTags' (vectors of (dim, tag) pairs) in the OpenCASCADE CAD
            representation. Return the resulting entities in `outDimTags', and the
            correspondance between input and resulting entities in `outDimTagsMap'. If
            `tag' is positive, try to set the tag explicitly (only valid if the boolean
            operation results in a single entity). Remove the object if `removeObject'
            is set. Remove the tool if `removeTool' is set.

            Return `outDimTags', `outDimTagsMap'.

            Types:
            - `objectDimTags': vector of pairs of integers
            - `toolDimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            - `outDimTagsMap': vector of vectors of pairs of integers
            - `tag': integer
            - `removeObject': boolean
            - `removeTool': boolean
            """
            api_objectDimTags_, api_objectDimTags_n_ = _ivectorpair(objectDimTags)
            api_toolDimTags_, api_toolDimTags_n_ = _ivectorpair(toolDimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccFuse(
                api_objectDimTags_, api_objectDimTags_n_,
                api_toolDimTags_, api_toolDimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(api_outDimTagsMap_), byref(api_outDimTagsMap_n_), byref(api_outDimTagsMap_nn_),
                c_int(tag),
                c_int(bool(removeObject)),
                c_int(bool(removeTool)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorpair(api_outDimTags_, api_outDimTags_n_.value),
                _ovectorvectorpair(api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_))

        @staticmethod
        def intersect(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True):
            """
            gmsh.model.occ.intersect(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True)

            Compute the boolean intersection (the common parts) of the entities
            `objectDimTags' and `toolDimTags' (vectors of (dim, tag) pairs) in the
            OpenCASCADE CAD representation. Return the resulting entities in
            `outDimTags', and the correspondance between input and resulting entities
            in `outDimTagsMap'. If `tag' is positive, try to set the tag explicitly
            (only valid if the boolean operation results in a single entity). Remove
            the object if `removeObject' is set. Remove the tool if `removeTool' is
            set.

            Return `outDimTags', `outDimTagsMap'.

            Types:
            - `objectDimTags': vector of pairs of integers
            - `toolDimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            - `outDimTagsMap': vector of vectors of pairs of integers
            - `tag': integer
            - `removeObject': boolean
            - `removeTool': boolean
            """
            api_objectDimTags_, api_objectDimTags_n_ = _ivectorpair(objectDimTags)
            api_toolDimTags_, api_toolDimTags_n_ = _ivectorpair(toolDimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccIntersect(
                api_objectDimTags_, api_objectDimTags_n_,
                api_toolDimTags_, api_toolDimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(api_outDimTagsMap_), byref(api_outDimTagsMap_n_), byref(api_outDimTagsMap_nn_),
                c_int(tag),
                c_int(bool(removeObject)),
                c_int(bool(removeTool)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorpair(api_outDimTags_, api_outDimTags_n_.value),
                _ovectorvectorpair(api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_))

        @staticmethod
        def cut(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True):
            """
            gmsh.model.occ.cut(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True)

            Compute the boolean difference between the entities `objectDimTags' and
            `toolDimTags' (given as vectors of (dim, tag) pairs) in the OpenCASCADE CAD
            representation. Return the resulting entities in `outDimTags', and the
            correspondance between input and resulting entities in `outDimTagsMap'. If
            `tag' is positive, try to set the tag explicitly (only valid if the boolean
            operation results in a single entity). Remove the object if `removeObject'
            is set. Remove the tool if `removeTool' is set.

            Return `outDimTags', `outDimTagsMap'.

            Types:
            - `objectDimTags': vector of pairs of integers
            - `toolDimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            - `outDimTagsMap': vector of vectors of pairs of integers
            - `tag': integer
            - `removeObject': boolean
            - `removeTool': boolean
            """
            api_objectDimTags_, api_objectDimTags_n_ = _ivectorpair(objectDimTags)
            api_toolDimTags_, api_toolDimTags_n_ = _ivectorpair(toolDimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccCut(
                api_objectDimTags_, api_objectDimTags_n_,
                api_toolDimTags_, api_toolDimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(api_outDimTagsMap_), byref(api_outDimTagsMap_n_), byref(api_outDimTagsMap_nn_),
                c_int(tag),
                c_int(bool(removeObject)),
                c_int(bool(removeTool)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorpair(api_outDimTags_, api_outDimTags_n_.value),
                _ovectorvectorpair(api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_))

        @staticmethod
        def fragment(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True):
            """
            gmsh.model.occ.fragment(objectDimTags, toolDimTags, tag=-1, removeObject=True, removeTool=True)

            Compute the boolean fragments (general fuse) resulting from the
            intersection of the entities `objectDimTags' and `toolDimTags' (given as
            vectors of (dim, tag) pairs) in the OpenCASCADE CAD representation, making
            all interfaces conformal. When applied to entities of different dimensions,
            the lower dimensional entities will be automatically embedded in the higher
            dimensional entities if they are not on their boundary. Return the
            resulting entities in `outDimTags', and the correspondance between input
            and resulting entities in `outDimTagsMap'. If `tag' is positive, try to set
            the tag explicitly (only valid if the boolean operation results in a single
            entity). Remove the object if `removeObject' is set. Remove the tool if
            `removeTool' is set.

            Return `outDimTags', `outDimTagsMap'.

            Types:
            - `objectDimTags': vector of pairs of integers
            - `toolDimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            - `outDimTagsMap': vector of vectors of pairs of integers
            - `tag': integer
            - `removeObject': boolean
            - `removeTool': boolean
            """
            api_objectDimTags_, api_objectDimTags_n_ = _ivectorpair(objectDimTags)
            api_toolDimTags_, api_toolDimTags_n_ = _ivectorpair(toolDimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccFragment(
                api_objectDimTags_, api_objectDimTags_n_,
                api_toolDimTags_, api_toolDimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(api_outDimTagsMap_), byref(api_outDimTagsMap_n_), byref(api_outDimTagsMap_nn_),
                c_int(tag),
                c_int(bool(removeObject)),
                c_int(bool(removeTool)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorpair(api_outDimTags_, api_outDimTags_n_.value),
                _ovectorvectorpair(api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_))

        @staticmethod
        def translate(dimTags, dx, dy, dz):
            """
            gmsh.model.occ.translate(dimTags, dx, dy, dz)

            Translate the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation along (`dx', `dy', `dz').

            Types:
            - `dimTags': vector of pairs of integers
            - `dx': double
            - `dy': double
            - `dz': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccTranslate(
                api_dimTags_, api_dimTags_n_,
                c_double(dx),
                c_double(dy),
                c_double(dz),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def rotate(dimTags, x, y, z, ax, ay, az, angle):
            """
            gmsh.model.occ.rotate(dimTags, x, y, z, ax, ay, az, angle)

            Rotate the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation by `angle' radians around the axis of
            revolution defined by the point (`x', `y', `z') and the direction (`ax',
            `ay', `az').

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `ax': double
            - `ay': double
            - `az': double
            - `angle': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccRotate(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(ax),
                c_double(ay),
                c_double(az),
                c_double(angle),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def dilate(dimTags, x, y, z, a, b, c):
            """
            gmsh.model.occ.dilate(dimTags, x, y, z, a, b, c)

            Scale the entities `dimTags' (given as a vector of (dim, tag) pairs) in the
            OpenCASCADE CAD representation by factors `a', `b' and `c' along the three
            coordinate axes; use (`x', `y', `z') as the center of the homothetic
            transformation.

            Types:
            - `dimTags': vector of pairs of integers
            - `x': double
            - `y': double
            - `z': double
            - `a': double
            - `b': double
            - `c': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccDilate(
                api_dimTags_, api_dimTags_n_,
                c_double(x),
                c_double(y),
                c_double(z),
                c_double(a),
                c_double(b),
                c_double(c),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def mirror(dimTags, a, b, c, d):
            """
            gmsh.model.occ.mirror(dimTags, a, b, c, d)

            Mirror the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation, with respect to the plane of equation
            `a' * x + `b' * y + `c' * z + `d' = 0.

            Types:
            - `dimTags': vector of pairs of integers
            - `a': double
            - `b': double
            - `c': double
            - `d': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccMirror(
                api_dimTags_, api_dimTags_n_,
                c_double(a),
                c_double(b),
                c_double(c),
                c_double(d),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def symmetrize(dimTags, a, b, c, d):
            """
            gmsh.model.occ.symmetrize(dimTags, a, b, c, d)

            Mirror the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation, with respect to the plane of equation
            `a' * x + `b' * y + `c' * z + `d' = 0. (This is a deprecated synonym for
            `mirror'.)

            Types:
            - `dimTags': vector of pairs of integers
            - `a': double
            - `b': double
            - `c': double
            - `d': double
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccSymmetrize(
                api_dimTags_, api_dimTags_n_,
                c_double(a),
                c_double(b),
                c_double(c),
                c_double(d),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def affineTransform(dimTags, affineTransform):
            """
            gmsh.model.occ.affineTransform(dimTags, affineTransform)

            Apply a general affine transformation matrix `affineTransform' (16 entries
            of a 4x4 matrix, by row; only the 12 first can be provided for convenience)
            to the entities `dimTags' (given as a vector of (dim, tag) pairs) in the
            OpenCASCADE CAD representation.

            Types:
            - `dimTags': vector of pairs of integers
            - `affineTransform': vector of doubles
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_affineTransform_, api_affineTransform_n_ = _ivectordouble(affineTransform)
            ierr = c_int()
            lib.gmshModelOccAffineTransform(
                api_dimTags_, api_dimTags_n_,
                api_affineTransform_, api_affineTransform_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        affine_transform = affineTransform

        @staticmethod
        def copy(dimTags):
            """
            gmsh.model.occ.copy(dimTags)

            Copy the entities `dimTags' in the OpenCASCADE CAD representation; the new
            entities are returned in `outDimTags'.

            Return `outDimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `outDimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccCopy(
                api_dimTags_, api_dimTags_n_,
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)

        @staticmethod
        def remove(dimTags, recursive=False):
            """
            gmsh.model.occ.remove(dimTags, recursive=False)

            Remove the entities `dimTags' (given as a vector of (dim, tag) pairs) in
            the OpenCASCADE CAD representation, provided that they are not on the
            boundary of higher-dimensional entities. If `recursive' is true, remove all
            the entities on their boundaries, down to dimension 0.

            Types:
            - `dimTags': vector of pairs of integers
            - `recursive': boolean
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccRemove(
                api_dimTags_, api_dimTags_n_,
                c_int(bool(recursive)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())

        @staticmethod
        def removeAllDuplicates():
            """
            gmsh.model.occ.removeAllDuplicates()

            Remove all duplicate entities in the OpenCASCADE CAD representation
            (different entities at the same geometrical location) after intersecting
            (using boolean fragments) all highest dimensional entities.
            """
            ierr = c_int()
            lib.gmshModelOccRemoveAllDuplicates(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        remove_all_duplicates = removeAllDuplicates

        @staticmethod
        def healShapes(dimTags=[], tolerance=1e-8, fixDegenerated=True, fixSmallEdges=True, fixSmallFaces=True, sewFaces=True, makeSolids=True):
            """
            gmsh.model.occ.healShapes(dimTags=[], tolerance=1e-8, fixDegenerated=True, fixSmallEdges=True, fixSmallFaces=True, sewFaces=True, makeSolids=True)

            Apply various healing procedures to the entities `dimTags' (given as a
            vector of (dim, tag) pairs), or to all the entities in the model if
            `dimTags' is empty, in the OpenCASCADE CAD representation. Return the
            healed entities in `outDimTags'.

            Return `outDimTags'.

            Types:
            - `outDimTags': vector of pairs of integers
            - `dimTags': vector of pairs of integers
            - `tolerance': double
            - `fixDegenerated': boolean
            - `fixSmallEdges': boolean
            - `fixSmallFaces': boolean
            - `sewFaces': boolean
            - `makeSolids': boolean
            """
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccHealShapes(
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                api_dimTags_, api_dimTags_n_,
                c_double(tolerance),
                c_int(bool(fixDegenerated)),
                c_int(bool(fixSmallEdges)),
                c_int(bool(fixSmallFaces)),
                c_int(bool(sewFaces)),
                c_int(bool(makeSolids)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        heal_shapes = healShapes

        @staticmethod
        def convertToNURBS(dimTags):
            """
            gmsh.model.occ.convertToNURBS(dimTags)

            Convert the entities `dimTags' to NURBS.

            Types:
            - `dimTags': vector of pairs of integers
            """
            api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
            ierr = c_int()
            lib.gmshModelOccConvertToNURBS(
                api_dimTags_, api_dimTags_n_,
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        convert_to_nurbs = convertToNURBS

        @staticmethod
        def importShapes(fileName, highestDimOnly=True, format=""):
            """
            gmsh.model.occ.importShapes(fileName, highestDimOnly=True, format="")

            Import BREP, STEP or IGES shapes from the file `fileName' in the
            OpenCASCADE CAD representation. The imported entities are returned in
            `outDimTags', as a vector of (dim, tag) pairs. If the optional argument
            `highestDimOnly' is set, only import the highest dimensional entities in
            the file. The optional argument `format' can be used to force the format of
            the file (currently "brep", "step" or "iges").

            Return `outDimTags'.

            Types:
            - `fileName': string
            - `outDimTags': vector of pairs of integers
            - `highestDimOnly': boolean
            - `format': string
            """
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccImportShapes(
                c_char_p(fileName.encode()),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(bool(highestDimOnly)),
                c_char_p(format.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        import_shapes = importShapes

        @staticmethod
        def importShapesNativePointer(shape, highestDimOnly=True):
            """
            gmsh.model.occ.importShapesNativePointer(shape, highestDimOnly=True)

            Import an OpenCASCADE `shape' by providing a pointer to a native
            OpenCASCADE `TopoDS_Shape' object (passed as a pointer to void). The
            imported entities are returned in `outDimTags' as a vector of (dim, tag)
            pairs. If the optional argument `highestDimOnly' is set, only import the
            highest dimensional entities in `shape'. In Python, this function can be
            used for integration with PythonOCC, in which the SwigPyObject pointer of
            `TopoDS_Shape' must be passed as an int to `shape', i.e., `shape =
            int(pythonocc_shape.this)'. Warning: this function is unsafe, as providing
            an invalid pointer will lead to undefined behavior.

            Return `outDimTags'.

            Types:
            - `shape': pointer
            - `outDimTags': vector of pairs of integers
            - `highestDimOnly': boolean
            """
            api_outDimTags_, api_outDimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccImportShapesNativePointer(
                c_void_p(shape),
                byref(api_outDimTags_), byref(api_outDimTags_n_),
                c_int(bool(highestDimOnly)),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_outDimTags_, api_outDimTags_n_.value)
        import_shapes_native_pointer = importShapesNativePointer

        @staticmethod
        def getEntities(dim=-1):
            """
            gmsh.model.occ.getEntities(dim=-1)

            Get all the OpenCASCADE entities. If `dim' is >= 0, return only the
            entities of the specified dimension (e.g. points if `dim' == 0). The
            entities are returned as a vector of (dim, tag) pairs.

            Return `dimTags'.

            Types:
            - `dimTags': vector of pairs of integers
            - `dim': integer
            """
            api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccGetEntities(
                byref(api_dimTags_), byref(api_dimTags_n_),
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
        get_entities = getEntities

        @staticmethod
        def getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim=-1):
            """
            gmsh.model.occ.getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim=-1)

            Get the OpenCASCADE entities in the bounding box defined by the two points
            (`xmin', `ymin', `zmin') and (`xmax', `ymax', `zmax'). If `dim' is >= 0,
            return only the entities of the specified dimension (e.g. points if `dim'
            == 0).

            Return `dimTags'.

            Types:
            - `xmin': double
            - `ymin': double
            - `zmin': double
            - `xmax': double
            - `ymax': double
            - `zmax': double
            - `dimTags': vector of pairs of integers
            - `dim': integer
            """
            api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccGetEntitiesInBoundingBox(
                c_double(xmin),
                c_double(ymin),
                c_double(zmin),
                c_double(xmax),
                c_double(ymax),
                c_double(zmax),
                byref(api_dimTags_), byref(api_dimTags_n_),
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectorpair(api_dimTags_, api_dimTags_n_.value)
        get_entities_in_bounding_box = getEntitiesInBoundingBox

        @staticmethod
        def getBoundingBox(dim, tag):
            """
            gmsh.model.occ.getBoundingBox(dim, tag)

            Get the bounding box (`xmin', `ymin', `zmin'), (`xmax', `ymax', `zmax') of
            the OpenCASCADE entity of dimension `dim' and tag `tag'.

            Return `xmin', `ymin', `zmin', `xmax', `ymax', `zmax'.

            Types:
            - `dim': integer
            - `tag': integer
            - `xmin': double
            - `ymin': double
            - `zmin': double
            - `xmax': double
            - `ymax': double
            - `zmax': double
            """
            api_xmin_ = c_double()
            api_ymin_ = c_double()
            api_zmin_ = c_double()
            api_xmax_ = c_double()
            api_ymax_ = c_double()
            api_zmax_ = c_double()
            ierr = c_int()
            lib.gmshModelOccGetBoundingBox(
                c_int(dim),
                c_int(tag),
                byref(api_xmin_),
                byref(api_ymin_),
                byref(api_zmin_),
                byref(api_xmax_),
                byref(api_ymax_),
                byref(api_zmax_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_xmin_.value,
                api_ymin_.value,
                api_zmin_.value,
                api_xmax_.value,
                api_ymax_.value,
                api_zmax_.value)
        get_bounding_box = getBoundingBox

        @staticmethod
        def getCurveLoops(surfaceTag):
            """
            gmsh.model.occ.getCurveLoops(surfaceTag)

            Get the tags `curveLoopTags' of the curve loops making up the surface of
            tag `surfaceTag', as well as the tags `curveTags' of the curves making up
            each curve loop.

            Return `curveLoopTags', `curveTags'.

            Types:
            - `surfaceTag': integer
            - `curveLoopTags': vector of integers
            - `curveTags': vector of vectors of integers
            """
            api_curveLoopTags_, api_curveLoopTags_n_ = POINTER(c_int)(), c_size_t()
            api_curveTags_, api_curveTags_n_, api_curveTags_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccGetCurveLoops(
                c_int(surfaceTag),
                byref(api_curveLoopTags_), byref(api_curveLoopTags_n_),
                byref(api_curveTags_), byref(api_curveTags_n_), byref(api_curveTags_nn_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorint(api_curveLoopTags_, api_curveLoopTags_n_.value),
                _ovectorvectorint(api_curveTags_, api_curveTags_n_, api_curveTags_nn_))
        get_curve_loops = getCurveLoops

        @staticmethod
        def getSurfaceLoops(volumeTag):
            """
            gmsh.model.occ.getSurfaceLoops(volumeTag)

            Get the tags `surfaceLoopTags' of the surface loops making up the volume of
            tag `volumeTag', as well as the tags `surfaceTags' of the surfaces making
            up each surface loop.

            Return `surfaceLoopTags', `surfaceTags'.

            Types:
            - `volumeTag': integer
            - `surfaceLoopTags': vector of integers
            - `surfaceTags': vector of vectors of integers
            """
            api_surfaceLoopTags_, api_surfaceLoopTags_n_ = POINTER(c_int)(), c_size_t()
            api_surfaceTags_, api_surfaceTags_n_, api_surfaceTags_nn_ = POINTER(POINTER(c_int))(), POINTER(c_size_t)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccGetSurfaceLoops(
                c_int(volumeTag),
                byref(api_surfaceLoopTags_), byref(api_surfaceLoopTags_n_),
                byref(api_surfaceTags_), byref(api_surfaceTags_n_), byref(api_surfaceTags_nn_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                _ovectorint(api_surfaceLoopTags_, api_surfaceLoopTags_n_.value),
                _ovectorvectorint(api_surfaceTags_, api_surfaceTags_n_, api_surfaceTags_nn_))
        get_surface_loops = getSurfaceLoops

        @staticmethod
        def getMass(dim, tag):
            """
            gmsh.model.occ.getMass(dim, tag)

            Get the mass of the OpenCASCADE entity of dimension `dim' and tag `tag'. If
            no density is attached to the entity (the default), the value corresponds
            respectively to the length, area and volume for `dim' = 1, 2 and 3.

            Return `mass'.

            Types:
            - `dim': integer
            - `tag': integer
            - `mass': double
            """
            api_mass_ = c_double()
            ierr = c_int()
            lib.gmshModelOccGetMass(
                c_int(dim),
                c_int(tag),
                byref(api_mass_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_mass_.value
        get_mass = getMass

        @staticmethod
        def getCenterOfMass(dim, tag):
            """
            gmsh.model.occ.getCenterOfMass(dim, tag)

            Get the center of mass of the OpenCASCADE entity of dimension `dim' and tag
            `tag'.

            Return `x', `y', `z'.

            Types:
            - `dim': integer
            - `tag': integer
            - `x': double
            - `y': double
            - `z': double
            """
            api_x_ = c_double()
            api_y_ = c_double()
            api_z_ = c_double()
            ierr = c_int()
            lib.gmshModelOccGetCenterOfMass(
                c_int(dim),
                c_int(tag),
                byref(api_x_),
                byref(api_y_),
                byref(api_z_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_x_.value,
                api_y_.value,
                api_z_.value)
        get_center_of_mass = getCenterOfMass

        @staticmethod
        def getMatrixOfInertia(dim, tag):
            """
            gmsh.model.occ.getMatrixOfInertia(dim, tag)

            Get the matrix of inertia (by row) of the OpenCASCADE entity of dimension
            `dim' and tag `tag'.

            Return `mat'.

            Types:
            - `dim': integer
            - `tag': integer
            - `mat': vector of doubles
            """
            api_mat_, api_mat_n_ = POINTER(c_double)(), c_size_t()
            ierr = c_int()
            lib.gmshModelOccGetMatrixOfInertia(
                c_int(dim),
                c_int(tag),
                byref(api_mat_), byref(api_mat_n_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ovectordouble(api_mat_, api_mat_n_.value)
        get_matrix_of_inertia = getMatrixOfInertia

        @staticmethod
        def getMaxTag(dim):
            """
            gmsh.model.occ.getMaxTag(dim)

            Get the maximum tag of entities of dimension `dim' in the OpenCASCADE CAD
            representation.

            Return an integer.

            Types:
            - `dim': integer
            """
            ierr = c_int()
            api_result_ = lib.gmshModelOccGetMaxTag(
                c_int(dim),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_result_
        get_max_tag = getMaxTag

        @staticmethod
        def setMaxTag(dim, maxTag):
            """
            gmsh.model.occ.setMaxTag(dim, maxTag)

            Set the maximum tag `maxTag' for entities of dimension `dim' in the
            OpenCASCADE CAD representation.

            Types:
            - `dim': integer
            - `maxTag': integer
            """
            ierr = c_int()
            lib.gmshModelOccSetMaxTag(
                c_int(dim),
                c_int(maxTag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_max_tag = setMaxTag

        @staticmethod
        def synchronize():
            """
            gmsh.model.occ.synchronize()

            Synchronize the OpenCASCADE CAD representation with the current Gmsh model.
            This can be called at any time, but since it involves a non trivial amount
            of processing, the number of synchronization points should normally be
            minimized. Without synchronization the entities in the OpenCASCADE CAD
            representation are not available to any function outside of the OpenCASCADE
            CAD kernel functions.
            """
            ierr = c_int()
            lib.gmshModelOccSynchronize(
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())


        class mesh:
            """
            OpenCASCADE CAD kernel meshing constraints
            """

            @staticmethod
            def setSize(dimTags, size):
                """
                gmsh.model.occ.mesh.setSize(dimTags, size)

                Set a mesh size constraint on the entities `dimTags' (given as a vector of
                (dim, tag) pairs) in the OpenCASCADE CAD representation. Currently only
                entities of dimension 0 (points) are handled.

                Types:
                - `dimTags': vector of pairs of integers
                - `size': double
                """
                api_dimTags_, api_dimTags_n_ = _ivectorpair(dimTags)
                ierr = c_int()
                lib.gmshModelOccMeshSetSize(
                    api_dimTags_, api_dimTags_n_,
                    c_double(size),
                    byref(ierr))
                if ierr.value != 0:
                    raise Exception(logger.getLastError())
            set_size = setSize


class view:
    """
    Post-processing view functions
    """

    @staticmethod
    def add(name, tag=-1):
        """
        gmsh.view.add(name, tag=-1)

        Add a new post-processing view, with name `name'. If `tag' is positive use
        it (and remove the view with that tag if it already exists), otherwise
        associate a new tag. Return the view tag.

        Return an integer.

        Types:
        - `name': string
        - `tag': integer
        """
        ierr = c_int()
        api_result_ = lib.gmshViewAdd(
            c_char_p(name.encode()),
            c_int(tag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_

    @staticmethod
    def remove(tag):
        """
        gmsh.view.remove(tag)

        Remove the view with tag `tag'.

        Types:
        - `tag': integer
        """
        ierr = c_int()
        lib.gmshViewRemove(
            c_int(tag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def getIndex(tag):
        """
        gmsh.view.getIndex(tag)

        Get the index of the view with tag `tag' in the list of currently loaded
        views. This dynamic index (it can change when views are removed) is used to
        access view options.

        Return an integer.

        Types:
        - `tag': integer
        """
        ierr = c_int()
        api_result_ = lib.gmshViewGetIndex(
            c_int(tag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_index = getIndex

    @staticmethod
    def getTags():
        """
        gmsh.view.getTags()

        Get the tags of all views.

        Return `tags'.

        Types:
        - `tags': vector of integers
        """
        api_tags_, api_tags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        lib.gmshViewGetTags(
            byref(api_tags_), byref(api_tags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorint(api_tags_, api_tags_n_.value)
    get_tags = getTags

    @staticmethod
    def addModelData(tag, step, modelName, dataType, tags, data, time=0., numComponents=-1, partition=0):
        """
        gmsh.view.addModelData(tag, step, modelName, dataType, tags, data, time=0., numComponents=-1, partition=0)

        Add model-based post-processing data to the view with tag `tag'.
        `modelName' identifies the model the data is attached to. `dataType'
        specifies the type of data, currently either "NodeData", "ElementData" or
        "ElementNodeData". `step' specifies the identifier (>= 0) of the data in a
        sequence. `tags' gives the tags of the nodes or elements in the mesh to
        which the data is associated. `data' is a vector of the same length as
        `tags': each entry is the vector of double precision numbers representing
        the data associated with the corresponding tag. The optional `time'
        argument associate a time value with the data. `numComponents' gives the
        number of data components (1 for scalar data, 3 for vector data, etc.) per
        entity; if negative, it is automatically inferred (when possible) from the
        input data. `partition' allows one to specify data in several sub-sets.

        Types:
        - `tag': integer
        - `step': integer
        - `modelName': string
        - `dataType': string
        - `tags': vector of sizes
        - `data': vector of vectors of doubles
        - `time': double
        - `numComponents': integer
        - `partition': integer
        """
        api_tags_, api_tags_n_ = _ivectorsize(tags)
        api_data_, api_data_n_, api_data_nn_ = _ivectorvectordouble(data)
        ierr = c_int()
        lib.gmshViewAddModelData(
            c_int(tag),
            c_int(step),
            c_char_p(modelName.encode()),
            c_char_p(dataType.encode()),
            api_tags_, api_tags_n_,
            api_data_, api_data_n_, api_data_nn_,
            c_double(time),
            c_int(numComponents),
            c_int(partition),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    add_model_data = addModelData

    @staticmethod
    def addHomogeneousModelData(tag, step, modelName, dataType, tags, data, time=0., numComponents=-1, partition=0):
        """
        gmsh.view.addHomogeneousModelData(tag, step, modelName, dataType, tags, data, time=0., numComponents=-1, partition=0)

        Add homogeneous model-based post-processing data to the view with tag
        `tag'. The arguments have the same meaning as in `addModelData', except
        that `data' is supposed to be homogeneous and is thus flattened in a single
        vector. For data types that can lead to different data sizes per tag (like
        "ElementNodeData"), the data should be padded.

        Types:
        - `tag': integer
        - `step': integer
        - `modelName': string
        - `dataType': string
        - `tags': vector of sizes
        - `data': vector of doubles
        - `time': double
        - `numComponents': integer
        - `partition': integer
        """
        api_tags_, api_tags_n_ = _ivectorsize(tags)
        api_data_, api_data_n_ = _ivectordouble(data)
        ierr = c_int()
        lib.gmshViewAddHomogeneousModelData(
            c_int(tag),
            c_int(step),
            c_char_p(modelName.encode()),
            c_char_p(dataType.encode()),
            api_tags_, api_tags_n_,
            api_data_, api_data_n_,
            c_double(time),
            c_int(numComponents),
            c_int(partition),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    add_homogeneous_model_data = addHomogeneousModelData

    @staticmethod
    def getModelData(tag, step):
        """
        gmsh.view.getModelData(tag, step)

        Get model-based post-processing data from the view with tag `tag' at step
        `step'. Return the `data' associated to the nodes or the elements with tags
        `tags', as well as the `dataType' and the number of components
        `numComponents'.

        Return `dataType', `tags', `data', `time', `numComponents'.

        Types:
        - `tag': integer
        - `step': integer
        - `dataType': string
        - `tags': vector of sizes
        - `data': vector of vectors of doubles
        - `time': double
        - `numComponents': integer
        """
        api_dataType_ = c_char_p()
        api_tags_, api_tags_n_ = POINTER(c_size_t)(), c_size_t()
        api_data_, api_data_n_, api_data_nn_ = POINTER(POINTER(c_double))(), POINTER(c_size_t)(), c_size_t()
        api_time_ = c_double()
        api_numComponents_ = c_int()
        ierr = c_int()
        lib.gmshViewGetModelData(
            c_int(tag),
            c_int(step),
            byref(api_dataType_),
            byref(api_tags_), byref(api_tags_n_),
            byref(api_data_), byref(api_data_n_), byref(api_data_nn_),
            byref(api_time_),
            byref(api_numComponents_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ostring(api_dataType_),
            _ovectorsize(api_tags_, api_tags_n_.value),
            _ovectorvectordouble(api_data_, api_data_n_, api_data_nn_),
            api_time_.value,
            api_numComponents_.value)
    get_model_data = getModelData

    @staticmethod
    def getHomogeneousModelData(tag, step):
        """
        gmsh.view.getHomogeneousModelData(tag, step)

        Get homogeneous model-based post-processing data from the view with tag
        `tag' at step `step'. The arguments have the same meaning as in
        `getModelData', except that `data' is returned flattened in a single
        vector, with the appropriate padding if necessary.

        Return `dataType', `tags', `data', `time', `numComponents'.

        Types:
        - `tag': integer
        - `step': integer
        - `dataType': string
        - `tags': vector of sizes
        - `data': vector of doubles
        - `time': double
        - `numComponents': integer
        """
        api_dataType_ = c_char_p()
        api_tags_, api_tags_n_ = POINTER(c_size_t)(), c_size_t()
        api_data_, api_data_n_ = POINTER(c_double)(), c_size_t()
        api_time_ = c_double()
        api_numComponents_ = c_int()
        ierr = c_int()
        lib.gmshViewGetHomogeneousModelData(
            c_int(tag),
            c_int(step),
            byref(api_dataType_),
            byref(api_tags_), byref(api_tags_n_),
            byref(api_data_), byref(api_data_n_),
            byref(api_time_),
            byref(api_numComponents_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ostring(api_dataType_),
            _ovectorsize(api_tags_, api_tags_n_.value),
            _ovectordouble(api_data_, api_data_n_.value),
            api_time_.value,
            api_numComponents_.value)
    get_homogeneous_model_data = getHomogeneousModelData

    @staticmethod
    def addListData(tag, dataType, numEle, data):
        """
        gmsh.view.addListData(tag, dataType, numEle, data)

        Add list-based post-processing data to the view with tag `tag'. List-based
        datasets are independent from any model and any mesh. `dataType' identifies
        the data by concatenating the field type ("S" for scalar, "V" for vector,
        "T" for tensor) and the element type ("P" for point, "L" for line, "T" for
        triangle, "S" for tetrahedron, "I" for prism, "H" for hexaHedron, "Y" for
        pyramid). For example `dataType' should be "ST" for a scalar field on
        triangles. `numEle' gives the number of elements in the data. `data'
        contains the data for the `numEle' elements, concatenated, with node
        coordinates followed by values per node, repeated for each step: [e1x1,
        ..., e1xn, e1y1, ..., e1yn, e1z1, ..., e1zn, e1v1..., e1vN, e2x1, ...].

        Types:
        - `tag': integer
        - `dataType': string
        - `numEle': integer
        - `data': vector of doubles
        """
        api_data_, api_data_n_ = _ivectordouble(data)
        ierr = c_int()
        lib.gmshViewAddListData(
            c_int(tag),
            c_char_p(dataType.encode()),
            c_int(numEle),
            api_data_, api_data_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    add_list_data = addListData

    @staticmethod
    def getListData(tag, returnAdaptive=False):
        """
        gmsh.view.getListData(tag, returnAdaptive=False)

        Get list-based post-processing data from the view with tag `tag'. Return
        the types `dataTypes', the number of elements `numElements' for each data
        type and the `data' for each data type. If `returnAdaptive' is set, return
        the data obtained after adaptive refinement, if available.

        Return `dataType', `numElements', `data'.

        Types:
        - `tag': integer
        - `dataType': vector of strings
        - `numElements': vector of integers
        - `data': vector of vectors of doubles
        - `returnAdaptive': boolean
        """
        api_dataType_, api_dataType_n_ = POINTER(POINTER(c_char))(), c_size_t()
        api_numElements_, api_numElements_n_ = POINTER(c_int)(), c_size_t()
        api_data_, api_data_n_, api_data_nn_ = POINTER(POINTER(c_double))(), POINTER(c_size_t)(), c_size_t()
        ierr = c_int()
        lib.gmshViewGetListData(
            c_int(tag),
            byref(api_dataType_), byref(api_dataType_n_),
            byref(api_numElements_), byref(api_numElements_n_),
            byref(api_data_), byref(api_data_n_), byref(api_data_nn_),
            c_int(bool(returnAdaptive)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectorstring(api_dataType_, api_dataType_n_.value),
            _ovectorint(api_numElements_, api_numElements_n_.value),
            _ovectorvectordouble(api_data_, api_data_n_, api_data_nn_))
    get_list_data = getListData

    @staticmethod
    def addListDataString(tag, coord, data, style=[]):
        """
        gmsh.view.addListDataString(tag, coord, data, style=[])

        Add a string to a list-based post-processing view with tag `tag'. If
        `coord' contains 3 coordinates the string is positioned in the 3D model
        space ("3D string"); if it contains 2 coordinates it is positioned in the
        2D graphics viewport ("2D string"). `data' contains one or more (for
        multistep views) strings. `style' contains key-value pairs of styling
        parameters, concatenated. Available keys are "Font" (possible values:
        "Times-Roman", "Times-Bold", "Times-Italic", "Times-BoldItalic",
        "Helvetica", "Helvetica-Bold", "Helvetica-Oblique", "Helvetica-
        BoldOblique", "Courier", "Courier-Bold", "Courier-Oblique", "Courier-
        BoldOblique", "Symbol", "ZapfDingbats", "Screen"), "FontSize" and "Align"
        (possible values: "Left" or "BottomLeft", "Center" or "BottomCenter",
        "Right" or "BottomRight", "TopLeft", "TopCenter", "TopRight", "CenterLeft",
        "CenterCenter", "CenterRight").

        Types:
        - `tag': integer
        - `coord': vector of doubles
        - `data': vector of strings
        - `style': vector of strings
        """
        api_coord_, api_coord_n_ = _ivectordouble(coord)
        api_data_, api_data_n_ = _ivectorstring(data)
        api_style_, api_style_n_ = _ivectorstring(style)
        ierr = c_int()
        lib.gmshViewAddListDataString(
            c_int(tag),
            api_coord_, api_coord_n_,
            api_data_, api_data_n_,
            api_style_, api_style_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    add_list_data_string = addListDataString

    @staticmethod
    def getListDataStrings(tag, dim):
        """
        gmsh.view.getListDataStrings(tag, dim)

        Get list-based post-processing data strings (2D strings if `dim' == 2, 3D
        strings if `dim' = 3) from the view with tag `tag'. Return the coordinates
        in `coord', the strings in `data' and the styles in `style'.

        Return `coord', `data', `style'.

        Types:
        - `tag': integer
        - `dim': integer
        - `coord': vector of doubles
        - `data': vector of strings
        - `style': vector of strings
        """
        api_coord_, api_coord_n_ = POINTER(c_double)(), c_size_t()
        api_data_, api_data_n_ = POINTER(POINTER(c_char))(), c_size_t()
        api_style_, api_style_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshViewGetListDataStrings(
            c_int(tag),
            c_int(dim),
            byref(api_coord_), byref(api_coord_n_),
            byref(api_data_), byref(api_data_n_),
            byref(api_style_), byref(api_style_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectordouble(api_coord_, api_coord_n_.value),
            _ovectorstring(api_data_, api_data_n_.value),
            _ovectorstring(api_style_, api_style_n_.value))
    get_list_data_strings = getListDataStrings

    @staticmethod
    def setInterpolationMatrices(tag, type, d, coef, exp, dGeo=0, coefGeo=[], expGeo=[]):
        """
        gmsh.view.setInterpolationMatrices(tag, type, d, coef, exp, dGeo=0, coefGeo=[], expGeo=[])

        Set interpolation matrices for the element family `type' ("Line",
        "Triangle", "Quadrangle", "Tetrahedron", "Hexahedron", "Prism", "Pyramid")
        in the view `tag'. The approximation of the values over an element is
        written as a linear combination of `d' basis functions f_i(u, v, w) =
        sum_(j = 0, ..., `d' - 1) `coef'[i][j] u^`exp'[j][0] v^`exp'[j][1]
        w^`exp'[j][2], i = 0, ..., `d'-1, with u, v, w the coordinates in the
        reference element. The `coef' matrix (of size `d' x `d') and the `exp'
        matrix (of size `d' x 3) are stored as vectors, by row. If `dGeo' is
        positive, use `coefGeo' and `expGeo' to define the interpolation of the x,
        y, z coordinates of the element in terms of the u, v, w coordinates, in
        exactly the same way. If `d' < 0, remove the interpolation matrices.

        Types:
        - `tag': integer
        - `type': string
        - `d': integer
        - `coef': vector of doubles
        - `exp': vector of doubles
        - `dGeo': integer
        - `coefGeo': vector of doubles
        - `expGeo': vector of doubles
        """
        api_coef_, api_coef_n_ = _ivectordouble(coef)
        api_exp_, api_exp_n_ = _ivectordouble(exp)
        api_coefGeo_, api_coefGeo_n_ = _ivectordouble(coefGeo)
        api_expGeo_, api_expGeo_n_ = _ivectordouble(expGeo)
        ierr = c_int()
        lib.gmshViewSetInterpolationMatrices(
            c_int(tag),
            c_char_p(type.encode()),
            c_int(d),
            api_coef_, api_coef_n_,
            api_exp_, api_exp_n_,
            c_int(dGeo),
            api_coefGeo_, api_coefGeo_n_,
            api_expGeo_, api_expGeo_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_interpolation_matrices = setInterpolationMatrices

    @staticmethod
    def addAlias(refTag, copyOptions=False, tag=-1):
        """
        gmsh.view.addAlias(refTag, copyOptions=False, tag=-1)

        Add a post-processing view as an `alias' of the reference view with tag
        `refTag'. If `copyOptions' is set, copy the options of the reference view.
        If `tag' is positive use it (and remove the view with that tag if it
        already exists), otherwise associate a new tag. Return the view tag.

        Return an integer.

        Types:
        - `refTag': integer
        - `copyOptions': boolean
        - `tag': integer
        """
        ierr = c_int()
        api_result_ = lib.gmshViewAddAlias(
            c_int(refTag),
            c_int(bool(copyOptions)),
            c_int(tag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    add_alias = addAlias

    @staticmethod
    def combine(what, how, remove=True, copyOptions=True):
        """
        gmsh.view.combine(what, how, remove=True, copyOptions=True)

        Combine elements (if `what' == "elements") or steps (if `what' == "steps")
        of all views (`how' == "all"), all visible views (`how' == "visible") or
        all views having the same name (`how' == "name"). Remove original views if
        `remove' is set.

        Types:
        - `what': string
        - `how': string
        - `remove': boolean
        - `copyOptions': boolean
        """
        ierr = c_int()
        lib.gmshViewCombine(
            c_char_p(what.encode()),
            c_char_p(how.encode()),
            c_int(bool(remove)),
            c_int(bool(copyOptions)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def probe(tag, x, y, z, step=-1, numComp=-1, gradient=False, distanceMax=0., xElemCoord=[], yElemCoord=[], zElemCoord=[], dim=-1):
        """
        gmsh.view.probe(tag, x, y, z, step=-1, numComp=-1, gradient=False, distanceMax=0., xElemCoord=[], yElemCoord=[], zElemCoord=[], dim=-1)

        Probe the view `tag' for its `values' at point (`x', `y', `z'). If no match
        is found, `value' is returned empty. Return only the value at step `step'
        is `step' is positive. Return only values with `numComp' if `numComp' is
        positive. Return the gradient of the `values' if `gradient' is set. If
        `distanceMax' is zero, only return a result if an exact match inside an
        element in the view is found; if `distanceMax' is positive and an exact
        match is not found, return the value at the closest node if it is closer
        than `distanceMax'; if `distanceMax' is negative and an exact match is not
        found, always return the value at the closest node. The distance to the
        match is returned in `distance'. Return the result from the element
        described by its coordinates if `xElementCoord', `yElementCoord' and
        `zElementCoord' are provided. If `dim' is >= 0, return only matches from
        elements of the specified dimension.

        Return `values', `distance'.

        Types:
        - `tag': integer
        - `x': double
        - `y': double
        - `z': double
        - `values': vector of doubles
        - `distance': double
        - `step': integer
        - `numComp': integer
        - `gradient': boolean
        - `distanceMax': double
        - `xElemCoord': vector of doubles
        - `yElemCoord': vector of doubles
        - `zElemCoord': vector of doubles
        - `dim': integer
        """
        api_values_, api_values_n_ = POINTER(c_double)(), c_size_t()
        api_distance_ = c_double()
        api_xElemCoord_, api_xElemCoord_n_ = _ivectordouble(xElemCoord)
        api_yElemCoord_, api_yElemCoord_n_ = _ivectordouble(yElemCoord)
        api_zElemCoord_, api_zElemCoord_n_ = _ivectordouble(zElemCoord)
        ierr = c_int()
        lib.gmshViewProbe(
            c_int(tag),
            c_double(x),
            c_double(y),
            c_double(z),
            byref(api_values_), byref(api_values_n_),
            byref(api_distance_),
            c_int(step),
            c_int(numComp),
            c_int(bool(gradient)),
            c_double(distanceMax),
            api_xElemCoord_, api_xElemCoord_n_,
            api_yElemCoord_, api_yElemCoord_n_,
            api_zElemCoord_, api_zElemCoord_n_,
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            _ovectordouble(api_values_, api_values_n_.value),
            api_distance_.value)

    @staticmethod
    def write(tag, fileName, append=False):
        """
        gmsh.view.write(tag, fileName, append=False)

        Write the view to a file `fileName'. The export format is determined by the
        file extension. Append to the file if `append' is set.

        Types:
        - `tag': integer
        - `fileName': string
        - `append': boolean
        """
        ierr = c_int()
        lib.gmshViewWrite(
            c_int(tag),
            c_char_p(fileName.encode()),
            c_int(bool(append)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def setVisibilityPerWindow(tag, value, windowIndex=0):
        """
        gmsh.view.setVisibilityPerWindow(tag, value, windowIndex=0)

        Set the global visibility of the view `tag' per window to `value', where
        `windowIndex' identifies the window in the window list.

        Types:
        - `tag': integer
        - `value': integer
        - `windowIndex': integer
        """
        ierr = c_int()
        lib.gmshViewSetVisibilityPerWindow(
            c_int(tag),
            c_int(value),
            c_int(windowIndex),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_visibility_per_window = setVisibilityPerWindow


    class option:
        """
        View option handling functions
        """

        @staticmethod
        def setNumber(tag, name, value):
            """
            gmsh.view.option.setNumber(tag, name, value)

            Set the numerical option `name' to value `value' for the view with tag
            `tag'.

            Types:
            - `tag': integer
            - `name': string
            - `value': double
            """
            ierr = c_int()
            lib.gmshViewOptionSetNumber(
                c_int(tag),
                c_char_p(name.encode()),
                c_double(value),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_number = setNumber

        @staticmethod
        def getNumber(tag, name):
            """
            gmsh.view.option.getNumber(tag, name)

            Get the `value' of the numerical option `name' for the view with tag `tag'.

            Return `value'.

            Types:
            - `tag': integer
            - `name': string
            - `value': double
            """
            api_value_ = c_double()
            ierr = c_int()
            lib.gmshViewOptionGetNumber(
                c_int(tag),
                c_char_p(name.encode()),
                byref(api_value_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return api_value_.value
        get_number = getNumber

        @staticmethod
        def setString(tag, name, value):
            """
            gmsh.view.option.setString(tag, name, value)

            Set the string option `name' to value `value' for the view with tag `tag'.

            Types:
            - `tag': integer
            - `name': string
            - `value': string
            """
            ierr = c_int()
            lib.gmshViewOptionSetString(
                c_int(tag),
                c_char_p(name.encode()),
                c_char_p(value.encode()),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_string = setString

        @staticmethod
        def getString(tag, name):
            """
            gmsh.view.option.getString(tag, name)

            Get the `value' of the string option `name' for the view with tag `tag'.

            Return `value'.

            Types:
            - `tag': integer
            - `name': string
            - `value': string
            """
            api_value_ = c_char_p()
            ierr = c_int()
            lib.gmshViewOptionGetString(
                c_int(tag),
                c_char_p(name.encode()),
                byref(api_value_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return _ostring(api_value_)
        get_string = getString

        @staticmethod
        def setColor(tag, name, r, g, b, a=255):
            """
            gmsh.view.option.setColor(tag, name, r, g, b, a=255)

            Set the color option `name' to the RGBA value (`r', `g', `b', `a') for the
            view with tag `tag', where where `r', `g', `b' and `a' should be integers
            between 0 and 255.

            Types:
            - `tag': integer
            - `name': string
            - `r': integer
            - `g': integer
            - `b': integer
            - `a': integer
            """
            ierr = c_int()
            lib.gmshViewOptionSetColor(
                c_int(tag),
                c_char_p(name.encode()),
                c_int(r),
                c_int(g),
                c_int(b),
                c_int(a),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
        set_color = setColor

        @staticmethod
        def getColor(tag, name):
            """
            gmsh.view.option.getColor(tag, name)

            Get the `r', `g', `b', `a' value of the color option `name' for the view
            with tag `tag'.

            Return `r', `g', `b', `a'.

            Types:
            - `tag': integer
            - `name': string
            - `r': integer
            - `g': integer
            - `b': integer
            - `a': integer
            """
            api_r_ = c_int()
            api_g_ = c_int()
            api_b_ = c_int()
            api_a_ = c_int()
            ierr = c_int()
            lib.gmshViewOptionGetColor(
                c_int(tag),
                c_char_p(name.encode()),
                byref(api_r_),
                byref(api_g_),
                byref(api_b_),
                byref(api_a_),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())
            return (
                api_r_.value,
                api_g_.value,
                api_b_.value,
                api_a_.value)
        get_color = getColor

        @staticmethod
        def copy(refTag, tag):
            """
            gmsh.view.option.copy(refTag, tag)

            Copy the options from the view with tag `refTag' to the view with tag
            `tag'.

            Types:
            - `refTag': integer
            - `tag': integer
            """
            ierr = c_int()
            lib.gmshViewOptionCopy(
                c_int(refTag),
                c_int(tag),
                byref(ierr))
            if ierr.value != 0:
                raise Exception(logger.getLastError())


class algorithm:
    """
    Raw algorithms
    """

    @staticmethod
    def triangulate(coordinates, edges=[]):
        """
        gmsh.algorithm.triangulate(coordinates, edges=[])

        Triangulate the points given in the `coordinates' vector as concatenated
        pairs of u, v coordinates, with (optional) constrained edges given in the
        `edges' vector as pair of indexes (with numbering starting at 1), and
        return the triangles as concatenated triplets of point indexes (with
        numbering starting at 1) in `triangles'.

        Return `triangles'.

        Types:
        - `coordinates': vector of doubles
        - `triangles': vector of sizes
        - `edges': vector of sizes
        """
        api_coordinates_, api_coordinates_n_ = _ivectordouble(coordinates)
        api_triangles_, api_triangles_n_ = POINTER(c_size_t)(), c_size_t()
        api_edges_, api_edges_n_ = _ivectorsize(edges)
        ierr = c_int()
        lib.gmshAlgorithmTriangulate(
            api_coordinates_, api_coordinates_n_,
            byref(api_triangles_), byref(api_triangles_n_),
            api_edges_, api_edges_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorsize(api_triangles_, api_triangles_n_.value)

    @staticmethod
    def tetrahedralize(coordinates):
        """
        gmsh.algorithm.tetrahedralize(coordinates)

        Tetrahedralize the points given in the `coordinates' vector as concatenated
        triplets of x, y, z coordinates, and return the tetrahedra as concatenated
        quadruplets of point indexes (with numbering starting at 1) in
        `tetrahedra'.

        Return `tetrahedra'.

        Types:
        - `coordinates': vector of doubles
        - `tetrahedra': vector of sizes
        """
        api_coordinates_, api_coordinates_n_ = _ivectordouble(coordinates)
        api_tetrahedra_, api_tetrahedra_n_ = POINTER(c_size_t)(), c_size_t()
        ierr = c_int()
        lib.gmshAlgorithmTetrahedralize(
            api_coordinates_, api_coordinates_n_,
            byref(api_tetrahedra_), byref(api_tetrahedra_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorsize(api_tetrahedra_, api_tetrahedra_n_.value)


class plugin:
    """
    Plugin functions
    """

    @staticmethod
    def setNumber(name, option, value):
        """
        gmsh.plugin.setNumber(name, option, value)

        Set the numerical option `option' to the value `value' for plugin `name'.
        Plugins available in the official Gmsh release are listed in the "Gmsh
        plugins" chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-plugins).

        Types:
        - `name': string
        - `option': string
        - `value': double
        """
        ierr = c_int()
        lib.gmshPluginSetNumber(
            c_char_p(name.encode()),
            c_char_p(option.encode()),
            c_double(value),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_number = setNumber

    @staticmethod
    def setString(name, option, value):
        """
        gmsh.plugin.setString(name, option, value)

        Set the string option `option' to the value `value' for plugin `name'.
        Plugins available in the official Gmsh release are listed in the "Gmsh
        plugins" chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-plugins).

        Types:
        - `name': string
        - `option': string
        - `value': string
        """
        ierr = c_int()
        lib.gmshPluginSetString(
            c_char_p(name.encode()),
            c_char_p(option.encode()),
            c_char_p(value.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_string = setString

    @staticmethod
    def run(name):
        """
        gmsh.plugin.run(name)

        Run the plugin `name'. Return the tag of the created view (if any). Plugins
        available in the official Gmsh release are listed in the "Gmsh plugins"
        chapter of the Gmsh reference manual
        (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-plugins).

        Return an integer.

        Types:
        - `name': string
        """
        ierr = c_int()
        api_result_ = lib.gmshPluginRun(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_


class graphics:
    """
    Graphics functions
    """

    @staticmethod
    def draw():
        """
        gmsh.graphics.draw()

        Draw all the OpenGL scenes.
        """
        ierr = c_int()
        lib.gmshGraphicsDraw(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())


class fltk:
    """
    FLTK graphical user interface functions
    """

    @staticmethod
    def initialize():
        """
        gmsh.fltk.initialize()

        Create the FLTK graphical user interface. Can only be called in the main
        thread.
        """
        ierr = c_int()
        lib.gmshFltkInitialize(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def finalize():
        """
        gmsh.fltk.finalize()

        Close the FLTK graphical user interface. Can only be called in the main
        thread.
        """
        ierr = c_int()
        lib.gmshFltkFinalize(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def wait(time=-1.):
        """
        gmsh.fltk.wait(time=-1.)

        Wait at most `time' seconds for user interface events and return. If `time'
        < 0, wait indefinitely. First automatically create the user interface if it
        has not yet been initialized. Can only be called in the main thread.

        Types:
        - `time': double
        """
        ierr = c_int()
        lib.gmshFltkWait(
            c_double(time),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def update():
        """
        gmsh.fltk.update()

        Update the user interface (potentially creating new widgets and windows).
        First automatically create the user interface if it has not yet been
        initialized. Can only be called in the main thread: use `awake("update")'
        to trigger an update of the user interface from another thread.
        """
        ierr = c_int()
        lib.gmshFltkUpdate(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def awake(action=""):
        """
        gmsh.fltk.awake(action="")

        Awake the main user interface thread and process pending events, and
        optionally perform an action (currently the only `action' allowed is
        "update").

        Types:
        - `action': string
        """
        ierr = c_int()
        lib.gmshFltkAwake(
            c_char_p(action.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def lock():
        """
        gmsh.fltk.lock()

        Block the current thread until it can safely modify the user interface.
        """
        ierr = c_int()
        lib.gmshFltkLock(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def unlock():
        """
        gmsh.fltk.unlock()

        Release the lock that was set using lock.
        """
        ierr = c_int()
        lib.gmshFltkUnlock(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def run():
        """
        gmsh.fltk.run()

        Run the event loop of the graphical user interface, i.e. repeatedly call
        `wait()'. First automatically create the user interface if it has not yet
        been initialized. Can only be called in the main thread.
        """
        ierr = c_int()
        lib.gmshFltkRun(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def isAvailable():
        """
        gmsh.fltk.isAvailable()

        Check if the user interface is available (e.g. to detect if it has been
        closed).

        Return an integer.
        """
        ierr = c_int()
        api_result_ = lib.gmshFltkIsAvailable(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    is_available = isAvailable

    @staticmethod
    def selectEntities(dim=-1):
        """
        gmsh.fltk.selectEntities(dim=-1)

        Select entities in the user interface. Return the selected entities as a
        vector of (dim, tag) pairs. If `dim' is >= 0, return only the entities of
        the specified dimension (e.g. points if `dim' == 0).

        Return an integer, `dimTags'.

        Types:
        - `dimTags': vector of pairs of integers
        - `dim': integer
        """
        api_dimTags_, api_dimTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        api_result_ = lib.gmshFltkSelectEntities(
            byref(api_dimTags_), byref(api_dimTags_n_),
            c_int(dim),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_result_,
            _ovectorpair(api_dimTags_, api_dimTags_n_.value))
    select_entities = selectEntities

    @staticmethod
    def selectElements():
        """
        gmsh.fltk.selectElements()

        Select elements in the user interface.

        Return an integer, `elementTags'.

        Types:
        - `elementTags': vector of sizes
        """
        api_elementTags_, api_elementTags_n_ = POINTER(c_size_t)(), c_size_t()
        ierr = c_int()
        api_result_ = lib.gmshFltkSelectElements(
            byref(api_elementTags_), byref(api_elementTags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_result_,
            _ovectorsize(api_elementTags_, api_elementTags_n_.value))
    select_elements = selectElements

    @staticmethod
    def selectViews():
        """
        gmsh.fltk.selectViews()

        Select views in the user interface.

        Return an integer, `viewTags'.

        Types:
        - `viewTags': vector of integers
        """
        api_viewTags_, api_viewTags_n_ = POINTER(c_int)(), c_size_t()
        ierr = c_int()
        api_result_ = lib.gmshFltkSelectViews(
            byref(api_viewTags_), byref(api_viewTags_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return (
            api_result_,
            _ovectorint(api_viewTags_, api_viewTags_n_.value))
    select_views = selectViews

    @staticmethod
    def splitCurrentWindow(how="v", ratio=0.5):
        """
        gmsh.fltk.splitCurrentWindow(how="v", ratio=0.5)

        Split the current window horizontally (if `how' == "h") or vertically (if
        `how' == "v"), using ratio `ratio'. If `how' == "u", restore a single
        window.

        Types:
        - `how': string
        - `ratio': double
        """
        ierr = c_int()
        lib.gmshFltkSplitCurrentWindow(
            c_char_p(how.encode()),
            c_double(ratio),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    split_current_window = splitCurrentWindow

    @staticmethod
    def setCurrentWindow(windowIndex=0):
        """
        gmsh.fltk.setCurrentWindow(windowIndex=0)

        Set the current window by speficying its index (starting at 0) in the list
        of all windows. When new windows are created by splits, new windows are
        appended at the end of the list.

        Types:
        - `windowIndex': integer
        """
        ierr = c_int()
        lib.gmshFltkSetCurrentWindow(
            c_int(windowIndex),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_current_window = setCurrentWindow

    @staticmethod
    def setStatusMessage(message, graphics=False):
        """
        gmsh.fltk.setStatusMessage(message, graphics=False)

        Set a status message in the current window. If `graphics' is set, display
        the message inside the graphic window instead of the status bar.

        Types:
        - `message': string
        - `graphics': boolean
        """
        ierr = c_int()
        lib.gmshFltkSetStatusMessage(
            c_char_p(message.encode()),
            c_int(bool(graphics)),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_status_message = setStatusMessage

    @staticmethod
    def showContextWindow(dim, tag):
        """
        gmsh.fltk.showContextWindow(dim, tag)

        Show context window for the entity of dimension `dim' and tag `tag'.

        Types:
        - `dim': integer
        - `tag': integer
        """
        ierr = c_int()
        lib.gmshFltkShowContextWindow(
            c_int(dim),
            c_int(tag),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    show_context_window = showContextWindow

    @staticmethod
    def openTreeItem(name):
        """
        gmsh.fltk.openTreeItem(name)

        Open the `name' item in the menu tree.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshFltkOpenTreeItem(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    open_tree_item = openTreeItem

    @staticmethod
    def closeTreeItem(name):
        """
        gmsh.fltk.closeTreeItem(name)

        Close the `name' item in the menu tree.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshFltkCloseTreeItem(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    close_tree_item = closeTreeItem


class parser:
    """
    Parser functions
    """

    @staticmethod
    def getNames(search=""):
        """
        gmsh.parser.getNames(search="")

        Get the names of the variables in the Gmsh parser matching the `search'
        regular expression. If `search' is empty, return all the names.

        Return `names'.

        Types:
        - `names': vector of strings
        - `search': string
        """
        api_names_, api_names_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshParserGetNames(
            byref(api_names_), byref(api_names_n_),
            c_char_p(search.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_names_, api_names_n_.value)
    get_names = getNames

    @staticmethod
    def setNumber(name, value):
        """
        gmsh.parser.setNumber(name, value)

        Set the value of the number variable `name' in the Gmsh parser. Create the
        variable if it does not exist; update the value if the variable exists.

        Types:
        - `name': string
        - `value': vector of doubles
        """
        api_value_, api_value_n_ = _ivectordouble(value)
        ierr = c_int()
        lib.gmshParserSetNumber(
            c_char_p(name.encode()),
            api_value_, api_value_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_number = setNumber

    @staticmethod
    def setString(name, value):
        """
        gmsh.parser.setString(name, value)

        Set the value of the string variable `name' in the Gmsh parser. Create the
        variable if it does not exist; update the value if the variable exists.

        Types:
        - `name': string
        - `value': vector of strings
        """
        api_value_, api_value_n_ = _ivectorstring(value)
        ierr = c_int()
        lib.gmshParserSetString(
            c_char_p(name.encode()),
            api_value_, api_value_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_string = setString

    @staticmethod
    def getNumber(name):
        """
        gmsh.parser.getNumber(name)

        Get the value of the number variable `name' from the Gmsh parser. Return an
        empty vector if the variable does not exist.

        Return `value'.

        Types:
        - `name': string
        - `value': vector of doubles
        """
        api_value_, api_value_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshParserGetNumber(
            c_char_p(name.encode()),
            byref(api_value_), byref(api_value_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_value_, api_value_n_.value)
    get_number = getNumber

    @staticmethod
    def getString(name):
        """
        gmsh.parser.getString(name)

        Get the value of the string variable `name' from the Gmsh parser. Return an
        empty vector if the variable does not exist.

        Return `value'.

        Types:
        - `name': string
        - `value': vector of strings
        """
        api_value_, api_value_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshParserGetString(
            c_char_p(name.encode()),
            byref(api_value_), byref(api_value_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_value_, api_value_n_.value)
    get_string = getString

    @staticmethod
    def clear(name=""):
        """
        gmsh.parser.clear(name="")

        Clear all the Gmsh parser variables, or remove a single variable if `name'
        is given.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshParserClear(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def parse(fileName):
        """
        gmsh.parser.parse(fileName)

        Parse the file `fileName' with the Gmsh parser.

        Types:
        - `fileName': string
        """
        ierr = c_int()
        lib.gmshParserParse(
            c_char_p(fileName.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())


class onelab:
    """
    ONELAB server functions
    """

    @staticmethod
    def set(data, format="json"):
        """
        gmsh.onelab.set(data, format="json")

        Set one or more parameters in the ONELAB database, encoded in `format'.

        Types:
        - `data': string
        - `format': string
        """
        ierr = c_int()
        lib.gmshOnelabSet(
            c_char_p(data.encode()),
            c_char_p(format.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def get(name="", format="json"):
        """
        gmsh.onelab.get(name="", format="json")

        Get all the parameters (or a single one if `name' is specified) from the
        ONELAB database, encoded in `format'.

        Return `data'.

        Types:
        - `data': string
        - `name': string
        - `format': string
        """
        api_data_ = c_char_p()
        ierr = c_int()
        lib.gmshOnelabGet(
            byref(api_data_),
            c_char_p(name.encode()),
            c_char_p(format.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ostring(api_data_)

    @staticmethod
    def getNames(search=""):
        """
        gmsh.onelab.getNames(search="")

        Get the names of the parameters in the ONELAB database matching the
        `search' regular expression. If `search' is empty, return all the names.

        Return `names'.

        Types:
        - `names': vector of strings
        - `search': string
        """
        api_names_, api_names_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshOnelabGetNames(
            byref(api_names_), byref(api_names_n_),
            c_char_p(search.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_names_, api_names_n_.value)
    get_names = getNames

    @staticmethod
    def setNumber(name, value):
        """
        gmsh.onelab.setNumber(name, value)

        Set the value of the number parameter `name' in the ONELAB database. Create
        the parameter if it does not exist; update the value if the parameter
        exists.

        Types:
        - `name': string
        - `value': vector of doubles
        """
        api_value_, api_value_n_ = _ivectordouble(value)
        ierr = c_int()
        lib.gmshOnelabSetNumber(
            c_char_p(name.encode()),
            api_value_, api_value_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_number = setNumber

    @staticmethod
    def setString(name, value):
        """
        gmsh.onelab.setString(name, value)

        Set the value of the string parameter `name' in the ONELAB database. Create
        the parameter if it does not exist; update the value if the parameter
        exists.

        Types:
        - `name': string
        - `value': vector of strings
        """
        api_value_, api_value_n_ = _ivectorstring(value)
        ierr = c_int()
        lib.gmshOnelabSetString(
            c_char_p(name.encode()),
            api_value_, api_value_n_,
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_string = setString

    @staticmethod
    def getNumber(name):
        """
        gmsh.onelab.getNumber(name)

        Get the value of the number parameter `name' from the ONELAB database.
        Return an empty vector if the parameter does not exist.

        Return `value'.

        Types:
        - `name': string
        - `value': vector of doubles
        """
        api_value_, api_value_n_ = POINTER(c_double)(), c_size_t()
        ierr = c_int()
        lib.gmshOnelabGetNumber(
            c_char_p(name.encode()),
            byref(api_value_), byref(api_value_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectordouble(api_value_, api_value_n_.value)
    get_number = getNumber

    @staticmethod
    def getString(name):
        """
        gmsh.onelab.getString(name)

        Get the value of the string parameter `name' from the ONELAB database.
        Return an empty vector if the parameter does not exist.

        Return `value'.

        Types:
        - `name': string
        - `value': vector of strings
        """
        api_value_, api_value_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshOnelabGetString(
            c_char_p(name.encode()),
            byref(api_value_), byref(api_value_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_value_, api_value_n_.value)
    get_string = getString

    @staticmethod
    def getChanged(name):
        """
        gmsh.onelab.getChanged(name)

        Check if any parameters in the ONELAB database used by the client `name'
        have been changed.

        Return an integer.

        Types:
        - `name': string
        """
        ierr = c_int()
        api_result_ = lib.gmshOnelabGetChanged(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_changed = getChanged

    @staticmethod
    def setChanged(name, value):
        """
        gmsh.onelab.setChanged(name, value)

        Set the changed flag to value `value' for all the parameters in the ONELAB
        database used by the client `name'.

        Types:
        - `name': string
        - `value': integer
        """
        ierr = c_int()
        lib.gmshOnelabSetChanged(
            c_char_p(name.encode()),
            c_int(value),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
    set_changed = setChanged

    @staticmethod
    def clear(name=""):
        """
        gmsh.onelab.clear(name="")

        Clear the ONELAB database, or remove a single parameter if `name' is given.

        Types:
        - `name': string
        """
        ierr = c_int()
        lib.gmshOnelabClear(
            c_char_p(name.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def run(name="", command=""):
        """
        gmsh.onelab.run(name="", command="")

        Run a ONELAB client. If `name' is provided, create a new ONELAB client with
        name `name' and executes `command'. If not, try to run a client that might
        be linked to the processed input files.

        Types:
        - `name': string
        - `command': string
        """
        ierr = c_int()
        lib.gmshOnelabRun(
            c_char_p(name.encode()),
            c_char_p(command.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())


class logger:
    """
    Information logging functions
    """

    @staticmethod
    def write(message, level="info"):
        """
        gmsh.logger.write(message, level="info")

        Write a `message'. `level' can be "info", "warning" or "error".

        Types:
        - `message': string
        - `level': string
        """
        ierr = c_int()
        lib.gmshLoggerWrite(
            c_char_p(message.encode()),
            c_char_p(level.encode()),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def start():
        """
        gmsh.logger.start()

        Start logging messages.
        """
        ierr = c_int()
        lib.gmshLoggerStart(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def get():
        """
        gmsh.logger.get()

        Get logged messages.

        Return `log'.

        Types:
        - `log': vector of strings
        """
        api_log_, api_log_n_ = POINTER(POINTER(c_char))(), c_size_t()
        ierr = c_int()
        lib.gmshLoggerGet(
            byref(api_log_), byref(api_log_n_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return _ovectorstring(api_log_, api_log_n_.value)

    @staticmethod
    def stop():
        """
        gmsh.logger.stop()

        Stop logging messages.
        """
        ierr = c_int()
        lib.gmshLoggerStop(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())

    @staticmethod
    def getWallTime():
        """
        gmsh.logger.getWallTime()

        Return wall clock time (in s).

        Return a double.
        """
        ierr = c_int()
        lib.gmshLoggerGetWallTime.restype = c_double
        api_result_ = lib.gmshLoggerGetWallTime(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_wall_time = getWallTime

    @staticmethod
    def getCpuTime():
        """
        gmsh.logger.getCpuTime()

        Return CPU time (in s).

        Return a double.
        """
        ierr = c_int()
        lib.gmshLoggerGetCpuTime.restype = c_double
        api_result_ = lib.gmshLoggerGetCpuTime(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_cpu_time = getCpuTime

    @staticmethod
    def getMemory():
        """
        gmsh.logger.getMemory()

        Return memory usage (in Mb).

        Return a double.
        """
        ierr = c_int()
        lib.gmshLoggerGetMemory.restype = c_double
        api_result_ = lib.gmshLoggerGetMemory(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_memory = getMemory

    @staticmethod
    def getTotalMemory():
        """
        gmsh.logger.getTotalMemory()

        Return total available memory (in Mb).

        Return a double.
        """
        ierr = c_int()
        lib.gmshLoggerGetTotalMemory.restype = c_double
        api_result_ = lib.gmshLoggerGetTotalMemory(
            byref(ierr))
        if ierr.value != 0:
            raise Exception(logger.getLastError())
        return api_result_
    get_total_memory = getTotalMemory

    @staticmethod
    def getLastError():
        """
        gmsh.logger.getLastError()

        Return last error message, if any.

        Return `error'.

        Types:
        - `error': string
        """
        api_error_ = c_char_p()
        ierr = c_int()
        lib.gmshLoggerGetLastError(
            byref(api_error_),
            byref(ierr))
        if ierr.value != 0:
            raise Exception('Could not get last error')
        return _ostring(api_error_)
    get_last_error = getLastError
