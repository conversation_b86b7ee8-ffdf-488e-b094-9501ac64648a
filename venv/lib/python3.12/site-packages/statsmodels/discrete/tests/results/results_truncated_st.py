# flake8: noqa
# file is autogenerated

import numpy as np

est = dict(
           r2_p = .0777591927880298,
           p = 0,
           chi2 = 2117.638300165287,
           df_m = 2,
           ll_0 = -13616.64276748558,
           k_eq_model = 1,
           ll = -12557.82361740294,
           k_autoCns = 0,
           rc = 0,
           converged = 1,
           k_dv = 1,
           k_eq = 1,
           k = 3,
           ic = 2,
           N = 3237,
           rank = 3,
           cmdline = "tpoisson docvis aget totchr if docvis > 0",
           title = "Truncated Poisson regression",
           title2 = "Truncation points: 0",
           llopt = "0",
           cmd = "tpoisson",
           predict = "tpoisson_p",
           chi2type = "LR",
           opt = "moptimize",
           vce = "oim",
           user = "tpoiss_d2",
           crittype = "log likelihood",
           ml_method = "e2",
           singularHmethod = "m-marquardt",
           technique = "nr",
           which = "max",
           depvar = "docvis",
           properties = "b V",
          )

params_table = np.array([
     .01227632422998,  .00504328169164,  2.4341936422712,  .01492500815025,
     .00239167375047,  .02216097470949, np.nan,  1.9599639845401,
                   0,  .20994371600381,  .00449374445996,   46.71910427362,
                   0,  .20113613870656,  .21875129330105, np.nan,
     1.9599639845401,                0,  1.5417559978312,  .01547272444235,
     99.643472846381,                0,  1.5114300151815,   1.572081980481,
    np.nan,  1.9599639845401,                0]).reshape(3,9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = 'aget totchr _cons'.split()

cov = np.array([
     .00002543469022, -1.201653007e-06, -.00004595604713, -1.201653007e-06,
     .00002019373927, -.00004598669366, -.00004595604713, -.00004598669366,
     .00023940520167]).reshape(3,3)

cov_colnames = 'aget totchr _cons'.split()

cov_rownames = 'aget totchr _cons'.split()

icr = np.array([
                3237, -13616.642767486, -12557.823617403,                3,
     25121.647234806,  25139.894441568])

icr_colnames = 'N ll0 ll df AIC BIC'.split()

icr_rownames = '.'.split()

class Bunch(dict):
    def __init__(self, **kw):
        dict.__init__(self, kw)
        self.__dict__ = self

        if hasattr(self, 'params_table'):
            for i,att in enumerate(['params', 'bse', 'tvalues', 'pvalues']):
                self[att] = self.params_table[:,i]


results_trunc_poisson = Bunch(
                params_table=params_table,
                params_table_colnames=params_table_colnames,
                params_table_rownames=params_table_rownames,
                cov=cov,
                cov_colnames=cov_colnames,
                cov_rownames=cov_rownames,
                icr=icr,
                icr_colnames=icr_colnames,
                icr_rownames=icr_rownames,
                **est
                )

table = np.array([
     7.5616311400077,  .04812900313217,  157.11173404616,                0,
     7.4673000272568,  7.6559622527585, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson.margins_cm = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     7.5503819076573,  .04846533862271,  155.78931504916,                0,
     7.4553915894583,  7.6453722258564, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson.margins_means = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     7.2430661225168,  .04840412009716,  149.63738846977,                0,
      7.148195790423,  7.3379364546105, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson.margins_atmeans = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .01124923235036,  .00049084317554,  22.918180207056,  3.06106238e-116,
     .01028719740425,  .01221126729646, np.nan,  1.9599639845401,
                   0,  .03157461406623,  .00105301458741,  29.984973089368,
     1.54089108e-197,  .02951074339971,  .03363848473275, np.nan,
     1.9599639845401,                0,  .06090587962511,  .00146586027013,
     41.549580724723,                0,  .05803284628928,  .06377891296095,
    np.nan,  1.9599639845401,                0,   .8962702739583,
     .00300215903947,  298.54190340135,                0,  .89038615036507,
     .90215439755153, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict'.split()


results_trunc_poisson.margins_cpr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .00204639655178,  .00011129186954,  18.387655453284,  1.649609265e-75,
     .00182826849572,  .00226452460784, np.nan,  1.9599639845401,
                   0,   .0111917391283,  .00048491494502,  23.079798309369,
     7.38815718e-118,  .01024132330049,  .01214215495611, np.nan,
     1.9599639845401,                0,  .03142959767164,  .00103996422108,
     30.221806707029,  1.22479233e-200,  .02939130525312,  .03346789009016,
    np.nan,  1.9599639845401,                0,  .06065903309159,
     .00144697985213,  41.921131799076,                0,  .05782300469507,
     .06349506148811, np.nan,  1.9599639845401,                0,
     .89467323355669,   .0030739863865,  291.04658286247,                0,
     .88864833095018,   .9006981361632, np.nan,  1.9599639845401,
                   0]).reshape(5,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict 5._predict'.split()


results_trunc_poisson.margins_pr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

est = dict(
           k_aux = 1,
           r2_p = .0215428784838031,
           chi2_c = 6444.56377006125,
           ll_c = -12557.82361740294,
           p = 5.42079209773e-90,
           chi2 = 411.0848328423963,
           df_m = 2,
           rank0 = 2,
           ll_0 = -9541.08414879351,
           k_eq_model = 1,
           ll = -9335.541732372312,
           k_autoCns = 0,
           rc = 0,
           converged = 1,
           k_dv = 1,
           k_eq = 2,
           k = 4,
           ic = 3,
           N = 3237,
           rank = 4,
           alpha = .5883738271406221,
           cmdline = "tnbreg docvis aget totchr if docvis > 0",
           title = "Truncated negative binomial regression",
           title2 = "Truncation points: 0",
           llopt = "0",
           cmd = "tnbreg",
           predict = "tnbreg_p",
           dispers = "mean",
           diparm_opt2 = "noprob",
           chi2_ct = "LR",
           chi2type = "LR",
           opt = "moptimize",
           vce = "oim",
           diparm1 = "lnalpha, exp label(",
           user = "tnbreg_mean",
           crittype = "log likelihood",
           ml_method = "e2",
           singularHmethod = "m-marquardt",
           technique = "nr",
           which = "max",
           depvar = "docvis",
           properties = "b V",
          )

params_table = np.array([
     .01798960271895,  .01237555909354,  1.4536395958332,  .14604625306297,
    -.00626604739294,  .04224525283084, np.nan,  1.9599639845401,
                   0,  .23731215078822,  .01166878414467,  20.337350305391,
     6.009046369e-92,  .21444175412129,  .26018254745516, np.nan,
     1.9599639845401,                0,  1.4035619564653,   .0366296524502,
     38.317643291146,                0,  1.3317691568967,  1.4753547560339,
    np.nan,  1.9599639845401,                0, -.53039277265033,
     .04237842368148, -12.515632403809,  6.131755594e-36, -.61345295678762,
    -.44733258851304, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = 'aget totchr _cons _cons'.split()

cov = np.array([
     .00015315446288, -8.133778629e-06,  -.0002727009794,  4.768991952e-06,
    -8.133778629e-06,  .00013616052341, -.00027073391794,  .00003663514391,
     -.0002727009794, -.00027073391794,  .00134173143862, -.00027563132595,
     4.768991952e-06,  .00003663514391, -.00027563132595,  .00179593079373
    ]).reshape(4,4)

cov_colnames = 'aget totchr _cons _cons'.split()

cov_rownames = 'aget totchr _cons _cons'.split()

icr = np.array([
                3237, -9541.0841487935, -9335.5417323723,                4,
     18679.083464745,   18703.41307376])

icr_colnames = 'N ll0 ll df AIC BIC'.split()

icr_rownames = '.'.split()


results_trunc_negbin = Bunch(
                params_table=params_table,
                params_table_colnames=params_table_colnames,
                params_table_rownames=params_table_rownames,
                cov=cov,
                cov_colnames=cov_colnames,
                cov_rownames=cov_rownames,
                icr=icr,
                icr_colnames=icr_colnames,
                icr_rownames=icr_rownames,
                **est
                )

table = np.array([
     7.5768532635168,  .11034667559901,  68.664082740929,                0,
      7.360577753529,  7.7931287735046, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin.margins_cm = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     7.1017589207455,  .11718653140646,  60.602177020781,                0,
     6.8720775397156,  7.3314403017753, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin.margins_means = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     6.7307995444045,  .10830267362473,  62.148046018945,                0,
     6.5185302046706,  6.9430688841384, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin.margins_atmeans = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .10130558155035,  .00372164932482,  27.220614493326,  3.70370053e-163,
     .09401128291061,  .10859988019009, np.nan,  1.9599639845401,
                   0,  .10609694010369,  .00217062159601,  48.878597862821,
                   0,  .10184259995145,  .11035128025594, np.nan,
     1.9599639845401,                0,  .10187507062355,  .00125382749214,
     81.251265634444,                0,  .09941761389613,  .10433252735097,
    np.nan,  1.9599639845401,                0,  .69072240772241,
     .00681119370137,  101.40989054297,                0,  .67737271337601,
     .70407210206881, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict'.split()


results_trunc_negbin.margins_cpr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .07074667643427,  .00449635579252,  15.734225603755,  8.812829317e-56,
     .06193398101925,  .07955937184929, np.nan,  1.9599639845401,
                   0,  .09312895729354,   .0029475364353,  31.595523698388,
     4.25362024e-219,  .08735189203722,  .09890602254985, np.nan,
     1.9599639845401,                0,  .09770317477771,  .00158068603995,
     61.810614067951,                0,  .09460508706855,  .10080126248687,
    np.nan,  1.9599639845401,                0,  .09397857365281,
     .00100790260574,  93.241721092808,                0,  .09200312084564,
     .09595402645998, np.nan,  1.9599639845401,                0,
     .64444261784167,  .00909052984414,  70.891645359594,                0,
     .62662550674676,  .66225972893657, np.nan,  1.9599639845401,
                   0]).reshape(5,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict 5._predict'.split()


results_trunc_negbin.margins_pr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

est = dict(
           r2_p = .0631714878006528,
           p = 4.9406564584e-324,
           chi2 = 1489.753609191444,
           df_m = 2,
           ll_0 = -11791.34496477735,
           k_eq_model = 1,
           ll = -11046.46816018163,
           k_autoCns = 0,
           rc = 0,
           converged = 1,
           k_dv = 1,
           k_eq = 1,
           k = 3,
           ic = 2,
           N = 2925,
           rank = 3,
           cmdline = "tpoisson docvis aget totchr if docvis > 1, ll(1)",
           title = "Truncated Poisson regression",
           title2 = "Truncation points: 1",
           llopt = "1",
           cmd = "tpoisson",
           predict = "tpoisson_p",
           chi2type = "LR",
           opt = "moptimize",
           vce = "oim",
           user = "tpoiss_d2",
           crittype = "log likelihood",
           ml_method = "e2",
           singularHmethod = "m-marquardt",
           technique = "nr",
           which = "max",
           depvar = "docvis",
           properties = "b V",
          )

params_table = np.array([
     .00741559993424,  .00511787438189,  1.4489609124605,  .14734849869339,
    -.00261524953166,  .01744644940014, np.nan,  1.9599639845401,
                   0,  .18069681610939,  .00462967674542,  39.030115069715,
                   0,   .1716228164283,  .18977081579048, np.nan,
     1.9599639845401,                0,  1.6905464469665,  .01602194453256,
     105.51443637387,                0,  1.6591440127204,  1.7219488812126,
    np.nan,  1.9599639845401,                0]).reshape(3,9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = 'aget totchr _cons'.split()

cov = np.array([
     .00002619263819, -9.943128998e-07, -.00004800030105, -9.943128998e-07,
     .00002143390677, -.00005015292878, -.00004800030105, -.00005015292878,
      .0002567027066]).reshape(3,3)

cov_colnames = 'aget totchr _cons'.split()

cov_rownames = 'aget totchr _cons'.split()

icr = np.array([
                2925, -11791.344964777, -11046.468160182,                3,
     22098.936320363,  22116.879469642])

icr_colnames = 'N ll0 ll df AIC BIC'.split()

icr_rownames = '.'.split()


results_trunc_poisson1 = Bunch(
                params_table=params_table,
                params_table_colnames=params_table_colnames,
                params_table_rownames=params_table_rownames,
                cov=cov,
                cov_colnames=cov_colnames,
                cov_rownames=cov_rownames,
                icr=icr,
                icr_colnames=icr_colnames,
                icr_rownames=icr_rownames,
                **est
                )

table = np.array([
     8.2615384672996,  .05245366864144,  157.50163298917,                0,
     8.1587311659054,  8.3643457686938, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson1.margins_cm = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     8.2234692879205,  .05360982028302,  153.39483035955,                0,
     8.1183959709481,  8.3285426048929, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson1.margins_means = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     7.9777789306415,  .05376558351155,  148.38077464422,                0,
     7.8724003233511,  8.0831575379319, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_poisson1.margins_atmeans = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .01903458968954,  .00079296931232,  24.004194606123,  2.51401100e-127,
     .01748039839655,  .02058878098252, np.nan,  1.9599639845401,
                   0,  .04148049972971,  .00131417109881,  31.564002409827,
     1.15213452e-218,  .03890477170652,  .04405622775289, np.nan,
     1.9599639845401,                0,   .0696144656319,  .00158464648625,
     43.930596657447,                0,  .06650861559064,  .07272031567317,
    np.nan,  1.9599639845401,                0,  .86987044494885,
     .00367826110927,  236.48958546081,                0,  .86266118564894,
     .87707970424876, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict'.split()


results_trunc_poisson1.margins_cpr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .00093735269068,  .00005905857331,  15.871576946126,  9.970398969e-57,
     .00082160001401,  .00105310536736, np.nan,  1.9599639845401,
                   0,  .00586646311539,  .00030222083986,  19.411179977019,
     6.208483313e-84,  .00527412115388,   .0064588050769, np.nan,
     1.9599639845401,                0,  .01875199435581,  .00076464697042,
       24.5237280485,  8.24915097e-133,  .01725331383289,  .02025067487873,
    np.nan,  1.9599639845401,                0,  .04092373966247,
     .00126585942523,  32.328818545641,  2.75392481e-229,  .03844270077953,
     .04340477854541, np.nan,  1.9599639845401,                0,
     .93352045017566,  .00238739224619,  391.02097766504,                0,
     .92884124735615,  .93819965299516, np.nan,  1.9599639845401,
                   0]).reshape(5,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict 5._predict'.split()


results_trunc_poisson1.margins_pr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

est = dict(
           k_aux = 1,
           r2_p = .0176349216287439,
           chi2_c = 5373.418644637513,
           ll_c = -11046.46816018163,
           p = 6.68889408978e-66,
           chi2 = 300.1403351701738,
           df_m = 2,
           rank0 = 2,
           ll_0 = -8509.829005447959,
           k_eq_model = 1,
           ll = -8359.758837862872,
           k_autoCns = 0,
           rc = 0,
           converged = 1,
           k_dv = 1,
           k_eq = 2,
           k = 4,
           ic = 4,
           N = 2925,
           rank = 4,
           alpha = .6463397153671251,
           cmdline = "tnbreg docvis aget totchr if docvis > 1, ll(1)",
           title = "Truncated negative binomial regression",
           title2 = "Truncation points: 1",
           llopt = "1",
           cmd = "tnbreg",
           predict = "tnbreg_p",
           dispers = "mean",
           diparm_opt2 = "noprob",
           chi2_ct = "LR",
           chi2type = "LR",
           opt = "moptimize",
           vce = "oim",
           diparm1 = "lnalpha, exp label(",
           user = "tnbreg_mean",
           crittype = "log likelihood",
           ml_method = "e1",
           singularHmethod = "m-marquardt",
           technique = "nr",
           which = "max",
           depvar = "docvis",
           properties = "b V",
          )

params_table = np.array([
     .01248138313122,  .01373594189048,  .90866598233607,  .36352646578614,
    -.01444056826785,  .03940333453029, np.nan,  1.9599639845401,
                   0,  .22357307664024,  .01291927204045,   17.30539274506,
     4.283502186e-67,  .19825176873449,    .248894384546, np.nan,
     1.9599639845401,                0,  1.4174570100091,   .0439841469348,
     32.226543170437,  7.49935100e-228,  1.3312496661262,   1.503664353892,
    np.nan,  1.9599639845401,                0, -.43643003824212,
     .05825731989132, -7.4914197744811,  6.813252519e-14, -.55061228706494,
     -.3222477894193, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = 'aget totchr _cons _cons'.split()

cov = np.array([
     .00018867609962, -8.418931354e-06, -.00034269534637,  8.523991115e-06,
    -8.418931354e-06,  .00016690759006,  -.0003648589293,  .00010167740775,
    -.00034269534637,  -.0003648589293,  .00193460518158, -.00096544368473,
     8.523991115e-06,  .00010167740775, -.00096544368473,  .00339391532092
    ]).reshape(4,4)

cov_colnames = 'aget totchr _cons _cons'.split()

cov_rownames = 'aget totchr _cons _cons'.split()

icr = np.array([
                2925,  -8509.829005448, -8359.7588378629,                4,
     16727.517675726,  16751.441874764])

icr_colnames = 'N ll0 ll df AIC BIC'.split()

icr_rownames = '.'.split()


results_trunc_negbin1 = Bunch(
                params_table=params_table,
                params_table_colnames=params_table_colnames,
                params_table_rownames=params_table_rownames,
                cov=cov,
                cov_colnames=cov_colnames,
                cov_rownames=cov_rownames,
                icr=icr,
                icr_colnames=icr_colnames,
                icr_rownames=icr_rownames,
                **est
                )

table = np.array([
     8.2699639050783,  .11716249080665,   70.58542241754,                0,
     8.0403296427583,  8.4995981673984, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin1.margins_cm = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
      7.017795994882,   .1490968794197,   47.06869803175,                0,
     6.7255714810121,  7.3100205087519, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin1.margins_means = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     6.6955630594846,  .14246748862917,  46.997129828773,                0,
     6.4163319128035,  6.9747942061656, np.nan,  1.9599639845401,
                   0])

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '_cons'.split()


results_trunc_negbin1.margins_atmeans = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .12259218620014,  .00421507068546,  29.084253942174,  5.67883794e-186,
     .11433079946434,  .13085357293593, np.nan,  1.9599639845401,
                   0,  .11517369272078,  .00228641261466,  50.373100630412,
                   0,  .11069240634225,  .11965497909931, np.nan,
     1.9599639845401,                0,  .10432064271736,  .00125159955387,
     83.349856106274,                0,  .10186755266872,  .10677373276601,
    np.nan,  1.9599639845401,                0,  .65791347836172,
     .00742267145141,  88.635672839381,                0,  .64336530964788,
     .67246164707557, np.nan,  1.9599639845401,                0
    ]).reshape(4,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict'.split()


results_trunc_negbin1.margins_cpr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
     .07948896451521,  .00728414519354,  10.912600230113,  1.003444098e-27,
     .06521230227771,  .09376562675271, np.nan,  1.9599639845401,
                   0,  .09752506791268,  .00432307282658,  22.559200787217,
     1.09075552e-112,  .08905200087003,  .10599813495533, np.nan,
     1.9599639845401,                0,  .09876742202689,  .00202841049952,
     48.692028586009,                0,  .09479181050196,  .10274303355181,
    np.nan,  1.9599639845401,                0,   .0931207168728,
     .00101384753518,  91.848836872758,                0,  .09113361221802,
     .09510782152757, np.nan,  1.9599639845401,                0,
     .63109782867243,  .01370605085442,  46.045198239483,                0,
     .60423446262748,  .65796119471738, np.nan,  1.9599639845401,
                   0]).reshape(5,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = '1bn._predict 2._predict 3._predict 4._predict 5._predict'.split()


results_trunc_negbin1.margins_pr = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

