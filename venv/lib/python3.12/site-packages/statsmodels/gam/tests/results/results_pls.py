import numpy as np

from statsmodels.tools.tools import Bunch


pls5 = Bunch()

pls5.smooth = Bunch()
pls5.smooth.term = 'times'
pls5.smooth.bs_dim = 7
pls5.smooth.dim = 1
pls5.smooth.by = 'NA'
pls5.smooth.label = 's(times)'
pls5.smooth.sp = 1
pls5.smooth.BD = np.array([
    -0.0322305472050642, 0.0332895629742452, -0.00907144581575865,
    0.00386174436551668, -0.00624916066961505, 0.0181385348730838,
    0.0292384327901831, -0.0717740723184547, 0.054914261809955,
    -0.0158383049768667, 0.00626042823599089, -0.0103543594891998,
    -0.011074996356557, 0.0526128870346165, -0.0930190975208449,
    0.0595200902721069, -0.0135721686724522, 0.00600098849448633,
    0.00625687187895293, -0.0145166841858048, 0.0594020303618183,
    -0.0946831790103269, 0.0511018949689336, -0.0114519440129956,
    -0.00994300967116444, 0.00619931821053046, -0.0156728054209229,
    0.0550549724656574, -0.0669161912708059, 0.0271416199423184,
    0.0177532485636497, -0.00581101171513275, 0.00344705658575325,
    -0.00791532311608746, 0.0293751974079486, -0.0294748398076931
    ]).reshape(6, 6, order='F')

pls5.smooth.xp = np.array([
    2.4, 11.2, 17.8, 24.8, 31.2, 41, 57.6
    ])
pls5.smooth.rank = 5
pls5.smooth.df = 5
pls5.smooth.null_space_dim = 0
pls5.smooth.S_scale = 0.0263073404164214
pls5.smooth.vn = 'times'
pls5.smooth.first_para = 1
pls5.smooth.last_para = 5
pls5.smooth.S = np.array([
    1.63230340200229, -0.830028815742871, 0.901802118895566,
    -0.00398686659390206, 0.518361615576888, -0.830028815742871,
    1.06825930315182, -1.16452750566645, 0.27022513792646,
    -0.156140353580413, 0.901802118895566, -1.16452750566645,
    1.99828270457965, -1.0130803218067, 0.537502835084835,
    -0.00398686659390202, 0.27022513792646, -1.0130803218067,
    1.00336165100886, -0.42156685405732, 0.518361615576888,
    -0.156140353580413, 0.537502835084835, -0.42156685405732,
    0.53026887882367
    ]).reshape(5, 5, order='F')

pls5.coefficients = np.array([
    20.036025308233, -62.6817097954891, -51.0985219408637,
    42.0072653496657, 22.0294169083686
    ])
pls5.residuals = np.array([
    -27.9959461270327, -29.9873153013088, -33.4871658551864,
    -32.1806279683964, -36.2429080803179, -42.1809749693548,
    -42.7967018255375, -41.6315122829302, -43.3634717352437,
    -43.0512160670889, -40.6362776289091, -42.0362776289091,
    -39.5456492135197, -37.7626842421689, -39.4270272919089,
    -34.3534190712615, -34.2581579025003, -25.620485822405,
    -9.20474188594053, -4.25666047069376, 0.973620269858165,
    -1.98289744771254, 5.91710255228746, 5.91710255228746,
    2.01710255228746, -4.68289744771254, -11.4828974477125,
    11.2258684745284, -1.09750724577815, -10.3975072457782,
    -31.7975072457782, -33.1975072457782, -15.9436533005046,
    2.75634669949536, 5.28012541131612, -24.0198745886839,
    -13.6327100060003, 2.46728999399974, 10.2113015518619,
    -19.0886984481381, -29.9886984481382, 28.7056211892181,
    -46.2943788107819, -22.5562899896158, -32.2809708803244,
    -52.3809708803244, -38.9809708803244, 9.5618305244081,
    -38.5381694755919, -76.0381694755919, -54.8381694755919,
    -50.1747823609389, -55.4747823609389, -57.1466027401597,
    4.55339725984031, -64.0315014095472, -25.5086559459592,
    -12.2086559459592, -66.1950864997126, -60.0191254292233,
    -54.3517512165439, -69.7295615244473, -37.7403864731789,
    -44.8180470316897, -59.986700874309, -65.3734281515862,
    -72.1088351546469, -60.8834427039697, -45.3341377885245,
    -32.0341377885245, -7.8297215083258, -23.3552855597822,
    -16.5552855597822, -36.3779785846358, -8.37797858463581,
    6.39642948584952, 22.0623645725674, -82.6278644992308,
    2.97213550076917, -44.1880620398212, -4.09724591449898,
    -36.9460977704119, -15.5460977704119, 18.1539022295881,
    6.0922444071392, 4.22670173667689, -32.5314729126827, 26.4685270873173,
    32.6407254849959, -44.0843113342156, -1.69362292204985,
    27.696595904506, -41.2062580917222, -0.884830576710961,
    -7.58483057671096, -13.0853931943938, -45.7920300180993,
    -16.8154499346668, -61.2575969312418, 12.8304879487218,
    -77.4317921021069, -116.331792102107, 8.65836551460932,
    -25.5758467119592, -28.2758467119592, -95.7639644866625,
    -35.4639644866625, -2.21637929151667, -38.4163792915167,
    -36.3347820148442, -41.7779999343159, -58.240721990024,
    -47.6160223101961, 3.1108829637715, -38.3891170362285,
    5.46970557944417, -22.2450916690901, -32.9450916690901,
    -6.74997284201094, -19.2369803024212, -16.7419595489538,
    -4.46697733958345, -1.48504457098591, -37.8385180985158,
    -25.7385180985158, -24.0434946415046, -11.5970529121364,
    -2.67189343191111, -30.2794900717192, -22.6274495550672,
    -9.22744955506716, -23.7435355032739, -17.2959461270327
    ])
pls5.fitted_values = np.array([
    27.9959461270327, 28.6873153013088, 30.7871658551864, 32.1806279683964,
    33.5429080803179, 39.4809749693548, 40.0967018255375, 40.3315122829302,
    40.6634717352437, 40.3512160670889, 39.3362776289091, 39.3362776289091,
    36.8456492135197, 35.0626842421689, 34.0270272919089, 31.6534190712615,
    28.8581579025003, 25.620485822405, 6.50474188594053, 1.55666047069376,
    -0.973620269858165, -11.3171025522875, -11.3171025522875,
    -11.3171025522875, -11.3171025522875, -11.3171025522875,
    -11.3171025522875, -13.9258684745284, -21.7024927542218,
    -21.7024927542218, -21.7024927542218, -21.7024927542218,
    -24.2563466994954, -24.2563466994954, -26.7801254113161,
    -26.7801254113161, -29.2672899939997, -29.2672899939997,
    -31.7113015518619, -31.7113015518619, -31.7113015518619,
    -34.1056211892181, -34.1056211892181, -36.4437100103842,
    -38.7190291196756, -38.7190291196756, -38.7190291196756,
    -47.0618305244081, -47.0618305244081, -47.0618305244081,
    -47.0618305244081, -48.9252176390611, -48.9252176390611,
    -55.3533972598403, -55.3533972598403, -59.0684985904528,
    -60.0913440540408, -60.0913440540408, -61.0049135002874,
    -63.0808745707767, -63.5482487834561, -64.2704384755527,
    -64.1596135268211, -63.5819529683103, -63.113299125691,
    -57.7265718484138, -56.3911648453531, -51.6165572960303,
    -49.7658622114755, -49.7658622114755, -45.6702784916742,
    -41.0447144402178, -41.0447144402178, -35.9220214153642,
    -35.9220214153642, -33.1964294858495, -27.4623645725674,
    -24.4721355007692, -24.4721355007692, -21.4119379601788,
    -11.902754085501, -8.65390222958812, -8.65390222958812,
    -8.65390222958812, -2.0922444071392, 7.77329826332311,
    11.0314729126827, 11.0314729126827, 14.2592745150041, 26.6843113342156,
    37.8936229220499, 47.303404095494, 49.3062580917222, 55.784830576711,
    55.784830576711, 59.9853931943938, 61.7920300180993, 62.4154499346668,
    62.5575969312418, 62.1695120512782, 61.4317921021069, 61.4317921021069,
    60.9416344853907, 60.3758467119592, 60.3758467119592, 58.2639644866625,
    58.2639644866625, 49.1163792915167, 49.1163792915167, 41.7347820148442,
    40.4779999343159, 36.740721990024, 34.3160223101961, 27.6891170362285,
    27.6891170362285, 23.9302944205558, 22.2450916690901, 22.2450916690901,
    21.4499728420109, 17.9369803024212, 16.7419595489538, 15.1669773395835,
    12.1850445709859, 11.0385180985158, 11.0385180985158, 10.7434946415046,
    11.5970529121364, 13.3718934319111, 15.5794900717192, 19.9274495550672,
    19.9274495550672, 21.0435355032739, 27.9959461270327
    ])
pls5.linear_predictors = np.array([
    27.9959461270327, 28.6873153013088, 30.7871658551864, 32.1806279683964,
    33.5429080803179, 39.4809749693548, 40.0967018255375, 40.3315122829302,
    40.6634717352437, 40.3512160670889, 39.3362776289091, 39.3362776289091,
    36.8456492135197, 35.0626842421689, 34.0270272919089, 31.6534190712615,
    28.8581579025003, 25.620485822405, 6.50474188594053, 1.55666047069376,
    -0.973620269858165, -11.3171025522875, -11.3171025522875,
    -11.3171025522875, -11.3171025522875, -11.3171025522875,
    -11.3171025522875, -13.9258684745284, -21.7024927542218,
    -21.7024927542218, -21.7024927542218, -21.7024927542218,
    -24.2563466994954, -24.2563466994954, -26.7801254113161,
    -26.7801254113161, -29.2672899939997, -29.2672899939997,
    -31.7113015518619, -31.7113015518619, -31.7113015518619,
    -34.1056211892181, -34.1056211892181, -36.4437100103842,
    -38.7190291196756, -38.7190291196756, -38.7190291196756,
    -47.0618305244081, -47.0618305244081, -47.0618305244081,
    -47.0618305244081, -48.9252176390611, -48.9252176390611,
    -55.3533972598403, -55.3533972598403, -59.0684985904528,
    -60.0913440540408, -60.0913440540408, -61.0049135002874,
    -63.0808745707767, -63.5482487834561, -64.2704384755527,
    -64.1596135268211, -63.5819529683103, -63.113299125691,
    -57.7265718484138, -56.3911648453531, -51.6165572960303,
    -49.7658622114755, -49.7658622114755, -45.6702784916742,
    -41.0447144402178, -41.0447144402178, -35.9220214153642,
    -35.9220214153642, -33.1964294858495, -27.4623645725674,
    -24.4721355007692, -24.4721355007692, -21.4119379601788,
    -11.902754085501, -8.65390222958812, -8.65390222958812,
    -8.65390222958812, -2.0922444071392, 7.77329826332311,
    11.0314729126827, 11.0314729126827, 14.2592745150041, 26.6843113342156,
    37.8936229220499, 47.303404095494, 49.3062580917222, 55.784830576711,
    55.784830576711, 59.9853931943938, 61.7920300180993, 62.4154499346668,
    62.5575969312418, 62.1695120512782, 61.4317921021069, 61.4317921021069,
    60.9416344853907, 60.3758467119592, 60.3758467119592, 58.2639644866625,
    58.2639644866625, 49.1163792915167, 49.1163792915167, 41.7347820148442,
    40.4779999343159, 36.740721990024, 34.3160223101961, 27.6891170362285,
    27.6891170362285, 23.9302944205558, 22.2450916690901, 22.2450916690901,
    21.4499728420109, 17.9369803024212, 16.7419595489538, 15.1669773395835,
    12.1850445709859, 11.0385180985158, 11.0385180985158, 10.7434946415046,
    11.5970529121364, 13.3718934319111, 15.5794900717192, 19.9274495550672,
    19.9274495550672, 21.0435355032739, 27.9959461270327
    ])
pls5.deviance = 180391.104352065
pls5.null_deviance = 395017.34
pls5.iter = 1
pls5.weights = np.array([
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
    ])
pls5.prior_weights = np.array([
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
    ])
pls5.df_null = 133
pls5.y = np.array([
    0, -1.3, -2.7, 0, -2.7, -2.7, -2.7, -1.3, -2.7, -2.7, -1.3, -2.7, -2.7,
    -2.7, -5.4, -2.7, -5.4, 0, -2.7, -2.7, 0, -13.3, -5.4, -5.4, -9.3, -16,
    -22.8, -2.7, -22.8, -32.1, -53.5, -54.9, -40.2, -21.5, -21.5, -50.8,
    -42.9, -26.8, -21.5, -50.8, -61.7, -5.4, -80.4, -59, -71, -91.1, -77.7,
    -37.5, -85.6, -123.1, -101.9, -99.1, -104.4, -112.5, -50.8, -123.1,
    -85.6, -72.3, -127.2, -123.1, -117.9, -134, -101.9, -108.4, -123.1,
    -123.1, -128.5, -112.5, -95.1, -81.8, -53.5, -64.4, -57.6, -72.3,
    -44.3, -26.8, -5.4, -107.1, -21.5, -65.6, -16, -45.6, -24.2, 9.5, 4,
    12, -21.5, 37.5, 46.9, -17.4, 36.2, 75, 8.1, 54.9, 48.2, 46.9, 16,
    45.6, 1.3, 75, -16, -54.9, 69.6, 34.8, 32.1, -37.5, 22.8, 46.9, 10.7,
    5.4, -1.3, -21.5, -13.3, 30.8, -10.7, 29.4, 0, -10.7, 14.7, -1.3, 0,
    10.7, 10.7, -26.8, -14.7, -13.3, 0, 10.7, -14.7, -2.7, 10.7, -2.7, 10.7
    ])
pls5.sig2 = 1405.72589061551
pls5.edf = np.array([
    0.893973446261888, 0.964904681210716, 0.910197466057859,
    0.951689945168155, 0.953288730104318
    ])
pls5.edf1 = np.array([
    0.981711944544246, 0.996264240571943, 0.982938111847731,
    0.994003453703398, 0.993140527833467
    ])
pls5.hat = np.array([
    0.0648926406262456, 0.0631966900584761, 0.0577531173208777,
    0.054095436609033, 0.050646403216983, 0.0414030519922767,
    0.0419393714799485, 0.0424407004729246, 0.0466884028411439,
    0.0488322930145467, 0.0519315914083648, 0.0519315914083648,
    0.0547480920171652, 0.0551378158184696, 0.055000499312335,
    0.0539763060097797, 0.0518779851195461, 0.0486811707669425,
    0.0278002106937094, 0.0236664981666194, 0.0218678587405494,
    0.0168702076728429, 0.0168702076728429, 0.0168702076728429,
    0.0168702076728429, 0.0168702076728429, 0.0168702076728429,
    0.0162099114442375, 0.0155969292014215, 0.0155969292014215,
    0.0155969292014215, 0.0155969292014215, 0.0158045134149012,
    0.0158045134149012, 0.0161857205884225, 0.0161857205884225,
    0.016715730291764, 0.016715730291764, 0.0173667434517008,
    0.0173667434517008, 0.0173667434517008, 0.0181085269560167,
    0.0181085269560167, 0.01890902644145, 0.0197350472655703,
    0.0197350472655703, 0.0197350472655703, 0.0226344032048916,
    0.0226344032048916, 0.0226344032048916, 0.0226344032048916,
    0.0231064605220144, 0.0231064605220144, 0.0236010474476975,
    0.0236010474476975, 0.0229362836765587, 0.0226341946832984,
    0.0226341946832984, 0.022331993508414, 0.0216507968090011,
    0.0215654072946402, 0.022307353392817, 0.0227976525538385,
    0.0241408042749582, 0.0249779152827976, 0.0312910194327127,
    0.0323419434967748, 0.0349400511510723, 0.0355272516690269,
    0.0355272516690269, 0.0361324218251331, 0.0358606650811796,
    0.0358606650811796, 0.0347553939582878, 0.0347553939582878,
    0.0339600300427633, 0.0320525342614235, 0.0310119683533198,
    0.0310119683533198, 0.0299615237560085, 0.0270931967217139,
    0.0263353031813311, 0.0263353031813311, 0.0263353031813311,
    0.0252733837531764, 0.0250806228807981, 0.0254302543661375,
    0.0254302543661375, 0.0259902742331093, 0.0301636177707758,
    0.0364915828338694, 0.0429804641044596, 0.044338813283762,
    0.0478470725808693, 0.0478470725808693, 0.0481431076555552,
    0.0465422084456883, 0.0448254504760713, 0.0416389240869886,
    0.0393309802887003, 0.0370523590709184, 0.0370523590709184,
    0.0359684768956268, 0.0349434963301099, 0.0349434963301099,
    0.0323665679080144, 0.0323665679080144, 0.0312847023217674,
    0.0312847023217674, 0.0364198732168461, 0.037600534161656,
    0.0414170598382483, 0.0440177673095073, 0.0505139848525673,
    0.0505139848525673, 0.0529266400408666, 0.0535167237447347,
    0.0535167237447347, 0.0536669701648488, 0.0531784414477218,
    0.0525292698619672, 0.0512621693606931, 0.0477153739370841,
    0.04661692338803, 0.04661692338803, 0.0475945362276516,
    0.054093097958197, 0.0620671413133406, 0.0689518115696408,
    0.0748549607794503, 0.0748549607794503, 0.0749105486240515,
    0.0648926406262456
    ])
pls5.R = np.array([
    0.419812589435278, -0.402017675646454, 0.488999441150794,
    3.98552866496736, 0, 4.86284047828201, 0, 0, 0, 0, -0.642048459345638,
    4.51912765154044, 0, 0, 0, -0.287016774143419, 0.0590176744049711,
    4.48152596952222, 0, 0, -0.881192283208667, -0.721748467517155,
    0.141530733755857, -0.975518963486962, 3.68675964176163
    ]).reshape(5, 5, order='F')

pls5.sp = None
pls5.nsdf = 0
pls5.Ve = np.array([
    72.3041357618152, 1.66913532916499, 0.502555357170923,
    -6.08025176950849, 12.003156582929, 1.66913532916499, 60.8548565958852,
    15.0519202741408, 3.17016170151452, 18.3724060596875,
    0.502555357170922, 15.0519202741408, 59.9017269122546,
    5.31826437849191, 9.33171431050043, -6.08025176950849,
    3.17016170151452, 5.31826437849191, 64.066598433814, 0.697025995194202,
    12.003156582929, 18.3724060596875, 9.33171431050043, 0.697025995194202,
    90.4171907271362
    ]).reshape(5, 5, order='F')

pls5.Vp = np.array([
    82.3434717904795, -0.200148537531833, 5.72440590880333,
    -8.16239095841476, 17.8905796109278, -0.200148537531833,
    62.7551455396847, 13.6184531397824, 3.34367783462779, 18.6689815079065,
    5.72440590880333, 13.6184531397824, 65.7343350358227, 1.64716527529062,
    13.483565756752, -8.16239095841476, 3.34367783462779, 1.64716527529062,
    67.4465665922581, -2.30098498787076, 17.8905796109278,
    18.6689815079065, 13.483565756752, -2.30098498787076, 96.3416133118078
    ]).reshape(5, 5, order='F')

pls5.rV = np.array([
    -0.0392296145497642, -0.148679582608057, 0.0918117471236096,
    0.00695938411477142, 0.0468995189236096, -0.00179994351487469,
    0.056193728117226, 0.144936306168724, -0.104973822562831,
    -0.0915159363023183, -0.112299188421777, 0.00531211090361532,
    -0.0656150416863762, -0.150002132732638, 0.073614883684395,
    0.168793284880097, -0.101999375560153, -0.0661656482881794,
    -0.11825783453015, -0.035090842164745, 0.126224824188388,
    0.0945889794789336, 0.0929660567115005, -0.020649293225403,
    0.226516853583393
    ]).reshape(5, 5, order='F')

pls5.gcv_ubre = 1456.9270648002
pls5.aic = 1348.0527067527
pls5.rank = 5
pls5.gcv_ubre_dev = 1456.92706480021
pls5.method = 'GCV'
pls5.full_sp = 1
pls5.cmX = np.array([
    0, 0, 0, 0, 0
    ])
pls5.model = np.array([
    0, -1.3, -2.7, 0, -2.7, -2.7, -2.7, -1.3, -2.7, -2.7, -1.3, -2.7, -2.7,
    -2.7, -5.4, -2.7, -5.4, 0, -2.7, -2.7, 0, -13.3, -5.4, -5.4, -9.3, -16,
    -22.8, -2.7, -22.8, -32.1, -53.5, -54.9, -40.2, -21.5, -21.5, -50.8,
    -42.9, -26.8, -21.5, -50.8, -61.7, -5.4, -80.4, -59, -71, -91.1, -77.7,
    -37.5, -85.6, -123.1, -101.9, -99.1, -104.4, -112.5, -50.8, -123.1,
    -85.6, -72.3, -127.2, -123.1, -117.9, -134, -101.9, -108.4, -123.1,
    -123.1, -128.5, -112.5, -95.1, -81.8, -53.5, -64.4, -57.6, -72.3,
    -44.3, -26.8, -5.4, -107.1, -21.5, -65.6, -16, -45.6, -24.2, 9.5, 4,
    12, -21.5, 37.5, 46.9, -17.4, 36.2, 75, 8.1, 54.9, 48.2, 46.9, 16,
    45.6, 1.3, 75, -16, -54.9, 69.6, 34.8, 32.1, -37.5, 22.8, 46.9, 10.7,
    5.4, -1.3, -21.5, -13.3, 30.8, -10.7, 29.4, 0, -10.7, 14.7, -1.3, 0,
    10.7, 10.7, -26.8, -14.7, -13.3, 0, 10.7, -14.7, -2.7, 10.7, -2.7,
    10.7, 2.4, 2.6, 3.2, 3.6, 4, 6.2, 6.6, 6.8, 7.8, 8.2, 8.8, 8.8, 9.6,
    10, 10.2, 10.6, 11, 11.4, 13.2, 13.6, 13.8, 14.6, 14.6, 14.6, 14.6,
    14.6, 14.6, 14.8, 15.4, 15.4, 15.4, 15.4, 15.6, 15.6, 15.8, 15.8, 16,
    16, 16.2, 16.2, 16.2, 16.4, 16.4, 16.6, 16.8, 16.8, 16.8, 17.6, 17.6,
    17.6, 17.6, 17.8, 17.8, 18.6, 18.6, 19.2, 19.4, 19.4, 19.6, 20.2, 20.4,
    21.2, 21.4, 21.8, 22, 23.2, 23.4, 24, 24.2, 24.2, 24.6, 25, 25, 25.4,
    25.4, 25.6, 26, 26.2, 26.2, 26.4, 27, 27.2, 27.2, 27.2, 27.6, 28.2,
    28.4, 28.4, 28.6, 29.4, 30.2, 31, 31.2, 32, 32, 32.8, 33.4, 33.8, 34.4,
    34.8, 35.2, 35.2, 35.4, 35.6, 35.6, 36.2, 36.2, 38, 38, 39.2, 39.4, 40,
    40.4, 41.6, 41.6, 42.4, 42.8, 42.8, 43, 44, 44.4, 45, 46.6, 47.8, 47.8,
    48.8, 50.6, 52, 53.2, 55, 55, 55.4, 57.6
    ]).reshape(133, 2, order='F')

pls5.assign = None
pls5.offset = np.array([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
    ])
pls5.df_residual = 128.325945731197
pls5.min_edf = 0
pls5.optimizer = 'magic'
pls5.p_coeff = None
pls5.se = np.array([
    9.07433037697435, 7.92181453580458, 8.10767136950078, 8.21258586513761,
    9.81537637137811
    ])
pls5.p_t = None
pls5.p_pv = None
pls5.residual_df = 128.325945731197
pls5.m = 1
pls5.chi_sq = 147.917821150898
pls5.s_pv = 8.54761661656479e-25
pls5.scale = 1405.72589061551
pls5.r_sq = 0.687640791604775
pls5.n = 133
pls5.dev_expl = 0.543333706940396
pls5.edf = 4.67405426880294
pls5.dispersion = 1405.72589061551
pls5.pTerms_pv = None
pls5.pTerms_chi_sq = None
pls5.pTerms_df = None
pls5.cov_unscaled = np.array([
    0.0585771894365725, -0.000142380914279238, 0.00407220635759709,
    -0.0058065309979037, 0.0127269332736656, -0.000142380914279238,
    0.0446425195399984, 0.00968784400337069, 0.00237861296924945,
    0.0132806698891575, 0.00407220635759709, 0.00968784400337069,
    0.0467618441651097, 0.00117175424190942, 0.00959188832386668,
    -0.0058065309979037, 0.00237861296924945, 0.00117175424190942,
    0.0479798850135184, -0.00163686605136315, 0.0127269332736656,
    0.0132806698891575, 0.00959188832386668, -0.00163686605136315,
    0.0685351347335742
    ]).reshape(5, 5, order='F')

pls5.cov_scaled = np.array([
    82.3434717904795, -0.200148537531833, 5.72440590880333,
    -8.16239095841476, 17.8905796109278, -0.200148537531833,
    62.7551455396847, 13.6184531397824, 3.34367783462779, 18.6689815079065,
    5.72440590880333, 13.6184531397824, 65.7343350358227, 1.64716527529062,
    13.483565756752, -8.16239095841476, 3.34367783462779, 1.64716527529062,
    67.4465665922581, -2.30098498787076, 17.8905796109278,
    18.6689815079065, 13.483565756752, -2.30098498787076, 96.3416133118078
    ]).reshape(5, 5, order='F')

pls5.s_table = np.array([
    4.67405426880294, 5, 29.5835642301796, 8.54761661656479e-25
    ]).reshape(1, 4, order='F')

pls5.method = 'GCV'
pls5.sp_criterion = 1456.9270648002
pls5.rank = 5
pls5.np = 5
pls5.params = np.array([
    20.036025308233, -62.6817097954891, -51.0985219408637,
    42.0072653496657, 22.0294169083686
    ])
