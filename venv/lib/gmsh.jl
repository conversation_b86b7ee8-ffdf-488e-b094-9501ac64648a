# Gmsh - Copyright (C) 1997-2025 <PERSON><PERSON>, J.-<PERSON><PERSON>
#
# See the LICENSE.txt file in the Gmsh root directory for license information.
# Please report all issues on https://gitlab.onelab.info/gmsh/gmsh/issues.

# This file defines the Gmsh Julia API (v4.14.0).
#
# Do not edit this file directly: it is automatically generated by `api/gen.py'.
#
# By design, the Gmsh Julia API is purely functional, and only uses elementary
# Julia types. See `tutorials/julia' and `examples/api' for tutorials and
# examples.

"""
    module gmsh

Gmsh is an automatic three-dimensional finite element mesh generator with a
built-in CAD engine and post-processor. Its design goal is to provide a fast,
light and user-friendly meshing tool with parametric input and flexible
visualization capabilities. Gmsh is built around four modules (geometry, mesh,
solver and post-processing), which can be controlled with the graphical user
interface, from the command line, using text files written in Gmsh's own
scripting language (.geo files), or through the C++, C, Python, Julia and
Fortran application programming interface (API).

This module defines the Gmsh Julia API.
"""
module gmsh

const GMSH_API_VERSION = "4.14.0"
const GMSH_API_VERSION_MAJOR = 4
const GMSH_API_VERSION_MINOR = 14
const GMSH_API_VERSION_PATCH = 0
const libdir = dirname(@__FILE__)
const libname = Sys.iswindows() ? "gmsh-4.14.dll" : "libgmsh"
import Libdl
const lib = Libdl.find_library([libname], [libdir])

"""
    gmsh.initialize(argv = Vector{String}(), readConfigFiles = true, run = false)

Initialize the Gmsh API. This must be called before any call to the other
functions in the API. If `argc` and `argv` (or just `argv` in Python or Julia)
are provided, they will be handled in the same way as the command line arguments
in the Gmsh app. If `readConfigFiles` is set, read system Gmsh configuration
files (gmshrc and gmsh-options). If `run` is set, run in the same way as the
Gmsh app, either interactively or in batch mode depending on the command line
arguments. If `run` is not set, initializing the API sets the options
"General.AbortOnError" to 2 and "General.Terminal" to 1.

Types:
 - `argv`: command line arguments
 - `readConfigFiles`: boolean
 - `run`: boolean
"""
function initialize(argv = Vector{String}(), readConfigFiles = true, run = false)
    ierr = Ref{Cint}()
    ccall((:gmshInitialize, lib), Cvoid,
          (Cint, Ptr{Ptr{Cchar}}, Cint, Cint, Ptr{Cint}),
          length(argv), argv, readConfigFiles, run, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.isInitialized()

Return 1 if the Gmsh API is initialized, and 0 if not.

Return an integer.
"""
function isInitialized()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshIsInitialized, lib), Cint,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const is_initialized = isInitialized

"""
    gmsh.finalize()

Finalize the Gmsh API. This must be called when you are done using the Gmsh API.
"""
function finalize()
    ierr = Ref{Cint}()
    ccall((:gmshFinalize, lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.open(fileName)

Open a file. Equivalent to the `File->Open` menu in the Gmsh app. Handling of
the file depends on its extension and/or its contents: opening a file with model
data will create a new model.

Types:
 - `fileName`: string
"""
function open(fileName)
    ierr = Ref{Cint}()
    ccall((:gmshOpen, lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          fileName, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.merge(fileName)

Merge a file. Equivalent to the `File->Merge` menu in the Gmsh app. Handling of
the file depends on its extension and/or its contents. Merging a file with model
data will add the data to the current model.

Types:
 - `fileName`: string
"""
function merge(fileName)
    ierr = Ref{Cint}()
    ccall((:gmshMerge, lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          fileName, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.write(fileName)

Write a file. The export format is determined by the file extension.

Types:
 - `fileName`: string
"""
function write(fileName)
    ierr = Ref{Cint}()
    ccall((:gmshWrite, lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          fileName, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.clear()

Clear all loaded models and post-processing data, and add a new empty model.
"""
function clear()
    ierr = Ref{Cint}()
    ccall((:gmshClear, lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    module gmsh.option

Option handling functions
"""
module option

import ..gmsh

"""
    gmsh.option.setNumber(name, value)

Set a numerical option to `value`. `name` is of the form "Category.Option" or
"Category[num].Option". Available categories and options are listed in the "Gmsh
options" chapter of the Gmsh reference manual
(https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

Types:
 - `name`: string
 - `value`: double
"""
function setNumber(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshOptionSetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cdouble, Ptr{Cint}),
          name, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.option.getNumber(name)

Get the `value` of a numerical option. `name` is of the form "Category.Option"
or "Category[num].Option". Available categories and options are listed in the
"Gmsh options" chapter of the Gmsh reference manual
(https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

Return `value`.

Types:
 - `name`: string
 - `value`: double
"""
function getNumber(name)
    api_value_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshOptionGetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cdouble}, Ptr{Cint}),
          name, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_value_[]
end
const get_number = getNumber

"""
    gmsh.option.setString(name, value)

Set a string option to `value`. `name` is of the form "Category.Option" or
"Category[num].Option". Available categories and options are listed in the "Gmsh
options" chapter of the Gmsh reference manual
(https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

Types:
 - `name`: string
 - `value`: string
"""
function setString(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshOptionSetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          name, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.option.getString(name)

Get the `value` of a string option. `name` is of the form "Category.Option" or
"Category[num].Option". Available categories and options are listed in the "Gmsh
options" chapter of the Gmsh reference manual
(https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options).

Return `value`.

Types:
 - `name`: string
 - `value`: string
"""
function getString(name)
    api_value_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshOptionGetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          name, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    value = unsafe_string(api_value_[])
    return value
end
const get_string = getString

"""
    gmsh.option.setColor(name, r, g, b, a = 255)

Set a color option to the RGBA value (`r`, `g`, `b`, `a`), where where `r`, `g`,
`b` and `a` should be integers between 0 and 255. `name` is of the form
"Category.Color.Option" or "Category[num].Color.Option". Available categories
and options are listed in the "Gmsh options" chapter of the Gmsh reference
manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options). For conciseness
"Color." can be ommitted in `name`.

Types:
 - `name`: string
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
"""
function setColor(name, r, g, b, a = 255)
    ierr = Ref{Cint}()
    ccall((:gmshOptionSetColor, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cint, Cint, Cint, Cint, Ptr{Cint}),
          name, r, g, b, a, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_color = setColor

"""
    gmsh.option.getColor(name)

Get the `r`, `g`, `b`, `a` value of a color option. `name` is of the form
"Category.Color.Option" or "Category[num].Color.Option". Available categories
and options are listed in the "Gmsh options" chapter of the Gmsh reference
manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-options). For conciseness
"Color." can be ommitted in `name`.

Return `r`, `g`, `b`, `a`.

Types:
 - `name`: string
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
"""
function getColor(name)
    api_r_ = Ref{Cint}()
    api_g_ = Ref{Cint}()
    api_b_ = Ref{Cint}()
    api_a_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshOptionGetColor, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          name, api_r_, api_g_, api_b_, api_a_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_r_[], api_g_[], api_b_[], api_a_[]
end
const get_color = getColor

"""
    gmsh.option.restoreDefaults()

Restore all options to default settings.
"""
function restoreDefaults()
    ierr = Ref{Cint}()
    ccall((:gmshOptionRestoreDefaults, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const restore_defaults = restoreDefaults

end # end of module option

"""
    module gmsh.model

Model functions
"""
module model

import ..gmsh

"""
    gmsh.model.add(name)

Add a new model, with name `name`, and set it as the current model.

Types:
 - `name`: string
"""
function add(name)
    ierr = Ref{Cint}()
    ccall((:gmshModelAdd, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.remove()

Remove the current model.
"""
function remove()
    ierr = Ref{Cint}()
    ccall((:gmshModelRemove, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.list()

List the names of all models.

Return `names`.

Types:
 - `names`: vector of strings
"""
function list()
    api_names_ = Ref{Ptr{Ptr{Cchar}}}()
    api_names_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelList, gmsh.lib), Cvoid,
          (Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          api_names_, api_names_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_names_ = unsafe_wrap(Array, api_names_[], api_names_n_[], own = true)
    names = [unsafe_string(tmp_api_names_[i]) for i in 1:length(tmp_api_names_) ]
    return names
end

"""
    gmsh.model.getCurrent()

Get the name of the current model.

Return `name`.

Types:
 - `name`: string
"""
function getCurrent()
    api_name_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetCurrent, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cchar}}, Ptr{Cint}),
          api_name_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    name = unsafe_string(api_name_[])
    return name
end
const get_current = getCurrent

"""
    gmsh.model.setCurrent(name)

Set the current model to the model with name `name`. If several models have the
same name, select the one that was added first.

Types:
 - `name`: string
"""
function setCurrent(name)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetCurrent, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_current = setCurrent

"""
    gmsh.model.getFileName()

Get the file name (if any) associated with the current model. A file name is
associated when a model is read from a file on disk.

Return `fileName`.

Types:
 - `fileName`: string
"""
function getFileName()
    api_fileName_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetFileName, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cchar}}, Ptr{Cint}),
          api_fileName_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    fileName = unsafe_string(api_fileName_[])
    return fileName
end
const get_file_name = getFileName

"""
    gmsh.model.setFileName(fileName)

Set the file name associated with the current model.

Types:
 - `fileName`: string
"""
function setFileName(fileName)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetFileName, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          fileName, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_file_name = setFileName

"""
    gmsh.model.getEntities(dim = -1)

Get all the entities in the current model. A model entity is represented by two
integers: its dimension (dim == 0, 1, 2 or 3) and its tag (its unique, strictly
positive identifier). If `dim` is >= 0, return only the entities of the
specified dimension (e.g. points if `dim` == 0). The entities are returned as a
vector of (dim, tag) pairs.

Return `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function getEntities(dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntities, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_entities = getEntities

"""
    gmsh.model.setEntityName(dim, tag, name)

Set the name of the entity of dimension `dim` and tag `tag`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `name`: string
"""
function setEntityName(dim, tag, name)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetEntityName, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cint}),
          dim, tag, name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_entity_name = setEntityName

"""
    gmsh.model.getEntityName(dim, tag)

Get the name of the entity of dimension `dim` and tag `tag`.

Return `name`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `name`: string
"""
function getEntityName(dim, tag)
    api_name_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntityName, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          dim, tag, api_name_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    name = unsafe_string(api_name_[])
    return name
end
const get_entity_name = getEntityName

"""
    gmsh.model.removeEntityName(name)

Remove the entity name `name` from the current model.

Types:
 - `name`: string
"""
function removeEntityName(name)
    ierr = Ref{Cint}()
    ccall((:gmshModelRemoveEntityName, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_entity_name = removeEntityName

"""
    gmsh.model.getPhysicalGroups(dim = -1)

Get the physical groups in the current model. The physical groups are returned
as a vector of (dim, tag) pairs. If `dim` is >= 0, return only the groups of the
specified dimension (e.g. physical points if `dim` == 0).

Return `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function getPhysicalGroups(dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPhysicalGroups, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_physical_groups = getPhysicalGroups

"""
    gmsh.model.getPhysicalGroupsEntities(dim = -1)

Get the physical groups in the current model as well as the model entities that
make them up. The physical groups are returned as the vector of (dim, tag) pairs
`dimTags`. The model entities making up the corresponding physical groups are
returned in `entities`. If `dim` is >= 0, return only the groups of the
specified dimension (e.g. physical points if `dim` == 0).

Return `dimTags`, `entities`.

Types:
 - `dimTags`: vector of pairs of integers
 - `entities`: vector of vectors of pairs of integers
 - `dim`: integer
"""
function getPhysicalGroupsEntities(dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    api_entities_ = Ref{Ptr{Ptr{Cint}}}()
    api_entities_n_ = Ref{Ptr{Csize_t}}()
    api_entities_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPhysicalGroupsEntities, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_entities_, api_entities_n_, api_entities_nn_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    tmp_api_entities_ = unsafe_wrap(Array, api_entities_[], api_entities_nn_[], own = true)
    tmp_api_entities_n_ = unsafe_wrap(Array, api_entities_n_[], api_entities_nn_[], own = true)
    entities = Vector{Tuple{Cint,Cint}}[]
    resize!(entities, api_entities_nn_[])
    for i in 1:api_entities_nn_[]
        tmp = unsafe_wrap(Array, tmp_api_entities_[i], tmp_api_entities_n_[i], own = true)
        entities[i] = [(tmp[i], tmp[i+1]) for i in 1:2:length(tmp)]
    end
    return dimTags, entities
end
const get_physical_groups_entities = getPhysicalGroupsEntities

"""
    gmsh.model.getEntitiesForPhysicalGroup(dim, tag)

Get the tags of the model entities making up the physical group of dimension
`dim` and tag `tag`.

Return `tags`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `tags`: vector of integers
"""
function getEntitiesForPhysicalGroup(dim, tag)
    api_tags_ = Ref{Ptr{Cint}}()
    api_tags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntitiesForPhysicalGroup, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_tags_, api_tags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    return tags
end
const get_entities_for_physical_group = getEntitiesForPhysicalGroup

"""
    gmsh.model.getEntitiesForPhysicalName(name)

Get the model entities (as a vector (dim, tag) pairs) making up the physical
group with name `name`.

Return `dimTags`.

Types:
 - `name`: string
 - `dimTags`: vector of pairs of integers
"""
function getEntitiesForPhysicalName(name)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntitiesForPhysicalName, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_entities_for_physical_name = getEntitiesForPhysicalName

"""
    gmsh.model.getPhysicalGroupsForEntity(dim, tag)

Get the tags of the physical groups (if any) to which the model entity of
dimension `dim` and tag `tag` belongs.

Return `physicalTags`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `physicalTags`: vector of integers
"""
function getPhysicalGroupsForEntity(dim, tag)
    api_physicalTags_ = Ref{Ptr{Cint}}()
    api_physicalTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPhysicalGroupsForEntity, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_physicalTags_, api_physicalTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    physicalTags = unsafe_wrap(Array, api_physicalTags_[], api_physicalTags_n_[], own = true)
    return physicalTags
end
const get_physical_groups_for_entity = getPhysicalGroupsForEntity

"""
    gmsh.model.addPhysicalGroup(dim, tags, tag = -1, name = "")

Add a physical group of dimension `dim`, grouping the model entities with tags
`tags`. Return the tag of the physical group, equal to `tag` if `tag` is
positive, or a new tag if `tag` < 0. Set the name of the physical group if
`name` is not empty.

Return an integer.

Types:
 - `dim`: integer
 - `tags`: vector of integers
 - `tag`: integer
 - `name`: string
"""
function addPhysicalGroup(dim, tags, tag = -1, name = "")
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelAddPhysicalGroup, gmsh.lib), Cint,
          (Cint, Ptr{Cint}, Csize_t, Cint, Ptr{Cchar}, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), tag, name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_physical_group = addPhysicalGroup

"""
    gmsh.model.removePhysicalGroups(dimTags = Tuple{Cint,Cint}[])

Remove the physical groups `dimTags` (given as a vector of (dim, tag) pairs)
from the current model. If `dimTags` is empty, remove all groups.

Types:
 - `dimTags`: vector of pairs of integers
"""
function removePhysicalGroups(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelRemovePhysicalGroups, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_physical_groups = removePhysicalGroups

"""
    gmsh.model.setPhysicalName(dim, tag, name)

Set the name of the physical group of dimension `dim` and tag `tag`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `name`: string
"""
function setPhysicalName(dim, tag, name)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetPhysicalName, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cint}),
          dim, tag, name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_physical_name = setPhysicalName

"""
    gmsh.model.getPhysicalName(dim, tag)

Get the name of the physical group of dimension `dim` and tag `tag`.

Return `name`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `name`: string
"""
function getPhysicalName(dim, tag)
    api_name_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPhysicalName, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          dim, tag, api_name_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    name = unsafe_string(api_name_[])
    return name
end
const get_physical_name = getPhysicalName

"""
    gmsh.model.removePhysicalName(name)

Remove the physical name `name` from the current model.

Types:
 - `name`: string
"""
function removePhysicalName(name)
    ierr = Ref{Cint}()
    ccall((:gmshModelRemovePhysicalName, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_physical_name = removePhysicalName

"""
    gmsh.model.setTag(dim, tag, newTag)

Set the tag of the entity of dimension `dim` and tag `tag` to the new value
`newTag`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `newTag`: integer
"""
function setTag(dim, tag, newTag)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetTag, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, newTag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_tag = setTag

"""
    gmsh.model.getBoundary(dimTags, combined = true, oriented = true, recursive = false)

Get the boundary of the model entities `dimTags`, given as a vector of (dim,
tag) pairs. Return in `outDimTags` the boundary of the individual entities (if
`combined` is false) or the boundary of the combined geometrical shape formed by
all input entities (if `combined` is true). Return tags multiplied by the sign
of the boundary entity if `oriented` is true. Apply the boundary operator
recursively down to dimension 0 (i.e. to points) if `recursive` is true.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `combined`: boolean
 - `oriented`: boolean
 - `recursive`: boolean
"""
function getBoundary(dimTags, combined = true, oriented = true, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetBoundary, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Cint, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_outDimTags_, api_outDimTags_n_, combined, oriented, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const get_boundary = getBoundary

"""
    gmsh.model.getAdjacencies(dim, tag)

Get the upward and downward adjacencies of the model entity of dimension `dim`
and tag `tag`. The `upward` vector returns the tags of adjacent entities of
dimension `dim` + 1; the `downward` vector returns the tags of adjacent entities
of dimension `dim` - 1.

Return `upward`, `downward`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `upward`: vector of integers
 - `downward`: vector of integers
"""
function getAdjacencies(dim, tag)
    api_upward_ = Ref{Ptr{Cint}}()
    api_upward_n_ = Ref{Csize_t}()
    api_downward_ = Ref{Ptr{Cint}}()
    api_downward_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetAdjacencies, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_upward_, api_upward_n_, api_downward_, api_downward_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    upward = unsafe_wrap(Array, api_upward_[], api_upward_n_[], own = true)
    downward = unsafe_wrap(Array, api_downward_[], api_downward_n_[], own = true)
    return upward, downward
end
const get_adjacencies = getAdjacencies

"""
    gmsh.model.getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim = -1)

Get the model entities in the bounding box defined by the two points (`xmin`,
`ymin`, `zmin`) and (`xmax`, `ymax`, `zmax`). If `dim` is >= 0, return only the
entities of the specified dimension (e.g. points if `dim` == 0).

Return `dimTags`.

Types:
 - `xmin`: double
 - `ymin`: double
 - `zmin`: double
 - `xmax`: double
 - `ymax`: double
 - `zmax`: double
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntitiesInBoundingBox, gmsh.lib), Cvoid,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          xmin, ymin, zmin, xmax, ymax, zmax, api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_entities_in_bounding_box = getEntitiesInBoundingBox

"""
    gmsh.model.getBoundingBox(dim, tag)

Get the bounding box (`xmin`, `ymin`, `zmin`), (`xmax`, `ymax`, `zmax`) of the
model entity of dimension `dim` and tag `tag`. If `dim` and `tag` are negative,
get the bounding box of the whole model.

Return `xmin`, `ymin`, `zmin`, `xmax`, `ymax`, `zmax`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `xmin`: double
 - `ymin`: double
 - `zmin`: double
 - `xmax`: double
 - `ymax`: double
 - `zmax`: double
"""
function getBoundingBox(dim, tag)
    api_xmin_ = Ref{Cdouble}()
    api_ymin_ = Ref{Cdouble}()
    api_zmin_ = Ref{Cdouble}()
    api_xmax_ = Ref{Cdouble}()
    api_ymax_ = Ref{Cdouble}()
    api_zmax_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetBoundingBox, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cint}),
          dim, tag, api_xmin_, api_ymin_, api_zmin_, api_xmax_, api_ymax_, api_zmax_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_xmin_[], api_ymin_[], api_zmin_[], api_xmax_[], api_ymax_[], api_zmax_[]
end
const get_bounding_box = getBoundingBox

"""
    gmsh.model.getDimension()

Return the geometrical dimension of the current model.

Return an integer.
"""
function getDimension()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGetDimension, gmsh.lib), Cint,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_dimension = getDimension

"""
    gmsh.model.addDiscreteEntity(dim, tag = -1, boundary = Cint[])

Add a discrete model entity (defined by a mesh) of dimension `dim` in the
current model. Return the tag of the new discrete entity, equal to `tag` if
`tag` is positive, or a new tag if `tag` < 0. `boundary` specifies the tags of
the entities on the boundary of the discrete entity, if any. Specifying
`boundary` allows Gmsh to construct the topology of the overall model.

Return an integer.

Types:
 - `dim`: integer
 - `tag`: integer
 - `boundary`: vector of integers
"""
function addDiscreteEntity(dim, tag = -1, boundary = Cint[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelAddDiscreteEntity, gmsh.lib), Cint,
          (Cint, Cint, Ptr{Cint}, Csize_t, Ptr{Cint}),
          dim, tag, convert(Vector{Cint}, boundary), length(boundary), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_discrete_entity = addDiscreteEntity

"""
    gmsh.model.removeEntities(dimTags, recursive = false)

Remove the entities `dimTags` (given as a vector of (dim, tag) pairs) of the
current model, provided that they are not on the boundary of (or embedded in)
higher-dimensional entities. If `recursive` is true, remove all the entities on
their boundaries, down to dimension 0.

Types:
 - `dimTags`: vector of pairs of integers
 - `recursive`: boolean
"""
function removeEntities(dimTags, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelRemoveEntities, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_entities = removeEntities

"""
    gmsh.model.getEntityType(dim, tag)

Get the type of the entity of dimension `dim` and tag `tag`.

Return `entityType`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `entityType`: string
"""
function getEntityType(dim, tag)
    api_entityType_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntityType, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          dim, tag, api_entityType_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    entityType = unsafe_string(api_entityType_[])
    return entityType
end
const get_entity_type = getEntityType

"""
    gmsh.model.getType(dim, tag)

Get the type of the entity of dimension `dim` and tag `tag`. (This is a
deprecated synonym for `getType`.)

Return `entityType`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `entityType`: string
"""
function getType(dim, tag)
    api_entityType_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetType, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          dim, tag, api_entityType_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    entityType = unsafe_string(api_entityType_[])
    return entityType
end
const get_type = getType

"""
    gmsh.model.getEntityProperties(dim, tag)

Get the properties of the entity of dimension `dim` and tag `tag`. The `reals`
vector contains the 4 coefficients of the cartesian equation for a plane
surface; the center coordinates, axis direction, major radius and minor radius
for a torus; the center coordinates, axis direction and radius for a cylinder;
the center coordinates, axis direction, radius and semi-angle for surfaces of
revolution; the center coordinates and the radius for a sphere.

Return `integers`, `reals`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `integers`: vector of integers
 - `reals`: vector of doubles
"""
function getEntityProperties(dim, tag)
    api_integers_ = Ref{Ptr{Cint}}()
    api_integers_n_ = Ref{Csize_t}()
    api_reals_ = Ref{Ptr{Cdouble}}()
    api_reals_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetEntityProperties, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_integers_, api_integers_n_, api_reals_, api_reals_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    integers = unsafe_wrap(Array, api_integers_[], api_integers_n_[], own = true)
    reals = unsafe_wrap(Array, api_reals_[], api_reals_n_[], own = true)
    return integers, reals
end
const get_entity_properties = getEntityProperties

"""
    gmsh.model.getParent(dim, tag)

In a partitioned model, get the parent of the entity of dimension `dim` and tag
`tag`, i.e. from which the entity is a part of, if any. `parentDim` and
`parentTag` are set to -1 if the entity has no parent.

Return `parentDim`, `parentTag`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parentDim`: integer
 - `parentTag`: integer
"""
function getParent(dim, tag)
    api_parentDim_ = Ref{Cint}()
    api_parentTag_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetParent, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          dim, tag, api_parentDim_, api_parentTag_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_parentDim_[], api_parentTag_[]
end
const get_parent = getParent

"""
    gmsh.model.getNumberOfPartitions()

Return the number of partitions in the model.

Return an integer.
"""
function getNumberOfPartitions()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGetNumberOfPartitions, gmsh.lib), Cint,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_number_of_partitions = getNumberOfPartitions

"""
    gmsh.model.getPartitions(dim, tag)

In a partitioned model, return the tags of the partition(s) to which the entity
belongs.

Return `partitions`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `partitions`: vector of integers
"""
function getPartitions(dim, tag)
    api_partitions_ = Ref{Ptr{Cint}}()
    api_partitions_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPartitions, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_partitions_, api_partitions_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    partitions = unsafe_wrap(Array, api_partitions_[], api_partitions_n_[], own = true)
    return partitions
end
const get_partitions = getPartitions

"""
    gmsh.model.getValue(dim, tag, parametricCoord)

Evaluate the parametrization of the entity of dimension `dim` and tag `tag` at
the parametric coordinates `parametricCoord`. Only valid for `dim` equal to 0
(with empty `parametricCoord`), 1 (with `parametricCoord` containing parametric
coordinates on the curve) or 2 (with `parametricCoord` containing u, v
parametric coordinates on the surface, concatenated: [p1u, p1v, p2u, ...]).
Return x, y, z coordinates in `coord`, concatenated: [p1x, p1y, p1z, p2x, ...].

Return `coord`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `coord`: vector of doubles
"""
function getValue(dim, tag, parametricCoord)
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetValue, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_coord_, api_coord_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return coord
end
const get_value = getValue

"""
    gmsh.model.getDerivative(dim, tag, parametricCoord)

Evaluate the derivative of the parametrization of the entity of dimension `dim`
and tag `tag` at the parametric coordinates `parametricCoord`. Only valid for
`dim` equal to 1 (with `parametricCoord` containing parametric coordinates on
the curve) or 2 (with `parametricCoord` containing u, v parametric coordinates
on the surface, concatenated: [p1u, p1v, p2u, ...]). For `dim` equal to 1 return
the x, y, z components of the derivative with respect to u [d1ux, d1uy, d1uz,
d2ux, ...]; for `dim` equal to 2 return the x, y, z components of the derivative
with respect to u and v: [d1ux, d1uy, d1uz, d1vx, d1vy, d1vz, d2ux, ...].

Return `derivatives`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `derivatives`: vector of doubles
"""
function getDerivative(dim, tag, parametricCoord)
    api_derivatives_ = Ref{Ptr{Cdouble}}()
    api_derivatives_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetDerivative, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_derivatives_, api_derivatives_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    derivatives = unsafe_wrap(Array, api_derivatives_[], api_derivatives_n_[], own = true)
    return derivatives
end
const get_derivative = getDerivative

"""
    gmsh.model.getSecondDerivative(dim, tag, parametricCoord)

Evaluate the second derivative of the parametrization of the entity of dimension
`dim` and tag `tag` at the parametric coordinates `parametricCoord`. Only valid
for `dim` equal to 1 (with `parametricCoord` containing parametric coordinates
on the curve) or 2 (with `parametricCoord` containing u, v parametric
coordinates on the surface, concatenated: [p1u, p1v, p2u, ...]). For `dim` equal
to 1 return the x, y, z components of the second derivative with respect to u
[d1uux, d1uuy, d1uuz, d2uux, ...]; for `dim` equal to 2 return the x, y, z
components of the second derivative with respect to u and v, and the mixed
derivative with respect to u and v: [d1uux, d1uuy, d1uuz, d1vvx, d1vvy, d1vvz,
d1uvx, d1uvy, d1uvz, d2uux, ...].

Return `derivatives`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `derivatives`: vector of doubles
"""
function getSecondDerivative(dim, tag, parametricCoord)
    api_derivatives_ = Ref{Ptr{Cdouble}}()
    api_derivatives_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetSecondDerivative, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_derivatives_, api_derivatives_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    derivatives = unsafe_wrap(Array, api_derivatives_[], api_derivatives_n_[], own = true)
    return derivatives
end
const get_second_derivative = getSecondDerivative

"""
    gmsh.model.getCurvature(dim, tag, parametricCoord)

Evaluate the (maximum) curvature of the entity of dimension `dim` and tag `tag`
at the parametric coordinates `parametricCoord`. Only valid for `dim` equal to 1
(with `parametricCoord` containing parametric coordinates on the curve) or 2
(with `parametricCoord` containing u, v parametric coordinates on the surface,
concatenated: [p1u, p1v, p2u, ...]).

Return `curvatures`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `curvatures`: vector of doubles
"""
function getCurvature(dim, tag, parametricCoord)
    api_curvatures_ = Ref{Ptr{Cdouble}}()
    api_curvatures_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetCurvature, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_curvatures_, api_curvatures_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    curvatures = unsafe_wrap(Array, api_curvatures_[], api_curvatures_n_[], own = true)
    return curvatures
end
const get_curvature = getCurvature

"""
    gmsh.model.getPrincipalCurvatures(tag, parametricCoord)

Evaluate the principal curvatures of the surface with tag `tag` at the
parametric coordinates `parametricCoord`, as well as their respective
directions. `parametricCoord` are given by pair of u and v coordinates,
concatenated: [p1u, p1v, p2u, ...].

Return `curvatureMax`, `curvatureMin`, `directionMax`, `directionMin`.

Types:
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `curvatureMax`: vector of doubles
 - `curvatureMin`: vector of doubles
 - `directionMax`: vector of doubles
 - `directionMin`: vector of doubles
"""
function getPrincipalCurvatures(tag, parametricCoord)
    api_curvatureMax_ = Ref{Ptr{Cdouble}}()
    api_curvatureMax_n_ = Ref{Csize_t}()
    api_curvatureMin_ = Ref{Ptr{Cdouble}}()
    api_curvatureMin_n_ = Ref{Csize_t}()
    api_directionMax_ = Ref{Ptr{Cdouble}}()
    api_directionMax_n_ = Ref{Csize_t}()
    api_directionMin_ = Ref{Ptr{Cdouble}}()
    api_directionMin_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetPrincipalCurvatures, gmsh.lib), Cvoid,
          (Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_curvatureMax_, api_curvatureMax_n_, api_curvatureMin_, api_curvatureMin_n_, api_directionMax_, api_directionMax_n_, api_directionMin_, api_directionMin_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    curvatureMax = unsafe_wrap(Array, api_curvatureMax_[], api_curvatureMax_n_[], own = true)
    curvatureMin = unsafe_wrap(Array, api_curvatureMin_[], api_curvatureMin_n_[], own = true)
    directionMax = unsafe_wrap(Array, api_directionMax_[], api_directionMax_n_[], own = true)
    directionMin = unsafe_wrap(Array, api_directionMin_[], api_directionMin_n_[], own = true)
    return curvatureMax, curvatureMin, directionMax, directionMin
end
const get_principal_curvatures = getPrincipalCurvatures

"""
    gmsh.model.getNormal(tag, parametricCoord)

Get the normal to the surface with tag `tag` at the parametric coordinates
`parametricCoord`. The `parametricCoord` vector should contain u and v
coordinates, concatenated: [p1u, p1v, p2u, ...]. `normals` are returned as a
vector of x, y, z components, concatenated: [n1x, n1y, n1z, n2x, ...].

Return `normals`.

Types:
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `normals`: vector of doubles
"""
function getNormal(tag, parametricCoord)
    api_normals_ = Ref{Ptr{Cdouble}}()
    api_normals_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetNormal, gmsh.lib), Cvoid,
          (Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), api_normals_, api_normals_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    normals = unsafe_wrap(Array, api_normals_[], api_normals_n_[], own = true)
    return normals
end
const get_normal = getNormal

"""
    gmsh.model.getParametrization(dim, tag, coord)

Get the parametric coordinates `parametricCoord` for the points `coord` on the
entity of dimension `dim` and tag `tag`. `coord` are given as x, y, z
coordinates, concatenated: [p1x, p1y, p1z, p2x, ...]. `parametricCoord` returns
the parametric coordinates t on the curve (if `dim` = 1) or u and v coordinates
concatenated on the surface (if `dim` == 2), i.e. [p1t, p2t, ...] or [p1u, p1v,
p2u, ...].

Return `parametricCoord`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
"""
function getParametrization(dim, tag, coord)
    api_parametricCoord_ = Ref{Ptr{Cdouble}}()
    api_parametricCoord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetParametrization, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, coord), length(coord), api_parametricCoord_, api_parametricCoord_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    parametricCoord = unsafe_wrap(Array, api_parametricCoord_[], api_parametricCoord_n_[], own = true)
    return parametricCoord
end
const get_parametrization = getParametrization

"""
    gmsh.model.getParametrizationBounds(dim, tag)

Get the `min` and `max` bounds of the parametric coordinates for the entity of
dimension `dim` and tag `tag`.

Return `min`, `max`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `min`: vector of doubles
 - `max`: vector of doubles
"""
function getParametrizationBounds(dim, tag)
    api_min_ = Ref{Ptr{Cdouble}}()
    api_min_n_ = Ref{Csize_t}()
    api_max_ = Ref{Ptr{Cdouble}}()
    api_max_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetParametrizationBounds, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_min_, api_min_n_, api_max_, api_max_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    min = unsafe_wrap(Array, api_min_[], api_min_n_[], own = true)
    max = unsafe_wrap(Array, api_max_[], api_max_n_[], own = true)
    return min, max
end
const get_parametrization_bounds = getParametrizationBounds

"""
    gmsh.model.isInside(dim, tag, coord, parametric = false)

Check if the coordinates (or the parametric coordinates if `parametric` is set)
provided in `coord` correspond to points inside the entity of dimension `dim`
and tag `tag`, and return the number of points inside. This feature is only
available for a subset of entities, depending on the underlying geometrical
representation.

Return an integer.

Types:
 - `dim`: integer
 - `tag`: integer
 - `coord`: vector of doubles
 - `parametric`: boolean
"""
function isInside(dim, tag, coord, parametric = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelIsInside, gmsh.lib), Cint,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, coord), length(coord), parametric, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const is_inside = isInside

"""
    gmsh.model.getClosestPoint(dim, tag, coord)

Get the points `closestCoord` on the entity of dimension `dim` and tag `tag` to
the points `coord`, by orthogonal projection. `coord` and `closestCoord` are
given as x, y, z coordinates, concatenated: [p1x, p1y, p1z, p2x, ...].
`parametricCoord` returns the parametric coordinates t on the curve (if `dim` ==
1) or u and v coordinates concatenated on the surface (if `dim` = 2), i.e. [p1t,
p2t, ...] or [p1u, p1v, p2u, ...]. The closest points can lie outside the
(trimmed) entities: use `isInside()` to check.

Return `closestCoord`, `parametricCoord`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `coord`: vector of doubles
 - `closestCoord`: vector of doubles
 - `parametricCoord`: vector of doubles
"""
function getClosestPoint(dim, tag, coord)
    api_closestCoord_ = Ref{Ptr{Cdouble}}()
    api_closestCoord_n_ = Ref{Csize_t}()
    api_parametricCoord_ = Ref{Ptr{Cdouble}}()
    api_parametricCoord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetClosestPoint, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, coord), length(coord), api_closestCoord_, api_closestCoord_n_, api_parametricCoord_, api_parametricCoord_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    closestCoord = unsafe_wrap(Array, api_closestCoord_[], api_closestCoord_n_[], own = true)
    parametricCoord = unsafe_wrap(Array, api_parametricCoord_[], api_parametricCoord_n_[], own = true)
    return closestCoord, parametricCoord
end
const get_closest_point = getClosestPoint

"""
    gmsh.model.reparametrizeOnSurface(dim, tag, parametricCoord, surfaceTag, which = 0)

Reparametrize the boundary entity (point or curve, i.e. with `dim` == 0 or `dim`
== 1) of tag `tag` on the surface `surfaceTag`. If `dim` == 1, reparametrize all
the points corresponding to the parametric coordinates `parametricCoord`.
Multiple matches in case of periodic surfaces can be selected with `which`. This
feature is only available for a subset of entities, depending on the underlying
geometrical representation.

Return `surfaceParametricCoord`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `surfaceTag`: integer
 - `surfaceParametricCoord`: vector of doubles
 - `which`: integer
"""
function reparametrizeOnSurface(dim, tag, parametricCoord, surfaceTag, which = 0)
    api_surfaceParametricCoord_ = Ref{Ptr{Cdouble}}()
    api_surfaceParametricCoord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelReparametrizeOnSurface, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Cint, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), surfaceTag, api_surfaceParametricCoord_, api_surfaceParametricCoord_n_, which, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    surfaceParametricCoord = unsafe_wrap(Array, api_surfaceParametricCoord_[], api_surfaceParametricCoord_n_[], own = true)
    return surfaceParametricCoord
end
const reparametrize_on_surface = reparametrizeOnSurface

"""
    gmsh.model.setVisibility(dimTags, value, recursive = false)

Set the visibility of the model entities `dimTags` (given as a vector of (dim,
tag) pairs) to `value`. Apply the visibility setting recursively if `recursive`
is true.

Types:
 - `dimTags`: vector of pairs of integers
 - `value`: integer
 - `recursive`: boolean
"""
function setVisibility(dimTags, value, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetVisibility, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, value, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_visibility = setVisibility

"""
    gmsh.model.getVisibility(dim, tag)

Get the visibility of the model entity of dimension `dim` and tag `tag`.

Return `value`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `value`: integer
"""
function getVisibility(dim, tag)
    api_value_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetVisibility, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}, Ptr{Cint}),
          dim, tag, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_value_[]
end
const get_visibility = getVisibility

"""
    gmsh.model.setVisibilityPerWindow(value, windowIndex = 0)

Set the global visibility of the model per window to `value`, where
`windowIndex` identifies the window in the window list.

Types:
 - `value`: integer
 - `windowIndex`: integer
"""
function setVisibilityPerWindow(value, windowIndex = 0)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetVisibilityPerWindow, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          value, windowIndex, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_visibility_per_window = setVisibilityPerWindow

"""
    gmsh.model.setColor(dimTags, r, g, b, a = 255, recursive = false)

Set the color of the model entities `dimTags` (given as a vector of (dim, tag)
pairs) to the RGBA value (`r`, `g`, `b`, `a`), where `r`, `g`, `b` and `a`
should be integers between 0 and 255. Apply the color setting recursively if
`recursive` is true.

Types:
 - `dimTags`: vector of pairs of integers
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
 - `recursive`: boolean
"""
function setColor(dimTags, r, g, b, a = 255, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetColor, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Cint, Cint, Cint, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, r, g, b, a, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_color = setColor

"""
    gmsh.model.getColor(dim, tag)

Get the color of the model entity of dimension `dim` and tag `tag`. If no color
is specified for the entity, return fully transparent blue, i.e. (0, 0, 255, 0).

Return `r`, `g`, `b`, `a`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
"""
function getColor(dim, tag)
    api_r_ = Ref{Cint}()
    api_g_ = Ref{Cint}()
    api_b_ = Ref{Cint}()
    api_a_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetColor, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          dim, tag, api_r_, api_g_, api_b_, api_a_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_r_[], api_g_[], api_b_[], api_a_[]
end
const get_color = getColor

"""
    gmsh.model.setCoordinates(tag, x, y, z)

Set the `x`, `y`, `z` coordinates of a geometrical point.

Types:
 - `tag`: integer
 - `x`: double
 - `y`: double
 - `z`: double
"""
function setCoordinates(tag, x, y, z)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetCoordinates, gmsh.lib), Cvoid,
          (Cint, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          tag, x, y, z, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_coordinates = setCoordinates

"""
    gmsh.model.setAttribute(name, values)

Set the values of the attribute with name `name`.

Types:
 - `name`: string
 - `values`: vector of strings
"""
function setAttribute(name, values)
    ierr = Ref{Cint}()
    ccall((:gmshModelSetAttribute, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cchar}}, Csize_t, Ptr{Cint}),
          name, values, length(values), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_attribute = setAttribute

"""
    gmsh.model.getAttribute(name)

Get the values of the attribute with name `name`.

Return `values`.

Types:
 - `name`: string
 - `values`: vector of strings
"""
function getAttribute(name)
    api_values_ = Ref{Ptr{Ptr{Cchar}}}()
    api_values_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetAttribute, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_values_, api_values_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_values_ = unsafe_wrap(Array, api_values_[], api_values_n_[], own = true)
    values = [unsafe_string(tmp_api_values_[i]) for i in 1:length(tmp_api_values_) ]
    return values
end
const get_attribute = getAttribute

"""
    gmsh.model.getAttributeNames()

Get the names of any optional attributes stored in the model.

Return `names`.

Types:
 - `names`: vector of strings
"""
function getAttributeNames()
    api_names_ = Ref{Ptr{Ptr{Cchar}}}()
    api_names_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGetAttributeNames, gmsh.lib), Cvoid,
          (Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          api_names_, api_names_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_names_ = unsafe_wrap(Array, api_names_[], api_names_n_[], own = true)
    names = [unsafe_string(tmp_api_names_[i]) for i in 1:length(tmp_api_names_) ]
    return names
end
const get_attribute_names = getAttributeNames

"""
    gmsh.model.removeAttribute(name)

Remove the attribute with name `name`.

Types:
 - `name`: string
"""
function removeAttribute(name)
    ierr = Ref{Cint}()
    ccall((:gmshModelRemoveAttribute, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_attribute = removeAttribute

"""
    module gmsh.model.mesh

Mesh functions
"""
module mesh

import ...gmsh

"""
    gmsh.model.mesh.generate(dim = 3)

Generate a mesh of the current model, up to dimension `dim` (0, 1, 2 or 3).

Types:
 - `dim`: integer
"""
function generate(dim = 3)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGenerate, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.partition(numPart, elementTags = Csize_t[], partitions = Cint[])

Partition the mesh of the current model into `numPart` partitions. Optionally,
`elementTags` and `partitions` can be provided to specify the partition of each
element explicitly.

Types:
 - `numPart`: integer
 - `elementTags`: vector of sizes
 - `partitions`: vector of integers
"""
function partition(numPart, elementTags = Csize_t[], partitions = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshPartition, gmsh.lib), Cvoid,
          (Cint, Ptr{Csize_t}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}),
          numPart, convert(Vector{Csize_t}, elementTags), length(elementTags), convert(Vector{Cint}, partitions), length(partitions), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.unpartition()

Unpartition the mesh of the current model.
"""
function unpartition()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshUnpartition, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.optimize(method = "", force = false, niter = 1, dimTags = Tuple{Cint,Cint}[])

Optimize the mesh of the current model using `method` (empty for default
tetrahedral mesh optimizer, "Netgen" for Netgen optimizer, "HighOrder" for
direct high-order mesh optimizer, "HighOrderElastic" for high-order elastic
smoother, "HighOrderFastCurving" for fast curving algorithm, "Laplace2D" for
Laplace smoothing, "Relocate2D" and "Relocate3D" for node relocation,
"QuadQuasiStructured" for quad mesh optimization, "UntangleMeshGeometry" for
untangling). If `force` is set apply the optimization also to discrete entities.
If `dimTags` (given as a vector of (dim, tag) pairs) is given, only apply the
optimizer to the given entities.

Types:
 - `method`: string
 - `force`: boolean
 - `niter`: integer
 - `dimTags`: vector of pairs of integers
"""
function optimize(method = "", force = false, niter = 1, dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshOptimize, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cint, Cint, Ptr{Cint}, Csize_t, Ptr{Cint}),
          method, force, niter, api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.recombine()

Recombine the mesh of the current model.
"""
function recombine()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRecombine, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.refine()

Refine the mesh of the current model by uniformly splitting the elements.
"""
function refine()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRefine, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.setOrder(order)

Change the order of the elements in the mesh of the current model to `order`.

Types:
 - `order`: integer
"""
function setOrder(order)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetOrder, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          order, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_order = setOrder

"""
    gmsh.model.mesh.getLastEntityError()

Get the last entities `dimTags` (as a vector of (dim, tag) pairs) where a
meshing error occurred. Currently only populated by the new 3D meshing
algorithms.

Return `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
"""
function getLastEntityError()
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetLastEntityError, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_last_entity_error = getLastEntityError

"""
    gmsh.model.mesh.getLastNodeError()

Get the last node tags `nodeTags` where a meshing error occurred. Currently only
populated by the new 3D meshing algorithms.

Return `nodeTags`.

Types:
 - `nodeTags`: vector of sizes
"""
function getLastNodeError()
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetLastNodeError, gmsh.lib), Cvoid,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          api_nodeTags_, api_nodeTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return nodeTags
end
const get_last_node_error = getLastNodeError

"""
    gmsh.model.mesh.clear(dimTags = Tuple{Cint,Cint}[])

Clear the mesh, i.e. delete all the nodes and elements, for the entities
`dimTags`, given as a vector of (dim, tag) pairs. If `dimTags` is empty, clear
the whole mesh. Note that the mesh of an entity can only be cleared if this
entity is not on the boundary of another entity with a non-empty mesh.

Types:
 - `dimTags`: vector of pairs of integers
"""
function clear(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshClear, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.removeElements(dim, tag, elementTags = Csize_t[])

Remove the elements with tags `elementTags` from the entity of dimension `dim`
and tag `tag`. If `elementTags` is empty, remove all the elements classified on
the entity. To get consistent node classification on model entities,
`reclassifyNodes()` should be called afterwards.

Types:
 - `dim`: integer
 - `tag`: integer
 - `elementTags`: vector of sizes
"""
function removeElements(dim, tag, elementTags = Csize_t[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveElements, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          dim, tag, convert(Vector{Csize_t}, elementTags), length(elementTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_elements = removeElements

"""
    gmsh.model.mesh.reverse(dimTags = Tuple{Cint,Cint}[])

Reverse the orientation of the elements in the entities `dimTags`, given as a
vector of (dim, tag) pairs. If `dimTags` is empty, reverse the orientation of
the elements in the whole mesh.

Types:
 - `dimTags`: vector of pairs of integers
"""
function reverse(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshReverse, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.reverseElements(elementTags)

Reverse the orientation of the elements with tags `elementTags`.

Types:
 - `elementTags`: vector of sizes
"""
function reverseElements(elementTags)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshReverseElements, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          convert(Vector{Csize_t}, elementTags), length(elementTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const reverse_elements = reverseElements

"""
    gmsh.model.mesh.affineTransform(affineTransform, dimTags = Tuple{Cint,Cint}[])

Apply the affine transformation `affineTransform` (16 entries of a 4x4 matrix,
by row; only the 12 first can be provided for convenience) to the coordinates of
the nodes classified on the entities `dimTags`, given as a vector of (dim, tag)
pairs. If `dimTags` is empty, transform all the nodes in the mesh.

Types:
 - `affineTransform`: vector of doubles
 - `dimTags`: vector of pairs of integers
"""
function affineTransform(affineTransform, dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAffineTransform, gmsh.lib), Cvoid,
          (Ptr{Cdouble}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}),
          convert(Vector{Cdouble}, affineTransform), length(affineTransform), api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const affine_transform = affineTransform

"""
    gmsh.model.mesh.getNodes(dim = -1, tag = -1, includeBoundary = false, returnParametricCoord = true)

Get the nodes classified on the entity of dimension `dim` and tag `tag`. If
`tag` < 0, get the nodes for all entities of dimension `dim`. If `dim` and `tag`
are negative, get all the nodes in the mesh. `nodeTags` contains the node tags
(their unique, strictly positive identification numbers). `coord` is a vector of
length 3 times the length of `nodeTags` that contains the x, y, z coordinates of
the nodes, concatenated: [n1x, n1y, n1z, n2x, ...]. If `dim` >= 0 and
`returnParamtricCoord` is set, `parametricCoord` contains the parametric
coordinates ([u1, u2, ...] or [u1, v1, u2, ...]) of the nodes, if available. The
length of `parametricCoord` can be 0 or `dim` times the length of `nodeTags`. If
`includeBoundary` is set, also return the nodes classified on the boundary of
the entity (which will be reparametrized on the entity if `dim` >= 0 in order to
compute their parametric coordinates).

Return `nodeTags`, `coord`, `parametricCoord`.

Types:
 - `nodeTags`: vector of sizes
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
 - `dim`: integer
 - `tag`: integer
 - `includeBoundary`: boolean
 - `returnParametricCoord`: boolean
"""
function getNodes(dim = -1, tag = -1, includeBoundary = false, returnParametricCoord = true)
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    api_parametricCoord_ = Ref{Ptr{Cdouble}}()
    api_parametricCoord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetNodes, gmsh.lib), Cvoid,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Cint, Cint, Cint, Ptr{Cint}),
          api_nodeTags_, api_nodeTags_n_, api_coord_, api_coord_n_, api_parametricCoord_, api_parametricCoord_n_, dim, tag, includeBoundary, returnParametricCoord, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    parametricCoord = unsafe_wrap(Array, api_parametricCoord_[], api_parametricCoord_n_[], own = true)
    return nodeTags, coord, parametricCoord
end
const get_nodes = getNodes

"""
    gmsh.model.mesh.getNodesByElementType(elementType, tag = -1, returnParametricCoord = true)

Get the nodes classified on the entity of tag `tag`, for all the elements of
type `elementType`. The other arguments are treated as in `getNodes`.

Return `nodeTags`, `coord`, `parametricCoord`.

Types:
 - `elementType`: integer
 - `nodeTags`: vector of sizes
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
 - `tag`: integer
 - `returnParametricCoord`: boolean
"""
function getNodesByElementType(elementType, tag = -1, returnParametricCoord = true)
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    api_parametricCoord_ = Ref{Ptr{Cdouble}}()
    api_parametricCoord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetNodesByElementType, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Cint, Ptr{Cint}),
          elementType, api_nodeTags_, api_nodeTags_n_, api_coord_, api_coord_n_, api_parametricCoord_, api_parametricCoord_n_, tag, returnParametricCoord, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    parametricCoord = unsafe_wrap(Array, api_parametricCoord_[], api_parametricCoord_n_[], own = true)
    return nodeTags, coord, parametricCoord
end
const get_nodes_by_element_type = getNodesByElementType

"""
    gmsh.model.mesh.getNode(nodeTag)

Get the coordinates and the parametric coordinates (if any) of the node with tag
`tag`, as well as the dimension `dim` and tag `tag` of the entity on which the
node is classified. This function relies on an internal cache (a vector in case
of dense node numbering, a map otherwise); for large meshes accessing nodes in
bulk is often preferable.

Return `coord`, `parametricCoord`, `dim`, `tag`.

Types:
 - `nodeTag`: size
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
 - `dim`: integer
 - `tag`: integer
"""
function getNode(nodeTag)
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    api_parametricCoord_ = Ref{Ptr{Cdouble}}()
    api_parametricCoord_n_ = Ref{Csize_t}()
    api_dim_ = Ref{Cint}()
    api_tag_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetNode, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          nodeTag, api_coord_, api_coord_n_, api_parametricCoord_, api_parametricCoord_n_, api_dim_, api_tag_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    parametricCoord = unsafe_wrap(Array, api_parametricCoord_[], api_parametricCoord_n_[], own = true)
    return coord, parametricCoord, api_dim_[], api_tag_[]
end
const get_node = getNode

"""
    gmsh.model.mesh.setNode(nodeTag, coord, parametricCoord)

Set the coordinates and the parametric coordinates (if any) of the node with tag
`tag`. This function relies on an internal cache (a vector in case of dense node
numbering, a map otherwise); for large meshes accessing nodes in bulk is often
preferable.

Types:
 - `nodeTag`: size
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
"""
function setNode(nodeTag, coord, parametricCoord)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetNode, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          nodeTag, convert(Vector{Cdouble}, coord), length(coord), convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_node = setNode

"""
    gmsh.model.mesh.rebuildNodeCache(onlyIfNecessary = true)

Rebuild the node cache.

Types:
 - `onlyIfNecessary`: boolean
"""
function rebuildNodeCache(onlyIfNecessary = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRebuildNodeCache, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          onlyIfNecessary, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const rebuild_node_cache = rebuildNodeCache

"""
    gmsh.model.mesh.rebuildElementCache(onlyIfNecessary = true)

Rebuild the element cache.

Types:
 - `onlyIfNecessary`: boolean
"""
function rebuildElementCache(onlyIfNecessary = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRebuildElementCache, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          onlyIfNecessary, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const rebuild_element_cache = rebuildElementCache

"""
    gmsh.model.mesh.getNodesForPhysicalGroup(dim, tag)

Get the nodes from all the elements belonging to the physical group of dimension
`dim` and tag `tag`. `nodeTags` contains the node tags; `coord` is a vector of
length 3 times the length of `nodeTags` that contains the x, y, z coordinates of
the nodes, concatenated: [n1x, n1y, n1z, n2x, ...].

Return `nodeTags`, `coord`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `nodeTags`: vector of sizes
 - `coord`: vector of doubles
"""
function getNodesForPhysicalGroup(dim, tag)
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetNodesForPhysicalGroup, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_nodeTags_, api_nodeTags_n_, api_coord_, api_coord_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return nodeTags, coord
end
const get_nodes_for_physical_group = getNodesForPhysicalGroup

"""
    gmsh.model.mesh.getMaxNodeTag()

Get the maximum tag `maxTag` of a node in the mesh.

Return `maxTag`.

Types:
 - `maxTag`: size
"""
function getMaxNodeTag()
    api_maxTag_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetMaxNodeTag, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Ptr{Cint}),
          api_maxTag_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_maxTag_[]
end
const get_max_node_tag = getMaxNodeTag

"""
    gmsh.model.mesh.addNodes(dim, tag, nodeTags, coord, parametricCoord = Cdouble[])

Add nodes classified on the model entity of dimension `dim` and tag `tag`.
`nodeTags` contains the node tags (their unique, strictly positive
identification numbers). `coord` is a vector of length 3 times the length of
`nodeTags` that contains the x, y, z coordinates of the nodes, concatenated:
[n1x, n1y, n1z, n2x, ...]. The optional `parametricCoord` vector contains the
parametric coordinates of the nodes, if any. The length of `parametricCoord` can
be 0 or `dim` times the length of `nodeTags`. If the `nodeTags` vector is empty,
new tags are automatically assigned to the nodes.

Types:
 - `dim`: integer
 - `tag`: integer
 - `nodeTags`: vector of sizes
 - `coord`: vector of doubles
 - `parametricCoord`: vector of doubles
"""
function addNodes(dim, tag, nodeTags, coord, parametricCoord = Cdouble[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddNodes, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Csize_t}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          dim, tag, convert(Vector{Csize_t}, nodeTags), length(nodeTags), convert(Vector{Cdouble}, coord), length(coord), convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_nodes = addNodes

"""
    gmsh.model.mesh.reclassifyNodes()

Reclassify all nodes on their associated model entity, based on the elements.
Can be used when importing nodes in bulk (e.g. by associating them all to a
single volume), to reclassify them correctly on model surfaces, curves, etc.
after the elements have been set.
"""
function reclassifyNodes()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshReclassifyNodes, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const reclassify_nodes = reclassifyNodes

"""
    gmsh.model.mesh.relocateNodes(dim = -1, tag = -1)

Relocate the nodes classified on the entity of dimension `dim` and tag `tag`
using their parametric coordinates. If `tag` < 0, relocate the nodes for all
entities of dimension `dim`. If `dim` and `tag` are negative, relocate all the
nodes in the mesh.

Types:
 - `dim`: integer
 - `tag`: integer
"""
function relocateNodes(dim = -1, tag = -1)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRelocateNodes, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          dim, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const relocate_nodes = relocateNodes

"""
    gmsh.model.mesh.getElements(dim = -1, tag = -1)

Get the elements classified on the entity of dimension `dim` and tag `tag`. If
`tag` < 0, get the elements for all entities of dimension `dim`. If `dim` and
`tag` are negative, get all the elements in the mesh. `elementTypes` contains
the MSH types of the elements (e.g. `2` for 3-node triangles: see
`getElementProperties` to obtain the properties for a given element type).
`elementTags` is a vector of the same length as `elementTypes`; each entry is a
vector containing the tags (unique, strictly positive identifiers) of the
elements of the corresponding type. `nodeTags` is also a vector of the same
length as `elementTypes`; each entry is a vector of length equal to the number
of elements of the given type times the number N of nodes for this type of
element, that contains the node tags of all the elements of the given type,
concatenated: [e1n1, e1n2, ..., e1nN, e2n1, ...].

Return `elementTypes`, `elementTags`, `nodeTags`.

Types:
 - `elementTypes`: vector of integers
 - `elementTags`: vector of vectors of sizes
 - `nodeTags`: vector of vectors of sizes
 - `dim`: integer
 - `tag`: integer
"""
function getElements(dim = -1, tag = -1)
    api_elementTypes_ = Ref{Ptr{Cint}}()
    api_elementTypes_n_ = Ref{Csize_t}()
    api_elementTags_ = Ref{Ptr{Ptr{Csize_t}}}()
    api_elementTags_n_ = Ref{Ptr{Csize_t}}()
    api_elementTags_nn_ = Ref{Csize_t}()
    api_nodeTags_ = Ref{Ptr{Ptr{Csize_t}}}()
    api_nodeTags_n_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElements, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Csize_t}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Csize_t}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Ptr{Cint}),
          api_elementTypes_, api_elementTypes_n_, api_elementTags_, api_elementTags_n_, api_elementTags_nn_, api_nodeTags_, api_nodeTags_n_, api_nodeTags_nn_, dim, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTypes = unsafe_wrap(Array, api_elementTypes_[], api_elementTypes_n_[], own = true)
    tmp_api_elementTags_ = unsafe_wrap(Array, api_elementTags_[], api_elementTags_nn_[], own = true)
    tmp_api_elementTags_n_ = unsafe_wrap(Array, api_elementTags_n_[], api_elementTags_nn_[], own = true)
    elementTags = [ unsafe_wrap(Array, tmp_api_elementTags_[i], tmp_api_elementTags_n_[i], own = true) for i in 1:api_elementTags_nn_[] ]
    tmp_api_nodeTags_ = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_nn_[], own = true)
    tmp_api_nodeTags_n_ = unsafe_wrap(Array, api_nodeTags_n_[], api_nodeTags_nn_[], own = true)
    nodeTags = [ unsafe_wrap(Array, tmp_api_nodeTags_[i], tmp_api_nodeTags_n_[i], own = true) for i in 1:api_nodeTags_nn_[] ]
    return elementTypes, elementTags, nodeTags
end
const get_elements = getElements

"""
    gmsh.model.mesh.getElement(elementTag)

Get the type and node tags of the element with tag `tag`, as well as the
dimension `dim` and tag `tag` of the entity on which the element is classified.
This function relies on an internal cache (a vector in case of dense element
numbering, a map otherwise); for large meshes accessing elements in bulk is
often preferable.

Return `elementType`, `nodeTags`, `dim`, `tag`.

Types:
 - `elementTag`: size
 - `elementType`: integer
 - `nodeTags`: vector of sizes
 - `dim`: integer
 - `tag`: integer
"""
function getElement(elementTag)
    api_elementType_ = Ref{Cint}()
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_dim_ = Ref{Cint}()
    api_tag_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElement, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Cint}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          elementTag, api_elementType_, api_nodeTags_, api_nodeTags_n_, api_dim_, api_tag_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return api_elementType_[], nodeTags, api_dim_[], api_tag_[]
end
const get_element = getElement

"""
    gmsh.model.mesh.getElementByCoordinates(x, y, z, dim = -1, strict = false)

Search the mesh for an element located at coordinates (`x`, `y`, `z`). This
function performs a search in a spatial octree. If an element is found, return
its tag, type and node tags, as well as the local coordinates (`u`, `v`, `w`)
within the reference element corresponding to search location. If `dim` is >= 0,
only search for elements of the given dimension. If `strict` is not set, use a
tolerance to find elements near the search location.

Return `elementTag`, `elementType`, `nodeTags`, `u`, `v`, `w`.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `elementTag`: size
 - `elementType`: integer
 - `nodeTags`: vector of sizes
 - `u`: double
 - `v`: double
 - `w`: double
 - `dim`: integer
 - `strict`: boolean
"""
function getElementByCoordinates(x, y, z, dim = -1, strict = false)
    api_elementTag_ = Ref{Csize_t}()
    api_elementType_ = Ref{Cint}()
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_u_ = Ref{Cdouble}()
    api_v_ = Ref{Cdouble}()
    api_w_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementByCoordinates, gmsh.lib), Cvoid,
          (Cdouble, Cdouble, Cdouble, Ptr{Csize_t}, Ptr{Cint}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Cint, Cint, Ptr{Cint}),
          x, y, z, api_elementTag_, api_elementType_, api_nodeTags_, api_nodeTags_n_, api_u_, api_v_, api_w_, dim, strict, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return api_elementTag_[], api_elementType_[], nodeTags, api_u_[], api_v_[], api_w_[]
end
const get_element_by_coordinates = getElementByCoordinates

"""
    gmsh.model.mesh.getElementsByCoordinates(x, y, z, dim = -1, strict = false)

Search the mesh for element(s) located at coordinates (`x`, `y`, `z`). This
function performs a search in a spatial octree. Return the tags of all found
elements in `elementTags`. Additional information about the elements can be
accessed through `getElement` and `getLocalCoordinatesInElement`. If `dim` is >=
0, only search for elements of the given dimension. If `strict` is not set, use
a tolerance to find elements near the search location.

Return `elementTags`.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `elementTags`: vector of sizes
 - `dim`: integer
 - `strict`: boolean
"""
function getElementsByCoordinates(x, y, z, dim = -1, strict = false)
    api_elementTags_ = Ref{Ptr{Csize_t}}()
    api_elementTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementsByCoordinates, gmsh.lib), Cvoid,
          (Cdouble, Cdouble, Cdouble, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Ptr{Cint}),
          x, y, z, api_elementTags_, api_elementTags_n_, dim, strict, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTags = unsafe_wrap(Array, api_elementTags_[], api_elementTags_n_[], own = true)
    return elementTags
end
const get_elements_by_coordinates = getElementsByCoordinates

"""
    gmsh.model.mesh.getLocalCoordinatesInElement(elementTag, x, y, z)

Return the local coordinates (`u`, `v`, `w`) within the element `elementTag`
corresponding to the model coordinates (`x`, `y`, `z`). This function relies on
an internal cache (a vector in case of dense element numbering, a map
otherwise); for large meshes accessing elements in bulk is often preferable.

Return `u`, `v`, `w`.

Types:
 - `elementTag`: size
 - `x`: double
 - `y`: double
 - `z`: double
 - `u`: double
 - `v`: double
 - `w`: double
"""
function getLocalCoordinatesInElement(elementTag, x, y, z)
    api_u_ = Ref{Cdouble}()
    api_v_ = Ref{Cdouble}()
    api_w_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetLocalCoordinatesInElement, gmsh.lib), Cvoid,
          (Csize_t, Cdouble, Cdouble, Cdouble, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cint}),
          elementTag, x, y, z, api_u_, api_v_, api_w_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_u_[], api_v_[], api_w_[]
end
const get_local_coordinates_in_element = getLocalCoordinatesInElement

"""
    gmsh.model.mesh.getElementTypes(dim = -1, tag = -1)

Get the types of elements in the entity of dimension `dim` and tag `tag`. If
`tag` < 0, get the types for all entities of dimension `dim`. If `dim` and `tag`
are negative, get all the types in the mesh.

Return `elementTypes`.

Types:
 - `elementTypes`: vector of integers
 - `dim`: integer
 - `tag`: integer
"""
function getElementTypes(dim = -1, tag = -1)
    api_elementTypes_ = Ref{Ptr{Cint}}()
    api_elementTypes_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementTypes, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Cint, Ptr{Cint}),
          api_elementTypes_, api_elementTypes_n_, dim, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTypes = unsafe_wrap(Array, api_elementTypes_[], api_elementTypes_n_[], own = true)
    return elementTypes
end
const get_element_types = getElementTypes

"""
    gmsh.model.mesh.getElementType(familyName, order, serendip = false)

Return an element type given its family name `familyName` ("Point", "Line",
"Triangle", "Quadrangle", "Tetrahedron", "Pyramid", "Prism", "Hexahedron") and
polynomial order `order`. If `serendip` is true, return the corresponding
serendip element type (element without interior nodes).

Return an integer.

Types:
 - `familyName`: string
 - `order`: integer
 - `serendip`: boolean
"""
function getElementType(familyName, order, serendip = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelMeshGetElementType, gmsh.lib), Cint,
          (Ptr{Cchar}, Cint, Cint, Ptr{Cint}),
          familyName, order, serendip, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_element_type = getElementType

"""
    gmsh.model.mesh.getElementProperties(elementType)

Get the properties of an element of type `elementType`: its name
(`elementName`), dimension (`dim`), order (`order`), number of nodes
(`numNodes`), local coordinates of the nodes in the reference element
(`localNodeCoord` vector, of length `dim` times `numNodes`) and number of
primary (first order) nodes (`numPrimaryNodes`).

Return `elementName`, `dim`, `order`, `numNodes`, `localNodeCoord`, `numPrimaryNodes`.

Types:
 - `elementType`: integer
 - `elementName`: string
 - `dim`: integer
 - `order`: integer
 - `numNodes`: integer
 - `localNodeCoord`: vector of doubles
 - `numPrimaryNodes`: integer
"""
function getElementProperties(elementType)
    api_elementName_ = Ref{Ptr{Cchar}}()
    api_dim_ = Ref{Cint}()
    api_order_ = Ref{Cint}()
    api_numNodes_ = Ref{Cint}()
    api_localNodeCoord_ = Ref{Ptr{Cdouble}}()
    api_localNodeCoord_n_ = Ref{Csize_t}()
    api_numPrimaryNodes_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementProperties, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}, Ptr{Cint}),
          elementType, api_elementName_, api_dim_, api_order_, api_numNodes_, api_localNodeCoord_, api_localNodeCoord_n_, api_numPrimaryNodes_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementName = unsafe_string(api_elementName_[])
    localNodeCoord = unsafe_wrap(Array, api_localNodeCoord_[], api_localNodeCoord_n_[], own = true)
    return elementName, api_dim_[], api_order_[], api_numNodes_[], localNodeCoord, api_numPrimaryNodes_[]
end
const get_element_properties = getElementProperties

"""
    gmsh.model.mesh.getElementsByType(elementType, tag = -1, task = 0, numTasks = 1)

Get the elements of type `elementType` classified on the entity of tag `tag`. If
`tag` < 0, get the elements for all entities. `elementTags` is a vector
containing the tags (unique, strictly positive identifiers) of the elements of
the corresponding type. `nodeTags` is a vector of length equal to the number of
elements of the given type times the number N of nodes for this type of element,
that contains the node tags of all the elements of the given type, concatenated:
[e1n1, e1n2, ..., e1nN, e2n1, ...]. If `numTasks` > 1, only compute and return
the part of the data indexed by `task` (for C++ only; output vectors must be
preallocated).

Return `elementTags`, `nodeTags`.

Types:
 - `elementType`: integer
 - `elementTags`: vector of sizes
 - `nodeTags`: vector of sizes
 - `tag`: integer
 - `task`: size
 - `numTasks`: size
"""
function getElementsByType(elementType, tag = -1, task = 0, numTasks = 1)
    api_elementTags_ = Ref{Ptr{Csize_t}}()
    api_elementTags_n_ = Ref{Csize_t}()
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementsByType, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Csize_t, Csize_t, Ptr{Cint}),
          elementType, api_elementTags_, api_elementTags_n_, api_nodeTags_, api_nodeTags_n_, tag, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTags = unsafe_wrap(Array, api_elementTags_[], api_elementTags_n_[], own = true)
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return elementTags, nodeTags
end
const get_elements_by_type = getElementsByType

"""
    gmsh.model.mesh.getMaxElementTag()

Get the maximum tag `maxTag` of an element in the mesh.

Return `maxTag`.

Types:
 - `maxTag`: size
"""
function getMaxElementTag()
    api_maxTag_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetMaxElementTag, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Ptr{Cint}),
          api_maxTag_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_maxTag_[]
end
const get_max_element_tag = getMaxElementTag

"""
    gmsh.model.mesh.getElementQualities(elementTags, qualityName = "minSICN", task = 0, numTasks = 1)

Get the quality `elementQualities` of the elements with tags `elementTags`.
`qualityType` is the requested quality measure: "minDetJac" and "maxDetJac" for
the adaptively computed minimal and maximal Jacobian determinant, "minSJ" for
the sampled minimal scaled jacobien, "minSICN" for the sampled minimal signed
inverted condition number, "minSIGE" for the sampled signed inverted gradient
error, "gamma" for the ratio of the inscribed to circumcribed sphere radius,
"innerRadius" for the inner radius, "outerRadius" for the outerRadius,
"minIsotropy" for the minimum isotropy measure, "angleShape" for the angle shape
measure, "minEdge" for the minimum straight edge length, "maxEdge" for the
maximum straight edge length, "volume" for the volume. If `numTasks` > 1, only
compute and return the part of the data indexed by `task` (for C++ only; output
vector must be preallocated).

Return `elementsQuality`.

Types:
 - `elementTags`: vector of sizes
 - `elementsQuality`: vector of doubles
 - `qualityName`: string
 - `task`: size
 - `numTasks`: size
"""
function getElementQualities(elementTags, qualityName = "minSICN", task = 0, numTasks = 1)
    api_elementsQuality_ = Ref{Ptr{Cdouble}}()
    api_elementsQuality_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementQualities, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cchar}, Csize_t, Csize_t, Ptr{Cint}),
          convert(Vector{Csize_t}, elementTags), length(elementTags), api_elementsQuality_, api_elementsQuality_n_, qualityName, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementsQuality = unsafe_wrap(Array, api_elementsQuality_[], api_elementsQuality_n_[], own = true)
    return elementsQuality
end
const get_element_qualities = getElementQualities

"""
    gmsh.model.mesh.addElements(dim, tag, elementTypes, elementTags, nodeTags)

Add elements classified on the entity of dimension `dim` and tag `tag`. `types`
contains the MSH types of the elements (e.g. `2` for 3-node triangles: see the
Gmsh reference manual). `elementTags` is a vector of the same length as `types`;
each entry is a vector containing the tags (unique, strictly positive
identifiers) of the elements of the corresponding type. `nodeTags` is also a
vector of the same length as `types`; each entry is a vector of length equal to
the number of elements of the given type times the number N of nodes per
element, that contains the node tags of all the elements of the given type,
concatenated: [e1n1, e1n2, ..., e1nN, e2n1, ...].

Types:
 - `dim`: integer
 - `tag`: integer
 - `elementTypes`: vector of integers
 - `elementTags`: vector of vectors of integers (size)
 - `nodeTags`: vector of vectors of integers (size)
"""
function addElements(dim, tag, elementTypes, elementTags, nodeTags)
    api_elementTags_n_ = [ length(elementTags[i]) for i in 1:length(elementTags) ]
    api_nodeTags_n_ = [ length(nodeTags[i]) for i in 1:length(nodeTags) ]
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddElements, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          dim, tag, convert(Vector{Cint}, elementTypes), length(elementTypes), convert(Vector{Vector{Csize_t}},elementTags), api_elementTags_n_, length(elementTags), convert(Vector{Vector{Csize_t}},nodeTags), api_nodeTags_n_, length(nodeTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_elements = addElements

"""
    gmsh.model.mesh.addElementsByType(tag, elementType, elementTags, nodeTags)

Add elements of type `elementType` classified on the entity of tag `tag`.
`elementTags` contains the tags (unique, strictly positive identifiers) of the
elements of the corresponding type. `nodeTags` is a vector of length equal to
the number of elements times the number N of nodes per element, that contains
the node tags of all the elements, concatenated: [e1n1, e1n2, ..., e1nN, e2n1,
...]. If the `elementTag` vector is empty, new tags are automatically assigned
to the elements.

Types:
 - `tag`: integer
 - `elementType`: integer
 - `elementTags`: vector of sizes
 - `nodeTags`: vector of sizes
"""
function addElementsByType(tag, elementType, elementTags, nodeTags)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddElementsByType, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Csize_t}, Csize_t, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          tag, elementType, convert(Vector{Csize_t}, elementTags), length(elementTags), convert(Vector{Csize_t}, nodeTags), length(nodeTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_elements_by_type = addElementsByType

"""
    gmsh.model.mesh.getIntegrationPoints(elementType, integrationType)

Get the numerical quadrature information for the given element type
`elementType` and integration rule `integrationType`, where `integrationType`
concatenates the integration rule family name with the desired order (e.g.
"Gauss4" for a quadrature suited for integrating 4th order polynomials). The
"CompositeGauss" family uses tensor-product rules based the 1D Gauss-Legendre
rule; the "Gauss" family uses an economic scheme when available (i.e. with a
minimal number of points), and falls back to "CompositeGauss" otherwise. Note
that integration points for the "Gauss" family can fall outside of the reference
element for high-order rules. `localCoord` contains the u, v, w coordinates of
the G integration points in the reference element: [g1u, g1v, g1w, ..., gGu,
gGv, gGw]. `weights` contains the associated weights: [g1q, ..., gGq].

Return `localCoord`, `weights`.

Types:
 - `elementType`: integer
 - `integrationType`: string
 - `localCoord`: vector of doubles
 - `weights`: vector of doubles
"""
function getIntegrationPoints(elementType, integrationType)
    api_localCoord_ = Ref{Ptr{Cdouble}}()
    api_localCoord_n_ = Ref{Csize_t}()
    api_weights_ = Ref{Ptr{Cdouble}}()
    api_weights_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetIntegrationPoints, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          elementType, integrationType, api_localCoord_, api_localCoord_n_, api_weights_, api_weights_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    localCoord = unsafe_wrap(Array, api_localCoord_[], api_localCoord_n_[], own = true)
    weights = unsafe_wrap(Array, api_weights_[], api_weights_n_[], own = true)
    return localCoord, weights
end
const get_integration_points = getIntegrationPoints

"""
    gmsh.model.mesh.getJacobians(elementType, localCoord, tag = -1, task = 0, numTasks = 1)

Get the Jacobians of all the elements of type `elementType` classified on the
entity of tag `tag`, at the G evaluation points `localCoord` given as
concatenated u, v, w coordinates in the reference element [g1u, g1v, g1w, ...,
gGu, gGv, gGw]. Data is returned by element, with elements in the same order as
in `getElements` and `getElementsByType`. `jacobians` contains for each element
the 9 entries of the 3x3 Jacobian matrix at each evaluation point. The matrix is
returned by column: [e1g1Jxu, e1g1Jyu, e1g1Jzu, e1g1Jxv, ..., e1g1Jzw, e1g2Jxu,
..., e1gGJzw, e2g1Jxu, ...], with Jxu = dx/du, Jyu = dy/du, etc. `determinants`
contains for each element the determinant of the Jacobian matrix at each
evaluation point: [e1g1, e1g2, ... e1gG, e2g1, ...]. `coord` contains for each
element the x, y, z coordinates of the evaluation points. If `tag` < 0, get the
Jacobian data for all entities. If `numTasks` > 1, only compute and return the
part of the data indexed by `task` (for C++ only; output vectors must be
preallocated).

Return `jacobians`, `determinants`, `coord`.

Types:
 - `elementType`: integer
 - `localCoord`: vector of doubles
 - `jacobians`: vector of doubles
 - `determinants`: vector of doubles
 - `coord`: vector of doubles
 - `tag`: integer
 - `task`: size
 - `numTasks`: size
"""
function getJacobians(elementType, localCoord, tag = -1, task = 0, numTasks = 1)
    api_jacobians_ = Ref{Ptr{Cdouble}}()
    api_jacobians_n_ = Ref{Csize_t}()
    api_determinants_ = Ref{Ptr{Cdouble}}()
    api_determinants_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetJacobians, gmsh.lib), Cvoid,
          (Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Csize_t, Csize_t, Ptr{Cint}),
          elementType, convert(Vector{Cdouble}, localCoord), length(localCoord), api_jacobians_, api_jacobians_n_, api_determinants_, api_determinants_n_, api_coord_, api_coord_n_, tag, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    jacobians = unsafe_wrap(Array, api_jacobians_[], api_jacobians_n_[], own = true)
    determinants = unsafe_wrap(Array, api_determinants_[], api_determinants_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return jacobians, determinants, coord
end
const get_jacobians = getJacobians

"""
    gmsh.model.mesh.getJacobian(elementTag, localCoord)

Get the Jacobian for a single element `elementTag`, at the G evaluation points
`localCoord` given as concatenated u, v, w coordinates in the reference element
[g1u, g1v, g1w, ..., gGu, gGv, gGw]. `jacobians` contains the 9 entries of the
3x3 Jacobian matrix at each evaluation point. The matrix is returned by column:
[e1g1Jxu, e1g1Jyu, e1g1Jzu, e1g1Jxv, ..., e1g1Jzw, e1g2Jxu, ..., e1gGJzw,
e2g1Jxu, ...], with Jxu = dx/du, Jyu = dy/du, etc. `determinants` contains the
determinant of the Jacobian matrix at each evaluation point. `coord` contains
the x, y, z coordinates of the evaluation points. This function relies on an
internal cache (a vector in case of dense element numbering, a map otherwise);
for large meshes accessing Jacobians in bulk is often preferable.

Return `jacobians`, `determinants`, `coord`.

Types:
 - `elementTag`: size
 - `localCoord`: vector of doubles
 - `jacobians`: vector of doubles
 - `determinants`: vector of doubles
 - `coord`: vector of doubles
"""
function getJacobian(elementTag, localCoord)
    api_jacobians_ = Ref{Ptr{Cdouble}}()
    api_jacobians_n_ = Ref{Csize_t}()
    api_determinants_ = Ref{Ptr{Cdouble}}()
    api_determinants_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetJacobian, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          elementTag, convert(Vector{Cdouble}, localCoord), length(localCoord), api_jacobians_, api_jacobians_n_, api_determinants_, api_determinants_n_, api_coord_, api_coord_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    jacobians = unsafe_wrap(Array, api_jacobians_[], api_jacobians_n_[], own = true)
    determinants = unsafe_wrap(Array, api_determinants_[], api_determinants_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return jacobians, determinants, coord
end
const get_jacobian = getJacobian

"""
    gmsh.model.mesh.getBasisFunctions(elementType, localCoord, functionSpaceType, wantedOrientations = Cint[])

Get the basis functions of the element of type `elementType` at the evaluation
points `localCoord` (given as concatenated u, v, w coordinates in the reference
element [g1u, g1v, g1w, ..., gGu, gGv, gGw]), for the function space
`functionSpaceType`. Currently supported function spaces include "Lagrange" and
"GradLagrange" for isoparametric Lagrange basis functions and their gradient in
the u, v, w coordinates of the reference element; "LagrangeN" and
"GradLagrangeN", with N = 1, 2, ..., for N-th order Lagrange basis functions;
"H1LegendreN" and "GradH1LegendreN", with N = 1, 2, ..., for N-th order
hierarchical H1 Legendre functions; "HcurlLegendreN" and "CurlHcurlLegendreN",
with N = 1, 2, ..., for N-th order curl-conforming basis functions.
`numComponents` returns the number C of components of a basis function (e.g. 1
for scalar functions and 3 for vector functions). `basisFunctions` returns the
value of the N basis functions at the evaluation points, i.e. [g1f1, g1f2, ...,
g1fN, g2f1, ...] when C == 1 or [g1f1u, g1f1v, g1f1w, g1f2u, ..., g1fNw, g2f1u,
...] when C == 3. For basis functions that depend on the orientation of the
elements, all values for the first orientation are returned first, followed by
values for the second, etc. `numOrientations` returns the overall number of
orientations. If the `wantedOrientations` vector is not empty, only return the
values for the desired orientation indices.

Return `numComponents`, `basisFunctions`, `numOrientations`.

Types:
 - `elementType`: integer
 - `localCoord`: vector of doubles
 - `functionSpaceType`: string
 - `numComponents`: integer
 - `basisFunctions`: vector of doubles
 - `numOrientations`: integer
 - `wantedOrientations`: vector of integers
"""
function getBasisFunctions(elementType, localCoord, functionSpaceType, wantedOrientations = Cint[])
    api_numComponents_ = Ref{Cint}()
    api_basisFunctions_ = Ref{Ptr{Cdouble}}()
    api_basisFunctions_n_ = Ref{Csize_t}()
    api_numOrientations_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetBasisFunctions, gmsh.lib), Cvoid,
          (Cint, Ptr{Cdouble}, Csize_t, Ptr{Cchar}, Ptr{Cint}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}, Ptr{Cint}, Csize_t, Ptr{Cint}),
          elementType, convert(Vector{Cdouble}, localCoord), length(localCoord), functionSpaceType, api_numComponents_, api_basisFunctions_, api_basisFunctions_n_, api_numOrientations_, convert(Vector{Cint}, wantedOrientations), length(wantedOrientations), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    basisFunctions = unsafe_wrap(Array, api_basisFunctions_[], api_basisFunctions_n_[], own = true)
    return api_numComponents_[], basisFunctions, api_numOrientations_[]
end
const get_basis_functions = getBasisFunctions

"""
    gmsh.model.mesh.getBasisFunctionsOrientation(elementType, functionSpaceType, tag = -1, task = 0, numTasks = 1)

Get the orientation index of the elements of type `elementType` in the entity of
tag `tag`. The arguments have the same meaning as in `getBasisFunctions`.
`basisFunctionsOrientation` is a vector giving for each element the orientation
index in the values returned by `getBasisFunctions`. For Lagrange basis
functions the call is superfluous as it will return a vector of zeros. If
`numTasks` > 1, only compute and return the part of the data indexed by `task`
(for C++ only; output vector must be preallocated).

Return `basisFunctionsOrientation`.

Types:
 - `elementType`: integer
 - `functionSpaceType`: string
 - `basisFunctionsOrientation`: vector of integers
 - `tag`: integer
 - `task`: size
 - `numTasks`: size
"""
function getBasisFunctionsOrientation(elementType, functionSpaceType, tag = -1, task = 0, numTasks = 1)
    api_basisFunctionsOrientation_ = Ref{Ptr{Cint}}()
    api_basisFunctionsOrientation_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetBasisFunctionsOrientation, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Csize_t, Csize_t, Ptr{Cint}),
          elementType, functionSpaceType, api_basisFunctionsOrientation_, api_basisFunctionsOrientation_n_, tag, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    basisFunctionsOrientation = unsafe_wrap(Array, api_basisFunctionsOrientation_[], api_basisFunctionsOrientation_n_[], own = true)
    return basisFunctionsOrientation
end
const get_basis_functions_orientation = getBasisFunctionsOrientation

"""
    gmsh.model.mesh.getBasisFunctionsOrientationForElement(elementTag, functionSpaceType)

Get the orientation of a single element `elementTag`.

Return `basisFunctionsOrientation`.

Types:
 - `elementTag`: size
 - `functionSpaceType`: string
 - `basisFunctionsOrientation`: integer
"""
function getBasisFunctionsOrientationForElement(elementTag, functionSpaceType)
    api_basisFunctionsOrientation_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetBasisFunctionsOrientationForElement, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Cchar}, Ptr{Cint}, Ptr{Cint}),
          elementTag, functionSpaceType, api_basisFunctionsOrientation_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_basisFunctionsOrientation_[]
end
const get_basis_functions_orientation_for_element = getBasisFunctionsOrientationForElement

"""
    gmsh.model.mesh.getNumberOfOrientations(elementType, functionSpaceType)

Get the number of possible orientations for elements of type `elementType` and
function space named `functionSpaceType`.

Return an integer.

Types:
 - `elementType`: integer
 - `functionSpaceType`: string
"""
function getNumberOfOrientations(elementType, functionSpaceType)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelMeshGetNumberOfOrientations, gmsh.lib), Cint,
          (Cint, Ptr{Cchar}, Ptr{Cint}),
          elementType, functionSpaceType, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_number_of_orientations = getNumberOfOrientations

"""
    gmsh.model.mesh.getEdges(nodeTags)

Get the global unique mesh edge identifiers `edgeTags` and orientations
`edgeOrientation` for an input list of node tag pairs defining these edges,
concatenated in the vector `nodeTags`. Mesh edges are created e.g. by
`createEdges()`, `getKeys()` or `addEdges()`. The reference positive orientation
is n1 < n2, where n1 and n2 are the tags of the two edge nodes, which
corresponds to the local orientation of edge-based basis functions as well.

Return `edgeTags`, `edgeOrientations`.

Types:
 - `nodeTags`: vector of sizes
 - `edgeTags`: vector of sizes
 - `edgeOrientations`: vector of integers
"""
function getEdges(nodeTags)
    api_edgeTags_ = Ref{Ptr{Csize_t}}()
    api_edgeTags_n_ = Ref{Csize_t}()
    api_edgeOrientations_ = Ref{Ptr{Cint}}()
    api_edgeOrientations_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetEdges, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          convert(Vector{Csize_t}, nodeTags), length(nodeTags), api_edgeTags_, api_edgeTags_n_, api_edgeOrientations_, api_edgeOrientations_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    edgeTags = unsafe_wrap(Array, api_edgeTags_[], api_edgeTags_n_[], own = true)
    edgeOrientations = unsafe_wrap(Array, api_edgeOrientations_[], api_edgeOrientations_n_[], own = true)
    return edgeTags, edgeOrientations
end
const get_edges = getEdges

"""
    gmsh.model.mesh.getFaces(faceType, nodeTags)

Get the global unique mesh face identifiers `faceTags` and orientations
`faceOrientations` for an input list of a multiple of three (if `faceType` == 3)
or four (if `faceType` == 4) node tags defining these faces, concatenated in the
vector `nodeTags`. Mesh faces are created e.g. by `createFaces()`, `getKeys()`
or `addFaces()`.

Return `faceTags`, `faceOrientations`.

Types:
 - `faceType`: integer
 - `nodeTags`: vector of sizes
 - `faceTags`: vector of sizes
 - `faceOrientations`: vector of integers
"""
function getFaces(faceType, nodeTags)
    api_faceTags_ = Ref{Ptr{Csize_t}}()
    api_faceTags_n_ = Ref{Csize_t}()
    api_faceOrientations_ = Ref{Ptr{Cint}}()
    api_faceOrientations_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetFaces, gmsh.lib), Cvoid,
          (Cint, Ptr{Csize_t}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          faceType, convert(Vector{Csize_t}, nodeTags), length(nodeTags), api_faceTags_, api_faceTags_n_, api_faceOrientations_, api_faceOrientations_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    faceTags = unsafe_wrap(Array, api_faceTags_[], api_faceTags_n_[], own = true)
    faceOrientations = unsafe_wrap(Array, api_faceOrientations_[], api_faceOrientations_n_[], own = true)
    return faceTags, faceOrientations
end
const get_faces = getFaces

"""
    gmsh.model.mesh.createEdges(dimTags = Tuple{Cint,Cint}[])

Create unique mesh edges for the entities `dimTags`, given as a vector of (dim,
tag) pairs.

Types:
 - `dimTags`: vector of pairs of integers
"""
function createEdges(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshCreateEdges, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const create_edges = createEdges

"""
    gmsh.model.mesh.createFaces(dimTags = Tuple{Cint,Cint}[])

Create unique mesh faces for the entities `dimTags`, given as a vector of (dim,
tag) pairs.

Types:
 - `dimTags`: vector of pairs of integers
"""
function createFaces(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshCreateFaces, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const create_faces = createFaces

"""
    gmsh.model.mesh.getAllEdges()

Get the global unique identifiers `edgeTags` and the nodes `edgeNodes` of the
edges in the mesh. Mesh edges are created e.g. by `createEdges()`, `getKeys()`
or addEdges().

Return `edgeTags`, `edgeNodes`.

Types:
 - `edgeTags`: vector of sizes
 - `edgeNodes`: vector of sizes
"""
function getAllEdges()
    api_edgeTags_ = Ref{Ptr{Csize_t}}()
    api_edgeTags_n_ = Ref{Csize_t}()
    api_edgeNodes_ = Ref{Ptr{Csize_t}}()
    api_edgeNodes_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetAllEdges, gmsh.lib), Cvoid,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          api_edgeTags_, api_edgeTags_n_, api_edgeNodes_, api_edgeNodes_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    edgeTags = unsafe_wrap(Array, api_edgeTags_[], api_edgeTags_n_[], own = true)
    edgeNodes = unsafe_wrap(Array, api_edgeNodes_[], api_edgeNodes_n_[], own = true)
    return edgeTags, edgeNodes
end
const get_all_edges = getAllEdges

"""
    gmsh.model.mesh.getAllFaces(faceType)

Get the global unique identifiers `faceTags` and the nodes `faceNodes` of the
faces of type `faceType` in the mesh. Mesh faces are created e.g. by
`createFaces()`, `getKeys()` or addFaces().

Return `faceTags`, `faceNodes`.

Types:
 - `faceType`: integer
 - `faceTags`: vector of sizes
 - `faceNodes`: vector of sizes
"""
function getAllFaces(faceType)
    api_faceTags_ = Ref{Ptr{Csize_t}}()
    api_faceTags_n_ = Ref{Csize_t}()
    api_faceNodes_ = Ref{Ptr{Csize_t}}()
    api_faceNodes_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetAllFaces, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          faceType, api_faceTags_, api_faceTags_n_, api_faceNodes_, api_faceNodes_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    faceTags = unsafe_wrap(Array, api_faceTags_[], api_faceTags_n_[], own = true)
    faceNodes = unsafe_wrap(Array, api_faceNodes_[], api_faceNodes_n_[], own = true)
    return faceTags, faceNodes
end
const get_all_faces = getAllFaces

"""
    gmsh.model.mesh.addEdges(edgeTags, edgeNodes)

Add mesh edges defined by their global unique identifiers `edgeTags` and their
nodes `edgeNodes`.

Types:
 - `edgeTags`: vector of sizes
 - `edgeNodes`: vector of sizes
"""
function addEdges(edgeTags, edgeNodes)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddEdges, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          convert(Vector{Csize_t}, edgeTags), length(edgeTags), convert(Vector{Csize_t}, edgeNodes), length(edgeNodes), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_edges = addEdges

"""
    gmsh.model.mesh.addFaces(faceType, faceTags, faceNodes)

Add mesh faces of type `faceType` defined by their global unique identifiers
`faceTags` and their nodes `faceNodes`.

Types:
 - `faceType`: integer
 - `faceTags`: vector of sizes
 - `faceNodes`: vector of sizes
"""
function addFaces(faceType, faceTags, faceNodes)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddFaces, gmsh.lib), Cvoid,
          (Cint, Ptr{Csize_t}, Csize_t, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          faceType, convert(Vector{Csize_t}, faceTags), length(faceTags), convert(Vector{Csize_t}, faceNodes), length(faceNodes), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_faces = addFaces

"""
    gmsh.model.mesh.getKeys(elementType, functionSpaceType, tag = -1, returnCoord = true)

Generate the pair of keys for the elements of type `elementType` in the entity
of tag `tag`, for the `functionSpaceType` function space. Each pair (`typeKey`,
`entityKey`) uniquely identifies a basis function in the function space. If
`returnCoord` is set, the `coord` vector contains the x, y, z coordinates
locating basis functions for sorting purposes. Warning: this is an experimental
feature and will probably change in a future release.

Return `typeKeys`, `entityKeys`, `coord`.

Types:
 - `elementType`: integer
 - `functionSpaceType`: string
 - `typeKeys`: vector of integers
 - `entityKeys`: vector of sizes
 - `coord`: vector of doubles
 - `tag`: integer
 - `returnCoord`: boolean
"""
function getKeys(elementType, functionSpaceType, tag = -1, returnCoord = true)
    api_typeKeys_ = Ref{Ptr{Cint}}()
    api_typeKeys_n_ = Ref{Csize_t}()
    api_entityKeys_ = Ref{Ptr{Csize_t}}()
    api_entityKeys_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetKeys, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Cint, Ptr{Cint}),
          elementType, functionSpaceType, api_typeKeys_, api_typeKeys_n_, api_entityKeys_, api_entityKeys_n_, api_coord_, api_coord_n_, tag, returnCoord, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    typeKeys = unsafe_wrap(Array, api_typeKeys_[], api_typeKeys_n_[], own = true)
    entityKeys = unsafe_wrap(Array, api_entityKeys_[], api_entityKeys_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return typeKeys, entityKeys, coord
end
const get_keys = getKeys

"""
    gmsh.model.mesh.getKeysForElement(elementTag, functionSpaceType, returnCoord = true)

Get the pair of keys for a single element `elementTag`.

Return `typeKeys`, `entityKeys`, `coord`.

Types:
 - `elementTag`: size
 - `functionSpaceType`: string
 - `typeKeys`: vector of integers
 - `entityKeys`: vector of sizes
 - `coord`: vector of doubles
 - `returnCoord`: boolean
"""
function getKeysForElement(elementTag, functionSpaceType, returnCoord = true)
    api_typeKeys_ = Ref{Ptr{Cint}}()
    api_typeKeys_n_ = Ref{Csize_t}()
    api_entityKeys_ = Ref{Ptr{Csize_t}}()
    api_entityKeys_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetKeysForElement, gmsh.lib), Cvoid,
          (Csize_t, Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          elementTag, functionSpaceType, api_typeKeys_, api_typeKeys_n_, api_entityKeys_, api_entityKeys_n_, api_coord_, api_coord_n_, returnCoord, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    typeKeys = unsafe_wrap(Array, api_typeKeys_[], api_typeKeys_n_[], own = true)
    entityKeys = unsafe_wrap(Array, api_entityKeys_[], api_entityKeys_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    return typeKeys, entityKeys, coord
end
const get_keys_for_element = getKeysForElement

"""
    gmsh.model.mesh.getNumberOfKeys(elementType, functionSpaceType)

Get the number of keys by elements of type `elementType` for function space
named `functionSpaceType`.

Return an integer.

Types:
 - `elementType`: integer
 - `functionSpaceType`: string
"""
function getNumberOfKeys(elementType, functionSpaceType)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelMeshGetNumberOfKeys, gmsh.lib), Cint,
          (Cint, Ptr{Cchar}, Ptr{Cint}),
          elementType, functionSpaceType, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_number_of_keys = getNumberOfKeys

"""
    gmsh.model.mesh.getKeysInformation(typeKeys, entityKeys, elementType, functionSpaceType)

Get information about the pair of `keys`. `infoKeys` returns information about
the functions associated with the pairs (`typeKeys`, `entityKey`).
`infoKeys[0].first` describes the type of function (0 for  vertex function, 1
for edge function, 2 for face function and 3 for bubble function).
`infoKeys[0].second` gives the order of the function associated with the key.
Warning: this is an experimental feature and will probably change in a future
release.

Return `infoKeys`.

Types:
 - `typeKeys`: vector of integers
 - `entityKeys`: vector of sizes
 - `elementType`: integer
 - `functionSpaceType`: string
 - `infoKeys`: vector of pairs of integers
"""
function getKeysInformation(typeKeys, entityKeys, elementType, functionSpaceType)
    api_infoKeys_ = Ref{Ptr{Cint}}()
    api_infoKeys_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetKeysInformation, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Csize_t}, Csize_t, Cint, Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          convert(Vector{Cint}, typeKeys), length(typeKeys), convert(Vector{Csize_t}, entityKeys), length(entityKeys), elementType, functionSpaceType, api_infoKeys_, api_infoKeys_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_infoKeys_ = unsafe_wrap(Array, api_infoKeys_[], api_infoKeys_n_[], own = true)
    infoKeys = [ (tmp_api_infoKeys_[i], tmp_api_infoKeys_[i+1]) for i in 1:2:length(tmp_api_infoKeys_) ]
    return infoKeys
end
const get_keys_information = getKeysInformation

"""
    gmsh.model.mesh.getBarycenters(elementType, tag, fast, primary, task = 0, numTasks = 1)

Get the barycenters of all elements of type `elementType` classified on the
entity of tag `tag`. If `primary` is set, only the primary nodes of the elements
are taken into account for the barycenter calculation. If `fast` is set, the
function returns the sum of the primary node coordinates (without normalizing by
the number of nodes). If `tag` < 0, get the barycenters for all entities. If
`numTasks` > 1, only compute and return the part of the data indexed by `task`
(for C++ only; output vector must be preallocated).

Return `barycenters`.

Types:
 - `elementType`: integer
 - `tag`: integer
 - `fast`: boolean
 - `primary`: boolean
 - `barycenters`: vector of doubles
 - `task`: size
 - `numTasks`: size
"""
function getBarycenters(elementType, tag, fast, primary, task = 0, numTasks = 1)
    api_barycenters_ = Ref{Ptr{Cdouble}}()
    api_barycenters_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetBarycenters, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Cint, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Csize_t, Csize_t, Ptr{Cint}),
          elementType, tag, fast, primary, api_barycenters_, api_barycenters_n_, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    barycenters = unsafe_wrap(Array, api_barycenters_[], api_barycenters_n_[], own = true)
    return barycenters
end
const get_barycenters = getBarycenters

"""
    gmsh.model.mesh.getElementEdgeNodes(elementType, tag = -1, primary = false, task = 0, numTasks = 1)

Get the nodes on the edges of all elements of type `elementType` classified on
the entity of tag `tag`. `nodeTags` contains the node tags of the edges for all
the elements: [e1a1n1, e1a1n2, e1a2n1, ...]. Data is returned by element, with
elements in the same order as in `getElements` and `getElementsByType`. If
`primary` is set, only the primary (begin/end) nodes of the edges are returned.
If `tag` < 0, get the edge nodes for all entities. If `numTasks` > 1, only
compute and return the part of the data indexed by `task` (for C++ only; output
vector must be preallocated).

Return `nodeTags`.

Types:
 - `elementType`: integer
 - `nodeTags`: vector of sizes
 - `tag`: integer
 - `primary`: boolean
 - `task`: size
 - `numTasks`: size
"""
function getElementEdgeNodes(elementType, tag = -1, primary = false, task = 0, numTasks = 1)
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementEdgeNodes, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Csize_t, Csize_t, Ptr{Cint}),
          elementType, api_nodeTags_, api_nodeTags_n_, tag, primary, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return nodeTags
end
const get_element_edge_nodes = getElementEdgeNodes

"""
    gmsh.model.mesh.getElementFaceNodes(elementType, faceType, tag = -1, primary = false, task = 0, numTasks = 1)

Get the nodes on the faces of type `faceType` (3 for triangular faces, 4 for
quadrangular faces) of all elements of type `elementType` classified on the
entity of tag `tag`. `nodeTags` contains the node tags of the faces for all
elements: [e1f1n1, ..., e1f1nFaceType, e1f2n1, ...]. Data is returned by
element, with elements in the same order as in `getElements` and
`getElementsByType`. If `primary` is set, only the primary (corner) nodes of the
faces are returned. If `tag` < 0, get the face nodes for all entities. If
`numTasks` > 1, only compute and return the part of the data indexed by `task`
(for C++ only; output vector must be preallocated).

Return `nodeTags`.

Types:
 - `elementType`: integer
 - `faceType`: integer
 - `nodeTags`: vector of sizes
 - `tag`: integer
 - `primary`: boolean
 - `task`: size
 - `numTasks`: size
"""
function getElementFaceNodes(elementType, faceType, tag = -1, primary = false, task = 0, numTasks = 1)
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetElementFaceNodes, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Csize_t, Csize_t, Ptr{Cint}),
          elementType, faceType, api_nodeTags_, api_nodeTags_n_, tag, primary, task, numTasks, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    return nodeTags
end
const get_element_face_nodes = getElementFaceNodes

"""
    gmsh.model.mesh.getGhostElements(dim, tag)

Get the ghost elements `elementTags` and their associated `partitions` stored in
the ghost entity of dimension `dim` and tag `tag`.

Return `elementTags`, `partitions`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `elementTags`: vector of sizes
 - `partitions`: vector of integers
"""
function getGhostElements(dim, tag)
    api_elementTags_ = Ref{Ptr{Csize_t}}()
    api_elementTags_n_ = Ref{Csize_t}()
    api_partitions_ = Ref{Ptr{Cint}}()
    api_partitions_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetGhostElements, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_elementTags_, api_elementTags_n_, api_partitions_, api_partitions_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTags = unsafe_wrap(Array, api_elementTags_[], api_elementTags_n_[], own = true)
    partitions = unsafe_wrap(Array, api_partitions_[], api_partitions_n_[], own = true)
    return elementTags, partitions
end
const get_ghost_elements = getGhostElements

"""
    gmsh.model.mesh.setSize(dimTags, size)

Set a mesh size constraint on the model entities `dimTags`, given as a vector of
(dim, tag) pairs. Currently only entities of dimension 0 (points) are handled.

Types:
 - `dimTags`: vector of pairs of integers
 - `size`: double
"""
function setSize(dimTags, size)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetSize, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, size, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size = setSize

"""
    gmsh.model.mesh.getSizes(dimTags)

Get the mesh size constraints (if any) associated with the model entities
`dimTags`, given as a vector of (dim, tag) pairs. A zero entry in the output
`sizes` vector indicates that no size constraint is specified on the
corresponding entity.

Return `sizes`.

Types:
 - `dimTags`: vector of pairs of integers
 - `sizes`: vector of doubles
"""
function getSizes(dimTags)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_sizes_ = Ref{Ptr{Cdouble}}()
    api_sizes_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetSizes, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_sizes_, api_sizes_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    sizes = unsafe_wrap(Array, api_sizes_[], api_sizes_n_[], own = true)
    return sizes
end
const get_sizes = getSizes

"""
    gmsh.model.mesh.setSizeAtParametricPoints(dim, tag, parametricCoord, sizes)

Set mesh size constraints at the given parametric points `parametricCoord` on
the model entity of dimension `dim` and tag `tag`. Currently only entities of
dimension 1 (lines) are handled.

Types:
 - `dim`: integer
 - `tag`: integer
 - `parametricCoord`: vector of doubles
 - `sizes`: vector of doubles
"""
function setSizeAtParametricPoints(dim, tag, parametricCoord, sizes)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetSizeAtParametricPoints, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          dim, tag, convert(Vector{Cdouble}, parametricCoord), length(parametricCoord), convert(Vector{Cdouble}, sizes), length(sizes), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size_at_parametric_points = setSizeAtParametricPoints

"""
    gmsh.model.mesh.setSizeCallback(callback)

Set a mesh size callback for the current model. The callback function should
take six arguments as input (`dim`, `tag`, `x`, `y`, `z` and `lc`). The first
two integer arguments correspond to the dimension `dim` and tag `tag` of the
entity being meshed. The next four double precision arguments correspond to the
coordinates `x`, `y` and `z` around which to prescribe the mesh size and to the
mesh size `lc` that would be prescribed if the callback had not been called. The
callback function should return a double precision number specifying the desired
mesh size; returning `lc` is equivalent to a no-op.

Types:
 - `callback`: 
"""
function setSizeCallback(callback)
    api_callback__(dim, tag, x, y, z, lc, data) = callback(dim, tag, x, y, z, lc)
    api_callback_ = @cfunction($api_callback__, Cdouble, (Cint, Cint, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cvoid}))
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetSizeCallback, gmsh.lib), Cvoid,
          (Ptr{Cvoid}, Ptr{Cvoid}, Ptr{Cint}),
          api_callback_, C_NULL, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size_callback = setSizeCallback

"""
    gmsh.model.mesh.removeSizeCallback()

Remove the mesh size callback from the current model.
"""
function removeSizeCallback()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveSizeCallback, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_size_callback = removeSizeCallback

"""
    gmsh.model.mesh.setTransfiniteCurve(tag, numNodes, meshType = "Progression", coef = 1.)

Set a transfinite meshing constraint on the curve `tag`, with `numNodes` nodes
distributed according to `meshType` and `coef`. Currently supported types are
"Progression" (geometrical progression with power `coef`), "Bump" (refinement
toward both extremities of the curve) and "Beta" (beta law).

Types:
 - `tag`: integer
 - `numNodes`: integer
 - `meshType`: string
 - `coef`: double
"""
function setTransfiniteCurve(tag, numNodes, meshType = "Progression", coef = 1.)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetTransfiniteCurve, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Cdouble, Ptr{Cint}),
          tag, numNodes, meshType, coef, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_curve = setTransfiniteCurve

"""
    gmsh.model.mesh.setTransfiniteSurface(tag, arrangement = "Left", cornerTags = Cint[])

Set a transfinite meshing constraint on the surface `tag`. `arrangement`
describes the arrangement of the triangles when the surface is not flagged as
recombined: currently supported values are "Left", "Right", "AlternateLeft" and
"AlternateRight". `cornerTags` can be used to specify the (3 or 4) corners of
the transfinite interpolation explicitly; specifying the corners explicitly is
mandatory if the surface has more that 3 or 4 points on its boundary.

Types:
 - `tag`: integer
 - `arrangement`: string
 - `cornerTags`: vector of integers
"""
function setTransfiniteSurface(tag, arrangement = "Left", cornerTags = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetTransfiniteSurface, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cint}, Csize_t, Ptr{Cint}),
          tag, arrangement, convert(Vector{Cint}, cornerTags), length(cornerTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_surface = setTransfiniteSurface

"""
    gmsh.model.mesh.setTransfiniteVolume(tag, cornerTags = Cint[])

Set a transfinite meshing constraint on the surface `tag`. `cornerTags` can be
used to specify the (6 or 8) corners of the transfinite interpolation
explicitly.

Types:
 - `tag`: integer
 - `cornerTags`: vector of integers
"""
function setTransfiniteVolume(tag, cornerTags = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetTransfiniteVolume, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Cint}),
          tag, convert(Vector{Cint}, cornerTags), length(cornerTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_volume = setTransfiniteVolume

"""
    gmsh.model.mesh.setTransfiniteAutomatic(dimTags = Tuple{Cint,Cint}[], cornerAngle = 2.35, recombine = true)

Set transfinite meshing constraints on the model entities in `dimTags`, given as
a vector of (dim, tag) pairs. Transfinite meshing constraints are added to the
curves of the quadrangular surfaces and to the faces of 6-sided volumes.
Quadragular faces with a corner angle superior to `cornerAngle` (in radians) are
ignored. The number of points is automatically determined from the sizing
constraints. If `dimTag` is empty, the constraints are applied to all entities
in the model. If `recombine` is true, the recombine flag is automatically set on
the transfinite surfaces.

Types:
 - `dimTags`: vector of pairs of integers
 - `cornerAngle`: double
 - `recombine`: boolean
"""
function setTransfiniteAutomatic(dimTags = Tuple{Cint,Cint}[], cornerAngle = 2.35, recombine = true)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetTransfiniteAutomatic, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, cornerAngle, recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_automatic = setTransfiniteAutomatic

"""
    gmsh.model.mesh.setRecombine(dim, tag, angle = 45.)

Set a recombination meshing constraint on the model entity of dimension `dim`
and tag `tag`. Currently only entities of dimension 2 (to recombine triangles
into quadrangles) are supported; `angle` specifies the threshold angle for the
simple recombination algorithm..

Types:
 - `dim`: integer
 - `tag`: integer
 - `angle`: double
"""
function setRecombine(dim, tag, angle = 45.)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetRecombine, gmsh.lib), Cvoid,
          (Cint, Cint, Cdouble, Ptr{Cint}),
          dim, tag, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_recombine = setRecombine

"""
    gmsh.model.mesh.setSmoothing(dim, tag, val)

Set a smoothing meshing constraint on the model entity of dimension `dim` and
tag `tag`. `val` iterations of a Laplace smoother are applied.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setSmoothing(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetSmoothing, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_smoothing = setSmoothing

"""
    gmsh.model.mesh.setReverse(dim, tag, val = true)

Set a reverse meshing constraint on the model entity of dimension `dim` and tag
`tag`. If `val` is true, the mesh orientation will be reversed with respect to
the natural mesh orientation (i.e. the orientation consistent with the
orientation of the geometry). If `val` is false, the mesh is left as-is.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: boolean
"""
function setReverse(dim, tag, val = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetReverse, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_reverse = setReverse

"""
    gmsh.model.mesh.setAlgorithm(dim, tag, val)

Set the meshing algorithm on the model entity of dimension `dim` and tag `tag`.
Supported values are those of the `Mesh.Algorithm` option, as listed in the Gmsh
reference manual. Currently only supported for `dim` == 2.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setAlgorithm(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetAlgorithm, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_algorithm = setAlgorithm

"""
    gmsh.model.mesh.setSizeFromBoundary(dim, tag, val)

Force the mesh size to be extended from the boundary, or not, for the model
entity of dimension `dim` and tag `tag`. Currently only supported for `dim` ==
2.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setSizeFromBoundary(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetSizeFromBoundary, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size_from_boundary = setSizeFromBoundary

"""
    gmsh.model.mesh.setCompound(dim, tags)

Set a compound meshing constraint on the model entities of dimension `dim` and
tags `tags`. During meshing, compound entities are treated as a single discrete
entity, which is automatically reparametrized.

Types:
 - `dim`: integer
 - `tags`: vector of integers
"""
function setCompound(dim, tags)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetCompound, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_compound = setCompound

"""
    gmsh.model.mesh.setOutwardOrientation(tag)

Set meshing constraints on the bounding surfaces of the volume of tag `tag` so
that all surfaces are oriented with outward pointing normals; and if a mesh
already exists, reorient it. Currently only available with the OpenCASCADE
kernel, as it relies on the STL triangulation.

Types:
 - `tag`: integer
"""
function setOutwardOrientation(tag)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetOutwardOrientation, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_outward_orientation = setOutwardOrientation

"""
    gmsh.model.mesh.removeConstraints(dimTags = Tuple{Cint,Cint}[])

Remove all meshing constraints from the model entities `dimTags`, given as a
vector of (dim, tag) pairs. If `dimTags` is empty, remove all constraings.

Types:
 - `dimTags`: vector of pairs of integers
"""
function removeConstraints(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveConstraints, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_constraints = removeConstraints

"""
    gmsh.model.mesh.embed(dim, tags, inDim, inTag)

Embed the model entities of dimension `dim` and tags `tags` in the (`inDim`,
`inTag`) model entity. The dimension `dim` can 0, 1 or 2 and must be strictly
smaller than `inDim`, which must be either 2 or 3. The embedded entities should
not intersect each other or be part of the boundary of the entity `inTag`, whose
mesh will conform to the mesh of the embedded entities. With the OpenCASCADE
kernel, if the `fragment` operation is applied to entities of different
dimensions, the lower dimensional entities will be automatically embedded in the
higher dimensional entities if they are not on their boundary.

Types:
 - `dim`: integer
 - `tags`: vector of integers
 - `inDim`: integer
 - `inTag`: integer
"""
function embed(dim, tags, inDim, inTag)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshEmbed, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), inDim, inTag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.removeEmbedded(dimTags, dim = -1)

Remove embedded entities from the model entities `dimTags`, given as a vector of
(dim, tag) pairs. if `dim` is >= 0, only remove embedded entities of the given
dimension (e.g. embedded points if `dim` == 0).

Types:
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function removeEmbedded(dimTags, dim = -1)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveEmbedded, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_embedded = removeEmbedded

"""
    gmsh.model.mesh.getEmbedded(dim, tag)

Get the entities (if any) embedded in the model entity of dimension `dim` and
tag `tag`.

Return `dimTags`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `dimTags`: vector of pairs of integers
"""
function getEmbedded(dim, tag)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetEmbedded, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_embedded = getEmbedded

"""
    gmsh.model.mesh.reorderElements(elementType, tag, ordering)

Reorder the elements of type `elementType` classified on the entity of tag `tag`
according to the `ordering` vector.

Types:
 - `elementType`: integer
 - `tag`: integer
 - `ordering`: vector of sizes
"""
function reorderElements(elementType, tag, ordering)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshReorderElements, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          elementType, tag, convert(Vector{Csize_t}, ordering), length(ordering), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const reorder_elements = reorderElements

"""
    gmsh.model.mesh.computeRenumbering(method = "RCMK", elementTags = Csize_t[])

Compute a renumbering vector `newTags` corresponding to the input tags `oldTags`
for a given list of element tags `elementTags`. If `elementTags` is empty,
compute the renumbering on the full mesh. If `method` is equal to "RCMK",
compute a node renumering with Reverse Cuthill McKee. If `method` is equal to
"Hilbert", compute a node renumering along a Hilbert curve. If `method` is equal
to "Metis", compute a node renumering using Metis. Element renumbering is not
available yet.

Return `oldTags`, `newTags`.

Types:
 - `oldTags`: vector of sizes
 - `newTags`: vector of sizes
 - `method`: string
 - `elementTags`: vector of sizes
"""
function computeRenumbering(method = "RCMK", elementTags = Csize_t[])
    api_oldTags_ = Ref{Ptr{Csize_t}}()
    api_oldTags_n_ = Ref{Csize_t}()
    api_newTags_ = Ref{Ptr{Csize_t}}()
    api_newTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshComputeRenumbering, gmsh.lib), Cvoid,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cchar}, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          api_oldTags_, api_oldTags_n_, api_newTags_, api_newTags_n_, method, convert(Vector{Csize_t}, elementTags), length(elementTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    oldTags = unsafe_wrap(Array, api_oldTags_[], api_oldTags_n_[], own = true)
    newTags = unsafe_wrap(Array, api_newTags_[], api_newTags_n_[], own = true)
    return oldTags, newTags
end
const compute_renumbering = computeRenumbering

"""
    gmsh.model.mesh.renumberNodes(oldTags = Csize_t[], newTags = Csize_t[])

Renumber the node tags. If no explicit renumbering is provided through the
`oldTags` and `newTags` vectors, renumber the nodes in a continuous sequence,
taking into account the subset of elements to be saved later on if the option
"Mesh.SaveAll" is not set.

Types:
 - `oldTags`: vector of sizes
 - `newTags`: vector of sizes
"""
function renumberNodes(oldTags = Csize_t[], newTags = Csize_t[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRenumberNodes, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          convert(Vector{Csize_t}, oldTags), length(oldTags), convert(Vector{Csize_t}, newTags), length(newTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const renumber_nodes = renumberNodes

"""
    gmsh.model.mesh.renumberElements(oldTags = Csize_t[], newTags = Csize_t[])

Renumber the element tags in a continuous sequence. If no explicit renumbering
is provided through the `oldTags` and `newTags` vectors, renumber the elements
in a continuous sequence, taking into account the subset of elements to be saved
later on if the option "Mesh.SaveAll" is not set.

Types:
 - `oldTags`: vector of sizes
 - `newTags`: vector of sizes
"""
function renumberElements(oldTags = Csize_t[], newTags = Csize_t[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRenumberElements, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          convert(Vector{Csize_t}, oldTags), length(oldTags), convert(Vector{Csize_t}, newTags), length(newTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const renumber_elements = renumberElements

"""
    gmsh.model.mesh.setPeriodic(dim, tags, tagsMaster, affineTransform)

Set the meshes of the entities of dimension `dim` and tag `tags` as periodic
copies of the meshes of entities `tagsMaster`, using the affine transformation
specified in `affineTransformation` (16 entries of a 4x4 matrix, by row). If
used after meshing, generate the periodic node correspondence information
assuming the meshes of entities `tags` effectively match the meshes of entities
`tagsMaster` (useful for structured and extruded meshes). Currently only
available for @code{dim} == 1 and @code{dim} == 2.

Types:
 - `dim`: integer
 - `tags`: vector of integers
 - `tagsMaster`: vector of integers
 - `affineTransform`: vector of doubles
"""
function setPeriodic(dim, tags, tagsMaster, affineTransform)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetPeriodic, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), convert(Vector{Cint}, tagsMaster), length(tagsMaster), convert(Vector{Cdouble}, affineTransform), length(affineTransform), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_periodic = setPeriodic

"""
    gmsh.model.mesh.getPeriodic(dim, tags)

Get master entities `tagsMaster` for the entities of dimension `dim` and tags
`tags`.

Return `tagMaster`.

Types:
 - `dim`: integer
 - `tags`: vector of integers
 - `tagMaster`: vector of integers
"""
function getPeriodic(dim, tags)
    api_tagMaster_ = Ref{Ptr{Cint}}()
    api_tagMaster_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetPeriodic, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), api_tagMaster_, api_tagMaster_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tagMaster = unsafe_wrap(Array, api_tagMaster_[], api_tagMaster_n_[], own = true)
    return tagMaster
end
const get_periodic = getPeriodic

"""
    gmsh.model.mesh.getPeriodicNodes(dim, tag, includeHighOrderNodes = false)

Get the master entity `tagMaster`, the node tags `nodeTags` and their
corresponding master node tags `nodeTagsMaster`, and the affine transform
`affineTransform` for the entity of dimension `dim` and tag `tag`. If
`includeHighOrderNodes` is set, include high-order nodes in the returned data.

Return `tagMaster`, `nodeTags`, `nodeTagsMaster`, `affineTransform`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `tagMaster`: integer
 - `nodeTags`: vector of sizes
 - `nodeTagsMaster`: vector of sizes
 - `affineTransform`: vector of doubles
 - `includeHighOrderNodes`: boolean
"""
function getPeriodicNodes(dim, tag, includeHighOrderNodes = false)
    api_tagMaster_ = Ref{Cint}()
    api_nodeTags_ = Ref{Ptr{Csize_t}}()
    api_nodeTags_n_ = Ref{Csize_t}()
    api_nodeTagsMaster_ = Ref{Ptr{Csize_t}}()
    api_nodeTagsMaster_n_ = Ref{Csize_t}()
    api_affineTransform_ = Ref{Ptr{Cdouble}}()
    api_affineTransform_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetPeriodicNodes, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          dim, tag, api_tagMaster_, api_nodeTags_, api_nodeTags_n_, api_nodeTagsMaster_, api_nodeTagsMaster_n_, api_affineTransform_, api_affineTransform_n_, includeHighOrderNodes, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    nodeTags = unsafe_wrap(Array, api_nodeTags_[], api_nodeTags_n_[], own = true)
    nodeTagsMaster = unsafe_wrap(Array, api_nodeTagsMaster_[], api_nodeTagsMaster_n_[], own = true)
    affineTransform = unsafe_wrap(Array, api_affineTransform_[], api_affineTransform_n_[], own = true)
    return api_tagMaster_[], nodeTags, nodeTagsMaster, affineTransform
end
const get_periodic_nodes = getPeriodicNodes

"""
    gmsh.model.mesh.getPeriodicKeys(elementType, functionSpaceType, tag, returnCoord = true)

Get the master entity `tagMaster` and the key pairs (`typeKeyMaster`,
`entityKeyMaster`) corresponding to the entity `tag` and the key pairs
(`typeKey`, `entityKey`) for the elements of type `elementType` and function
space type `functionSpaceType`. If `returnCoord` is set, the `coord` and
`coordMaster` vectors contain the x, y, z coordinates locating basis functions
for sorting purposes.

Return `tagMaster`, `typeKeys`, `typeKeysMaster`, `entityKeys`, `entityKeysMaster`, `coord`, `coordMaster`.

Types:
 - `elementType`: integer
 - `functionSpaceType`: string
 - `tag`: integer
 - `tagMaster`: integer
 - `typeKeys`: vector of integers
 - `typeKeysMaster`: vector of integers
 - `entityKeys`: vector of sizes
 - `entityKeysMaster`: vector of sizes
 - `coord`: vector of doubles
 - `coordMaster`: vector of doubles
 - `returnCoord`: boolean
"""
function getPeriodicKeys(elementType, functionSpaceType, tag, returnCoord = true)
    api_tagMaster_ = Ref{Cint}()
    api_typeKeys_ = Ref{Ptr{Cint}}()
    api_typeKeys_n_ = Ref{Csize_t}()
    api_typeKeysMaster_ = Ref{Ptr{Cint}}()
    api_typeKeysMaster_n_ = Ref{Csize_t}()
    api_entityKeys_ = Ref{Ptr{Csize_t}}()
    api_entityKeys_n_ = Ref{Csize_t}()
    api_entityKeysMaster_ = Ref{Ptr{Csize_t}}()
    api_entityKeysMaster_n_ = Ref{Csize_t}()
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    api_coordMaster_ = Ref{Ptr{Cdouble}}()
    api_coordMaster_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetPeriodicKeys, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cint, Ptr{Cint}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          elementType, functionSpaceType, tag, api_tagMaster_, api_typeKeys_, api_typeKeys_n_, api_typeKeysMaster_, api_typeKeysMaster_n_, api_entityKeys_, api_entityKeys_n_, api_entityKeysMaster_, api_entityKeysMaster_n_, api_coord_, api_coord_n_, api_coordMaster_, api_coordMaster_n_, returnCoord, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    typeKeys = unsafe_wrap(Array, api_typeKeys_[], api_typeKeys_n_[], own = true)
    typeKeysMaster = unsafe_wrap(Array, api_typeKeysMaster_[], api_typeKeysMaster_n_[], own = true)
    entityKeys = unsafe_wrap(Array, api_entityKeys_[], api_entityKeys_n_[], own = true)
    entityKeysMaster = unsafe_wrap(Array, api_entityKeysMaster_[], api_entityKeysMaster_n_[], own = true)
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    coordMaster = unsafe_wrap(Array, api_coordMaster_[], api_coordMaster_n_[], own = true)
    return api_tagMaster_[], typeKeys, typeKeysMaster, entityKeys, entityKeysMaster, coord, coordMaster
end
const get_periodic_keys = getPeriodicKeys

"""
    gmsh.model.mesh.importStl()

Import the model STL representation (if available) as the current mesh.
"""
function importStl()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshImportStl, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const import_stl = importStl

"""
    gmsh.model.mesh.getDuplicateNodes(dimTags = Tuple{Cint,Cint}[])

Get the `tags` of any duplicate nodes in the mesh of the entities `dimTags`,
given as a vector of (dim, tag) pairs. If `dimTags` is empty, consider the whole
mesh.

Return `tags`.

Types:
 - `tags`: vector of sizes
 - `dimTags`: vector of pairs of integers
"""
function getDuplicateNodes(dimTags = Tuple{Cint,Cint}[])
    api_tags_ = Ref{Ptr{Csize_t}}()
    api_tags_n_ = Ref{Csize_t}()
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetDuplicateNodes, gmsh.lib), Cvoid,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_tags_, api_tags_n_, api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    return tags
end
const get_duplicate_nodes = getDuplicateNodes

"""
    gmsh.model.mesh.removeDuplicateNodes(dimTags = Tuple{Cint,Cint}[])

Remove duplicate nodes in the mesh of the entities `dimTags`, given as a vector
of (dim, tag) pairs. If `dimTags` is empty, consider the whole mesh.

Types:
 - `dimTags`: vector of pairs of integers
"""
function removeDuplicateNodes(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveDuplicateNodes, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_duplicate_nodes = removeDuplicateNodes

"""
    gmsh.model.mesh.removeDuplicateElements(dimTags = Tuple{Cint,Cint}[])

Remove duplicate elements (defined by the same nodes, in the same entity) in the
mesh of the entities `dimTags`, given as a vector of (dim, tag) pairs. If
`dimTags` is empty, consider the whole mesh.

Types:
 - `dimTags`: vector of pairs of integers
"""
function removeDuplicateElements(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshRemoveDuplicateElements, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_duplicate_elements = removeDuplicateElements

"""
    gmsh.model.mesh.splitQuadrangles(quality = 1., tag = -1)

Split (into two triangles) all quadrangles in surface `tag` whose quality is
lower than `quality`. If `tag` < 0, split quadrangles in all surfaces.

Types:
 - `quality`: double
 - `tag`: integer
"""
function splitQuadrangles(quality = 1., tag = -1)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSplitQuadrangles, gmsh.lib), Cvoid,
          (Cdouble, Cint, Ptr{Cint}),
          quality, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const split_quadrangles = splitQuadrangles

"""
    gmsh.model.mesh.setVisibility(elementTags, value)

Set the visibility of the elements of tags `elementTags` to `value`.

Types:
 - `elementTags`: vector of sizes
 - `value`: integer
"""
function setVisibility(elementTags, value)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshSetVisibility, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Csize_t}, elementTags), length(elementTags), value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_visibility = setVisibility

"""
    gmsh.model.mesh.getVisibility(elementTags)

Get the visibility of the elements of tags `elementTags`.

Return `values`.

Types:
 - `elementTags`: vector of sizes
 - `values`: vector of integers
"""
function getVisibility(elementTags)
    api_values_ = Ref{Ptr{Cint}}()
    api_values_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshGetVisibility, gmsh.lib), Cvoid,
          (Ptr{Csize_t}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          convert(Vector{Csize_t}, elementTags), length(elementTags), api_values_, api_values_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    values = unsafe_wrap(Array, api_values_[], api_values_n_[], own = true)
    return values
end
const get_visibility = getVisibility

"""
    gmsh.model.mesh.classifySurfaces(angle, boundary = true, forReparametrization = false, curveAngle = pi, exportDiscrete = true)

Classify ("color") the surface mesh based on the angle threshold `angle` (in
radians), and create new discrete surfaces, curves and points accordingly. If
`boundary` is set, also create discrete curves on the boundary if the surface is
open. If `forReparametrization` is set, create curves and surfaces that can be
reparametrized using a single map. If `curveAngle` is less than Pi, also force
curves to be split according to `curveAngle`. If `exportDiscrete` is set, clear
any built-in CAD kernel entities and export the discrete entities in the built-
in CAD kernel.

Types:
 - `angle`: double
 - `boundary`: boolean
 - `forReparametrization`: boolean
 - `curveAngle`: double
 - `exportDiscrete`: boolean
"""
function classifySurfaces(angle, boundary = true, forReparametrization = false, curveAngle = pi, exportDiscrete = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshClassifySurfaces, gmsh.lib), Cvoid,
          (Cdouble, Cint, Cint, Cdouble, Cint, Ptr{Cint}),
          angle, boundary, forReparametrization, curveAngle, exportDiscrete, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const classify_surfaces = classifySurfaces

"""
    gmsh.model.mesh.createGeometry(dimTags = Tuple{Cint,Cint}[])

Create a geometry for the discrete entities `dimTags` (given as a vector of
(dim, tag) pairs) represented solely by a mesh (without an underlying CAD
description), i.e. create a parametrization for discrete curves and surfaces,
assuming that each can be parametrized with a single map. If `dimTags` is empty,
create a geometry for all the discrete entities.

Types:
 - `dimTags`: vector of pairs of integers
"""
function createGeometry(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshCreateGeometry, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const create_geometry = createGeometry

"""
    gmsh.model.mesh.createTopology(makeSimplyConnected = true, exportDiscrete = true)

Create a boundary representation from the mesh if the model does not have one
(e.g. when imported from mesh file formats with no BRep representation of the
underlying model). If `makeSimplyConnected` is set, enforce simply connected
discrete surfaces and volumes. If `exportDiscrete` is set, clear any built-in
CAD kernel entities and export the discrete entities in the built-in CAD kernel.

Types:
 - `makeSimplyConnected`: boolean
 - `exportDiscrete`: boolean
"""
function createTopology(makeSimplyConnected = true, exportDiscrete = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshCreateTopology, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          makeSimplyConnected, exportDiscrete, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const create_topology = createTopology

"""
    gmsh.model.mesh.addHomologyRequest(type = "Homology", domainTags = Cint[], subdomainTags = Cint[], dims = Cint[])

Add a request to compute a basis representation for homology spaces (if `type`
== "Homology") or cohomology spaces (if `type` == "Cohomology"). The computation
domain is given in a list of physical group tags `domainTags`; if empty, the
whole mesh is the domain. The computation subdomain for relative (co)homology
computation is given in a list of physical group tags `subdomainTags`; if empty,
absolute (co)homology is computed. The dimensions of the (co)homology bases to
be computed are given in the list `dim`; if empty, all bases are computed.
Resulting basis representation (co)chains are stored as physical groups in the
mesh. If the request is added before mesh generation, the computation will be
performed at the end of the meshing pipeline.

Types:
 - `type`: string
 - `domainTags`: vector of integers
 - `subdomainTags`: vector of integers
 - `dims`: vector of integers
"""
function addHomologyRequest(type = "Homology", domainTags = Cint[], subdomainTags = Cint[], dims = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshAddHomologyRequest, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}),
          type, convert(Vector{Cint}, domainTags), length(domainTags), convert(Vector{Cint}, subdomainTags), length(subdomainTags), convert(Vector{Cint}, dims), length(dims), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_homology_request = addHomologyRequest

"""
    gmsh.model.mesh.clearHomologyRequests()

Clear all (co)homology computation requests.
"""
function clearHomologyRequests()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshClearHomologyRequests, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const clear_homology_requests = clearHomologyRequests

"""
    gmsh.model.mesh.computeHomology()

Perform the (co)homology computations requested by addHomologyRequest(). The
newly created physical groups are returned in `dimTags` as a vector of (dim,
tag) pairs.

Return `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
"""
function computeHomology()
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshComputeHomology, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const compute_homology = computeHomology

"""
    gmsh.model.mesh.computeCrossField()

Compute a cross field for the current mesh. The function creates 3 views: the H
function, the Theta function and cross directions. Return the tags of the views.

Return `viewTags`.

Types:
 - `viewTags`: vector of integers
"""
function computeCrossField()
    api_viewTags_ = Ref{Ptr{Cint}}()
    api_viewTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshComputeCrossField, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_viewTags_, api_viewTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    viewTags = unsafe_wrap(Array, api_viewTags_[], api_viewTags_n_[], own = true)
    return viewTags
end
const compute_cross_field = computeCrossField

"""
    module gmsh.model.mesh.field

Mesh size field functions
"""
module field

import ....gmsh

"""
    gmsh.model.mesh.field.add(fieldType, tag = -1)

Add a new mesh size field of type `fieldType`. If `tag` is positive, assign the
tag explicitly; otherwise a new tag is assigned automatically. Return the field
tag. Available field types are listed in the "Gmsh mesh size fields" chapter of
the Gmsh reference manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-mesh-
size-fields).

Return an integer.

Types:
 - `fieldType`: string
 - `tag`: integer
"""
function add(fieldType, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelMeshFieldAdd, gmsh.lib), Cint,
          (Ptr{Cchar}, Cint, Ptr{Cint}),
          fieldType, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end

"""
    gmsh.model.mesh.field.remove(tag)

Remove the field with tag `tag`.

Types:
 - `tag`: integer
"""
function remove(tag)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldRemove, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.mesh.field.list()

Get the list of all fields.

Return `tags`.

Types:
 - `tags`: vector of integers
"""
function list()
    api_tags_ = Ref{Ptr{Cint}}()
    api_tags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldList, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_tags_, api_tags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    return tags
end

"""
    gmsh.model.mesh.field.getType(tag)

Get the type `fieldType` of the field with tag `tag`.

Return `fileType`.

Types:
 - `tag`: integer
 - `fileType`: string
"""
function getType(tag)
    api_fileType_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldGetType, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          tag, api_fileType_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    fileType = unsafe_string(api_fileType_[])
    return fileType
end
const get_type = getType

"""
    gmsh.model.mesh.field.setNumber(tag, option, value)

Set the numerical option `option` to value `value` for field `tag`.

Types:
 - `tag`: integer
 - `option`: string
 - `value`: double
"""
function setNumber(tag, option, value)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldSetNumber, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cdouble, Ptr{Cint}),
          tag, option, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.model.mesh.field.getNumber(tag, option)

Get the value of the numerical option `option` for field `tag`.

Return `value`.

Types:
 - `tag`: integer
 - `option`: string
 - `value`: double
"""
function getNumber(tag, option)
    api_value_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldGetNumber, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cdouble}, Ptr{Cint}),
          tag, option, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_value_[]
end
const get_number = getNumber

"""
    gmsh.model.mesh.field.setString(tag, option, value)

Set the string option `option` to value `value` for field `tag`.

Types:
 - `tag`: integer
 - `option`: string
 - `value`: string
"""
function setString(tag, option, value)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldSetString, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          tag, option, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.model.mesh.field.getString(tag, option)

Get the value of the string option `option` for field `tag`.

Return `value`.

Types:
 - `tag`: integer
 - `option`: string
 - `value`: string
"""
function getString(tag, option)
    api_value_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldGetString, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          tag, option, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    value = unsafe_string(api_value_[])
    return value
end
const get_string = getString

"""
    gmsh.model.mesh.field.setNumbers(tag, option, values)

Set the numerical list option `option` to value `values` for field `tag`.

Types:
 - `tag`: integer
 - `option`: string
 - `values`: vector of doubles
"""
function setNumbers(tag, option, values)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldSetNumbers, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          tag, option, convert(Vector{Cdouble}, values), length(values), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_numbers = setNumbers

"""
    gmsh.model.mesh.field.getNumbers(tag, option)

Get the value of the numerical list option `option` for field `tag`.

Return `values`.

Types:
 - `tag`: integer
 - `option`: string
 - `values`: vector of doubles
"""
function getNumbers(tag, option)
    api_values_ = Ref{Ptr{Cdouble}}()
    api_values_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldGetNumbers, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          tag, option, api_values_, api_values_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    values = unsafe_wrap(Array, api_values_[], api_values_n_[], own = true)
    return values
end
const get_numbers = getNumbers

"""
    gmsh.model.mesh.field.setAsBackgroundMesh(tag)

Set the field `tag` as the background mesh size field.

Types:
 - `tag`: integer
"""
function setAsBackgroundMesh(tag)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldSetAsBackgroundMesh, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_as_background_mesh = setAsBackgroundMesh

"""
    gmsh.model.mesh.field.setAsBoundaryLayer(tag)

Set the field `tag` as a boundary layer size field.

Types:
 - `tag`: integer
"""
function setAsBoundaryLayer(tag)
    ierr = Ref{Cint}()
    ccall((:gmshModelMeshFieldSetAsBoundaryLayer, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_as_boundary_layer = setAsBoundaryLayer

end # end of module field

end # end of module mesh

"""
    module gmsh.model.geo

Built-in CAD kernel functions
"""
module geo

import ...gmsh

"""
    gmsh.model.geo.addPoint(x, y, z, meshSize = 0., tag = -1)

Add a geometrical point in the built-in CAD representation, at coordinates (`x`,
`y`, `z`). If `meshSize` is > 0, add a meshing constraint at that point. If
`tag` is positive, set the tag explicitly; otherwise a new tag is selected
automatically. Return the tag of the point. (Note that the point will be added
in the current model only after `synchronize` is called. This behavior holds for
all the entities added in the geo module.)

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `meshSize`: double
 - `tag`: integer
"""
function addPoint(x, y, z, meshSize = 0., tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddPoint, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cint, Ptr{Cint}),
          x, y, z, meshSize, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_point = addPoint

"""
    gmsh.model.geo.addLine(startTag, endTag, tag = -1)

Add a straight line segment in the built-in CAD representation, between the two
points with tags `startTag` and `endTag`. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. Return the tag of the
line.

Return an integer.

Types:
 - `startTag`: integer
 - `endTag`: integer
 - `tag`: integer
"""
function addLine(startTag, endTag, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddLine, gmsh.lib), Cint,
          (Cint, Cint, Cint, Ptr{Cint}),
          startTag, endTag, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_line = addLine

"""
    gmsh.model.geo.addCircleArc(startTag, centerTag, endTag, tag = -1, nx = 0., ny = 0., nz = 0.)

Add a circle arc (strictly smaller than Pi) in the built-in CAD representation,
between the two points with tags `startTag` and `endTag`, and with center
`centerTag`. If `tag` is positive, set the tag explicitly; otherwise a new tag
is selected automatically. If (`nx`, `ny`, `nz`) != (0, 0, 0), explicitly set
the plane of the circle arc. Return the tag of the circle arc.

Return an integer.

Types:
 - `startTag`: integer
 - `centerTag`: integer
 - `endTag`: integer
 - `tag`: integer
 - `nx`: double
 - `ny`: double
 - `nz`: double
"""
function addCircleArc(startTag, centerTag, endTag, tag = -1, nx = 0., ny = 0., nz = 0.)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddCircleArc, gmsh.lib), Cint,
          (Cint, Cint, Cint, Cint, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          startTag, centerTag, endTag, tag, nx, ny, nz, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_circle_arc = addCircleArc

"""
    gmsh.model.geo.addEllipseArc(startTag, centerTag, majorTag, endTag, tag = -1, nx = 0., ny = 0., nz = 0.)

Add an ellipse arc (strictly smaller than Pi) in the built-in CAD
representation, between the two points `startTag` and `endTag`, and with center
`centerTag` and major axis point `majorTag`. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. If (`nx`, `ny`, `nz`)
!= (0, 0, 0), explicitly set the plane of the circle arc. Return the tag of the
ellipse arc.

Return an integer.

Types:
 - `startTag`: integer
 - `centerTag`: integer
 - `majorTag`: integer
 - `endTag`: integer
 - `tag`: integer
 - `nx`: double
 - `ny`: double
 - `nz`: double
"""
function addEllipseArc(startTag, centerTag, majorTag, endTag, tag = -1, nx = 0., ny = 0., nz = 0.)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddEllipseArc, gmsh.lib), Cint,
          (Cint, Cint, Cint, Cint, Cint, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          startTag, centerTag, majorTag, endTag, tag, nx, ny, nz, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_ellipse_arc = addEllipseArc

"""
    gmsh.model.geo.addSpline(pointTags, tag = -1)

Add a spline (Catmull-Rom) curve in the built-in CAD representation, going
through the points `pointTags`. If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. Create a periodic curve if the
first and last points are the same. Return the tag of the spline curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
"""
function addSpline(pointTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_spline = addSpline

"""
    gmsh.model.geo.addBSpline(pointTags, tag = -1)

Add a cubic b-spline curve in the built-in CAD representation, with `pointTags`
control points. If `tag` is positive, set the tag explicitly; otherwise a new
tag is selected automatically. Creates a periodic curve if the first and last
points are the same. Return the tag of the b-spline curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
"""
function addBSpline(pointTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddBSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bspline = addBSpline

"""
    gmsh.model.geo.addBezier(pointTags, tag = -1)

Add a Bezier curve in the built-in CAD representation, with `pointTags` control
points. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically.  Return the tag of the Bezier curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
"""
function addBezier(pointTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddBezier, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bezier = addBezier

"""
    gmsh.model.geo.addPolyline(pointTags, tag = -1)

Add a polyline curve in the built-in CAD representation, going through the
points `pointTags`. If `tag` is positive, set the tag explicitly; otherwise a
new tag is selected automatically. Create a periodic curve if the first and last
points are the same. Return the tag of the polyline curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
"""
function addPolyline(pointTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddPolyline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_polyline = addPolyline

"""
    gmsh.model.geo.addCompoundSpline(curveTags, numIntervals = 5, tag = -1)

Add a spline (Catmull-Rom) curve in the built-in CAD representation, going
through points sampling the curves in `curveTags`. The density of sampling
points on each curve is governed by `numIntervals`. If `tag` is positive, set
the tag explicitly; otherwise a new tag is selected automatically. Return the
tag of the spline.

Return an integer.

Types:
 - `curveTags`: vector of integers
 - `numIntervals`: integer
 - `tag`: integer
"""
function addCompoundSpline(curveTags, numIntervals = 5, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddCompoundSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), numIntervals, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_compound_spline = addCompoundSpline

"""
    gmsh.model.geo.addCompoundBSpline(curveTags, numIntervals = 20, tag = -1)

Add a b-spline curve in the built-in CAD representation, with control points
sampling the curves in `curveTags`. The density of sampling points on each curve
is governed by `numIntervals`. If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. Return the tag of the b-spline.

Return an integer.

Types:
 - `curveTags`: vector of integers
 - `numIntervals`: integer
 - `tag`: integer
"""
function addCompoundBSpline(curveTags, numIntervals = 20, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddCompoundBSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), numIntervals, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_compound_bspline = addCompoundBSpline

"""
    gmsh.model.geo.addCurveLoop(curveTags, tag = -1, reorient = false)

Add a curve loop (a closed wire) in the built-in CAD representation, formed by
the curves `curveTags`. `curveTags` should contain (signed) tags of model
entities of dimension 1 forming a closed loop: a negative tag signifies that the
underlying curve is considered with reversed orientation. If `tag` is positive,
set the tag explicitly; otherwise a new tag is selected automatically. If
`reorient` is set, automatically reorient the curves if necessary. Return the
tag of the curve loop.

Return an integer.

Types:
 - `curveTags`: vector of integers
 - `tag`: integer
 - `reorient`: boolean
"""
function addCurveLoop(curveTags, tag = -1, reorient = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddCurveLoop, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), tag, reorient, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_curve_loop = addCurveLoop

"""
    gmsh.model.geo.addCurveLoops(curveTags)

Add curve loops in the built-in CAD representation based on the curves
`curveTags`. Return the `tags` of found curve loops, if any.

Return `tags`.

Types:
 - `curveTags`: vector of integers
 - `tags`: vector of integers
"""
function addCurveLoops(curveTags)
    api_tags_ = Ref{Ptr{Cint}}()
    api_tags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoAddCurveLoops, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), api_tags_, api_tags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    return tags
end
const add_curve_loops = addCurveLoops

"""
    gmsh.model.geo.addPlaneSurface(wireTags, tag = -1)

Add a plane surface in the built-in CAD representation, defined by one or more
curve loops `wireTags`. The first curve loop defines the exterior contour;
additional curve loop define holes. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. Return the tag of the
surface.

Return an integer.

Types:
 - `wireTags`: vector of integers
 - `tag`: integer
"""
function addPlaneSurface(wireTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddPlaneSurface, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, wireTags), length(wireTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_plane_surface = addPlaneSurface

"""
    gmsh.model.geo.addSurfaceFilling(wireTags, tag = -1, sphereCenterTag = -1)

Add a surface in the built-in CAD representation, filling the curve loops in
`wireTags` using transfinite interpolation. Currently only a single curve loop
is supported; this curve loop should be composed by 3 or 4 curves only. If `tag`
is positive, set the tag explicitly; otherwise a new tag is selected
automatically. Return the tag of the surface.

Return an integer.

Types:
 - `wireTags`: vector of integers
 - `tag`: integer
 - `sphereCenterTag`: integer
"""
function addSurfaceFilling(wireTags, tag = -1, sphereCenterTag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddSurfaceFilling, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, wireTags), length(wireTags), tag, sphereCenterTag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_surface_filling = addSurfaceFilling

"""
    gmsh.model.geo.addSurfaceLoop(surfaceTags, tag = -1)

Add a surface loop (a closed shell) formed by `surfaceTags` in the built-in CAD
representation.  If `tag` is positive, set the tag explicitly; otherwise a new
tag is selected automatically. Return the tag of the shell.

Return an integer.

Types:
 - `surfaceTags`: vector of integers
 - `tag`: integer
"""
function addSurfaceLoop(surfaceTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddSurfaceLoop, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, surfaceTags), length(surfaceTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_surface_loop = addSurfaceLoop

"""
    gmsh.model.geo.addVolume(shellTags, tag = -1)

Add a volume (a region) in the built-in CAD representation, defined by one or
more shells `shellTags`. The first surface loop defines the exterior boundary;
additional surface loop define holes. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. Return the tag of the
volume.

Return an integer.

Types:
 - `shellTags`: vector of integers
 - `tag`: integer
"""
function addVolume(shellTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddVolume, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, shellTags), length(shellTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_volume = addVolume

"""
    gmsh.model.geo.addGeometry(geometry, numbers = Cdouble[], strings = [], tag = -1)

Add a `geometry` in the built-in CAD representation. `geometry` can currently be
one of "Sphere" or "PolarSphere" (where `numbers` should contain the x, y, z
coordinates of the center, followed by the radius), or "ParametricSurface"
(where `strings` should contains three expression evaluating to the x, y and z
coordinates in terms of parametric coordinates u and v). If `tag` is positive,
set the tag of the geometry explicitly; otherwise a new tag is selected
automatically. Return the tag of the geometry.

Return an integer.

Types:
 - `geometry`: string
 - `numbers`: vector of doubles
 - `strings`: vector of strings
 - `tag`: integer
"""
function addGeometry(geometry, numbers = Cdouble[], strings = [], tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddGeometry, gmsh.lib), Cint,
          (Ptr{Cchar}, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cchar}}, Csize_t, Cint, Ptr{Cint}),
          geometry, convert(Vector{Cdouble}, numbers), length(numbers), strings, length(strings), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_geometry = addGeometry

"""
    gmsh.model.geo.addPointOnGeometry(geometryTag, x, y, z = 0., meshSize = 0., tag = -1)

Add a point in the built-in CAD representation, at coordinates (`x`, `y`, `z`)
on the geometry `geometryTag`. If `meshSize` is > 0, add a meshing constraint at
that point. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. Return the tag of the point. For surface geometries,
only the `x` and `y` coordinates are used.

Return an integer.

Types:
 - `geometryTag`: integer
 - `x`: double
 - `y`: double
 - `z`: double
 - `meshSize`: double
 - `tag`: integer
"""
function addPointOnGeometry(geometryTag, x, y, z = 0., meshSize = 0., tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddPointOnGeometry, gmsh.lib), Cint,
          (Cint, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Ptr{Cint}),
          geometryTag, x, y, z, meshSize, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_point_on_geometry = addPointOnGeometry

"""
    gmsh.model.geo.extrude(dimTags, dx, dy, dz, numElements = Cint[], heights = Cdouble[], recombine = false)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, using a translation along (`dx`, `dy`, `dz`).
Return extruded entities in `outDimTags`. If the `numElements` vector is not
empty, also extrude the mesh: the entries in `numElements` give the number of
elements in each layer. If the `height` vector is not empty, it provides the
(cumulative) height of the different layers, normalized to 1. If `recombine` is
set, recombine the mesh in the layers.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
"""
function extrude(dimTags, dx, dy, dz, numElements = Cint[], heights = Cdouble[], recombine = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoExtrude, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dx, dy, dz, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.geo.revolve(dimTags, x, y, z, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, using a rotation of `angle` radians around the axis
of revolution defined by the point (`x`, `y`, `z`) and the direction (`ax`,
`ay`, `az`). The angle should be strictly smaller than Pi. Return extruded
entities in `outDimTags`. If the `numElements` vector is not empty, also extrude
the mesh: the entries in `numElements` give the number of elements in each
layer. If the `height` vector is not empty, it provides the (cumulative) height
of the different layers, normalized to 1. If `recombine` is set, recombine the
mesh in the layers.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `ax`: double
 - `ay`: double
 - `az`: double
 - `angle`: double
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
"""
function revolve(dimTags, x, y, z, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoRevolve, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, ax, ay, az, angle, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.geo.twist(dimTags, x, y, z, dx, dy, dz, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, using a combined translation and rotation of
`angle` radians, along (`dx`, `dy`, `dz`) and around the axis of revolution
defined by the point (`x`, `y`, `z`) and the direction (`ax`, `ay`, `az`). The
angle should be strictly smaller than Pi. Return extruded entities in
`outDimTags`. If the `numElements` vector is not empty, also extrude the mesh:
the entries in `numElements` give the number of elements in each layer. If the
`height` vector is not empty, it provides the (cumulative) height of the
different layers, normalized to 1. If `recombine` is set, recombine the mesh in
the layers.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `ax`: double
 - `ay`: double
 - `az`: double
 - `angle`: double
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
"""
function twist(dimTags, x, y, z, dx, dy, dz, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoTwist, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, dx, dy, dz, ax, ay, az, angle, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.geo.extrudeBoundaryLayer(dimTags, numElements = [1], heights = Cdouble[], recombine = false, second = false, viewIndex = -1)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation along the normals of the mesh, creating discrete
boundary layer entities. Return extruded entities in `outDimTags`. The entries
in `numElements` give the number of elements in each layer. If the `height`
vector is not empty, it provides the (cumulative) height of the different
layers. If `recombine` is set, recombine the mesh in the layers. A second
boundary layer can be created from the same entities if `second` is set. If
`viewIndex` is >= 0, use the corresponding view to either specify the normals
(if the view contains a vector field) or scale the normals (if the view is
scalar).

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
 - `second`: boolean
 - `viewIndex`: integer
"""
function extrudeBoundaryLayer(dimTags, numElements = [1], heights = Cdouble[], recombine = false, second = false, viewIndex = -1)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoExtrudeBoundaryLayer, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Cint, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, second, viewIndex, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const extrude_boundary_layer = extrudeBoundaryLayer

"""
    gmsh.model.geo.translate(dimTags, dx, dy, dz)

Translate the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation along (`dx`, `dy`, `dz`).

Types:
 - `dimTags`: vector of pairs of integers
 - `dx`: double
 - `dy`: double
 - `dz`: double
"""
function translate(dimTags, dx, dy, dz)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoTranslate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dx, dy, dz, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.rotate(dimTags, x, y, z, ax, ay, az, angle)

Rotate the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation by `angle` radians around the axis of revolution
defined by the point (`x`, `y`, `z`) and the direction (`ax`, `ay`, `az`).

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `ax`: double
 - `ay`: double
 - `az`: double
 - `angle`: double
"""
function rotate(dimTags, x, y, z, ax, ay, az, angle)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoRotate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, ax, ay, az, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.dilate(dimTags, x, y, z, a, b, c)

Scale the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation by factors `a`, `b` and `c` along the three
coordinate axes; use (`x`, `y`, `z`) as the center of the homothetic
transformation.

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `a`: double
 - `b`: double
 - `c`: double
"""
function dilate(dimTags, x, y, z, a, b, c)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoDilate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, a, b, c, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.mirror(dimTags, a, b, c, d)

Mirror the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, with respect to the plane of equation `a` * x + `b`
* y + `c` * z + `d` = 0.

Types:
 - `dimTags`: vector of pairs of integers
 - `a`: double
 - `b`: double
 - `c`: double
 - `d`: double
"""
function mirror(dimTags, a, b, c, d)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMirror, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, a, b, c, d, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.symmetrize(dimTags, a, b, c, d)

Mirror the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, with respect to the plane of equation `a` * x + `b`
* y + `c` * z + `d` = 0. (This is a deprecated synonym for `mirror`.)

Types:
 - `dimTags`: vector of pairs of integers
 - `a`: double
 - `b`: double
 - `c`: double
 - `d`: double
"""
function symmetrize(dimTags, a, b, c, d)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoSymmetrize, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, a, b, c, d, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.copy(dimTags)

Copy the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation; the new entities are returned in `outDimTags`.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
"""
function copy(dimTags)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoCopy, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_outDimTags_, api_outDimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.geo.remove(dimTags, recursive = false)

Remove the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
built-in CAD representation, provided that they are not on the boundary of
higher-dimensional entities. If `recursive` is true, remove all the entities on
their boundaries, down to dimension 0.

Types:
 - `dimTags`: vector of pairs of integers
 - `recursive`: boolean
"""
function remove(dimTags, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoRemove, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.geo.removeAllDuplicates()

Remove all duplicate entities in the built-in CAD representation (different
entities at the same geometrical location).
"""
function removeAllDuplicates()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoRemoveAllDuplicates, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_all_duplicates = removeAllDuplicates

"""
    gmsh.model.geo.splitCurve(tag, pointTags)

Split the curve of tag `tag` in the built-in CAD representation, on the
specified control points `pointTags`. This feature is only available for splines
and b-splines. Return the tag(s) `curveTags` of the newly created curve(s).

Return `curveTags`.

Types:
 - `tag`: integer
 - `pointTags`: vector of integers
 - `curveTags`: vector of integers
"""
function splitCurve(tag, pointTags)
    api_curveTags_ = Ref{Ptr{Cint}}()
    api_curveTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoSplitCurve, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          tag, convert(Vector{Cint}, pointTags), length(pointTags), api_curveTags_, api_curveTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    curveTags = unsafe_wrap(Array, api_curveTags_[], api_curveTags_n_[], own = true)
    return curveTags
end
const split_curve = splitCurve

"""
    gmsh.model.geo.getMaxTag(dim)

Get the maximum tag of entities of dimension `dim` in the built-in CAD
representation.

Return an integer.

Types:
 - `dim`: integer
"""
function getMaxTag(dim)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoGetMaxTag, gmsh.lib), Cint,
          (Cint, Ptr{Cint}),
          dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_max_tag = getMaxTag

"""
    gmsh.model.geo.setMaxTag(dim, maxTag)

Set the maximum tag `maxTag` for entities of dimension `dim` in the built-in CAD
representation.

Types:
 - `dim`: integer
 - `maxTag`: integer
"""
function setMaxTag(dim, maxTag)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoSetMaxTag, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          dim, maxTag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_max_tag = setMaxTag

"""
    gmsh.model.geo.addPhysicalGroup(dim, tags, tag = -1, name = "")

Add a physical group of dimension `dim`, grouping the entities with tags `tags`
in the built-in CAD representation. Return the tag of the physical group, equal
to `tag` if `tag` is positive, or a new tag if `tag` < 0. Set the name of the
physical group if `name` is not empty.

Return an integer.

Types:
 - `dim`: integer
 - `tags`: vector of integers
 - `tag`: integer
 - `name`: string
"""
function addPhysicalGroup(dim, tags, tag = -1, name = "")
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelGeoAddPhysicalGroup, gmsh.lib), Cint,
          (Cint, Ptr{Cint}, Csize_t, Cint, Ptr{Cchar}, Ptr{Cint}),
          dim, convert(Vector{Cint}, tags), length(tags), tag, name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_physical_group = addPhysicalGroup

"""
    gmsh.model.geo.removePhysicalGroups(dimTags = Tuple{Cint,Cint}[])

Remove the physical groups `dimTags` (given as a vector of (dim, tag) pairs)
from the built-in CAD representation. If `dimTags` is empty, remove all groups.

Types:
 - `dimTags`: vector of pairs of integers
"""
function removePhysicalGroups(dimTags = Tuple{Cint,Cint}[])
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoRemovePhysicalGroups, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_physical_groups = removePhysicalGroups

"""
    gmsh.model.geo.synchronize()

Synchronize the built-in CAD representation with the current Gmsh model. This
can be called at any time, but since it involves a non trivial amount of
processing, the number of synchronization points should normally be minimized.
Without synchronization the entities in the built-in CAD representation are not
available to any function outside of the built-in CAD kernel functions.
"""
function synchronize()
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoSynchronize, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    module gmsh.model.geo.mesh

Built-in CAD kernel meshing constraints
"""
module mesh

import ....gmsh

"""
    gmsh.model.geo.mesh.setSize(dimTags, size)

Set a mesh size constraint on the entities `dimTags` (given as a vector of (dim,
tag) pairs) in the built-in CAD kernel representation. Currently only entities
of dimension 0 (points) are handled.

Types:
 - `dimTags`: vector of pairs of integers
 - `size`: double
"""
function setSize(dimTags, size)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetSize, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, size, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size = setSize

"""
    gmsh.model.geo.mesh.setTransfiniteCurve(tag, nPoints, meshType = "Progression", coef = 1.)

Set a transfinite meshing constraint on the curve `tag` in the built-in CAD
kernel representation, with `numNodes` nodes distributed according to `meshType`
and `coef`. Currently supported types are "Progression" (geometrical progression
with power `coef`) and "Bump" (refinement toward both extremities of the curve).

Types:
 - `tag`: integer
 - `nPoints`: integer
 - `meshType`: string
 - `coef`: double
"""
function setTransfiniteCurve(tag, nPoints, meshType = "Progression", coef = 1.)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetTransfiniteCurve, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Cdouble, Ptr{Cint}),
          tag, nPoints, meshType, coef, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_curve = setTransfiniteCurve

"""
    gmsh.model.geo.mesh.setTransfiniteSurface(tag, arrangement = "Left", cornerTags = Cint[])

Set a transfinite meshing constraint on the surface `tag` in the built-in CAD
kernel representation. `arrangement` describes the arrangement of the triangles
when the surface is not flagged as recombined: currently supported values are
"Left", "Right", "AlternateLeft" and "AlternateRight". `cornerTags` can be used
to specify the (3 or 4) corners of the transfinite interpolation explicitly;
specifying the corners explicitly is mandatory if the surface has more that 3 or
4 points on its boundary.

Types:
 - `tag`: integer
 - `arrangement`: string
 - `cornerTags`: vector of integers
"""
function setTransfiniteSurface(tag, arrangement = "Left", cornerTags = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetTransfiniteSurface, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cint}, Csize_t, Ptr{Cint}),
          tag, arrangement, convert(Vector{Cint}, cornerTags), length(cornerTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_surface = setTransfiniteSurface

"""
    gmsh.model.geo.mesh.setTransfiniteVolume(tag, cornerTags = Cint[])

Set a transfinite meshing constraint on the surface `tag` in the built-in CAD
kernel representation. `cornerTags` can be used to specify the (6 or 8) corners
of the transfinite interpolation explicitly.

Types:
 - `tag`: integer
 - `cornerTags`: vector of integers
"""
function setTransfiniteVolume(tag, cornerTags = Cint[])
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetTransfiniteVolume, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Ptr{Cint}),
          tag, convert(Vector{Cint}, cornerTags), length(cornerTags), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_transfinite_volume = setTransfiniteVolume

"""
    gmsh.model.geo.mesh.setRecombine(dim, tag, angle = 45.)

Set a recombination meshing constraint on the entity of dimension `dim` and tag
`tag` in the built-in CAD kernel representation. Currently only entities of
dimension 2 (to recombine triangles into quadrangles) are supported; `angle`
specifies the threshold angle for the simple recombination algorithm.

Types:
 - `dim`: integer
 - `tag`: integer
 - `angle`: double
"""
function setRecombine(dim, tag, angle = 45.)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetRecombine, gmsh.lib), Cvoid,
          (Cint, Cint, Cdouble, Ptr{Cint}),
          dim, tag, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_recombine = setRecombine

"""
    gmsh.model.geo.mesh.setSmoothing(dim, tag, val)

Set a smoothing meshing constraint on the entity of dimension `dim` and tag
`tag` in the built-in CAD kernel representation. `val` iterations of a Laplace
smoother are applied.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setSmoothing(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetSmoothing, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_smoothing = setSmoothing

"""
    gmsh.model.geo.mesh.setReverse(dim, tag, val = true)

Set a reverse meshing constraint on the entity of dimension `dim` and tag `tag`
in the built-in CAD kernel representation. If `val` is true, the mesh
orientation will be reversed with respect to the natural mesh orientation (i.e.
the orientation consistent with the orientation of the geometry). If `val` is
false, the mesh is left as-is.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: boolean
"""
function setReverse(dim, tag, val = true)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetReverse, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_reverse = setReverse

"""
    gmsh.model.geo.mesh.setAlgorithm(dim, tag, val)

Set the meshing algorithm on the entity of dimension `dim` and tag `tag` in the
built-in CAD kernel representation. Currently only supported for `dim` == 2.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setAlgorithm(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetAlgorithm, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_algorithm = setAlgorithm

"""
    gmsh.model.geo.mesh.setSizeFromBoundary(dim, tag, val)

Force the mesh size to be extended from the boundary, or not, for the entity of
dimension `dim` and tag `tag` in the built-in CAD kernel representation.
Currently only supported for `dim` == 2.

Types:
 - `dim`: integer
 - `tag`: integer
 - `val`: integer
"""
function setSizeFromBoundary(dim, tag, val)
    ierr = Ref{Cint}()
    ccall((:gmshModelGeoMeshSetSizeFromBoundary, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          dim, tag, val, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size_from_boundary = setSizeFromBoundary

end # end of module mesh

end # end of module geo

"""
    module gmsh.model.occ

OpenCASCADE CAD kernel functions
"""
module occ

import ...gmsh

"""
    gmsh.model.occ.addPoint(x, y, z, meshSize = 0., tag = -1)

Add a geometrical point in the OpenCASCADE CAD representation, at coordinates
(`x`, `y`, `z`). If `meshSize` is > 0, add a meshing constraint at that point.
If `tag` is positive, set the tag explicitly; otherwise a new tag is selected
automatically. Return the tag of the point. (Note that the point will be added
in the current model only after `synchronize` is called. This behavior holds for
all the entities added in the occ module.)

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `meshSize`: double
 - `tag`: integer
"""
function addPoint(x, y, z, meshSize = 0., tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddPoint, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cint, Ptr{Cint}),
          x, y, z, meshSize, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_point = addPoint

"""
    gmsh.model.occ.addLine(startTag, endTag, tag = -1)

Add a straight line segment in the OpenCASCADE CAD representation, between the
two points with tags `startTag` and `endTag`. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. Return the tag of the
line.

Return an integer.

Types:
 - `startTag`: integer
 - `endTag`: integer
 - `tag`: integer
"""
function addLine(startTag, endTag, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddLine, gmsh.lib), Cint,
          (Cint, Cint, Cint, Ptr{Cint}),
          startTag, endTag, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_line = addLine

"""
    gmsh.model.occ.addCircleArc(startTag, middleTag, endTag, tag = -1, center = true)

Add a circle arc in the OpenCASCADE CAD representation, between the two points
with tags `startTag` and `endTag`, with middle point `middleTag`. If `center` is
true, the middle point is the center of the circle; otherwise the circle goes
through the middle point. If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. Return the tag of the circle arc.

Return an integer.

Types:
 - `startTag`: integer
 - `middleTag`: integer
 - `endTag`: integer
 - `tag`: integer
 - `center`: boolean
"""
function addCircleArc(startTag, middleTag, endTag, tag = -1, center = true)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddCircleArc, gmsh.lib), Cint,
          (Cint, Cint, Cint, Cint, Cint, Ptr{Cint}),
          startTag, middleTag, endTag, tag, center, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_circle_arc = addCircleArc

"""
    gmsh.model.occ.addCircle(x, y, z, r, tag = -1, angle1 = 0., angle2 = 2*pi, zAxis = Cdouble[], xAxis = Cdouble[])

Add a circle of center (`x`, `y`, `z`) and radius `r` in the OpenCASCADE CAD
representation. If `tag` is positive, set the tag explicitly; otherwise a new
tag is selected automatically. If `angle1` and `angle2` are specified, create a
circle arc between the two angles. If a vector `zAxis` of size 3 is provided,
use it as the normal to the circle plane (z-axis). If a vector `xAxis` of size 3
is provided in addition to `zAxis`, use it to define the x-axis. Return the tag
of the circle.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `r`: double
 - `tag`: integer
 - `angle1`: double
 - `angle2`: double
 - `zAxis`: vector of doubles
 - `xAxis`: vector of doubles
"""
function addCircle(x, y, z, r, tag = -1, angle1 = 0., angle2 = 2*pi, zAxis = Cdouble[], xAxis = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddCircle, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Cdouble, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          x, y, z, r, tag, angle1, angle2, convert(Vector{Cdouble}, zAxis), length(zAxis), convert(Vector{Cdouble}, xAxis), length(xAxis), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_circle = addCircle

"""
    gmsh.model.occ.addEllipseArc(startTag, centerTag, majorTag, endTag, tag = -1)

Add an ellipse arc in the OpenCASCADE CAD representation, between the two points
`startTag` and `endTag`, and with center `centerTag` and major axis point
`majorTag`. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. Return the tag of the ellipse arc. Note that OpenCASCADE
does not allow creating ellipse arcs with the major radius smaller than the
minor radius.

Return an integer.

Types:
 - `startTag`: integer
 - `centerTag`: integer
 - `majorTag`: integer
 - `endTag`: integer
 - `tag`: integer
"""
function addEllipseArc(startTag, centerTag, majorTag, endTag, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddEllipseArc, gmsh.lib), Cint,
          (Cint, Cint, Cint, Cint, Cint, Ptr{Cint}),
          startTag, centerTag, majorTag, endTag, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_ellipse_arc = addEllipseArc

"""
    gmsh.model.occ.addEllipse(x, y, z, r1, r2, tag = -1, angle1 = 0., angle2 = 2*pi, zAxis = Cdouble[], xAxis = Cdouble[])

Add an ellipse of center (`x`, `y`, `z`) and radii `r1` and `r2` (with `r1` >=
`r2`) along the x- and y-axes, respectively, in the OpenCASCADE CAD
representation. If `tag` is positive, set the tag explicitly; otherwise a new
tag is selected automatically. If `angle1` and `angle2` are specified, create an
ellipse arc between the two angles. If a vector `zAxis` of size 3 is provided,
use it as the normal to the ellipse plane (z-axis). If a vector `xAxis` of size
3 is provided in addition to `zAxis`, use it to define the x-axis. Return the
tag of the ellipse.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `r1`: double
 - `r2`: double
 - `tag`: integer
 - `angle1`: double
 - `angle2`: double
 - `zAxis`: vector of doubles
 - `xAxis`: vector of doubles
"""
function addEllipse(x, y, z, r1, r2, tag = -1, angle1 = 0., angle2 = 2*pi, zAxis = Cdouble[], xAxis = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddEllipse, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Cdouble, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          x, y, z, r1, r2, tag, angle1, angle2, convert(Vector{Cdouble}, zAxis), length(zAxis), convert(Vector{Cdouble}, xAxis), length(xAxis), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_ellipse = addEllipse

"""
    gmsh.model.occ.addSpline(pointTags, tag = -1, tangents = Cdouble[])

Add a spline (C2 b-spline) curve in the OpenCASCADE CAD representation, going
through the points `pointTags`. If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. Create a periodic curve if the
first and last points are the same. Return the tag of the spline curve. If the
`tangents` vector contains 6 entries, use them as concatenated x, y, z
components of the initial and final tangents of the b-spline; if it contains 3
times as many entries as the number of points, use them as concatenated x, y, z
components of the tangents at each point, unless the norm of the tangent is
zero.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
 - `tangents`: vector of doubles
"""
function addSpline(pointTags, tag = -1, tangents = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, convert(Vector{Cdouble}, tangents), length(tangents), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_spline = addSpline

"""
    gmsh.model.occ.addBSpline(pointTags, tag = -1, degree = 3, weights = Cdouble[], knots = Cdouble[], multiplicities = Cint[])

Add a b-spline curve of degree `degree` in the OpenCASCADE CAD representation,
with `pointTags` control points. If `weights`, `knots` or `multiplicities` are
not provided, default parameters are computed automatically. If `tag` is
positive, set the tag explicitly; otherwise a new tag is selected automatically.
Create a periodic curve if the first and last points are the same. Return the
tag of the b-spline curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
 - `degree`: integer
 - `weights`: vector of doubles
 - `knots`: vector of doubles
 - `multiplicities`: vector of integers
"""
function addBSpline(pointTags, tag = -1, degree = 3, weights = Cdouble[], knots = Cdouble[], multiplicities = Cint[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBSpline, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, degree, convert(Vector{Cdouble}, weights), length(weights), convert(Vector{Cdouble}, knots), length(knots), convert(Vector{Cint}, multiplicities), length(multiplicities), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bspline = addBSpline

"""
    gmsh.model.occ.addBezier(pointTags, tag = -1)

Add a Bezier curve in the OpenCASCADE CAD representation, with `pointTags`
control points. If `tag` is positive, set the tag explicitly; otherwise a new
tag is selected automatically. Return the tag of the Bezier curve.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `tag`: integer
"""
function addBezier(pointTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBezier, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bezier = addBezier

"""
    gmsh.model.occ.addWire(curveTags, tag = -1, checkClosed = false)

Add a wire (open or closed) in the OpenCASCADE CAD representation, formed by the
curves `curveTags`. Note that an OpenCASCADE wire can be made of curves that
share geometrically identical (but topologically different) points. If `tag` is
positive, set the tag explicitly; otherwise a new tag is selected automatically.
Return the tag of the wire.

Return an integer.

Types:
 - `curveTags`: vector of integers
 - `tag`: integer
 - `checkClosed`: boolean
"""
function addWire(curveTags, tag = -1, checkClosed = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddWire, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), tag, checkClosed, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_wire = addWire

"""
    gmsh.model.occ.addCurveLoop(curveTags, tag = -1)

Add a curve loop (a closed wire) in the OpenCASCADE CAD representation, formed
by the curves `curveTags`. `curveTags` should contain tags of curves forming a
closed loop. Negative tags can be specified for compatibility with the built-in
kernel, but are simply ignored: the wire is oriented according to the
orientation of its first curve. Note that an OpenCASCADE curve loop can be made
of curves that share geometrically identical (but topologically different)
points. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. Return the tag of the curve loop.

Return an integer.

Types:
 - `curveTags`: vector of integers
 - `tag`: integer
"""
function addCurveLoop(curveTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddCurveLoop, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, curveTags), length(curveTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_curve_loop = addCurveLoop

"""
    gmsh.model.occ.addRectangle(x, y, z, dx, dy, tag = -1, roundedRadius = 0.)

Add a rectangle in the OpenCASCADE CAD representation, with lower left corner at
(`x`, `y`, `z`) and upper right corner at (`x` + `dx`, `y` + `dy`, `z`). If
`tag` is positive, set the tag explicitly; otherwise a new tag is selected
automatically. Round the corners if `roundedRadius` is nonzero. Return the tag
of the rectangle.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `tag`: integer
 - `roundedRadius`: double
"""
function addRectangle(x, y, z, dx, dy, tag = -1, roundedRadius = 0.)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddRectangle, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Ptr{Cint}),
          x, y, z, dx, dy, tag, roundedRadius, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_rectangle = addRectangle

"""
    gmsh.model.occ.addDisk(xc, yc, zc, rx, ry, tag = -1, zAxis = Cdouble[], xAxis = Cdouble[])

Add a disk in the OpenCASCADE CAD representation, with center (`xc`, `yc`, `zc`)
and radius `rx` along the x-axis and `ry` along the y-axis (`rx` >= `ry`). If
`tag` is positive, set the tag explicitly; otherwise a new tag is selected
automatically. If a vector `zAxis` of size 3 is provided, use it as the normal
to the disk (z-axis). If a vector `xAxis` of size 3 is provided in addition to
`zAxis`, use it to define the x-axis. Return the tag of the disk.

Return an integer.

Types:
 - `xc`: double
 - `yc`: double
 - `zc`: double
 - `rx`: double
 - `ry`: double
 - `tag`: integer
 - `zAxis`: vector of doubles
 - `xAxis`: vector of doubles
"""
function addDisk(xc, yc, zc, rx, ry, tag = -1, zAxis = Cdouble[], xAxis = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddDisk, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          xc, yc, zc, rx, ry, tag, convert(Vector{Cdouble}, zAxis), length(zAxis), convert(Vector{Cdouble}, xAxis), length(xAxis), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_disk = addDisk

"""
    gmsh.model.occ.addPlaneSurface(wireTags, tag = -1)

Add a plane surface in the OpenCASCADE CAD representation, defined by one or
more curve loops (or closed wires) `wireTags`. The first curve loop defines the
exterior contour; additional curve loop define holes. If `tag` is positive, set
the tag explicitly; otherwise a new tag is selected automatically. Return the
tag of the surface.

Return an integer.

Types:
 - `wireTags`: vector of integers
 - `tag`: integer
"""
function addPlaneSurface(wireTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddPlaneSurface, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, wireTags), length(wireTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_plane_surface = addPlaneSurface

"""
    gmsh.model.occ.addSurfaceFilling(wireTag, tag = -1, pointTags = Cint[], degree = 2, numPointsOnCurves = 15, numIter = 2, anisotropic = false, tol2d = 0.00001, tol3d = 0.0001, tolAng = 0.01, tolCurv = 0.1, maxDegree = 8, maxSegments = 9)

Add a surface in the OpenCASCADE CAD representation, filling the curve loop
`wireTag`. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. Return the tag of the surface. If `pointTags` are
provided, force the surface to pass through the given points. The other optional
arguments are `degree` (the degree of the energy criterion to minimize for
computing the deformation of the surface), `numPointsOnCurves` (the average
number of points for discretisation of the bounding curves), `numIter` (the
maximum number of iterations of the optimization process), `anisotropic`
(improve performance when the ratio of the length along the two parametric
coordinates of the surface is high), `tol2d` (tolerance to the constraints in
the parametric plane of the surface), `tol3d` (the maximum distance allowed
between the support surface and the constraints), `tolAng` (the maximum angle
allowed between the normal of the surface and the constraints), `tolCurv` (the
maximum difference of curvature allowed between the surface and the constraint),
`maxDegree` (the highest degree which the polynomial defining the filling
surface can have) and, `maxSegments` (the largest number of segments which the
filling surface can have).

Return an integer.

Types:
 - `wireTag`: integer
 - `tag`: integer
 - `pointTags`: vector of integers
 - `degree`: integer
 - `numPointsOnCurves`: integer
 - `numIter`: integer
 - `anisotropic`: boolean
 - `tol2d`: double
 - `tol3d`: double
 - `tolAng`: double
 - `tolCurv`: double
 - `maxDegree`: integer
 - `maxSegments`: integer
"""
function addSurfaceFilling(wireTag, tag = -1, pointTags = Cint[], degree = 2, numPointsOnCurves = 15, numIter = 2, anisotropic = false, tol2d = 0.00001, tol3d = 0.0001, tolAng = 0.01, tolCurv = 0.1, maxDegree = 8, maxSegments = 9)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddSurfaceFilling, gmsh.lib), Cint,
          (Cint, Cint, Ptr{Cint}, Csize_t, Cint, Cint, Cint, Cint, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cint, Ptr{Cint}),
          wireTag, tag, convert(Vector{Cint}, pointTags), length(pointTags), degree, numPointsOnCurves, numIter, anisotropic, tol2d, tol3d, tolAng, tolCurv, maxDegree, maxSegments, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_surface_filling = addSurfaceFilling

"""
    gmsh.model.occ.addBSplineFilling(wireTag, tag = -1, type = "")

Add a BSpline surface in the OpenCASCADE CAD representation, filling the curve
loop `wireTag`. The curve loop should be made of 2, 3 or 4 curves. The optional
`type` argument specifies the type of filling: "Stretch" creates the flattest
patch, "Curved" (the default) creates the most rounded patch, and "Coons"
creates a rounded patch with less depth than "Curved". If `tag` is positive, set
the tag explicitly; otherwise a new tag is selected automatically. Return the
tag of the surface.

Return an integer.

Types:
 - `wireTag`: integer
 - `tag`: integer
 - `type`: string
"""
function addBSplineFilling(wireTag, tag = -1, type = "")
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBSplineFilling, gmsh.lib), Cint,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cint}),
          wireTag, tag, type, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bspline_filling = addBSplineFilling

"""
    gmsh.model.occ.addBezierFilling(wireTag, tag = -1, type = "")

Add a Bezier surface in the OpenCASCADE CAD representation, filling the curve
loop `wireTag`. The curve loop should be made of 2, 3 or 4 Bezier curves. The
optional `type` argument specifies the type of filling: "Stretch" creates the
flattest patch, "Curved" (the default) creates the most rounded patch, and
"Coons" creates a rounded patch with less depth than "Curved". If `tag` is
positive, set the tag explicitly; otherwise a new tag is selected automatically.
Return the tag of the surface.

Return an integer.

Types:
 - `wireTag`: integer
 - `tag`: integer
 - `type`: string
"""
function addBezierFilling(wireTag, tag = -1, type = "")
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBezierFilling, gmsh.lib), Cint,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cint}),
          wireTag, tag, type, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bezier_filling = addBezierFilling

"""
    gmsh.model.occ.addBSplineSurface(pointTags, numPointsU, tag = -1, degreeU = 3, degreeV = 3, weights = Cdouble[], knotsU = Cdouble[], knotsV = Cdouble[], multiplicitiesU = Cint[], multiplicitiesV = Cint[], wireTags = Cint[], wire3D = false)

Add a b-spline surface of degree `degreeU` x `degreeV` in the OpenCASCADE CAD
representation, with `pointTags` control points given as a single vector [Pu1v1,
... Pu`numPointsU`v1, Pu1v2, ...]. If `weights`, `knotsU`, `knotsV`,
`multiplicitiesU` or `multiplicitiesV` are not provided, default parameters are
computed automatically. If `tag` is positive, set the tag explicitly; otherwise
a new tag is selected automatically. If `wireTags` is provided, trim the
b-spline patch using the provided wires: the first wire defines the external
contour, the others define holes. If `wire3D` is set, consider wire curves as 3D
curves and project them on the b-spline surface; otherwise consider the wire
curves as defined in the parametric space of the surface. Return the tag of the
b-spline surface.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `numPointsU`: integer
 - `tag`: integer
 - `degreeU`: integer
 - `degreeV`: integer
 - `weights`: vector of doubles
 - `knotsU`: vector of doubles
 - `knotsV`: vector of doubles
 - `multiplicitiesU`: vector of integers
 - `multiplicitiesV`: vector of integers
 - `wireTags`: vector of integers
 - `wire3D`: boolean
"""
function addBSplineSurface(pointTags, numPointsU, tag = -1, degreeU = 3, degreeV = 3, weights = Cdouble[], knotsU = Cdouble[], knotsV = Cdouble[], multiplicitiesU = Cint[], multiplicitiesV = Cint[], wireTags = Cint[], wire3D = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBSplineSurface, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Cint, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), numPointsU, tag, degreeU, degreeV, convert(Vector{Cdouble}, weights), length(weights), convert(Vector{Cdouble}, knotsU), length(knotsU), convert(Vector{Cdouble}, knotsV), length(knotsV), convert(Vector{Cint}, multiplicitiesU), length(multiplicitiesU), convert(Vector{Cint}, multiplicitiesV), length(multiplicitiesV), convert(Vector{Cint}, wireTags), length(wireTags), wire3D, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bspline_surface = addBSplineSurface

"""
    gmsh.model.occ.addBezierSurface(pointTags, numPointsU, tag = -1, wireTags = Cint[], wire3D = false)

Add a Bezier surface in the OpenCASCADE CAD representation, with `pointTags`
control points given as a single vector [Pu1v1, ... Pu`numPointsU`v1, Pu1v2,
...]. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. If `wireTags` is provided, trim the Bezier patch using
the provided wires: the first wire defines the external contour, the others
define holes. If `wire3D` is set, consider wire curves as 3D curves and project
them on the Bezier surface; otherwise consider the wire curves as defined in the
parametric space of the surface. Return the tag of the Bezier surface.

Return an integer.

Types:
 - `pointTags`: vector of integers
 - `numPointsU`: integer
 - `tag`: integer
 - `wireTags`: vector of integers
 - `wire3D`: boolean
"""
function addBezierSurface(pointTags, numPointsU, tag = -1, wireTags = Cint[], wire3D = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBezierSurface, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, pointTags), length(pointTags), numPointsU, tag, convert(Vector{Cint}, wireTags), length(wireTags), wire3D, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_bezier_surface = addBezierSurface

"""
    gmsh.model.occ.addTrimmedSurface(surfaceTag, wireTags = Cint[], wire3D = false, tag = -1)

Trim the surface `surfaceTag` with the wires `wireTags`, replacing any existing
trimming curves. The first wire defines the external contour, the others define
holes. If `wire3D` is set, consider wire curves as 3D curves and project them on
the surface; otherwise consider the wire curves as defined in the parametric
space of the surface. If `tag` is positive, set the tag explicitly; otherwise a
new tag is selected automatically. Return the tag of the trimmed surface.

Return an integer.

Types:
 - `surfaceTag`: integer
 - `wireTags`: vector of integers
 - `wire3D`: boolean
 - `tag`: integer
"""
function addTrimmedSurface(surfaceTag, wireTags = Cint[], wire3D = false, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddTrimmedSurface, gmsh.lib), Cint,
          (Cint, Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          surfaceTag, convert(Vector{Cint}, wireTags), length(wireTags), wire3D, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_trimmed_surface = addTrimmedSurface

"""
    gmsh.model.occ.addSurfaceLoop(surfaceTags, tag = -1, sewing = false)

Add a surface loop (a closed shell) in the OpenCASCADE CAD representation,
formed by `surfaceTags`.  If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. Return the tag of the surface
loop. Setting `sewing` allows one to build a shell made of surfaces that share
geometrically identical (but topologically different) curves.

Return an integer.

Types:
 - `surfaceTags`: vector of integers
 - `tag`: integer
 - `sewing`: boolean
"""
function addSurfaceLoop(surfaceTags, tag = -1, sewing = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddSurfaceLoop, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Cint, Ptr{Cint}),
          convert(Vector{Cint}, surfaceTags), length(surfaceTags), tag, sewing, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_surface_loop = addSurfaceLoop

"""
    gmsh.model.occ.addVolume(shellTags, tag = -1)

Add a volume (a region) in the OpenCASCADE CAD representation, defined by one or
more surface loops `shellTags`. The first surface loop defines the exterior
boundary; additional surface loop define holes. If `tag` is positive, set the
tag explicitly; otherwise a new tag is selected automatically. Return the tag of
the volume.

Return an integer.

Types:
 - `shellTags`: vector of integers
 - `tag`: integer
"""
function addVolume(shellTags, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddVolume, gmsh.lib), Cint,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          convert(Vector{Cint}, shellTags), length(shellTags), tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_volume = addVolume

"""
    gmsh.model.occ.addSphere(xc, yc, zc, radius, tag = -1, angle1 = -pi/2, angle2 = pi/2, angle3 = 2*pi)

Add a sphere of center (`xc`, `yc`, `zc`) and radius `r` in the OpenCASCADE CAD
representation. The optional `angle1` and `angle2` arguments define the polar
angle opening (from -Pi/2 to Pi/2). The optional `angle3` argument defines the
azimuthal opening (from 0 to 2*Pi). If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. Return the tag of the
sphere.

Return an integer.

Types:
 - `xc`: double
 - `yc`: double
 - `zc`: double
 - `radius`: double
 - `tag`: integer
 - `angle1`: double
 - `angle2`: double
 - `angle3`: double
"""
function addSphere(xc, yc, zc, radius, tag = -1, angle1 = -pi/2, angle2 = pi/2, angle3 = 2*pi)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddSphere, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          xc, yc, zc, radius, tag, angle1, angle2, angle3, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_sphere = addSphere

"""
    gmsh.model.occ.addBox(x, y, z, dx, dy, dz, tag = -1)

Add a parallelepipedic box in the OpenCASCADE CAD representation, defined by a
point (`x`, `y`, `z`) and the extents along the x-, y- and z-axes. If `tag` is
positive, set the tag explicitly; otherwise a new tag is selected automatically.
Return the tag of the box.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `tag`: integer
"""
function addBox(x, y, z, dx, dy, dz, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddBox, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Ptr{Cint}),
          x, y, z, dx, dy, dz, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_box = addBox

"""
    gmsh.model.occ.addCylinder(x, y, z, dx, dy, dz, r, tag = -1, angle = 2*pi)

Add a cylinder in the OpenCASCADE CAD representation, defined by the center
(`x`, `y`, `z`) of its first circular face, the 3 components (`dx`, `dy`, `dz`)
of the vector defining its axis and its radius `r`. The optional `angle`
argument defines the angular opening (from 0 to 2*Pi). If `tag` is positive, set
the tag explicitly; otherwise a new tag is selected automatically. Return the
tag of the cylinder.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `r`: double
 - `tag`: integer
 - `angle`: double
"""
function addCylinder(x, y, z, dx, dy, dz, r, tag = -1, angle = 2*pi)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddCylinder, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Ptr{Cint}),
          x, y, z, dx, dy, dz, r, tag, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_cylinder = addCylinder

"""
    gmsh.model.occ.addCone(x, y, z, dx, dy, dz, r1, r2, tag = -1, angle = 2*pi)

Add a cone in the OpenCASCADE CAD representation, defined by the center (`x`,
`y`, `z`) of its first circular face, the 3 components of the vector (`dx`,
`dy`, `dz`) defining its axis and the two radii `r1` and `r2` of the faces
(these radii can be zero). If `tag` is positive, set the tag explicitly;
otherwise a new tag is selected automatically. `angle` defines the optional
angular opening (from 0 to 2*Pi). Return the tag of the cone.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `r1`: double
 - `r2`: double
 - `tag`: integer
 - `angle`: double
"""
function addCone(x, y, z, dx, dy, dz, r1, r2, tag = -1, angle = 2*pi)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddCone, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Ptr{Cint}),
          x, y, z, dx, dy, dz, r1, r2, tag, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_cone = addCone

"""
    gmsh.model.occ.addWedge(x, y, z, dx, dy, dz, tag = -1, ltx = 0., zAxis = Cdouble[])

Add a right angular wedge in the OpenCASCADE CAD representation, defined by the
right-angle point (`x`, `y`, `z`) and the 3 extends along the x-, y- and z-axes
(`dx`, `dy`, `dz`). If `tag` is positive, set the tag explicitly; otherwise a
new tag is selected automatically. The optional argument `ltx` defines the top
extent along the x-axis. If a vector `zAxis` of size 3 is provided, use it to
define the z-axis. Return the tag of the wedge.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `tag`: integer
 - `ltx`: double
 - `zAxis`: vector of doubles
"""
function addWedge(x, y, z, dx, dy, dz, tag = -1, ltx = 0., zAxis = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddWedge, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          x, y, z, dx, dy, dz, tag, ltx, convert(Vector{Cdouble}, zAxis), length(zAxis), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_wedge = addWedge

"""
    gmsh.model.occ.addTorus(x, y, z, r1, r2, tag = -1, angle = 2*pi, zAxis = Cdouble[])

Add a torus in the OpenCASCADE CAD representation, defined by its center (`x`,
`y`, `z`) and its 2 radii `r` and `r2`. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. The optional argument
`angle` defines the angular opening (from 0 to 2*Pi). If a vector `zAxis` of
size 3 is provided, use it to define the z-axis. Return the tag of the torus.

Return an integer.

Types:
 - `x`: double
 - `y`: double
 - `z`: double
 - `r1`: double
 - `r2`: double
 - `tag`: integer
 - `angle`: double
 - `zAxis`: vector of doubles
"""
function addTorus(x, y, z, r1, r2, tag = -1, angle = 2*pi, zAxis = Cdouble[])
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccAddTorus, gmsh.lib), Cint,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cint, Cdouble, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          x, y, z, r1, r2, tag, angle, convert(Vector{Cdouble}, zAxis), length(zAxis), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_torus = addTorus

"""
    gmsh.model.occ.addThruSections(wireTags, tag = -1, makeSolid = true, makeRuled = false, maxDegree = -1, continuity = "", parametrization = "", smoothing = false)

Add a volume (if the optional argument `makeSolid` is set) or surfaces in the
OpenCASCADE CAD representation, defined through the open or closed wires
`wireTags`. If `tag` is positive, set the tag explicitly; otherwise a new tag is
selected automatically. The new entities are returned in `outDimTags` as a
vector of (dim, tag) pairs. If the optional argument `makeRuled` is set, the
surfaces created on the boundary are forced to be ruled surfaces. If `maxDegree`
is positive, set the maximal degree of resulting surface. The optional argument
`continuity` allows to specify the continuity of the resulting shape ("C0",
"G1", "C1", "G2", "C2", "C3", "CN"). The optional argument `parametrization`
sets the parametrization type ("ChordLength", "Centripetal", "IsoParametric").
The optional argument `smoothing` determines if smoothing is applied.

Return `outDimTags`.

Types:
 - `wireTags`: vector of integers
 - `outDimTags`: vector of pairs of integers
 - `tag`: integer
 - `makeSolid`: boolean
 - `makeRuled`: boolean
 - `maxDegree`: integer
 - `continuity`: string
 - `parametrization`: string
 - `smoothing`: boolean
"""
function addThruSections(wireTags, tag = -1, makeSolid = true, makeRuled = false, maxDegree = -1, continuity = "", parametrization = "", smoothing = false)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccAddThruSections, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Cint, Cint, Cint, Ptr{Cchar}, Ptr{Cchar}, Cint, Ptr{Cint}),
          convert(Vector{Cint}, wireTags), length(wireTags), api_outDimTags_, api_outDimTags_n_, tag, makeSolid, makeRuled, maxDegree, continuity, parametrization, smoothing, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const add_thru_sections = addThruSections

"""
    gmsh.model.occ.addThickSolid(volumeTag, excludeSurfaceTags, offset, tag = -1)

Add a hollowed volume in the OpenCASCADE CAD representation, built from an
initial volume `volumeTag` and a set of faces from this volume
`excludeSurfaceTags`, which are to be removed. The remaining faces of the volume
become the walls of the hollowed solid, with thickness `offset`. If `tag` is
positive, set the tag explicitly; otherwise a new tag is selected automatically.

Return `outDimTags`.

Types:
 - `volumeTag`: integer
 - `excludeSurfaceTags`: vector of integers
 - `offset`: double
 - `outDimTags`: vector of pairs of integers
 - `tag`: integer
"""
function addThickSolid(volumeTag, excludeSurfaceTags, offset, tag = -1)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccAddThickSolid, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}, Csize_t, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          volumeTag, convert(Vector{Cint}, excludeSurfaceTags), length(excludeSurfaceTags), offset, api_outDimTags_, api_outDimTags_n_, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const add_thick_solid = addThickSolid

"""
    gmsh.model.occ.extrude(dimTags, dx, dy, dz, numElements = Cint[], heights = Cdouble[], recombine = false)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation, using a translation along (`dx`, `dy`, `dz`).
Return extruded entities in `outDimTags`. If the `numElements` vector is not
empty, also extrude the mesh: the entries in `numElements` give the number of
elements in each layer. If the `height` vector is not empty, it provides the
(cumulative) height of the different layers, normalized to 1. If `recombine` is
set, recombine the mesh in the layers.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dx`: double
 - `dy`: double
 - `dz`: double
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
"""
function extrude(dimTags, dx, dy, dz, numElements = Cint[], heights = Cdouble[], recombine = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccExtrude, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dx, dy, dz, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.revolve(dimTags, x, y, z, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)

Extrude the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation, using a rotation of `angle` radians around the
axis of revolution defined by the point (`x`, `y`, `z`) and the direction (`ax`,
`ay`, `az`). Return extruded entities in `outDimTags`. If the `numElements`
vector is not empty, also extrude the mesh: the entries in `numElements` give
the number of elements in each layer. If the `height` vector is not empty, it
provides the (cumulative) height of the different layers, normalized to 1. When
the mesh is extruded the angle should be strictly smaller than 2*Pi. If
`recombine` is set, recombine the mesh in the layers.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `ax`: double
 - `ay`: double
 - `az`: double
 - `angle`: double
 - `outDimTags`: vector of pairs of integers
 - `numElements`: vector of integers
 - `heights`: vector of doubles
 - `recombine`: boolean
"""
function revolve(dimTags, x, y, z, ax, ay, az, angle, numElements = Cint[], heights = Cdouble[], recombine = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccRevolve, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, ax, ay, az, angle, api_outDimTags_, api_outDimTags_n_, convert(Vector{Cint}, numElements), length(numElements), convert(Vector{Cdouble}, heights), length(heights), recombine, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.addPipe(dimTags, wireTag, trihedron = "")

Add a pipe in the OpenCASCADE CAD representation, by extruding the entities
`dimTags` (given as a vector of (dim, tag) pairs) along the wire `wireTag`. The
type of sweep can be specified with `trihedron` (possible values:
"DiscreteTrihedron", "CorrectedFrenet", "Fixed", "Frenet", "ConstantNormal",
"Darboux", "GuideAC", "GuidePlan", "GuideACWithContact",
"GuidePlanWithContact"). If `trihedron` is not provided, "DiscreteTrihedron" is
assumed. Return the pipe in `outDimTags`.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `wireTag`: integer
 - `outDimTags`: vector of pairs of integers
 - `trihedron`: string
"""
function addPipe(dimTags, wireTag, trihedron = "")
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccAddPipe, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cchar}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, wireTag, api_outDimTags_, api_outDimTags_n_, trihedron, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const add_pipe = addPipe

"""
    gmsh.model.occ.fillet(volumeTags, curveTags, radii, removeVolume = true)

Fillet the volumes `volumeTags` on the curves `curveTags` with radii `radii`.
The `radii` vector can either contain a single radius, as many radii as
`curveTags`, or twice as many as `curveTags` (in which case different radii are
provided for the begin and end points of the curves). Return the filleted
entities in `outDimTags` as a vector of (dim, tag) pairs. Remove the original
volume if `removeVolume` is set.

Return `outDimTags`.

Types:
 - `volumeTags`: vector of integers
 - `curveTags`: vector of integers
 - `radii`: vector of doubles
 - `outDimTags`: vector of pairs of integers
 - `removeVolume`: boolean
"""
function fillet(volumeTags, curveTags, radii, removeVolume = true)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccFillet, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          convert(Vector{Cint}, volumeTags), length(volumeTags), convert(Vector{Cint}, curveTags), length(curveTags), convert(Vector{Cdouble}, radii), length(radii), api_outDimTags_, api_outDimTags_n_, removeVolume, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.chamfer(volumeTags, curveTags, surfaceTags, distances, removeVolume = true)

Chamfer the volumes `volumeTags` on the curves `curveTags` with distances
`distances` measured on surfaces `surfaceTags`. The `distances` vector can
either contain a single distance, as many distances as `curveTags` and
`surfaceTags`, or twice as many as `curveTags` and `surfaceTags` (in which case
the first in each pair is measured on the corresponding surface in
`surfaceTags`, the other on the other adjacent surface). Return the chamfered
entities in `outDimTags`. Remove the original volume if `removeVolume` is set.

Return `outDimTags`.

Types:
 - `volumeTags`: vector of integers
 - `curveTags`: vector of integers
 - `surfaceTags`: vector of integers
 - `distances`: vector of doubles
 - `outDimTags`: vector of pairs of integers
 - `removeVolume`: boolean
"""
function chamfer(volumeTags, curveTags, surfaceTags, distances, removeVolume = true)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccChamfer, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          convert(Vector{Cint}, volumeTags), length(volumeTags), convert(Vector{Cint}, curveTags), length(curveTags), convert(Vector{Cint}, surfaceTags), length(surfaceTags), convert(Vector{Cdouble}, distances), length(distances), api_outDimTags_, api_outDimTags_n_, removeVolume, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.defeature(volumeTags, surfaceTags, removeVolume = true)

Defeature the volumes `volumeTags` by removing the surfaces `surfaceTags`.
Return the defeatured entities in `outDimTags`. Remove the original volume if
`removeVolume` is set.

Return `outDimTags`.

Types:
 - `volumeTags`: vector of integers
 - `surfaceTags`: vector of integers
 - `outDimTags`: vector of pairs of integers
 - `removeVolume`: boolean
"""
function defeature(volumeTags, surfaceTags, removeVolume = true)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccDefeature, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          convert(Vector{Cint}, volumeTags), length(volumeTags), convert(Vector{Cint}, surfaceTags), length(surfaceTags), api_outDimTags_, api_outDimTags_n_, removeVolume, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.fillet2D(edgeTag1, edgeTag2, radius, tag = -1, pointTag = -1, reverse = false)

Create a fillet edge between edges `edgeTag1` and `edgeTag2` with radius
`radius`. The modifed edges keep their tag. If `tag` is positive, set the tag
explicitly; otherwise a new tag is selected automatically. If `pointTag` is
positive, set the point on the edge at which the fillet is created. If `reverse`
is set, the normal of the plane through the two planes is reversed before the
fillet is created.

Return an integer.

Types:
 - `edgeTag1`: integer
 - `edgeTag2`: integer
 - `radius`: double
 - `tag`: integer
 - `pointTag`: integer
 - `reverse`: boolean
"""
function fillet2D(edgeTag1, edgeTag2, radius, tag = -1, pointTag = -1, reverse = false)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccFillet2D, gmsh.lib), Cint,
          (Cint, Cint, Cdouble, Cint, Cint, Cint, Ptr{Cint}),
          edgeTag1, edgeTag2, radius, tag, pointTag, reverse, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const fillet2_d = fillet2D

"""
    gmsh.model.occ.chamfer2D(edgeTag1, edgeTag2, distance1, distance2, tag = -1)

Create a chamfer edge between edges `edgeTag1` and `edgeTag2` with distance1
`distance1` and distance2 `distance2`. The modifed edges keep their tag. If
`tag` is positive, set the tag explicitly; otherwise a new tag is selected
automatically.

Return an integer.

Types:
 - `edgeTag1`: integer
 - `edgeTag2`: integer
 - `distance1`: double
 - `distance2`: double
 - `tag`: integer
"""
function chamfer2D(edgeTag1, edgeTag2, distance1, distance2, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccChamfer2D, gmsh.lib), Cint,
          (Cint, Cint, Cdouble, Cdouble, Cint, Ptr{Cint}),
          edgeTag1, edgeTag2, distance1, distance2, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const chamfer2_d = chamfer2D

"""
    gmsh.model.occ.offsetCurve(curveLoopTag, offset)

Create an offset curve based on the curve loop `curveLoopTag` with offset
`offset`. Return the offset curves in `outDimTags` as a vector of (dim, tag)
pairs.

Return `outDimTags`.

Types:
 - `curveLoopTag`: integer
 - `offset`: double
 - `outDimTags`: vector of pairs of integers
"""
function offsetCurve(curveLoopTag, offset)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccOffsetCurve, gmsh.lib), Cvoid,
          (Cint, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          curveLoopTag, offset, api_outDimTags_, api_outDimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const offset_curve = offsetCurve

"""
    gmsh.model.occ.getDistance(dim1, tag1, dim2, tag2)

Find the minimal distance between shape with `dim1` and `tag1` and shape with
`dim2` and `tag2` and the according coordinates. Return the distance in
`distance` and the coordinate of the points as `x1`, `y1`, `z1` and `x2`, `y2`,
`z2`.

Return `distance`, `x1`, `y1`, `z1`, `x2`, `y2`, `z2`.

Types:
 - `dim1`: integer
 - `tag1`: integer
 - `dim2`: integer
 - `tag2`: integer
 - `distance`: double
 - `x1`: double
 - `y1`: double
 - `z1`: double
 - `x2`: double
 - `y2`: double
 - `z2`: double
"""
function getDistance(dim1, tag1, dim2, tag2)
    api_distance_ = Ref{Cdouble}()
    api_x1_ = Ref{Cdouble}()
    api_y1_ = Ref{Cdouble}()
    api_z1_ = Ref{Cdouble}()
    api_x2_ = Ref{Cdouble}()
    api_y2_ = Ref{Cdouble}()
    api_z2_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetDistance, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Cint, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cint}),
          dim1, tag1, dim2, tag2, api_distance_, api_x1_, api_y1_, api_z1_, api_x2_, api_y2_, api_z2_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_distance_[], api_x1_[], api_y1_[], api_z1_[], api_x2_[], api_y2_[], api_z2_[]
end
const get_distance = getDistance

"""
    gmsh.model.occ.fuse(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)

Compute the boolean union (the fusion) of the entities `objectDimTags` and
`toolDimTags` (vectors of (dim, tag) pairs) in the OpenCASCADE CAD
representation. Return the resulting entities in `outDimTags`, and the
correspondance between input and resulting entities in `outDimTagsMap`. If `tag`
is positive, try to set the tag explicitly (only valid if the boolean operation
results in a single entity). Remove the object if `removeObject` is set. Remove
the tool if `removeTool` is set.

Return `outDimTags`, `outDimTagsMap`.

Types:
 - `objectDimTags`: vector of pairs of integers
 - `toolDimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `outDimTagsMap`: vector of vectors of pairs of integers
 - `tag`: integer
 - `removeObject`: boolean
 - `removeTool`: boolean
"""
function fuse(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)
    api_objectDimTags_ = collect(Cint, Iterators.flatten(objectDimTags))
    api_objectDimTags_n_ = length(api_objectDimTags_)
    api_toolDimTags_ = collect(Cint, Iterators.flatten(toolDimTags))
    api_toolDimTags_n_ = length(api_toolDimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    api_outDimTagsMap_ = Ref{Ptr{Ptr{Cint}}}()
    api_outDimTagsMap_n_ = Ref{Ptr{Csize_t}}()
    api_outDimTagsMap_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccFuse, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Cint, Ptr{Cint}),
          api_objectDimTags_, api_objectDimTags_n_, api_toolDimTags_, api_toolDimTags_n_, api_outDimTags_, api_outDimTags_n_, api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_, tag, removeObject, removeTool, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    tmp_api_outDimTagsMap_ = unsafe_wrap(Array, api_outDimTagsMap_[], api_outDimTagsMap_nn_[], own = true)
    tmp_api_outDimTagsMap_n_ = unsafe_wrap(Array, api_outDimTagsMap_n_[], api_outDimTagsMap_nn_[], own = true)
    outDimTagsMap = Vector{Tuple{Cint,Cint}}[]
    resize!(outDimTagsMap, api_outDimTagsMap_nn_[])
    for i in 1:api_outDimTagsMap_nn_[]
        tmp = unsafe_wrap(Array, tmp_api_outDimTagsMap_[i], tmp_api_outDimTagsMap_n_[i], own = true)
        outDimTagsMap[i] = [(tmp[i], tmp[i+1]) for i in 1:2:length(tmp)]
    end
    return outDimTags, outDimTagsMap
end

"""
    gmsh.model.occ.intersect(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)

Compute the boolean intersection (the common parts) of the entities
`objectDimTags` and `toolDimTags` (vectors of (dim, tag) pairs) in the
OpenCASCADE CAD representation. Return the resulting entities in `outDimTags`,
and the correspondance between input and resulting entities in `outDimTagsMap`.
If `tag` is positive, try to set the tag explicitly (only valid if the boolean
operation results in a single entity). Remove the object if `removeObject` is
set. Remove the tool if `removeTool` is set.

Return `outDimTags`, `outDimTagsMap`.

Types:
 - `objectDimTags`: vector of pairs of integers
 - `toolDimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `outDimTagsMap`: vector of vectors of pairs of integers
 - `tag`: integer
 - `removeObject`: boolean
 - `removeTool`: boolean
"""
function intersect(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)
    api_objectDimTags_ = collect(Cint, Iterators.flatten(objectDimTags))
    api_objectDimTags_n_ = length(api_objectDimTags_)
    api_toolDimTags_ = collect(Cint, Iterators.flatten(toolDimTags))
    api_toolDimTags_n_ = length(api_toolDimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    api_outDimTagsMap_ = Ref{Ptr{Ptr{Cint}}}()
    api_outDimTagsMap_n_ = Ref{Ptr{Csize_t}}()
    api_outDimTagsMap_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccIntersect, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Cint, Ptr{Cint}),
          api_objectDimTags_, api_objectDimTags_n_, api_toolDimTags_, api_toolDimTags_n_, api_outDimTags_, api_outDimTags_n_, api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_, tag, removeObject, removeTool, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    tmp_api_outDimTagsMap_ = unsafe_wrap(Array, api_outDimTagsMap_[], api_outDimTagsMap_nn_[], own = true)
    tmp_api_outDimTagsMap_n_ = unsafe_wrap(Array, api_outDimTagsMap_n_[], api_outDimTagsMap_nn_[], own = true)
    outDimTagsMap = Vector{Tuple{Cint,Cint}}[]
    resize!(outDimTagsMap, api_outDimTagsMap_nn_[])
    for i in 1:api_outDimTagsMap_nn_[]
        tmp = unsafe_wrap(Array, tmp_api_outDimTagsMap_[i], tmp_api_outDimTagsMap_n_[i], own = true)
        outDimTagsMap[i] = [(tmp[i], tmp[i+1]) for i in 1:2:length(tmp)]
    end
    return outDimTags, outDimTagsMap
end

"""
    gmsh.model.occ.cut(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)

Compute the boolean difference between the entities `objectDimTags` and
`toolDimTags` (given as vectors of (dim, tag) pairs) in the OpenCASCADE CAD
representation. Return the resulting entities in `outDimTags`, and the
correspondance between input and resulting entities in `outDimTagsMap`. If `tag`
is positive, try to set the tag explicitly (only valid if the boolean operation
results in a single entity). Remove the object if `removeObject` is set. Remove
the tool if `removeTool` is set.

Return `outDimTags`, `outDimTagsMap`.

Types:
 - `objectDimTags`: vector of pairs of integers
 - `toolDimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `outDimTagsMap`: vector of vectors of pairs of integers
 - `tag`: integer
 - `removeObject`: boolean
 - `removeTool`: boolean
"""
function cut(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)
    api_objectDimTags_ = collect(Cint, Iterators.flatten(objectDimTags))
    api_objectDimTags_n_ = length(api_objectDimTags_)
    api_toolDimTags_ = collect(Cint, Iterators.flatten(toolDimTags))
    api_toolDimTags_n_ = length(api_toolDimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    api_outDimTagsMap_ = Ref{Ptr{Ptr{Cint}}}()
    api_outDimTagsMap_n_ = Ref{Ptr{Csize_t}}()
    api_outDimTagsMap_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccCut, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Cint, Ptr{Cint}),
          api_objectDimTags_, api_objectDimTags_n_, api_toolDimTags_, api_toolDimTags_n_, api_outDimTags_, api_outDimTags_n_, api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_, tag, removeObject, removeTool, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    tmp_api_outDimTagsMap_ = unsafe_wrap(Array, api_outDimTagsMap_[], api_outDimTagsMap_nn_[], own = true)
    tmp_api_outDimTagsMap_n_ = unsafe_wrap(Array, api_outDimTagsMap_n_[], api_outDimTagsMap_nn_[], own = true)
    outDimTagsMap = Vector{Tuple{Cint,Cint}}[]
    resize!(outDimTagsMap, api_outDimTagsMap_nn_[])
    for i in 1:api_outDimTagsMap_nn_[]
        tmp = unsafe_wrap(Array, tmp_api_outDimTagsMap_[i], tmp_api_outDimTagsMap_n_[i], own = true)
        outDimTagsMap[i] = [(tmp[i], tmp[i+1]) for i in 1:2:length(tmp)]
    end
    return outDimTags, outDimTagsMap
end

"""
    gmsh.model.occ.fragment(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)

Compute the boolean fragments (general fuse) resulting from the intersection of
the entities `objectDimTags` and `toolDimTags` (given as vectors of (dim, tag)
pairs) in the OpenCASCADE CAD representation, making all interfaces conformal.
When applied to entities of different dimensions, the lower dimensional entities
will be automatically embedded in the higher dimensional entities if they are
not on their boundary. Return the resulting entities in `outDimTags`, and the
correspondance between input and resulting entities in `outDimTagsMap`. If `tag`
is positive, try to set the tag explicitly (only valid if the boolean operation
results in a single entity). Remove the object if `removeObject` is set. Remove
the tool if `removeTool` is set.

Return `outDimTags`, `outDimTagsMap`.

Types:
 - `objectDimTags`: vector of pairs of integers
 - `toolDimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
 - `outDimTagsMap`: vector of vectors of pairs of integers
 - `tag`: integer
 - `removeObject`: boolean
 - `removeTool`: boolean
"""
function fragment(objectDimTags, toolDimTags, tag = -1, removeObject = true, removeTool = true)
    api_objectDimTags_ = collect(Cint, Iterators.flatten(objectDimTags))
    api_objectDimTags_n_ = length(api_objectDimTags_)
    api_toolDimTags_ = collect(Cint, Iterators.flatten(toolDimTags))
    api_toolDimTags_n_ = length(api_toolDimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    api_outDimTagsMap_ = Ref{Ptr{Ptr{Cint}}}()
    api_outDimTagsMap_n_ = Ref{Ptr{Csize_t}}()
    api_outDimTagsMap_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccFragment, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Cint, Cint, Ptr{Cint}),
          api_objectDimTags_, api_objectDimTags_n_, api_toolDimTags_, api_toolDimTags_n_, api_outDimTags_, api_outDimTags_n_, api_outDimTagsMap_, api_outDimTagsMap_n_, api_outDimTagsMap_nn_, tag, removeObject, removeTool, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    tmp_api_outDimTagsMap_ = unsafe_wrap(Array, api_outDimTagsMap_[], api_outDimTagsMap_nn_[], own = true)
    tmp_api_outDimTagsMap_n_ = unsafe_wrap(Array, api_outDimTagsMap_n_[], api_outDimTagsMap_nn_[], own = true)
    outDimTagsMap = Vector{Tuple{Cint,Cint}}[]
    resize!(outDimTagsMap, api_outDimTagsMap_nn_[])
    for i in 1:api_outDimTagsMap_nn_[]
        tmp = unsafe_wrap(Array, tmp_api_outDimTagsMap_[i], tmp_api_outDimTagsMap_n_[i], own = true)
        outDimTagsMap[i] = [(tmp[i], tmp[i+1]) for i in 1:2:length(tmp)]
    end
    return outDimTags, outDimTagsMap
end

"""
    gmsh.model.occ.translate(dimTags, dx, dy, dz)

Translate the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation along (`dx`, `dy`, `dz`).

Types:
 - `dimTags`: vector of pairs of integers
 - `dx`: double
 - `dy`: double
 - `dz`: double
"""
function translate(dimTags, dx, dy, dz)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccTranslate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dx, dy, dz, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.rotate(dimTags, x, y, z, ax, ay, az, angle)

Rotate the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation by `angle` radians around the axis of revolution
defined by the point (`x`, `y`, `z`) and the direction (`ax`, `ay`, `az`).

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `ax`: double
 - `ay`: double
 - `az`: double
 - `angle`: double
"""
function rotate(dimTags, x, y, z, ax, ay, az, angle)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccRotate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, ax, ay, az, angle, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.dilate(dimTags, x, y, z, a, b, c)

Scale the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation by factors `a`, `b` and `c` along the three
coordinate axes; use (`x`, `y`, `z`) as the center of the homothetic
transformation.

Types:
 - `dimTags`: vector of pairs of integers
 - `x`: double
 - `y`: double
 - `z`: double
 - `a`: double
 - `b`: double
 - `c`: double
"""
function dilate(dimTags, x, y, z, a, b, c)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccDilate, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, x, y, z, a, b, c, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.mirror(dimTags, a, b, c, d)

Mirror the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation, with respect to the plane of equation `a` * x +
`b` * y + `c` * z + `d` = 0.

Types:
 - `dimTags`: vector of pairs of integers
 - `a`: double
 - `b`: double
 - `c`: double
 - `d`: double
"""
function mirror(dimTags, a, b, c, d)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccMirror, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, a, b, c, d, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.symmetrize(dimTags, a, b, c, d)

Mirror the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation, with respect to the plane of equation `a` * x +
`b` * y + `c` * z + `d` = 0. (This is a deprecated synonym for `mirror`.)

Types:
 - `dimTags`: vector of pairs of integers
 - `a`: double
 - `b`: double
 - `c`: double
 - `d`: double
"""
function symmetrize(dimTags, a, b, c, d)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccSymmetrize, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, a, b, c, d, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.affineTransform(dimTags, affineTransform)

Apply a general affine transformation matrix `affineTransform` (16 entries of a
4x4 matrix, by row; only the 12 first can be provided for convenience) to the
entities `dimTags` (given as a vector of (dim, tag) pairs) in the OpenCASCADE
CAD representation.

Types:
 - `dimTags`: vector of pairs of integers
 - `affineTransform`: vector of doubles
"""
function affineTransform(dimTags, affineTransform)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccAffineTransform, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, convert(Vector{Cdouble}, affineTransform), length(affineTransform), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const affine_transform = affineTransform

"""
    gmsh.model.occ.copy(dimTags)

Copy the entities `dimTags` in the OpenCASCADE CAD representation; the new
entities are returned in `outDimTags`.

Return `outDimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `outDimTags`: vector of pairs of integers
"""
function copy(dimTags)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccCopy, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, api_outDimTags_, api_outDimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end

"""
    gmsh.model.occ.remove(dimTags, recursive = false)

Remove the entities `dimTags` (given as a vector of (dim, tag) pairs) in the
OpenCASCADE CAD representation, provided that they are not on the boundary of
higher-dimensional entities. If `recursive` is true, remove all the entities on
their boundaries, down to dimension 0.

Types:
 - `dimTags`: vector of pairs of integers
 - `recursive`: boolean
"""
function remove(dimTags, recursive = false)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccRemove, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, recursive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.model.occ.removeAllDuplicates()

Remove all duplicate entities in the OpenCASCADE CAD representation (different
entities at the same geometrical location) after intersecting (using boolean
fragments) all highest dimensional entities.
"""
function removeAllDuplicates()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccRemoveAllDuplicates, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const remove_all_duplicates = removeAllDuplicates

"""
    gmsh.model.occ.healShapes(dimTags = Tuple{Cint,Cint}[], tolerance = 1e-8, fixDegenerated = true, fixSmallEdges = true, fixSmallFaces = true, sewFaces = true, makeSolids = true)

Apply various healing procedures to the entities `dimTags` (given as a vector of
(dim, tag) pairs), or to all the entities in the model if `dimTags` is empty, in
the OpenCASCADE CAD representation. Return the healed entities in `outDimTags`.

Return `outDimTags`.

Types:
 - `outDimTags`: vector of pairs of integers
 - `dimTags`: vector of pairs of integers
 - `tolerance`: double
 - `fixDegenerated`: boolean
 - `fixSmallEdges`: boolean
 - `fixSmallFaces`: boolean
 - `sewFaces`: boolean
 - `makeSolids`: boolean
"""
function healShapes(dimTags = Tuple{Cint,Cint}[], tolerance = 1e-8, fixDegenerated = true, fixSmallEdges = true, fixSmallFaces = true, sewFaces = true, makeSolids = true)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccHealShapes, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}, Csize_t, Cdouble, Cint, Cint, Cint, Cint, Cint, Ptr{Cint}),
          api_outDimTags_, api_outDimTags_n_, api_dimTags_, api_dimTags_n_, tolerance, fixDegenerated, fixSmallEdges, fixSmallFaces, sewFaces, makeSolids, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const heal_shapes = healShapes

"""
    gmsh.model.occ.convertToNURBS(dimTags)

Convert the entities `dimTags` to NURBS.

Types:
 - `dimTags`: vector of pairs of integers
"""
function convertToNURBS(dimTags)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccConvertToNURBS, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const convert_to_nurbs = convertToNURBS

"""
    gmsh.model.occ.importShapes(fileName, highestDimOnly = true, format = "")

Import BREP, STEP or IGES shapes from the file `fileName` in the OpenCASCADE CAD
representation. The imported entities are returned in `outDimTags`, as a vector
of (dim, tag) pairs. If the optional argument `highestDimOnly` is set, only
import the highest dimensional entities in the file. The optional argument
`format` can be used to force the format of the file (currently "brep", "step"
or "iges").

Return `outDimTags`.

Types:
 - `fileName`: string
 - `outDimTags`: vector of pairs of integers
 - `highestDimOnly`: boolean
 - `format`: string
"""
function importShapes(fileName, highestDimOnly = true, format = "")
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccImportShapes, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cchar}, Ptr{Cint}),
          fileName, api_outDimTags_, api_outDimTags_n_, highestDimOnly, format, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const import_shapes = importShapes

"""
    gmsh.model.occ.importShapesNativePointer(shape, highestDimOnly = true)

Import an OpenCASCADE `shape` by providing a pointer to a native OpenCASCADE
`TopoDS_Shape` object (passed as a pointer to void). The imported entities are
returned in `outDimTags` as a vector of (dim, tag) pairs. If the optional
argument `highestDimOnly` is set, only import the highest dimensional entities
in `shape`. In Python, this function can be used for integration with PythonOCC,
in which the SwigPyObject pointer of `TopoDS_Shape` must be passed as an int to
`shape`, i.e., `shape = int(pythonocc_shape.this)`. Warning: this function is
unsafe, as providing an invalid pointer will lead to undefined behavior.

Return `outDimTags`.

Types:
 - `shape`: pointer
 - `outDimTags`: vector of pairs of integers
 - `highestDimOnly`: boolean
"""
function importShapesNativePointer(shape, highestDimOnly = true)
    api_outDimTags_ = Ref{Ptr{Cint}}()
    api_outDimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccImportShapesNativePointer, gmsh.lib), Cvoid,
          (Ptr{Cvoid}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          shape, api_outDimTags_, api_outDimTags_n_, highestDimOnly, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_outDimTags_ = unsafe_wrap(Array, api_outDimTags_[], api_outDimTags_n_[], own = true)
    outDimTags = [ (tmp_api_outDimTags_[i], tmp_api_outDimTags_[i+1]) for i in 1:2:length(tmp_api_outDimTags_) ]
    return outDimTags
end
const import_shapes_native_pointer = importShapesNativePointer

"""
    gmsh.model.occ.getEntities(dim = -1)

Get all the OpenCASCADE entities. If `dim` is >= 0, return only the entities of
the specified dimension (e.g. points if `dim` == 0). The entities are returned
as a vector of (dim, tag) pairs.

Return `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function getEntities(dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetEntities, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_entities = getEntities

"""
    gmsh.model.occ.getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim = -1)

Get the OpenCASCADE entities in the bounding box defined by the two points
(`xmin`, `ymin`, `zmin`) and (`xmax`, `ymax`, `zmax`). If `dim` is >= 0, return
only the entities of the specified dimension (e.g. points if `dim` == 0).

Return `dimTags`.

Types:
 - `xmin`: double
 - `ymin`: double
 - `zmin`: double
 - `xmax`: double
 - `ymax`: double
 - `zmax`: double
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function getEntitiesInBoundingBox(xmin, ymin, zmin, xmax, ymax, zmax, dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetEntitiesInBoundingBox, gmsh.lib), Cvoid,
          (Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          xmin, ymin, zmin, xmax, ymax, zmax, api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return dimTags
end
const get_entities_in_bounding_box = getEntitiesInBoundingBox

"""
    gmsh.model.occ.getBoundingBox(dim, tag)

Get the bounding box (`xmin`, `ymin`, `zmin`), (`xmax`, `ymax`, `zmax`) of the
OpenCASCADE entity of dimension `dim` and tag `tag`.

Return `xmin`, `ymin`, `zmin`, `xmax`, `ymax`, `zmax`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `xmin`: double
 - `ymin`: double
 - `zmin`: double
 - `xmax`: double
 - `ymax`: double
 - `zmax`: double
"""
function getBoundingBox(dim, tag)
    api_xmin_ = Ref{Cdouble}()
    api_ymin_ = Ref{Cdouble}()
    api_zmin_ = Ref{Cdouble}()
    api_xmax_ = Ref{Cdouble}()
    api_ymax_ = Ref{Cdouble}()
    api_zmax_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetBoundingBox, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cint}),
          dim, tag, api_xmin_, api_ymin_, api_zmin_, api_xmax_, api_ymax_, api_zmax_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_xmin_[], api_ymin_[], api_zmin_[], api_xmax_[], api_ymax_[], api_zmax_[]
end
const get_bounding_box = getBoundingBox

"""
    gmsh.model.occ.getCurveLoops(surfaceTag)

Get the tags `curveLoopTags` of the curve loops making up the surface of tag
`surfaceTag`, as well as the tags `curveTags` of the curves making up each curve
loop.

Return `curveLoopTags`, `curveTags`.

Types:
 - `surfaceTag`: integer
 - `curveLoopTags`: vector of integers
 - `curveTags`: vector of vectors of integers
"""
function getCurveLoops(surfaceTag)
    api_curveLoopTags_ = Ref{Ptr{Cint}}()
    api_curveLoopTags_n_ = Ref{Csize_t}()
    api_curveTags_ = Ref{Ptr{Ptr{Cint}}}()
    api_curveTags_n_ = Ref{Ptr{Csize_t}}()
    api_curveTags_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetCurveLoops, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          surfaceTag, api_curveLoopTags_, api_curveLoopTags_n_, api_curveTags_, api_curveTags_n_, api_curveTags_nn_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    curveLoopTags = unsafe_wrap(Array, api_curveLoopTags_[], api_curveLoopTags_n_[], own = true)
    tmp_api_curveTags_ = unsafe_wrap(Array, api_curveTags_[], api_curveTags_nn_[], own = true)
    tmp_api_curveTags_n_ = unsafe_wrap(Array, api_curveTags_n_[], api_curveTags_nn_[], own = true)
    curveTags = [ unsafe_wrap(Array, tmp_api_curveTags_[i], tmp_api_curveTags_n_[i], own = true) for i in 1:api_curveTags_nn_[] ]
    return curveLoopTags, curveTags
end
const get_curve_loops = getCurveLoops

"""
    gmsh.model.occ.getSurfaceLoops(volumeTag)

Get the tags `surfaceLoopTags` of the surface loops making up the volume of tag
`volumeTag`, as well as the tags `surfaceTags` of the surfaces making up each
surface loop.

Return `surfaceLoopTags`, `surfaceTags`.

Types:
 - `volumeTag`: integer
 - `surfaceLoopTags`: vector of integers
 - `surfaceTags`: vector of vectors of integers
"""
function getSurfaceLoops(volumeTag)
    api_surfaceLoopTags_ = Ref{Ptr{Cint}}()
    api_surfaceLoopTags_n_ = Ref{Csize_t}()
    api_surfaceTags_ = Ref{Ptr{Ptr{Cint}}}()
    api_surfaceTags_n_ = Ref{Ptr{Csize_t}}()
    api_surfaceTags_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetSurfaceLoops, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cint}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          volumeTag, api_surfaceLoopTags_, api_surfaceLoopTags_n_, api_surfaceTags_, api_surfaceTags_n_, api_surfaceTags_nn_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    surfaceLoopTags = unsafe_wrap(Array, api_surfaceLoopTags_[], api_surfaceLoopTags_n_[], own = true)
    tmp_api_surfaceTags_ = unsafe_wrap(Array, api_surfaceTags_[], api_surfaceTags_nn_[], own = true)
    tmp_api_surfaceTags_n_ = unsafe_wrap(Array, api_surfaceTags_n_[], api_surfaceTags_nn_[], own = true)
    surfaceTags = [ unsafe_wrap(Array, tmp_api_surfaceTags_[i], tmp_api_surfaceTags_n_[i], own = true) for i in 1:api_surfaceTags_nn_[] ]
    return surfaceLoopTags, surfaceTags
end
const get_surface_loops = getSurfaceLoops

"""
    gmsh.model.occ.getMass(dim, tag)

Get the mass of the OpenCASCADE entity of dimension `dim` and tag `tag`. If no
density is attached to the entity (the default), the value corresponds
respectively to the length, area and volume for `dim` = 1, 2 and 3.

Return `mass`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `mass`: double
"""
function getMass(dim, tag)
    api_mass_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetMass, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Ptr{Cint}),
          dim, tag, api_mass_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_mass_[]
end
const get_mass = getMass

"""
    gmsh.model.occ.getCenterOfMass(dim, tag)

Get the center of mass of the OpenCASCADE entity of dimension `dim` and tag
`tag`.

Return `x`, `y`, `z`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `x`: double
 - `y`: double
 - `z`: double
"""
function getCenterOfMass(dim, tag)
    api_x_ = Ref{Cdouble}()
    api_y_ = Ref{Cdouble}()
    api_z_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetCenterOfMass, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cdouble}, Ptr{Cint}),
          dim, tag, api_x_, api_y_, api_z_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_x_[], api_y_[], api_z_[]
end
const get_center_of_mass = getCenterOfMass

"""
    gmsh.model.occ.getMatrixOfInertia(dim, tag)

Get the matrix of inertia (by row) of the OpenCASCADE entity of dimension `dim`
and tag `tag`.

Return `mat`.

Types:
 - `dim`: integer
 - `tag`: integer
 - `mat`: vector of doubles
"""
function getMatrixOfInertia(dim, tag)
    api_mat_ = Ref{Ptr{Cdouble}}()
    api_mat_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccGetMatrixOfInertia, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          dim, tag, api_mat_, api_mat_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    mat = unsafe_wrap(Array, api_mat_[], api_mat_n_[], own = true)
    return mat
end
const get_matrix_of_inertia = getMatrixOfInertia

"""
    gmsh.model.occ.getMaxTag(dim)

Get the maximum tag of entities of dimension `dim` in the OpenCASCADE CAD
representation.

Return an integer.

Types:
 - `dim`: integer
"""
function getMaxTag(dim)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshModelOccGetMaxTag, gmsh.lib), Cint,
          (Cint, Ptr{Cint}),
          dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_max_tag = getMaxTag

"""
    gmsh.model.occ.setMaxTag(dim, maxTag)

Set the maximum tag `maxTag` for entities of dimension `dim` in the OpenCASCADE
CAD representation.

Types:
 - `dim`: integer
 - `maxTag`: integer
"""
function setMaxTag(dim, maxTag)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccSetMaxTag, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          dim, maxTag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_max_tag = setMaxTag

"""
    gmsh.model.occ.synchronize()

Synchronize the OpenCASCADE CAD representation with the current Gmsh model. This
can be called at any time, but since it involves a non trivial amount of
processing, the number of synchronization points should normally be minimized.
Without synchronization the entities in the OpenCASCADE CAD representation are
not available to any function outside of the OpenCASCADE CAD kernel functions.
"""
function synchronize()
    ierr = Ref{Cint}()
    ccall((:gmshModelOccSynchronize, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    module gmsh.model.occ.mesh

OpenCASCADE CAD kernel meshing constraints
"""
module mesh

import ....gmsh

"""
    gmsh.model.occ.mesh.setSize(dimTags, size)

Set a mesh size constraint on the entities `dimTags` (given as a vector of (dim,
tag) pairs) in the OpenCASCADE CAD representation. Currently only entities of
dimension 0 (points) are handled.

Types:
 - `dimTags`: vector of pairs of integers
 - `size`: double
"""
function setSize(dimTags, size)
    api_dimTags_ = collect(Cint, Iterators.flatten(dimTags))
    api_dimTags_n_ = length(api_dimTags_)
    ierr = Ref{Cint}()
    ccall((:gmshModelOccMeshSetSize, gmsh.lib), Cvoid,
          (Ptr{Cint}, Csize_t, Cdouble, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, size, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_size = setSize

end # end of module mesh

end # end of module occ

end # end of module model

"""
    module gmsh.view

Post-processing view functions
"""
module view

import ..gmsh

"""
    gmsh.view.add(name, tag = -1)

Add a new post-processing view, with name `name`. If `tag` is positive use it
(and remove the view with that tag if it already exists), otherwise associate a
new tag. Return the view tag.

Return an integer.

Types:
 - `name`: string
 - `tag`: integer
"""
function add(name, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshViewAdd, gmsh.lib), Cint,
          (Ptr{Cchar}, Cint, Ptr{Cint}),
          name, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end

"""
    gmsh.view.remove(tag)

Remove the view with tag `tag`.

Types:
 - `tag`: integer
"""
function remove(tag)
    ierr = Ref{Cint}()
    ccall((:gmshViewRemove, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.view.getIndex(tag)

Get the index of the view with tag `tag` in the list of currently loaded views.
This dynamic index (it can change when views are removed) is used to access view
options.

Return an integer.

Types:
 - `tag`: integer
"""
function getIndex(tag)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshViewGetIndex, gmsh.lib), Cint,
          (Cint, Ptr{Cint}),
          tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_index = getIndex

"""
    gmsh.view.getTags()

Get the tags of all views.

Return `tags`.

Types:
 - `tags`: vector of integers
"""
function getTags()
    api_tags_ = Ref{Ptr{Cint}}()
    api_tags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshViewGetTags, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_tags_, api_tags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    return tags
end
const get_tags = getTags

"""
    gmsh.view.addModelData(tag, step, modelName, dataType, tags, data, time = 0., numComponents = -1, partition = 0)

Add model-based post-processing data to the view with tag `tag`. `modelName`
identifies the model the data is attached to. `dataType` specifies the type of
data, currently either "NodeData", "ElementData" or "ElementNodeData". `step`
specifies the identifier (>= 0) of the data in a sequence. `tags` gives the tags
of the nodes or elements in the mesh to which the data is associated. `data` is
a vector of the same length as `tags`: each entry is the vector of double
precision numbers representing the data associated with the corresponding tag.
The optional `time` argument associate a time value with the data.
`numComponents` gives the number of data components (1 for scalar data, 3 for
vector data, etc.) per entity; if negative, it is automatically inferred (when
possible) from the input data. `partition` allows one to specify data in several
sub-sets.

Types:
 - `tag`: integer
 - `step`: integer
 - `modelName`: string
 - `dataType`: string
 - `tags`: vector of sizes
 - `data`: vector of vectors of doubles
 - `time`: double
 - `numComponents`: integer
 - `partition`: integer
"""
function addModelData(tag, step, modelName, dataType, tags, data, time = 0., numComponents = -1, partition = 0)
    api_data_n_ = [ length(data[i]) for i in 1:length(data) ]
    ierr = Ref{Cint}()
    ccall((:gmshViewAddModelData, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cchar}, Ptr{Csize_t}, Csize_t, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Csize_t, Cdouble, Cint, Cint, Ptr{Cint}),
          tag, step, modelName, dataType, convert(Vector{Csize_t}, tags), length(tags), convert(Vector{Vector{Cdouble}},data), api_data_n_, length(data), time, numComponents, partition, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_model_data = addModelData

"""
    gmsh.view.addHomogeneousModelData(tag, step, modelName, dataType, tags, data, time = 0., numComponents = -1, partition = 0)

Add homogeneous model-based post-processing data to the view with tag `tag`. The
arguments have the same meaning as in `addModelData`, except that `data` is
supposed to be homogeneous and is thus flattened in a single vector. For data
types that can lead to different data sizes per tag (like "ElementNodeData"),
the data should be padded.

Types:
 - `tag`: integer
 - `step`: integer
 - `modelName`: string
 - `dataType`: string
 - `tags`: vector of sizes
 - `data`: vector of doubles
 - `time`: double
 - `numComponents`: integer
 - `partition`: integer
"""
function addHomogeneousModelData(tag, step, modelName, dataType, tags, data, time = 0., numComponents = -1, partition = 0)
    ierr = Ref{Cint}()
    ccall((:gmshViewAddHomogeneousModelData, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cchar}, Ptr{Cchar}, Ptr{Csize_t}, Csize_t, Ptr{Cdouble}, Csize_t, Cdouble, Cint, Cint, Ptr{Cint}),
          tag, step, modelName, dataType, convert(Vector{Csize_t}, tags), length(tags), convert(Vector{Cdouble}, data), length(data), time, numComponents, partition, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_homogeneous_model_data = addHomogeneousModelData

"""
    gmsh.view.getModelData(tag, step)

Get model-based post-processing data from the view with tag `tag` at step
`step`. Return the `data` associated to the nodes or the elements with tags
`tags`, as well as the `dataType` and the number of components `numComponents`.

Return `dataType`, `tags`, `data`, `time`, `numComponents`.

Types:
 - `tag`: integer
 - `step`: integer
 - `dataType`: string
 - `tags`: vector of sizes
 - `data`: vector of vectors of doubles
 - `time`: double
 - `numComponents`: integer
"""
function getModelData(tag, step)
    api_dataType_ = Ref{Ptr{Cchar}}()
    api_tags_ = Ref{Ptr{Csize_t}}()
    api_tags_n_ = Ref{Csize_t}()
    api_data_ = Ref{Ptr{Ptr{Cdouble}}}()
    api_data_n_ = Ref{Ptr{Csize_t}}()
    api_data_nn_ = Ref{Csize_t}()
    api_time_ = Ref{Cdouble}()
    api_numComponents_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshViewGetModelData, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cdouble}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cdouble}, Ptr{Cint}, Ptr{Cint}),
          tag, step, api_dataType_, api_tags_, api_tags_n_, api_data_, api_data_n_, api_data_nn_, api_time_, api_numComponents_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    dataType = unsafe_string(api_dataType_[])
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    tmp_api_data_ = unsafe_wrap(Array, api_data_[], api_data_nn_[], own = true)
    tmp_api_data_n_ = unsafe_wrap(Array, api_data_n_[], api_data_nn_[], own = true)
    data = [ unsafe_wrap(Array, tmp_api_data_[i], tmp_api_data_n_[i], own = true) for i in 1:api_data_nn_[] ]
    return dataType, tags, data, api_time_[], api_numComponents_[]
end
const get_model_data = getModelData

"""
    gmsh.view.getHomogeneousModelData(tag, step)

Get homogeneous model-based post-processing data from the view with tag `tag` at
step `step`. The arguments have the same meaning as in `getModelData`, except
that `data` is returned flattened in a single vector, with the appropriate
padding if necessary.

Return `dataType`, `tags`, `data`, `time`, `numComponents`.

Types:
 - `tag`: integer
 - `step`: integer
 - `dataType`: string
 - `tags`: vector of sizes
 - `data`: vector of doubles
 - `time`: double
 - `numComponents`: integer
"""
function getHomogeneousModelData(tag, step)
    api_dataType_ = Ref{Ptr{Cchar}}()
    api_tags_ = Ref{Ptr{Csize_t}}()
    api_tags_n_ = Ref{Csize_t}()
    api_data_ = Ref{Ptr{Cdouble}}()
    api_data_n_ = Ref{Csize_t}()
    api_time_ = Ref{Cdouble}()
    api_numComponents_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshViewGetHomogeneousModelData, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cchar}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cdouble}, Ptr{Cint}, Ptr{Cint}),
          tag, step, api_dataType_, api_tags_, api_tags_n_, api_data_, api_data_n_, api_time_, api_numComponents_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    dataType = unsafe_string(api_dataType_[])
    tags = unsafe_wrap(Array, api_tags_[], api_tags_n_[], own = true)
    data = unsafe_wrap(Array, api_data_[], api_data_n_[], own = true)
    return dataType, tags, data, api_time_[], api_numComponents_[]
end
const get_homogeneous_model_data = getHomogeneousModelData

"""
    gmsh.view.addListData(tag, dataType, numEle, data)

Add list-based post-processing data to the view with tag `tag`. List-based
datasets are independent from any model and any mesh. `dataType` identifies the
data by concatenating the field type ("S" for scalar, "V" for vector, "T" for
tensor) and the element type ("P" for point, "L" for line, "T" for triangle, "S"
for tetrahedron, "I" for prism, "H" for hexaHedron, "Y" for pyramid). For
example `dataType` should be "ST" for a scalar field on triangles. `numEle`
gives the number of elements in the data. `data` contains the data for the
`numEle` elements, concatenated, with node coordinates followed by values per
node, repeated for each step: [e1x1, ..., e1xn, e1y1, ..., e1yn, e1z1, ...,
e1zn, e1v1..., e1vN, e2x1, ...].

Types:
 - `tag`: integer
 - `dataType`: string
 - `numEle`: integer
 - `data`: vector of doubles
"""
function addListData(tag, dataType, numEle, data)
    ierr = Ref{Cint}()
    ccall((:gmshViewAddListData, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          tag, dataType, numEle, convert(Vector{Cdouble}, data), length(data), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_list_data = addListData

"""
    gmsh.view.getListData(tag, returnAdaptive = false)

Get list-based post-processing data from the view with tag `tag`. Return the
types `dataTypes`, the number of elements `numElements` for each data type and
the `data` for each data type. If `returnAdaptive` is set, return the data
obtained after adaptive refinement, if available.

Return `dataType`, `numElements`, `data`.

Types:
 - `tag`: integer
 - `dataType`: vector of strings
 - `numElements`: vector of integers
 - `data`: vector of vectors of doubles
 - `returnAdaptive`: boolean
"""
function getListData(tag, returnAdaptive = false)
    api_dataType_ = Ref{Ptr{Ptr{Cchar}}}()
    api_dataType_n_ = Ref{Csize_t}()
    api_numElements_ = Ref{Ptr{Cint}}()
    api_numElements_n_ = Ref{Csize_t}()
    api_data_ = Ref{Ptr{Ptr{Cdouble}}}()
    api_data_n_ = Ref{Ptr{Csize_t}}()
    api_data_nn_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshViewGetListData, gmsh.lib), Cvoid,
          (Cint, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cdouble}}}, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          tag, api_dataType_, api_dataType_n_, api_numElements_, api_numElements_n_, api_data_, api_data_n_, api_data_nn_, returnAdaptive, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dataType_ = unsafe_wrap(Array, api_dataType_[], api_dataType_n_[], own = true)
    dataType = [unsafe_string(tmp_api_dataType_[i]) for i in 1:length(tmp_api_dataType_) ]
    numElements = unsafe_wrap(Array, api_numElements_[], api_numElements_n_[], own = true)
    tmp_api_data_ = unsafe_wrap(Array, api_data_[], api_data_nn_[], own = true)
    tmp_api_data_n_ = unsafe_wrap(Array, api_data_n_[], api_data_nn_[], own = true)
    data = [ unsafe_wrap(Array, tmp_api_data_[i], tmp_api_data_n_[i], own = true) for i in 1:api_data_nn_[] ]
    return dataType, numElements, data
end
const get_list_data = getListData

"""
    gmsh.view.addListDataString(tag, coord, data, style = [])

Add a string to a list-based post-processing view with tag `tag`. If `coord`
contains 3 coordinates the string is positioned in the 3D model space ("3D
string"); if it contains 2 coordinates it is positioned in the 2D graphics
viewport ("2D string"). `data` contains one or more (for multistep views)
strings. `style` contains key-value pairs of styling parameters, concatenated.
Available keys are "Font" (possible values: "Times-Roman", "Times-Bold", "Times-
Italic", "Times-BoldItalic", "Helvetica", "Helvetica-Bold", "Helvetica-Oblique",
"Helvetica-BoldOblique", "Courier", "Courier-Bold", "Courier-Oblique", "Courier-
BoldOblique", "Symbol", "ZapfDingbats", "Screen"), "FontSize" and "Align"
(possible values: "Left" or "BottomLeft", "Center" or "BottomCenter", "Right" or
"BottomRight", "TopLeft", "TopCenter", "TopRight", "CenterLeft", "CenterCenter",
"CenterRight").

Types:
 - `tag`: integer
 - `coord`: vector of doubles
 - `data`: vector of strings
 - `style`: vector of strings
"""
function addListDataString(tag, coord, data, style = [])
    ierr = Ref{Cint}()
    ccall((:gmshViewAddListDataString, gmsh.lib), Cvoid,
          (Cint, Ptr{Cdouble}, Csize_t, Ptr{Ptr{Cchar}}, Csize_t, Ptr{Ptr{Cchar}}, Csize_t, Ptr{Cint}),
          tag, convert(Vector{Cdouble}, coord), length(coord), data, length(data), style, length(style), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const add_list_data_string = addListDataString

"""
    gmsh.view.getListDataStrings(tag, dim)

Get list-based post-processing data strings (2D strings if `dim` == 2, 3D
strings if `dim` = 3) from the view with tag `tag`. Return the coordinates in
`coord`, the strings in `data` and the styles in `style`.

Return `coord`, `data`, `style`.

Types:
 - `tag`: integer
 - `dim`: integer
 - `coord`: vector of doubles
 - `data`: vector of strings
 - `style`: vector of strings
"""
function getListDataStrings(tag, dim)
    api_coord_ = Ref{Ptr{Cdouble}}()
    api_coord_n_ = Ref{Csize_t}()
    api_data_ = Ref{Ptr{Ptr{Cchar}}}()
    api_data_n_ = Ref{Csize_t}()
    api_style_ = Ref{Ptr{Ptr{Cchar}}}()
    api_style_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshViewGetListDataStrings, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          tag, dim, api_coord_, api_coord_n_, api_data_, api_data_n_, api_style_, api_style_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    coord = unsafe_wrap(Array, api_coord_[], api_coord_n_[], own = true)
    tmp_api_data_ = unsafe_wrap(Array, api_data_[], api_data_n_[], own = true)
    data = [unsafe_string(tmp_api_data_[i]) for i in 1:length(tmp_api_data_) ]
    tmp_api_style_ = unsafe_wrap(Array, api_style_[], api_style_n_[], own = true)
    style = [unsafe_string(tmp_api_style_[i]) for i in 1:length(tmp_api_style_) ]
    return coord, data, style
end
const get_list_data_strings = getListDataStrings

"""
    gmsh.view.setInterpolationMatrices(tag, type, d, coef, exp, dGeo = 0, coefGeo = Cdouble[], expGeo = Cdouble[])

Set interpolation matrices for the element family `type` ("Line", "Triangle",
"Quadrangle", "Tetrahedron", "Hexahedron", "Prism", "Pyramid") in the view
`tag`. The approximation of the values over an element is written as a linear
combination of `d` basis functions f_i(u, v, w) = sum_(j = 0, ..., `d` - 1)
`coef`[i][j] u^`exp`[j][0] v^`exp`[j][1] w^`exp`[j][2], i = 0, ..., `d`-1, with
u, v, w the coordinates in the reference element. The `coef` matrix (of size `d`
x `d`) and the `exp` matrix (of size `d` x 3) are stored as vectors, by row. If
`dGeo` is positive, use `coefGeo` and `expGeo` to define the interpolation of
the x, y, z coordinates of the element in terms of the u, v, w coordinates, in
exactly the same way. If `d` < 0, remove the interpolation matrices.

Types:
 - `tag`: integer
 - `type`: string
 - `d`: integer
 - `coef`: vector of doubles
 - `exp`: vector of doubles
 - `dGeo`: integer
 - `coefGeo`: vector of doubles
 - `expGeo`: vector of doubles
"""
function setInterpolationMatrices(tag, type, d, coef, exp, dGeo = 0, coefGeo = Cdouble[], expGeo = Cdouble[])
    ierr = Ref{Cint}()
    ccall((:gmshViewSetInterpolationMatrices, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          tag, type, d, convert(Vector{Cdouble}, coef), length(coef), convert(Vector{Cdouble}, exp), length(exp), dGeo, convert(Vector{Cdouble}, coefGeo), length(coefGeo), convert(Vector{Cdouble}, expGeo), length(expGeo), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_interpolation_matrices = setInterpolationMatrices

"""
    gmsh.view.addAlias(refTag, copyOptions = false, tag = -1)

Add a post-processing view as an `alias` of the reference view with tag
`refTag`. If `copyOptions` is set, copy the options of the reference view. If
`tag` is positive use it (and remove the view with that tag if it already
exists), otherwise associate a new tag. Return the view tag.

Return an integer.

Types:
 - `refTag`: integer
 - `copyOptions`: boolean
 - `tag`: integer
"""
function addAlias(refTag, copyOptions = false, tag = -1)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshViewAddAlias, gmsh.lib), Cint,
          (Cint, Cint, Cint, Ptr{Cint}),
          refTag, copyOptions, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const add_alias = addAlias

"""
    gmsh.view.combine(what, how, remove = true, copyOptions = true)

Combine elements (if `what` == "elements") or steps (if `what` == "steps") of
all views (`how` == "all"), all visible views (`how` == "visible") or all views
having the same name (`how` == "name"). Remove original views if `remove` is
set.

Types:
 - `what`: string
 - `how`: string
 - `remove`: boolean
 - `copyOptions`: boolean
"""
function combine(what, how, remove = true, copyOptions = true)
    ierr = Ref{Cint}()
    ccall((:gmshViewCombine, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Cint, Cint, Ptr{Cint}),
          what, how, remove, copyOptions, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.view.probe(tag, x, y, z, step = -1, numComp = -1, gradient = false, distanceMax = 0., xElemCoord = Cdouble[], yElemCoord = Cdouble[], zElemCoord = Cdouble[], dim = -1)

Probe the view `tag` for its `values` at point (`x`, `y`, `z`). If no match is
found, `value` is returned empty. Return only the value at step `step` is `step`
is positive. Return only values with `numComp` if `numComp` is positive. Return
the gradient of the `values` if `gradient` is set. If `distanceMax` is zero,
only return a result if an exact match inside an element in the view is found;
if `distanceMax` is positive and an exact match is not found, return the value
at the closest node if it is closer than `distanceMax`; if `distanceMax` is
negative and an exact match is not found, always return the value at the closest
node. The distance to the match is returned in `distance`. Return the result
from the element described by its coordinates if `xElementCoord`,
`yElementCoord` and `zElementCoord` are provided. If `dim` is >= 0, return only
matches from elements of the specified dimension.

Return `values`, `distance`.

Types:
 - `tag`: integer
 - `x`: double
 - `y`: double
 - `z`: double
 - `values`: vector of doubles
 - `distance`: double
 - `step`: integer
 - `numComp`: integer
 - `gradient`: boolean
 - `distanceMax`: double
 - `xElemCoord`: vector of doubles
 - `yElemCoord`: vector of doubles
 - `zElemCoord`: vector of doubles
 - `dim`: integer
"""
function probe(tag, x, y, z, step = -1, numComp = -1, gradient = false, distanceMax = 0., xElemCoord = Cdouble[], yElemCoord = Cdouble[], zElemCoord = Cdouble[], dim = -1)
    api_values_ = Ref{Ptr{Cdouble}}()
    api_values_n_ = Ref{Csize_t}()
    api_distance_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshViewProbe, gmsh.lib), Cvoid,
          (Cint, Cdouble, Cdouble, Cdouble, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cdouble}, Cint, Cint, Cint, Cdouble, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Ptr{Cdouble}, Csize_t, Cint, Ptr{Cint}),
          tag, x, y, z, api_values_, api_values_n_, api_distance_, step, numComp, gradient, distanceMax, convert(Vector{Cdouble}, xElemCoord), length(xElemCoord), convert(Vector{Cdouble}, yElemCoord), length(yElemCoord), convert(Vector{Cdouble}, zElemCoord), length(zElemCoord), dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    values = unsafe_wrap(Array, api_values_[], api_values_n_[], own = true)
    return values, api_distance_[]
end

"""
    gmsh.view.write(tag, fileName, append = false)

Write the view to a file `fileName`. The export format is determined by the file
extension. Append to the file if `append` is set.

Types:
 - `tag`: integer
 - `fileName`: string
 - `append`: boolean
"""
function write(tag, fileName, append = false)
    ierr = Ref{Cint}()
    ccall((:gmshViewWrite, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cint, Ptr{Cint}),
          tag, fileName, append, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.view.setVisibilityPerWindow(tag, value, windowIndex = 0)

Set the global visibility of the view `tag` per window to `value`, where
`windowIndex` identifies the window in the window list.

Types:
 - `tag`: integer
 - `value`: integer
 - `windowIndex`: integer
"""
function setVisibilityPerWindow(tag, value, windowIndex = 0)
    ierr = Ref{Cint}()
    ccall((:gmshViewSetVisibilityPerWindow, gmsh.lib), Cvoid,
          (Cint, Cint, Cint, Ptr{Cint}),
          tag, value, windowIndex, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_visibility_per_window = setVisibilityPerWindow

"""
    module gmsh.view.option

View option handling functions
"""
module option

import ...gmsh

"""
    gmsh.view.option.setNumber(tag, name, value)

Set the numerical option `name` to value `value` for the view with tag `tag`.

Types:
 - `tag`: integer
 - `name`: string
 - `value`: double
"""
function setNumber(tag, name, value)
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionSetNumber, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cdouble, Ptr{Cint}),
          tag, name, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.view.option.getNumber(tag, name)

Get the `value` of the numerical option `name` for the view with tag `tag`.

Return `value`.

Types:
 - `tag`: integer
 - `name`: string
 - `value`: double
"""
function getNumber(tag, name)
    api_value_ = Ref{Cdouble}()
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionGetNumber, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cdouble}, Ptr{Cint}),
          tag, name, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_value_[]
end
const get_number = getNumber

"""
    gmsh.view.option.setString(tag, name, value)

Set the string option `name` to value `value` for the view with tag `tag`.

Types:
 - `tag`: integer
 - `name`: string
 - `value`: string
"""
function setString(tag, name, value)
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionSetString, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          tag, name, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.view.option.getString(tag, name)

Get the `value` of the string option `name` for the view with tag `tag`.

Return `value`.

Types:
 - `tag`: integer
 - `name`: string
 - `value`: string
"""
function getString(tag, name)
    api_value_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionGetString, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Ptr{Cchar}}, Ptr{Cint}),
          tag, name, api_value_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    value = unsafe_string(api_value_[])
    return value
end
const get_string = getString

"""
    gmsh.view.option.setColor(tag, name, r, g, b, a = 255)

Set the color option `name` to the RGBA value (`r`, `g`, `b`, `a`) for the view
with tag `tag`, where where `r`, `g`, `b` and `a` should be integers between 0
and 255.

Types:
 - `tag`: integer
 - `name`: string
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
"""
function setColor(tag, name, r, g, b, a = 255)
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionSetColor, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Cint, Cint, Cint, Cint, Ptr{Cint}),
          tag, name, r, g, b, a, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_color = setColor

"""
    gmsh.view.option.getColor(tag, name)

Get the `r`, `g`, `b`, `a` value of the color option `name` for the view with
tag `tag`.

Return `r`, `g`, `b`, `a`.

Types:
 - `tag`: integer
 - `name`: string
 - `r`: integer
 - `g`: integer
 - `b`: integer
 - `a`: integer
"""
function getColor(tag, name)
    api_r_ = Ref{Cint}()
    api_g_ = Ref{Cint}()
    api_b_ = Ref{Cint}()
    api_a_ = Ref{Cint}()
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionGetColor, gmsh.lib), Cvoid,
          (Cint, Ptr{Cchar}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}, Ptr{Cint}),
          tag, name, api_r_, api_g_, api_b_, api_a_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_r_[], api_g_[], api_b_[], api_a_[]
end
const get_color = getColor

"""
    gmsh.view.option.copy(refTag, tag)

Copy the options from the view with tag `refTag` to the view with tag `tag`.

Types:
 - `refTag`: integer
 - `tag`: integer
"""
function copy(refTag, tag)
    ierr = Ref{Cint}()
    ccall((:gmshViewOptionCopy, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          refTag, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

end # end of module option

end # end of module view

"""
    module gmsh.algorithm

Raw algorithms
"""
module algorithm

import ..gmsh

"""
    gmsh.algorithm.triangulate(coordinates, edges = Csize_t[])

Triangulate the points given in the `coordinates` vector as concatenated pairs
of u, v coordinates, with (optional) constrained edges given in the `edges`
vector as pair of indexes (with numbering starting at 1), and return the
triangles as concatenated triplets of point indexes (with numbering starting at
1) in `triangles`.

Return `triangles`.

Types:
 - `coordinates`: vector of doubles
 - `triangles`: vector of sizes
 - `edges`: vector of sizes
"""
function triangulate(coordinates, edges = Csize_t[])
    api_triangles_ = Ref{Ptr{Csize_t}}()
    api_triangles_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshAlgorithmTriangulate, gmsh.lib), Cvoid,
          (Ptr{Cdouble}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Csize_t}, Csize_t, Ptr{Cint}),
          convert(Vector{Cdouble}, coordinates), length(coordinates), api_triangles_, api_triangles_n_, convert(Vector{Csize_t}, edges), length(edges), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    triangles = unsafe_wrap(Array, api_triangles_[], api_triangles_n_[], own = true)
    return triangles
end

"""
    gmsh.algorithm.tetrahedralize(coordinates)

Tetrahedralize the points given in the `coordinates` vector as concatenated
triplets of x, y, z coordinates, and return the tetrahedra as concatenated
quadruplets of point indexes (with numbering starting at 1) in `tetrahedra`.

Return `tetrahedra`.

Types:
 - `coordinates`: vector of doubles
 - `tetrahedra`: vector of sizes
"""
function tetrahedralize(coordinates)
    api_tetrahedra_ = Ref{Ptr{Csize_t}}()
    api_tetrahedra_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshAlgorithmTetrahedralize, gmsh.lib), Cvoid,
          (Ptr{Cdouble}, Csize_t, Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          convert(Vector{Cdouble}, coordinates), length(coordinates), api_tetrahedra_, api_tetrahedra_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tetrahedra = unsafe_wrap(Array, api_tetrahedra_[], api_tetrahedra_n_[], own = true)
    return tetrahedra
end

end # end of module algorithm

"""
    module gmsh.plugin

Plugin functions
"""
module plugin

import ..gmsh

"""
    gmsh.plugin.setNumber(name, option, value)

Set the numerical option `option` to the value `value` for plugin `name`.
Plugins available in the official Gmsh release are listed in the "Gmsh plugins"
chapter of the Gmsh reference manual
(https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-plugins).

Types:
 - `name`: string
 - `option`: string
 - `value`: double
"""
function setNumber(name, option, value)
    ierr = Ref{Cint}()
    ccall((:gmshPluginSetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Cdouble, Ptr{Cint}),
          name, option, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.plugin.setString(name, option, value)

Set the string option `option` to the value `value` for plugin `name`. Plugins
available in the official Gmsh release are listed in the "Gmsh plugins" chapter
of the Gmsh reference manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-
plugins).

Types:
 - `name`: string
 - `option`: string
 - `value`: string
"""
function setString(name, option, value)
    ierr = Ref{Cint}()
    ccall((:gmshPluginSetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          name, option, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.plugin.run(name)

Run the plugin `name`. Return the tag of the created view (if any). Plugins
available in the official Gmsh release are listed in the "Gmsh plugins" chapter
of the Gmsh reference manual (https://gmsh.info/doc/texinfo/gmsh.html#Gmsh-
plugins).

Return an integer.

Types:
 - `name`: string
"""
function run(name)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshPluginRun, gmsh.lib), Cint,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end

end # end of module plugin

"""
    module gmsh.graphics

Graphics functions
"""
module graphics

import ..gmsh

"""
    gmsh.graphics.draw()

Draw all the OpenGL scenes.
"""
function draw()
    ierr = Ref{Cint}()
    ccall((:gmshGraphicsDraw, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

end # end of module graphics

"""
    module gmsh.fltk

FLTK graphical user interface functions
"""
module fltk

import ..gmsh

"""
    gmsh.fltk.initialize()

Create the FLTK graphical user interface. Can only be called in the main thread.
"""
function initialize()
    ierr = Ref{Cint}()
    ccall((:gmshFltkInitialize, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.finalize()

Close the FLTK graphical user interface. Can only be called in the main thread.
"""
function finalize()
    ierr = Ref{Cint}()
    ccall((:gmshFltkFinalize, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.wait(time = -1.)

Wait at most `time` seconds for user interface events and return. If `time` < 0,
wait indefinitely. First automatically create the user interface if it has not
yet been initialized. Can only be called in the main thread.

Types:
 - `time`: double
"""
function wait(time = -1.)
    ierr = Ref{Cint}()
    ccall((:gmshFltkWait, gmsh.lib), Cvoid,
          (Cdouble, Ptr{Cint}),
          time, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.update()

Update the user interface (potentially creating new widgets and windows). First
automatically create the user interface if it has not yet been initialized. Can
only be called in the main thread: use `awake("update")` to trigger an update of
the user interface from another thread.
"""
function update()
    ierr = Ref{Cint}()
    ccall((:gmshFltkUpdate, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.awake(action = "")

Awake the main user interface thread and process pending events, and optionally
perform an action (currently the only `action` allowed is "update").

Types:
 - `action`: string
"""
function awake(action = "")
    ierr = Ref{Cint}()
    ccall((:gmshFltkAwake, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          action, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.lock()

Block the current thread until it can safely modify the user interface.
"""
function lock()
    ierr = Ref{Cint}()
    ccall((:gmshFltkLock, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.unlock()

Release the lock that was set using lock.
"""
function unlock()
    ierr = Ref{Cint}()
    ccall((:gmshFltkUnlock, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.run()

Run the event loop of the graphical user interface, i.e. repeatedly call
`wait()`. First automatically create the user interface if it has not yet been
initialized. Can only be called in the main thread.
"""
function run()
    ierr = Ref{Cint}()
    ccall((:gmshFltkRun, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.fltk.isAvailable()

Check if the user interface is available (e.g. to detect if it has been closed).

Return an integer.
"""
function isAvailable()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshFltkIsAvailable, gmsh.lib), Cint,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const is_available = isAvailable

"""
    gmsh.fltk.selectEntities(dim = -1)

Select entities in the user interface. Return the selected entities as a vector
of (dim, tag) pairs. If `dim` is >= 0, return only the entities of the specified
dimension (e.g. points if `dim` == 0).

Return an integer, `dimTags`.

Types:
 - `dimTags`: vector of pairs of integers
 - `dim`: integer
"""
function selectEntities(dim = -1)
    api_dimTags_ = Ref{Ptr{Cint}}()
    api_dimTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshFltkSelectEntities, gmsh.lib), Cint,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Cint, Ptr{Cint}),
          api_dimTags_, api_dimTags_n_, dim, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_dimTags_ = unsafe_wrap(Array, api_dimTags_[], api_dimTags_n_[], own = true)
    dimTags = [ (tmp_api_dimTags_[i], tmp_api_dimTags_[i+1]) for i in 1:2:length(tmp_api_dimTags_) ]
    return api_result_, dimTags
end
const select_entities = selectEntities

"""
    gmsh.fltk.selectElements()

Select elements in the user interface.

Return an integer, `elementTags`.

Types:
 - `elementTags`: vector of sizes
"""
function selectElements()
    api_elementTags_ = Ref{Ptr{Csize_t}}()
    api_elementTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshFltkSelectElements, gmsh.lib), Cint,
          (Ptr{Ptr{Csize_t}}, Ptr{Csize_t}, Ptr{Cint}),
          api_elementTags_, api_elementTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    elementTags = unsafe_wrap(Array, api_elementTags_[], api_elementTags_n_[], own = true)
    return api_result_, elementTags
end
const select_elements = selectElements

"""
    gmsh.fltk.selectViews()

Select views in the user interface.

Return an integer, `viewTags`.

Types:
 - `viewTags`: vector of integers
"""
function selectViews()
    api_viewTags_ = Ref{Ptr{Cint}}()
    api_viewTags_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshFltkSelectViews, gmsh.lib), Cint,
          (Ptr{Ptr{Cint}}, Ptr{Csize_t}, Ptr{Cint}),
          api_viewTags_, api_viewTags_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    viewTags = unsafe_wrap(Array, api_viewTags_[], api_viewTags_n_[], own = true)
    return api_result_, viewTags
end
const select_views = selectViews

"""
    gmsh.fltk.splitCurrentWindow(how = "v", ratio = 0.5)

Split the current window horizontally (if `how` == "h") or vertically (if `how`
== "v"), using ratio `ratio`. If `how` == "u", restore a single window.

Types:
 - `how`: string
 - `ratio`: double
"""
function splitCurrentWindow(how = "v", ratio = 0.5)
    ierr = Ref{Cint}()
    ccall((:gmshFltkSplitCurrentWindow, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cdouble, Ptr{Cint}),
          how, ratio, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const split_current_window = splitCurrentWindow

"""
    gmsh.fltk.setCurrentWindow(windowIndex = 0)

Set the current window by speficying its index (starting at 0) in the list of
all windows. When new windows are created by splits, new windows are appended at
the end of the list.

Types:
 - `windowIndex`: integer
"""
function setCurrentWindow(windowIndex = 0)
    ierr = Ref{Cint}()
    ccall((:gmshFltkSetCurrentWindow, gmsh.lib), Cvoid,
          (Cint, Ptr{Cint}),
          windowIndex, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_current_window = setCurrentWindow

"""
    gmsh.fltk.setStatusMessage(message, graphics = false)

Set a status message in the current window. If `graphics` is set, display the
message inside the graphic window instead of the status bar.

Types:
 - `message`: string
 - `graphics`: boolean
"""
function setStatusMessage(message, graphics = false)
    ierr = Ref{Cint}()
    ccall((:gmshFltkSetStatusMessage, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cint, Ptr{Cint}),
          message, graphics, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_status_message = setStatusMessage

"""
    gmsh.fltk.showContextWindow(dim, tag)

Show context window for the entity of dimension `dim` and tag `tag`.

Types:
 - `dim`: integer
 - `tag`: integer
"""
function showContextWindow(dim, tag)
    ierr = Ref{Cint}()
    ccall((:gmshFltkShowContextWindow, gmsh.lib), Cvoid,
          (Cint, Cint, Ptr{Cint}),
          dim, tag, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const show_context_window = showContextWindow

"""
    gmsh.fltk.openTreeItem(name)

Open the `name` item in the menu tree.

Types:
 - `name`: string
"""
function openTreeItem(name)
    ierr = Ref{Cint}()
    ccall((:gmshFltkOpenTreeItem, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const open_tree_item = openTreeItem

"""
    gmsh.fltk.closeTreeItem(name)

Close the `name` item in the menu tree.

Types:
 - `name`: string
"""
function closeTreeItem(name)
    ierr = Ref{Cint}()
    ccall((:gmshFltkCloseTreeItem, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const close_tree_item = closeTreeItem

end # end of module fltk

"""
    module gmsh.parser

Parser functions
"""
module parser

import ..gmsh

"""
    gmsh.parser.getNames(search = "")

Get the names of the variables in the Gmsh parser matching the `search` regular
expression. If `search` is empty, return all the names.

Return `names`.

Types:
 - `names`: vector of strings
 - `search`: string
"""
function getNames(search = "")
    api_names_ = Ref{Ptr{Ptr{Cchar}}}()
    api_names_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshParserGetNames, gmsh.lib), Cvoid,
          (Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cchar}, Ptr{Cint}),
          api_names_, api_names_n_, search, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_names_ = unsafe_wrap(Array, api_names_[], api_names_n_[], own = true)
    names = [unsafe_string(tmp_api_names_[i]) for i in 1:length(tmp_api_names_) ]
    return names
end
const get_names = getNames

"""
    gmsh.parser.setNumber(name, value)

Set the value of the number variable `name` in the Gmsh parser. Create the
variable if it does not exist; update the value if the variable exists.

Types:
 - `name`: string
 - `value`: vector of doubles
"""
function setNumber(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshParserSetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          name, convert(Vector{Cdouble}, value), length(value), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.parser.setString(name, value)

Set the value of the string variable `name` in the Gmsh parser. Create the
variable if it does not exist; update the value if the variable exists.

Types:
 - `name`: string
 - `value`: vector of strings
"""
function setString(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshParserSetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cchar}}, Csize_t, Ptr{Cint}),
          name, value, length(value), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.parser.getNumber(name)

Get the value of the number variable `name` from the Gmsh parser. Return an
empty vector if the variable does not exist.

Return `value`.

Types:
 - `name`: string
 - `value`: vector of doubles
"""
function getNumber(name)
    api_value_ = Ref{Ptr{Cdouble}}()
    api_value_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshParserGetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_value_, api_value_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    value = unsafe_wrap(Array, api_value_[], api_value_n_[], own = true)
    return value
end
const get_number = getNumber

"""
    gmsh.parser.getString(name)

Get the value of the string variable `name` from the Gmsh parser. Return an
empty vector if the variable does not exist.

Return `value`.

Types:
 - `name`: string
 - `value`: vector of strings
"""
function getString(name)
    api_value_ = Ref{Ptr{Ptr{Cchar}}}()
    api_value_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshParserGetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_value_, api_value_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_value_ = unsafe_wrap(Array, api_value_[], api_value_n_[], own = true)
    value = [unsafe_string(tmp_api_value_[i]) for i in 1:length(tmp_api_value_) ]
    return value
end
const get_string = getString

"""
    gmsh.parser.clear(name = "")

Clear all the Gmsh parser variables, or remove a single variable if `name` is
given.

Types:
 - `name`: string
"""
function clear(name = "")
    ierr = Ref{Cint}()
    ccall((:gmshParserClear, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.parser.parse(fileName)

Parse the file `fileName` with the Gmsh parser.

Types:
 - `fileName`: string
"""
function parse(fileName)
    ierr = Ref{Cint}()
    ccall((:gmshParserParse, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          fileName, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

end # end of module parser

"""
    module gmsh.onelab

ONELAB server functions
"""
module onelab

import ..gmsh

"""
    gmsh.onelab.set(data, format = "json")

Set one or more parameters in the ONELAB database, encoded in `format`.

Types:
 - `data`: string
 - `format`: string
"""
function set(data, format = "json")
    ierr = Ref{Cint}()
    ccall((:gmshOnelabSet, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          data, format, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.onelab.get(name = "", format = "json")

Get all the parameters (or a single one if `name` is specified) from the ONELAB
database, encoded in `format`.

Return `data`.

Types:
 - `data`: string
 - `name`: string
 - `format`: string
"""
function get(name = "", format = "json")
    api_data_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshOnelabGet, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cchar}}, Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          api_data_, name, format, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    data = unsafe_string(api_data_[])
    return data
end

"""
    gmsh.onelab.getNames(search = "")

Get the names of the parameters in the ONELAB database matching the `search`
regular expression. If `search` is empty, return all the names.

Return `names`.

Types:
 - `names`: vector of strings
 - `search`: string
"""
function getNames(search = "")
    api_names_ = Ref{Ptr{Ptr{Cchar}}}()
    api_names_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshOnelabGetNames, gmsh.lib), Cvoid,
          (Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cchar}, Ptr{Cint}),
          api_names_, api_names_n_, search, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_names_ = unsafe_wrap(Array, api_names_[], api_names_n_[], own = true)
    names = [unsafe_string(tmp_api_names_[i]) for i in 1:length(tmp_api_names_) ]
    return names
end
const get_names = getNames

"""
    gmsh.onelab.setNumber(name, value)

Set the value of the number parameter `name` in the ONELAB database. Create the
parameter if it does not exist; update the value if the parameter exists.

Types:
 - `name`: string
 - `value`: vector of doubles
"""
function setNumber(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshOnelabSetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cdouble}, Csize_t, Ptr{Cint}),
          name, convert(Vector{Cdouble}, value), length(value), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_number = setNumber

"""
    gmsh.onelab.setString(name, value)

Set the value of the string parameter `name` in the ONELAB database. Create the
parameter if it does not exist; update the value if the parameter exists.

Types:
 - `name`: string
 - `value`: vector of strings
"""
function setString(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshOnelabSetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cchar}}, Csize_t, Ptr{Cint}),
          name, value, length(value), ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_string = setString

"""
    gmsh.onelab.getNumber(name)

Get the value of the number parameter `name` from the ONELAB database. Return an
empty vector if the parameter does not exist.

Return `value`.

Types:
 - `name`: string
 - `value`: vector of doubles
"""
function getNumber(name)
    api_value_ = Ref{Ptr{Cdouble}}()
    api_value_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshOnelabGetNumber, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Cdouble}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_value_, api_value_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    value = unsafe_wrap(Array, api_value_[], api_value_n_[], own = true)
    return value
end
const get_number = getNumber

"""
    gmsh.onelab.getString(name)

Get the value of the string parameter `name` from the ONELAB database. Return an
empty vector if the parameter does not exist.

Return `value`.

Types:
 - `name`: string
 - `value`: vector of strings
"""
function getString(name)
    api_value_ = Ref{Ptr{Ptr{Cchar}}}()
    api_value_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshOnelabGetString, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          name, api_value_, api_value_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_value_ = unsafe_wrap(Array, api_value_[], api_value_n_[], own = true)
    value = [unsafe_string(tmp_api_value_[i]) for i in 1:length(tmp_api_value_) ]
    return value
end
const get_string = getString

"""
    gmsh.onelab.getChanged(name)

Check if any parameters in the ONELAB database used by the client `name` have
been changed.

Return an integer.

Types:
 - `name`: string
"""
function getChanged(name)
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshOnelabGetChanged, gmsh.lib), Cint,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_changed = getChanged

"""
    gmsh.onelab.setChanged(name, value)

Set the changed flag to value `value` for all the parameters in the ONELAB
database used by the client `name`.

Types:
 - `name`: string
 - `value`: integer
"""
function setChanged(name, value)
    ierr = Ref{Cint}()
    ccall((:gmshOnelabSetChanged, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Cint, Ptr{Cint}),
          name, value, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end
const set_changed = setChanged

"""
    gmsh.onelab.clear(name = "")

Clear the ONELAB database, or remove a single parameter if `name` is given.

Types:
 - `name`: string
"""
function clear(name = "")
    ierr = Ref{Cint}()
    ccall((:gmshOnelabClear, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cint}),
          name, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.onelab.run(name = "", command = "")

Run a ONELAB client. If `name` is provided, create a new ONELAB client with name
`name` and executes `command`. If not, try to run a client that might be linked
to the processed input files.

Types:
 - `name`: string
 - `command`: string
"""
function run(name = "", command = "")
    ierr = Ref{Cint}()
    ccall((:gmshOnelabRun, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          name, command, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

end # end of module onelab

"""
    module gmsh.logger

Information logging functions
"""
module logger

import ..gmsh

"""
    gmsh.logger.write(message, level = "info")

Write a `message`. `level` can be "info", "warning" or "error".

Types:
 - `message`: string
 - `level`: string
"""
function write(message, level = "info")
    ierr = Ref{Cint}()
    ccall((:gmshLoggerWrite, gmsh.lib), Cvoid,
          (Ptr{Cchar}, Ptr{Cchar}, Ptr{Cint}),
          message, level, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.logger.start()

Start logging messages.
"""
function start()
    ierr = Ref{Cint}()
    ccall((:gmshLoggerStart, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.logger.get()

Get logged messages.

Return `log`.

Types:
 - `log`: vector of strings
"""
function get()
    api_log_ = Ref{Ptr{Ptr{Cchar}}}()
    api_log_n_ = Ref{Csize_t}()
    ierr = Ref{Cint}()
    ccall((:gmshLoggerGet, gmsh.lib), Cvoid,
          (Ptr{Ptr{Ptr{Cchar}}}, Ptr{Csize_t}, Ptr{Cint}),
          api_log_, api_log_n_, ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    tmp_api_log_ = unsafe_wrap(Array, api_log_[], api_log_n_[], own = true)
    log = [unsafe_string(tmp_api_log_[i]) for i in 1:length(tmp_api_log_) ]
    return log
end

"""
    gmsh.logger.stop()

Stop logging messages.
"""
function stop()
    ierr = Ref{Cint}()
    ccall((:gmshLoggerStop, gmsh.lib), Cvoid,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return nothing
end

"""
    gmsh.logger.getWallTime()

Return wall clock time (in s).

Return a double.
"""
function getWallTime()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshLoggerGetWallTime, gmsh.lib), Cdouble,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_wall_time = getWallTime

"""
    gmsh.logger.getCpuTime()

Return CPU time (in s).

Return a double.
"""
function getCpuTime()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshLoggerGetCpuTime, gmsh.lib), Cdouble,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_cpu_time = getCpuTime

"""
    gmsh.logger.getMemory()

Return memory usage (in Mb).

Return a double.
"""
function getMemory()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshLoggerGetMemory, gmsh.lib), Cdouble,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_memory = getMemory

"""
    gmsh.logger.getTotalMemory()

Return total available memory (in Mb).

Return a double.
"""
function getTotalMemory()
    ierr = Ref{Cint}()
    api_result_ = ccall((:gmshLoggerGetTotalMemory, gmsh.lib), Cdouble,
          (Ptr{Cint},),
          ierr)
    ierr[] != 0 && error(gmsh.logger.getLastError())
    return api_result_
end
const get_total_memory = getTotalMemory

"""
    gmsh.logger.getLastError()

Return last error message, if any.

Return `error`.

Types:
 - `error`: string
"""
function getLastError()
    api_error_ = Ref{Ptr{Cchar}}()
    ierr = Ref{Cint}()
    ccall((:gmshLoggerGetLastError, gmsh.lib), Cvoid,
          (Ptr{Ptr{Cchar}}, Ptr{Cint}),
          api_error_, ierr)
    ierr[] != 0 && error("Could not get last error")
    error = unsafe_string(api_error_[])
    return error
end
const get_last_error = getLastError

end # end of module logger

end # end of module gmsh
